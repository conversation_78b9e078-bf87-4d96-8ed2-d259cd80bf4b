apply plugin: "org.sonarqube"
apply plugin: "jacoco"

jaco<PERSON> {
    toolVersion = "0.8.2"
}

//Additional SourceSets can be added to the jacocoOfflineSourceSets as needed by
project.ext.jacocoOfflineSourceSets = [ 'main' ]
task doJacocoOfflineInstrumentation(dependsOn: [ classes, project.configurations.jacocoAnt ]) {
    inputs.files classes.outputs.files
    File outputDir = new File(project.buildDir, 'instrumentedClasses')
    outputs.dir outputDir
    doFirst {
        project.delete(outputDir)
        ant.taskdef(
                resource: 'org/jacoco/ant/antlib.xml',
                classpath: project.configurations.jacocoAnt.asPath,
                uri: 'jacoco'
        )
        def instrumented = false
        jacocoOfflineSourceSets.each { sourceSetName ->

            File oldOutputDir = new File(project.buildDir, 'classes/java/main')  // 注意这是最新版本的写法，否则会在gradle 5.x下报错

            if (file(oldOutputDir).exists()) {

                def instrumentedClassedDir = "${outputDir}/${sourceSetName}"
                ant.'jacoco:instrument'(destdir: instrumentedClassedDir) {
                    fileset(dir: oldOutputDir, includes: '**/*.class')
                }

                sourceSets.test.runtimeClasspath -= files(oldOutputDir)
                sourceSets.test.runtimeClasspath += files(instrumentedClassedDir)
                instrumented = true
            }
        }
        if (instrumented) {
            test.jvmArgs += '-noverify'
        }
    }
}
test.dependsOn doJacocoOfflineInstrumentation

jacocoTestReport {
    reports {
        xml.enabled false
        csv.enabled false
        html.enabled true
    }
}
check.dependsOn jacocoTestReport

test {
    jacoco  {
        excludes += "*"
    }
    ignoreFailures = true
    jvmArgs +=  "-javaagent:${classpath.find { it.name.contains("kmock") }.absolutePath}"
}
