buildscript {
    ext {
        springBootVersion = '2.2.1.RELEASE' // 迁移到此版本
    }
    repositories {
        maven {
            //allowInsecureProtocol = true
            url 'http://artifactory.evcard.vip:8081/artifactory/maven-virtual/'
            credentials {  username 'admin';            password 'Extracme123' }
        }
        maven {
            //allowInsecureProtocol = true
            url "https://plugins.gradle.org/m2/"
        }
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath("org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:2.6.2") // 代码检查， 生成单测覆盖率
        classpath("gradle.plugin.com.gorylenko.gradle-git-properties:gradle-git-properties:2.0.0") // 打包时自动加入git信息
    }
}

apply plugin: 'java'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'com.gorylenko.gradle-git-properties'

repositories {
    mavenLocal()    // 使用此配置可以调试 存库中不存在而本地有的jar包，如本地mv install的公共组件，本地打包的proto jar包
    maven {
        //allowInsecureProtocol = true
        url 'http://artifactory.evcard.vip:8081/artifactory/maven-virtual/'
        credentials {  username 'admin';            password 'Extracme123' }
    }
    mavenCentral()
}

//bootJar {
//    archiveFileName = "md-act-service.jar"
//}

sourceCompatibility = 1.8

compileJava.options.encoding = 'UTF-8'
tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}

group = 'com.saicmobility.evcard1'
version = '1.0.5'

dependencyManagement {
    imports {
        mavenBom 'com.extracme.evcard:evcard:3.0.0'
        mavenBom 'org.junit:junit-bom:5.7.2'
    }
}

dependencies {
    compile('org.springframework.boot:spring-boot-starter')
    compile('com.github.ulisesbocchio:jasypt-spring-boot-starter:1.14')
    /**
     * krpc & envconfig
     */
    compile 'krpc:spring-boot-starter:1.0.45'
    compile 'com.saicmobility.common:envconfig:1.7.49'
    //? netty-all:4.0.2.Final & krpc:0.1.0
    compile 'io.netty:netty-all:4.1.50.Final'
    compile 'com.google.protobuf:protobuf-java:3.5.1'
    compile('com.saicmobility.common:scheduler:1.0.9')
    /**
     * apis
     */
    compile('com.saicmobility.evcard.md:mdactservice_protos:1.0.722')
    compile('com.saicmobility.evcard.md:mdstoreservice_protos:1.0.+')
    compile('com.saicmobility.evcard.md:mdgoodsservice_protos:1.0.+')
    compile('com.saicmobility.evcard.md:mduserservice_protos:1.0.+')
    compile('com.saicmobility.evcard.md:mdorderservice_protos:1.0.+')
    compile('com.saicmobility.evcard.md:mdstockservice_protos:1.0.+')
    compile('com.saicmobility.evcard.md:mdempservice_protos:1.0.+')

    compile('com.extracme.evcard:evcard-coupon-service-api:2.6.11')
    compile('com.extracme.evcard:evcard-activity-service-api:2.1.0')

    /**
     * components
     */
    //compile 'com.extracme:extracme-framework-core:1.0.0'
    //compile 'com.extracme.evcard:evcard-core-rpc:3.0.0'
    compile 'com.ctrip.framework.apollo:apollo-client:1.8.0'
    //implementation 'com.google.guava:guava'
    compile('org.springframework.boot:spring-boot-starter-web')
    compile 'org.apache.httpcomponents:httpclient:4.5.2'
    compile 'org.apache.commons:commons-pool2:2.5.0'
    /**
     * redis
     */
    compile 'org.springframework.boot:spring-boot-starter-data-redis:2.6.2'

    /**
     * db & mybatis
     */
    compile('com.alibaba:druid-spring-boot-starter')
    compile('org.springframework.boot:spring-boot-starter-jdbc')
    compile 'mysql:mysql-connector-java:8.0.25'
    compile('org.mybatis.spring.boot:mybatis-spring-boot-starter:2.1.1')
    compile(group: 'org.mybatis.spring.boot', name: 'mybatis-spring-boot-starter', version: '1.3.2')
    compile('com.baomidou:mybatis-plus-boot-starter:*******')
    implementation 'com.baomidou:mybatis-plus-generator:3.4.1'
    implementation 'org.freemarker:freemarker:2.3.31'

    /**
     * commons
     */
    compile 'com.alibaba:fastjson:1.2.83'
    compile 'org.springframework.boot:spring-boot-starter-validation'
    compile 'org.apache.commons:commons-lang3:3.8'
    compile 'org.apache.commons:commons-collections4:4.4'
    compile('io.swagger:swagger-annotations:1.5.22')
    implementation 'org.projectlombok:lombok:1.18.8'
    annotationProcessor 'org.projectlombok:lombok:1.18.8'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.2'
    testCompileOnly 'org.projectlombok:lombok:1.18.2'

    compile('com.xuxueli:xxl-job-core:1.9.2')
    compile 'com.aliyun.oss:aliyun-sdk-oss:3.5.0'
    compile 'com.aliyun.openservices:ons-client:1.8.8.Final'
    compile('com.extracme.evcard:evcard-mq-bean:3.0.0.+')

    //hutool
    implementation group: 'cn.hutool', name: 'hutool-all', version: '5.7.16'


    //Junit
//    testCompile group: 'org.jmockit', name: 'jmockit', version: '1.37'
    testCompile('org.springframework.boot:spring-boot-starter-test')
    testCompile 'junit:junit:4.12'
    testCompile('kmock:kmock:1.0.36')
}

apply from:"jacoco.gradle"
//apply from:"jacoco.gradle"

processResources {
    from('src/main/java') {
        include '**/*.xml'
    }
}