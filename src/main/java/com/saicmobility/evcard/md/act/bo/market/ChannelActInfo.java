package com.saicmobility.evcard.md.act.bo.market;

import lombok.Data;

@Data
public class ChannelActInfo {
    private long id; // id
    private String discountCode; //优惠码
    private String actName; //活动名称
    private int actType; //活动类型
    private String startDate; //开始时间
    private String endDate; //结束时间
    private int actStatus; //活动状态 1-生效 2-已过期
    private int channel;
    private String channelText;
}
