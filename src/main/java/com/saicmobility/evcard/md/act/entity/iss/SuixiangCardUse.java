package com.saicmobility.evcard.md.act.entity.iss;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 随享卡用户使用表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SuixiangCardUse对象", description="随享卡用户使用表")
@TableName("suixiang_card_use")
public class SuixiangCardUse extends Model<SuixiangCardUse> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "随享卡基础表id")
    private Long cardBaseId;

    @ApiModelProperty(value = "卡价格表主键")
    private Long cardPriceId;

    @ApiModelProperty(value = "会员pk_id")
    private Long userId;

    @ApiModelProperty(value = "购买记录表id")
    private Long purchaseId;

    @ApiModelProperty(value = "卡类别：1随享卡")
    private Integer cardType;

    @ApiModelProperty(value = "卡片名称")
    private String cardName;

    @ApiModelProperty(value = "卡状态：1：生效中 2 已过期（过期自动失效）  3 生效中-已冻结 4：已退卡 5：已作废 6：过期已冻结  7已使用  8自动已冻结")
    private Integer cardStatus;

    @ApiModelProperty(value = "卡片有效期开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "卡片有效结束时间")
    private LocalDateTime expiresTime;

    @ApiModelProperty(value = "累计使用订单数")
    private Integer totalOrder;

    @ApiModelProperty(value = "累计节省金额")
    private BigDecimal totalDiscountAmount;

    @ApiModelProperty(value = "随享卡初始天数")
    private Integer initDays;

    @ApiModelProperty(value = "随享卡可用天数（剩余天数）")
    private Integer availableDays;

    @ApiModelProperty(value = "随享卡已经用过天数")
    private Integer usedDays;

    @ApiModelProperty(value = "随享卡冻结天数")
    private Integer frozenDays;

    @ApiModelProperty(value = "生效标识：0正常 1待作废")
    private Integer activeFlag;

    @ApiModelProperty(value = "合并标识（0：未合并 1：合并）")
    private Integer mergeFlag;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人")
    private String createOperName;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人id")
    private Long updateOperId;

    @ApiModelProperty(value = "修改人")
    private String updateOperName;

    @ApiModelProperty(value = "删除标记（0：正常 1：删除）")
    private Integer isDeleted;

}
