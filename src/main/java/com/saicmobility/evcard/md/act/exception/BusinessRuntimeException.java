package com.saicmobility.evcard.md.act.exception;

public class BusinessRuntimeException extends RuntimeException {
    private static final long serialVersionUID = -5878567402889945506L;
    private int code;
    private String message;
    public static final BusinessRuntimeException PARAM_EXEPTION = new BusinessRuntimeException(-1, "参数不满足要求");

    public synchronized Throwable fillInStackTrace() {
        return null;
    }

    public BusinessRuntimeException() {
    }
//
//    public BusinessRuntimeException(StatusCode statusCode) {
//        this.code = statusCode.getCode();
//        this.message = statusCode.getMsg();
//    }

    public BusinessRuntimeException(int code) {
        super(String.valueOf(code));
        this.code = code;
        this.message = String.valueOf(code);
    }

    public BusinessRuntimeException(String message) {
        super(message);
        this.message = message;
    }

    public BusinessRuntimeException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}

