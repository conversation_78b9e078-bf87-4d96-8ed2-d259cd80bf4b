package com.saicmobility.evcard.md.act.service.suixiangcard;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saicmobility.evcard.md.act.dto.suixiangcard.SuixiangCardCdkThirdSaleDto;
import com.saicmobility.evcard.md.act.entity.iss.SuixiangCardCdk;

/**
 * <p>
 * 随享卡兑换表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
public interface ISuixiangCardCdkService extends IService<SuixiangCardCdk> {

    SuixiangCardCdkThirdSaleDto getCdkThirdSaleDto(Long cdkId);
}
