package com.saicmobility.evcard.md.act.service.welfare;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.google.common.collect.Lists;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.dto.welfare.*;
import com.saicmobility.evcard.md.act.service.rest.CouponRestClient;
import com.saicmobility.evcard.md.act.service.rest.entity.coupon.ExchangeRequest;
import com.saicmobility.evcard.md.act.service.rest.entity.coupon.ExchangeResponse;
import com.saicmobility.evcard.md.act.service.suixiangcard.ISuixiangCardService;
import com.saicmobility.evcard.md.mduserservice.api.*;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 扫码福利服务实现
 */
@Slf4j
@Service
public class ScanCodeWelfareServiceImpl extends AbstractWelfareService {

    @Autowired
    private ISuixiangCardService suixiangCardService;
    @Autowired
    private MdUserService mdUserService;
    @Autowired
    private CouponRestClient couponRestClient;

    @Override
    protected List<CouponWelfareInfo> getCouponWelfareInfo(GetWelfareInfoInput input) {
        List<CouponWelfareInfo> result = new ArrayList<>();
        String couponCode = input.getKey();
        CouponWelfareInfo couponWelfareInfo = couponService.getCouponWelfareInfoByCode(couponCode);
        result.add(couponWelfareInfo);
        return result;
    }

    @Override
    protected ReceiveWelfareDto receiveCoupon(ReceiveWelfareInput input) throws BusinessException {
        ReceiveWelfareDto result = new ReceiveWelfareDto();
        GetWelfareInfoInput getWelfareInfoInput = input.getGetWelfareInfoInput();
        int type = input.getType();

        ExchangeRequest exchangeRequest = new ExchangeRequest();
        exchangeRequest.setOperateType(1);
        exchangeRequest.setCouponCode(getWelfareInfoInput.getKey());
        if (type == 1) {
            // 已注册的用户领取  couponService.exchange
            exchangeRequest.setType(0);
            GetMemberDetailByMIdRes existMemberRes = mdUserService.getMemberDetailByMId(GetMemberByMIdReq.newBuilder().setMid(input.getMid()).build());
            if (existMemberRes.getRetCode() == 0 && StringUtils.isNotBlank(existMemberRes.getMember().getAuthId())) {
                exchangeRequest.setAuthId(existMemberRes.getMember().getAuthId());
            } else {
                log.error("tid={} ,receiveCoupon existMember 用户未查到,input={}", Trace.currentTraceId(), JSON.toJSONString(input));
                throw new BusinessException(ErrorEnum.MEMBER_NOT_FOUND.getCode(), ErrorEnum.MEMBER_NOT_FOUND.getMsg());
            }
        } else if (type == 2) {
            exchangeRequest.setType(1);
            // 通过手机号重新查下，有没有用户。
            GetValidMemberByMobileRes memberRes = mdUserService.getValidMemberByMobile(GetValidMemberByMobileReq.newBuilder().setMobile(input.getMobile()).build());
            if (memberRes.getRetCode() == 0 && StringUtils.isNotBlank(memberRes.getMember().getMid())) {
                input.setType(1);
                if (StringUtils.isBlank(input.getMid())) {
                    input.setMid(memberRes.getMember().getMid());
                }
                return receiveCoupon(input);
            }
            // 预绑定的领取
            exchangeRequest.setMobilePhone(input.getMobile());
        }

        try {
            ExchangeResponse exchangeResponse = couponRestClient.exchange(exchangeRequest);
            log.info("tid={},receiveCoupon exchange 完成 入参exchangeRequest={}，input={},exchangeResponse={}", Trace.currentTraceId(), JSON.toJSONString(exchangeRequest), JSON.toJSONString(input), JSON.toJSONString(exchangeResponse));
            if (exchangeResponse == null) {
                log.error("tid={},receiveCoupon http 接口为空,exchangeRequest={},input={}", Trace.currentTraceId(), JSON.toJSONString(exchangeRequest), JSON.toJSONString(input));
                throw new BusinessException(ErrorEnum.GET_WELFARE_FAIL.getCode(), ErrorEnum.GET_WELFARE_FAIL.getMsg());
            }

            if (exchangeResponse.getCode() != 0) {
                log.error("tid={},exchange 失败，入参exchangeRequest={}，input={},exchangeResponse={}", Trace.currentTraceId(), JSON.toJSONString(exchangeRequest), JSON.toJSONString(input), JSON.toJSONString(exchangeResponse));
                ErrorEnum welfareFailEnumByCode = ErrorEnum.getWelfareFailEnumByCode(exchangeResponse.getCode());
                throw new BusinessException(welfareFailEnumByCode.getCode(), welfareFailEnumByCode.getMsg());
            } else {
                List<String> keys = Lists.newArrayList(String.valueOf(exchangeResponse.getId()));
                result.setKeys(keys);
            }
        } catch (BusinessException e) {
            log.error("tid={},receiveCoupon 接口业务异常，入参exchangeRequest={}，input={},e={}", Trace.currentTraceId(), JSON.toJSONString(exchangeRequest), JSON.toJSONString(input), e);
            throw e;
        } catch (Exception e) {
            log.error("tid={},receiveCoupon 接口异常，入参exchangeRequest={}，input={},e={}", Trace.currentTraceId(), JSON.toJSONString(exchangeRequest), JSON.toJSONString(input), e);
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), ErrorEnum.REQUEST_ERROR.getMsg());
        }
        return result;
    }

    @Override
    protected List<SuiXiangCardWelfareInfo> getSuiXiangCardWelfareInfo(GetWelfareInfoInput input) {
        List<SuiXiangCardWelfareInfo> result = new ArrayList<>();
        SuiXiangCardWelfareInfo suiXiangCardWelfareInfo = suixiangCardService.getSuiXiangCardWelfareInfo(input.getKey());
        result.add(suiXiangCardWelfareInfo);
        return result;
    }

    @Override
    protected ReceiveWelfareDto receiveSuiXiangCard(ReceiveWelfareInput input) throws BusinessException {
        ActivateSuiXiangCardCdkRes activateSuiXiangCardCdkRes = mdUserService.activateSuiXiangCardCdk(
                ActivateSuiXiangCardCdkReq
                        .newBuilder()
                        .setCdkey(input.getGetWelfareInfoInput().getKey())
                        .setMid(input.getMid())
                        .setMobile(input.getMobile())
                        .setObtainType(1)
                        .build()
        );
        if (activateSuiXiangCardCdkRes.getRetCode() < 0) {
            ErrorEnum errorEnum = ErrorEnum.getWelfareFailEnumByCode(activateSuiXiangCardCdkRes.getRetCode());
            throw new BusinessException(errorEnum.getCode(), errorEnum.getMsg());
        }
        ReceiveWelfareDto receiveWelfareDto = new ReceiveWelfareDto();
        List<String> keys = Lists.newArrayList(String.valueOf(activateSuiXiangCardCdkRes.getCardUseId()));
        receiveWelfareDto.setKeys(keys);
        return receiveWelfareDto;
    }
}
