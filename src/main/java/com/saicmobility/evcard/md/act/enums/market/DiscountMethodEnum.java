package com.saicmobility.evcard.md.act.enums.market;

public enum DiscountMethodEnum {
    AMOUNT(1, "针对金额"),
    RENT_DAYS(2, "针对租期"),
    DAY_MINUS_DAY(3, "满天减天"),
    DAY_MINUS_AMOUNT(4, "满天减金额");

    private Integer type;
    private String msg;

    DiscountMethodEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }
}
