package com.saicmobility.evcard.md.act.entity.iss;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.saicmobility.evcard.md.act.dto.welfare.CouponWelfareInfo;
import com.saicmobility.evcard.md.mdactservice.api.CouponDetailInfo;
import com.saicmobility.evcard.md.mdactservice.api.GetWelfareDetailInfoRes;
import com.saicmobility.evcard.md.mdactservice.api.SuixiangCardPriceInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

/**
 * <p>
 * 随享卡价格表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Data
@TableName("suixiang_card_price")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SuixiangCardPrice对象", description="随享卡价格表")
public class SuixiangCardPrice extends Model<SuixiangCardPrice> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "随享卡价格表主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "随享卡基础表表主键")
    private Long cardBaseId;

    @ApiModelProperty(value = "随享卡租期表主键")
    private Long cardRentId;

    @ApiModelProperty(value = "实际售价，单位：元")
    private BigDecimal salesPrice;

    @ApiModelProperty(value = "租车的划线价格，单位：元")
    private BigDecimal underlinePrice;

    @ApiModelProperty(value = "车型组")
    private String carModelGroup;

    @ApiModelProperty(value = "可用车型id（多个用逗号分割）-1-全部")
    private String carModelIds;

    @ApiModelProperty(value = "销量")
    private Integer sales;

    @ApiModelProperty(value = "落地页引导图完整路径地址")
    private String landingPagePicUrl;

    @ApiModelProperty(value = "落地页头图完整路径地址")
    private String landingPageHeadPicUrl;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人")
    private String createOperName;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人id")
    private Long updateOperId;

    @ApiModelProperty(value = "修改人")
    private String updateOperName;

    @ApiModelProperty(value = "删除标记（0：正常 1：删除）")
    private Integer isDeleted;


    public static SuixiangCardPriceInfo toRes(SuixiangCardPrice suixiangCardPrice) {
        SuixiangCardPriceInfo suixiangCardPriceInfo = SuixiangCardPriceInfo.newBuilder()
                .setId(suixiangCardPrice.getId())
                .setCardBaseId(suixiangCardPrice.getCardBaseId())
                .setCardRentId(suixiangCardPrice.getCardRentId())
                .setSalesPrice(suixiangCardPrice.getSalesPrice() == null ? null : suixiangCardPrice.toString())
                .setUnderlinePrice(suixiangCardPrice.getUnderlinePrice().toPlainString())
                .setCarModelGroup(suixiangCardPrice.getCarModelGroup())
                .setCarModelIds(suixiangCardPrice.getCarModelIds())
                .setSales(suixiangCardPrice.getSales()== null ? 0 : suixiangCardPrice.getSales())
                .setLandingPagePicUrl(suixiangCardPrice.getLandingPagePicUrl())
                .setLandingPageHeadPicUrl(suixiangCardPrice.getLandingPageHeadPicUrl())
                .setIsDeleted(suixiangCardPrice.getIsDeleted())
                .build();

        return suixiangCardPriceInfo;
    }

    public static List<SuixiangCardPriceInfo> listRoRes(List<SuixiangCardPrice> suixiangCardPriceList){
        if (CollectionUtils.isEmpty(suixiangCardPriceList)){
            return new ArrayList<>();
        }

        return suixiangCardPriceList.stream().map(SuixiangCardPrice::toRes).collect(Collectors.toList());

    }

}
