package com.saicmobility.evcard.md.act.entity.iss;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 随享卡兑换表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SuixiangCardCdk对象", description="随享卡兑换表")
@TableName("suixiang_card_cdk")
public class SuixiangCardCdk extends Model<SuixiangCardCdk> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "随享卡兑换配置详情表id")
    private Long cardCdkConfigDetailId;

    @ApiModelProperty(value = "随享卡基础表id")
    private Long cardBaseId;

    @ApiModelProperty(value = "随享卡租期表id")
    private Long cardRentId;

    @ApiModelProperty(value = "随享卡价格表id")
    private Long cardPriceId;

    @ApiModelProperty(value = "兑换码")
    private String cdkey;

    @ApiModelProperty(value = "是否兑换 0：未兑换 1：已兑换")
    private Integer isActivated;

    @ApiModelProperty(value = "兑换人MID，兑换后才有值")
    private String activatedMid;

    @ApiModelProperty(value = "兑换人姓名，兑换后才有值")
    private String activatedUserName;

    @ApiModelProperty(value = "兑换人手机号，兑换后才有值")
    private String activatedUserMobile;

    @ApiModelProperty(value = "兑换时间，兑换后才有值")
    private LocalDateTime activatedTime;

    @ApiModelProperty(value = "随享卡使用表id，兑换后才有值")
    private Long cardUseId;

    @ApiModelProperty(value = "微信小程序兑换二维码图片地址")
    private String wechatCdkQrUrl;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人")
    private String createOperName;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人id")
    private Long updateOperId;

    @ApiModelProperty(value = "修改人")
    private String updateOperName;

    @ApiModelProperty(value = "删除标记（0：正常 1：删除）")
    private Integer isDeleted;

}
