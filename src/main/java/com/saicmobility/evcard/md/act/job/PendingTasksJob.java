package com.saicmobility.evcard.md.act.job;

import com.saicmobility.evcard.md.act.constant.Constants;
import com.saicmobility.evcard.md.act.entity.ChannelActivity;
import com.saicmobility.evcard.md.act.entity.Task;
import com.saicmobility.evcard.md.act.iservice.ITaskService;
import com.saicmobility.evcard.md.act.mapper.act.ChannelActivityMapper;
import com.saicmobility.evcard.md.act.service.ExternalSystemFacade;
import com.saicmobility.evcard.md.act.util.CommonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import krpc.rpc.bootstrap.spring.AlarmUtils;
import krpc.rpc.impl.TracablePool;
import krpc.rpc.util.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13
 */
@Slf4j
@Component
@JobHandler("PendingTasksJob")
public class PendingTasksJob extends IJobHandler {

    @Autowired
    @Qualifier("channelActivitySyncPool")
    private TracablePool channelActivitySyncPool;
    @Autowired
    @Qualifier("ccbCouponStatusCallBackPool")
    private TracablePool ccbCouponStatusCallBackPool;
    @Autowired
    @Qualifier("ccbPushReconciliationFilePool")
    private TracablePool ccbPushReconciliationFilePool;

    @Value("${task.fail.times.limit:15}")
    private String failTimesLimit;

    @Autowired
    private ITaskService taskService;

    @Autowired
    private ExternalSystemFacade externalSystemFacade;

    @Resource
    private ChannelActivityMapper channelActivityMapper;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        List<Task> tasks = taskService.listPendingTasks();
        tasks.forEach(this::dispatch);
        return SUCCESS;
    }

    private void runTask(RunTaskContext ctx) {
        ctx.pool.post(() -> runTaskInPool(ctx));
    }

    private void runTaskInPool(RunTaskContext ctx) {
        try {
            ctx.r.run();
            ctx.lastRunMsg = CommonUtils.cutOffString(ctx.lastRunMsg, 200); // 防止数据库字段大小溢出
            switch (ctx.status) {
                case Constants.TASK_STATUS_PENDING:
                    Date nextRunTime = ctx.nextRunTime;
                    if (nextRunTime == null) {
                        nextRunTime = new Date(new Date().getTime() + ctx.seconds * 1000L);
                    }
                    taskService.updateNextRunTime(ctx.taskType, ctx.taskParam, nextRunTime, ctx.lastRunMsg, ctx.id);

                    // 告警到服务支撑平台
                    String alarmMsg = "任务重试失败, taskType=" + ctx.taskType + ",tackParam=" + ctx.taskParam + ",lastRunMsg=" + ctx.lastRunMsg;
                    AlarmUtils.alarm("2534100", alarmMsg, "tasks", IpUtils.localIp());
                    break;
                case Constants.TASK_STATUS_FINISHED:
                    taskService.updateToFinished(ctx.taskType, ctx.taskParam, ctx.lastRunMsg, ctx.id);
                    break;
                case Constants.TASK_STATUS_FAILED:
                    taskService.updateToFailed(ctx.taskType, ctx.taskParam, ctx.lastRunMsg, ctx.id);
                    break;
                default:
                    break;
            }

        } catch (Exception e) {
            log.error("runTaskInPool exception", e);
        }
    }

    public static class RunTaskContext {
        public int taskType;
        public String taskParam;
        public TracablePool pool;
        public int seconds;
        public Runnable r;
        public int status = 0;  // 1=下次继续尝试 2=已完成 3=已失败
        public String lastRunMsg;
        public Date nextRunTime; // 下次执行时间，可能为空
        public long id;

        public void setSuccess(String lastRunMsg) {
            status = 2;
            this.lastRunMsg = lastRunMsg;
        }

        public void setRetry(String lastRunMsg) {
            status = 1;
            this.lastRunMsg = lastRunMsg;
        }

        public void setRetry(String lastRunMsg, Date nextRunTime) {
            status = 1;
            this.lastRunMsg = lastRunMsg;
            this.nextRunTime = nextRunTime;
        }

        // stopRetry 用在失败次数太多不想再次重试的时候调用，否则会一直重试, 目前没用到
        public void stopRetry(String lastRunMsg) {
            status = 3;
            this.lastRunMsg = lastRunMsg;
        }
    }

    private void dispatch(Task task) {
        RunTaskContext ctx = new RunTaskContext();
        ctx.taskType = task.getTaskType();
        ctx.taskParam = task.getTaskParam();
        ctx.id = task.getId();
        Integer failedTimes = task.getFailedTimes();

        switch (ctx.taskType) {
            case Constants.TASK_TYPE_CHANNEL_ACT_SYNC:
                ctx.pool = channelActivitySyncPool;
                ctx.seconds = 10;
                // 默认失败次数
                int failTimes = 10;
                if (StringUtils.isNotBlank(failTimesLimit)) {
                    failTimes = Integer.valueOf(failTimesLimit);
                }
                if (failedTimes < failTimes) {
                    ctx.r = () -> channelActivitySync(ctx);
                } else {
                    int count = failTimes;
                    ctx.r = () -> ctx.stopRetry("达到设置的最大失败次数,failTimes=" + count);
                }
                runTask(ctx);
                break;
            case Constants.TASK_TYPE_CCB_COUPON_STATUS_CALLBACK:
                ctx.pool = ccbCouponStatusCallBackPool;
                ctx.seconds = 60;   //重试时间
                // 默认失败次数
                int tryTimes = 3;
                if (failedTimes < tryTimes) {
                    ctx.r = () -> ccbCouponStatusCallBack(ctx);
                } else {
                    int count = tryTimes;
                    ctx.r = () -> ctx.stopRetry("达到设置的最大失败次数,failTimes=" + count);
                }
                runTask(ctx);
                break;
            case Constants.TASK_TYPE_CCB_RECONCILIATION_FILE:
                ctx.pool = ccbPushReconciliationFilePool;
                ctx.seconds = 60;   //重试时间
                // 默认失败次数
                int pullTimes = 3;
                if (failedTimes < pullTimes) {
                    ctx.r = () -> ccbPushReconciliationFile(ctx);
                } else {
                    int count = pullTimes;
                    ctx.r = () -> ctx.stopRetry("达到设置的最大失败次数,failTimes=" + count);
                }
                runTask(ctx);
                break;
            default:
                break;
        }
    }

    private void channelActivitySync(RunTaskContext ctx) {
        try {
            ChannelActivity channelActivity = channelActivityMapper.selectById(Long.parseLong(ctx.taskParam));
            externalSystemFacade.notifyChannelActivitySync(channelActivity, true);
            ctx.setSuccess("成功");
        } catch (Exception e) {
            String msg = "擎路营销活动同步失败, message=" + e.getMessage();
            ctx.setRetry(msg);
            log.error("channelActivitySync exception, e=" + msg, e);
        }
    }
    private void ccbCouponStatusCallBack(RunTaskContext ctx) {
        try {
            externalSystemFacade.ccbCouponStatusCallBackTask(ctx.taskParam);
            ctx.setSuccess("成功");
        } catch (Exception e) {
            String msg = "建行优惠券状态回调失败, message=" + e.getMessage();
            ctx.setRetry(msg);
            log.error("ccbCouponStatusCallBack exception, e=" + msg, e);
        }
    }
    private void ccbPushReconciliationFile(RunTaskContext ctx) {
        try {
            externalSystemFacade.ccbPushReconciliationFileTask(ctx.taskParam);
            ctx.setSuccess("成功");
        } catch (Exception e) {
            String msg = "建行对账文件推送失败, message=" + e.getMessage();
            ctx.setRetry(msg);
            log.error("ccbPushReconciliationFile exception, e=" + msg, e);
        }
    }

}
