package com.saicmobility.evcard.md.act.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/24
 */
public class CommonUtils {

    public static String getValueAfterProcessNull(BigDecimal value) {
        return value == null ? null : value.toPlainString();
    }

    public static int getValueAfterProcessNull(Integer value) {
        return value == null ? 0 : value;
    }

    /**
     * 计算租期
     * @param startTime
     * @param endTime
     */
    public static Integer getRentDays(LocalDateTime startTime, LocalDateTime endTime) {
        long betweenMinutes = Duration.between(startTime, endTime).toMinutes();
        return (int) Math.ceil(betweenMinutes / (24 * 60D));
    }

    public static int getRentDays1(LocalDateTime startTime, LocalDateTime endTime) {
        long betweenMinutes = Duration.between(startTime, endTime).toMinutes();
        return Math.max((int) (betweenMinutes / (24 * 60D)), 1);
    }

    /**
     * 从分钟数转化成几小时几分钟
     * 例如：1320 -> 2200
     * 1350 -> 2230
     * 500  -> 820
     * 488  -> 808
     *
     * @param minutes
     * @return
     */
    public static int getHourAndMinuteFromMinutes(int minutes) {
        int hour = minutes / 60;
        int minute = minutes % 60;
        return Integer.parseInt(String.valueOf(hour) + leftZero(minute));
    }

    /**
     * 如果分钟数小于10，那么左边补一个0
     *
     * @param minute
     * @return
     */
    private static String leftZero(int minute) {
        if (minute < 10) {
            return "0" + String.valueOf(minute);
        } else {
            return String.valueOf(minute);
        }
    }

    /**
     * 入参字符串str只保留从左开始的length位的长度
     *
     * @param str
     * @param length
     * @return
     */
    public static String cutOffString(String str, int length) {
        if (StringUtils.isEmpty(str) || length < 0) {
            return str;
        }
        return str.length() > length ? str.substring(0, length) : str;
    }

    public static String orgCodeStr(List<String> orgCodeList) {
        if (CollectionUtils.isEmpty(orgCodeList)) {
            return "";
        }
        StringBuilder sb = new StringBuilder("");
        for (String orgCode : orgCodeList) {
            sb.append("(" + orgCode + ")");
        }
        return sb.toString();
    }

    public static List<String> orgCodesSplit(String orgCodes) {
        if (StringUtils.isEmpty(orgCodes)) {
            return new ArrayList<>();
        }
        orgCodes = orgCodes.substring(1, orgCodes.length() - 1); // 去除首尾字符
        String[] arr = orgCodes.split("\\)\\(");  // 根据字符串分割转数组
        List<String> list = Arrays.stream(arr).collect(Collectors.toList()); // 数组转List
        return list;
    }

    public static List<Long> storeIdsSplit(String storeIdList) {
        if (StringUtils.isEmpty(storeIdList)) {
            return new ArrayList<>();
        }
        String[] idsArray = storeIdList.split(",");  // 根据字符串分割转数组
        return Arrays.stream(idsArray)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    public static String toStringFromList(Collection<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        return list.stream()
                .map(Object::toString) // 将Long转换为String
                .collect(Collectors.joining(","));
    }

    public static String convertToFormattedString(Collection<Long> list) {
        if (list == null || list.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (Long item : list) {
            sb.append("(").append(item).append(")");
        }

        return sb.toString();
    }
}
