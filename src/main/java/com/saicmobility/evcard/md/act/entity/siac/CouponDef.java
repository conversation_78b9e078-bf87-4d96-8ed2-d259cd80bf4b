package com.saicmobility.evcard.md.act.entity.siac;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CouponDef对象", description="")
public class CouponDef extends Model<CouponDef> {

    @ApiModelProperty(value = "模板编号")
    @TableId(value = "COUPON_SEQ", type = IdType.AUTO)
    private Integer couponSeq;

    @ApiModelProperty(value = "用于规则引擎处理规则")
    @TableField("RULE_SEQ")
    private String ruleSeq;

    @ApiModelProperty(value = "优惠券类型 1:直扣 2：折扣")
    @TableField("COUPON_TYPE")
    private Integer couponType;

    @ApiModelProperty(value = "优惠券使用限制机构")
    @TableField("ORG_ID")
    private String orgId;

    @ApiModelProperty(value = "START_TIME和END_TIME限定的时间 0不限 1 取车时间  2 还车时间  3 取/还车时间")
    @TableField("TIME_TYPE")
    private Integer timeType;

    @ApiModelProperty(value = "优惠券可使用开始时间点(hhmmss)")
    @TableField("START_TIME")
    private String startTime;

    @ApiModelProperty(value = "优惠券可使用结束时间点(hhmmss)")
    @TableField("END_TIME")
    private String endTime;

    @ApiModelProperty(value = "可使用券的最低消费金额")
    @TableField("MIN_AMOUNT")
    private Double minAmount;

    @ApiModelProperty(value = "优惠券面值（如果是打折券 该值表示最大抵扣值）")
    @TableField("COUPON_VALUE")
    private Double couponValue;

    @ApiModelProperty(value = "折扣率  直扣券该值为100，打折券该值的单位是%")
    @TableField("DISCOUNT_RATE")
    private Integer discountRate;

    @ApiModelProperty(value = "优惠券描述（短）")
    @TableField("DES")
    private String des;

    @TableField("IMG_URL_IOS")
    private String imgUrlIos;

    @TableField("IMG_URL_ANDROID")
    private String imgUrlAndroid;

    @ApiModelProperty(value = "取车网点（多个网点以,分割）")
    @TableField("PICKSHOP_SEQ")
    private String pickshopSeq;

    @TableField("IMG_URL_IOS_EXP")
    private String imgUrlIosExp;

    @TableField("IMG_URL_ANDROID_EXP")
    private String imgUrlAndroidExp;

    @ApiModelProperty(value = "还车网点（多个网点以,分割）")
    @TableField("RETURNSHOP_SEQ")
    private String returnshopSeq;

    @ApiModelProperty(value = "资产车型（多个车型以,分割）")
    @TableField("VEHICLE_MODLE")
    private String vehicleModle;

    @ApiModelProperty(value = "车牌（车牌的开头'沪'或者'沪A' 多种的时候以,分割）")
    @TableField("VEHICLE_NO")
    private String vehicleNo;

    @ApiModelProperty(value = "取车门店/网点所在城市（多个以,分割）")
    @TableField("PICKSHOP_CITY")
    private String pickshopCity;

    @ApiModelProperty(value = "还车门店/网点所在城市（多个以,分割）")
    @TableField("RETURNSHOP_CITY")
    private String returnshopCity;

    @ApiModelProperty(value = "0: 可以与活动叠加使用，1：不可以与活动叠加使用")
    @TableField("ACTIVITY_OVERLAP")
    private Integer activityOverlap;

    @ApiModelProperty(value = "可用套餐id，逗号分隔，套餐类型_套餐id")
    private String packageIds;

    @ApiModelProperty(value = "业务类型（0不限/ 1分时/ 2短租/3长租/4充电 /11分时-预约送车）")
    @TableField("SERVICE_TYPE")
    private Integer serviceType;

    @ApiModelProperty(value = "有效期类型（1起止时间 2时长）")
    @TableField("VALID_TIME_TYPE")
    private Integer validTimeType;

    @ApiModelProperty(value = "到账几天有效")
    @TableField("EFFECTIVE_DAYS")
    private Integer effectiveDays;

    @ApiModelProperty(value = "有效时长，优惠券的有效期为发券时间 + 有效时长。当service_type字段值为1时，该字段值为0")
    @TableField("VALID_DAYS")
    private Integer validDays;

    @ApiModelProperty(value = "有效期开始时间 YYYY-MM-DD")
    @TableField("START_DATE")
    private String startDate;

    @ApiModelProperty(value = "有效结束时间  YYYY-MM-DD")
    @TableField("EXPIRES_DATE")
    private String expiresDate;

    @ApiModelProperty(value = "优惠券名称")
    @TableField("COUPON_NAME")
    private String couponName;

    @ApiModelProperty(value = "优惠券描述(长)")
    @TableField("COUPON_DES")
    private String couponDes;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATE_NAME")
    private String createName;

    @ApiModelProperty(value = "可用天限制,逗号分隔，1,3表示周一和周三可用")
    @TableField("AVAILABLE_DAYS_OF_WEEK")
    private String availableDaysOfWeek;

    @ApiModelProperty(value = "法定节假日可用限制, 1:可用，0:不可用")
    @TableField("HOLIDAYS_AVAILABLE")
    private Integer holidaysAvailable;

    @ApiModelProperty(value = "时长限制")
    private BigDecimal durationLimit;

    @ApiModelProperty(value = "租车模式 0 即时分时 2即时日租 3预约日租, 空表示不限，多个以逗号分隔")
    @TableField("RENT_METHOD")
    private String rentMethod;

    @ApiModelProperty(value = "使用模式限制 空-不限 0仅预约上门送取车辆")
    @TableField("USE_METHOD")
    private String useMethod;

    @ApiModelProperty(value = "产品线大类 1 分时 2日租, 空表示不限，多个以逗号分隔")
    private String rentMethodGroup;

    @ApiModelProperty(value = "商品车型（多个车型以,分割）")
    private String goodsVehicleModel;

    @ApiModelProperty(value = "取还车点限制类别 1网点 2门店")
    private Integer shopLimitType;


}
