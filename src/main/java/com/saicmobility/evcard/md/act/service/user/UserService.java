package com.saicmobility.evcard.md.act.service.user;

import com.saicmobility.evcard.md.act.constant.SystemConst;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import org.springframework.stereotype.Service;

@Service
public class UserService implements IUserService {

    @Override
    public UserDTO selectUserByUserName(String username) {
        // todo  mock
        return SystemConst.ELASTIC_JOB_USER;
    }
}
