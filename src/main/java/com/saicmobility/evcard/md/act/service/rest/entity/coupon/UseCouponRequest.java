package com.saicmobility.evcard.md.act.service.rest.entity.coupon;

import com.extracme.evcard.rpc.coupon.dto.UsedCouponDto;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.act.util.NumberUtils;
import com.saicmobility.evcard.md.mdactservice.api.UseCouponReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class UseCouponRequest extends UsedCouponDto {

    public static UseCouponRequest fromRes(UseCouponReq req, String authId) {
        UseCouponRequest request = new UseCouponRequest();
        request.setAuthId(authId);
        request.setUserCouponSeq(req.getUserCouponSeq());
        request.setOrderSeq(req.getOrderNo());
        request.setDiscount(NumberUtils.getBigDecimal(req.getDiscount()));
        request.setOptUser(req.getOperatorName());
        request.setOrderOrgId(req.getOrderOrgCode());
        request.setUpdatedTime(DateUtil.getSystemDate(DateUtil.DATE_TYPE1));
        return request;
    }
}
