package com.saicmobility.evcard.md.act.entity.siac;

import java.math.BigDecimal;
import java.util.Date;

public class UserCouponTransactionRecord {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.id
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.user_id
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private String userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.operate_type
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private Integer operateType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.transaction_type
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private Integer transactionType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.in_amount
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private BigDecimal inAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.out_amount
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private BigDecimal outAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.user_coupon_seq
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private Long userCouponSeq;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.transaction_amount
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private BigDecimal transactionAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.account_balance
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private BigDecimal accountBalance;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.status
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.misc_desc
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.create_time
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.create_oper_id
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.create_oper_name
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.update_time
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.update_oper_id
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.update_oper_name
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_transaction_record.account_type
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    private Integer accountType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.id
     *
     * @return the value of user_coupon_transaction_record.id
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.id
     *
     * @param id the value for user_coupon_transaction_record.id
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.user_id
     *
     * @return the value of user_coupon_transaction_record.user_id
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public String getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.user_id
     *
     * @param userId the value for user_coupon_transaction_record.user_id
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.operate_type
     *
     * @return the value of user_coupon_transaction_record.operate_type
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public Integer getOperateType() {
        return operateType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.operate_type
     *
     * @param operateType the value for user_coupon_transaction_record.operate_type
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.transaction_type
     *
     * @return the value of user_coupon_transaction_record.transaction_type
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public Integer getTransactionType() {
        return transactionType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.transaction_type
     *
     * @param transactionType the value for user_coupon_transaction_record.transaction_type
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setTransactionType(Integer transactionType) {
        this.transactionType = transactionType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.in_amount
     *
     * @return the value of user_coupon_transaction_record.in_amount
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public BigDecimal getInAmount() {
        return inAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.in_amount
     *
     * @param inAmount the value for user_coupon_transaction_record.in_amount
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setInAmount(BigDecimal inAmount) {
        this.inAmount = inAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.out_amount
     *
     * @return the value of user_coupon_transaction_record.out_amount
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public BigDecimal getOutAmount() {
        return outAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.out_amount
     *
     * @param outAmount the value for user_coupon_transaction_record.out_amount
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setOutAmount(BigDecimal outAmount) {
        this.outAmount = outAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.user_coupon_seq
     *
     * @return the value of user_coupon_transaction_record.user_coupon_seq
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public Long getUserCouponSeq() {
        return userCouponSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.user_coupon_seq
     *
     * @param userCouponSeq the value for user_coupon_transaction_record.user_coupon_seq
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setUserCouponSeq(Long userCouponSeq) {
        this.userCouponSeq = userCouponSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.transaction_amount
     *
     * @return the value of user_coupon_transaction_record.transaction_amount
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.transaction_amount
     *
     * @param transactionAmount the value for user_coupon_transaction_record.transaction_amount
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.account_balance
     *
     * @return the value of user_coupon_transaction_record.account_balance
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public BigDecimal getAccountBalance() {
        return accountBalance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.account_balance
     *
     * @param accountBalance the value for user_coupon_transaction_record.account_balance
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setAccountBalance(BigDecimal accountBalance) {
        this.accountBalance = accountBalance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.status
     *
     * @return the value of user_coupon_transaction_record.status
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.status
     *
     * @param status the value for user_coupon_transaction_record.status
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.misc_desc
     *
     * @return the value of user_coupon_transaction_record.misc_desc
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.misc_desc
     *
     * @param miscDesc the value for user_coupon_transaction_record.misc_desc
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.create_time
     *
     * @return the value of user_coupon_transaction_record.create_time
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.create_time
     *
     * @param createTime the value for user_coupon_transaction_record.create_time
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.create_oper_id
     *
     * @return the value of user_coupon_transaction_record.create_oper_id
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.create_oper_id
     *
     * @param createOperId the value for user_coupon_transaction_record.create_oper_id
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.create_oper_name
     *
     * @return the value of user_coupon_transaction_record.create_oper_name
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.create_oper_name
     *
     * @param createOperName the value for user_coupon_transaction_record.create_oper_name
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.update_time
     *
     * @return the value of user_coupon_transaction_record.update_time
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.update_time
     *
     * @param updateTime the value for user_coupon_transaction_record.update_time
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.update_oper_id
     *
     * @return the value of user_coupon_transaction_record.update_oper_id
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.update_oper_id
     *
     * @param updateOperId the value for user_coupon_transaction_record.update_oper_id
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.update_oper_name
     *
     * @return the value of user_coupon_transaction_record.update_oper_name
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.update_oper_name
     *
     * @param updateOperName the value for user_coupon_transaction_record.update_oper_name
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_coupon_transaction_record.account_type
     *
     * @return the value of user_coupon_transaction_record.account_type
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public Integer getAccountType() {
        return accountType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_coupon_transaction_record.account_type
     *
     * @param accountType the value for user_coupon_transaction_record.account_type
     *
     * @mbggenerated Thu May 09 08:58:03 CST 2019
     */
    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }
}