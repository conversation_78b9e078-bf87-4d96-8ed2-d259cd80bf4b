package com.saicmobility.evcard.md.act.mybatis;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CoderGeneratorPath {

    /**
     * 项目根路径
     */
    private String projectPath;

    /**
     * 实体类输出路径
     */
    private String entityOutputPath;

    /**
     * mapper层输出路径
     */
    private String mapperOutputPath;

    /**
     * dao层输出路径
     */
    private String daoOutputPath;

    /**
     * xml输出路径
     */
    private String xmlOutputPath;

    /**
     * 服务层输出路径
     */
    private String serviceOutputPath;

    /**
     * 服务层输出路径
     */
    private String serviceImplOutputPath;
    /**
     * 控制层输出路径
     */
    private String controllerOutputPath;
}
