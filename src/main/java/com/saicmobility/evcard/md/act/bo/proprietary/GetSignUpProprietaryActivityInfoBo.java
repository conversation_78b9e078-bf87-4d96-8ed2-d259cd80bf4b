package com.saicmobility.evcard.md.act.bo.proprietary;

import com.saicmobility.evcard.md.act.dto.proprietary.DiscountStandardPricing;
import com.saicmobility.evcard.md.act.dto.proprietary.FullMinusStandardPricing;
import com.saicmobility.evcard.md.mdactservice.api.GetSignUpProprietaryActivityInfoRes;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/8 13:26
 */
@Data
public class GetSignUpProprietaryActivityInfoBo {
    private long id; // 活动ID
    private long signUpId; //报名ID
    private String activityName; //活动名称
    private String orgCode; //报名机构
    private Long storeId;//门店Id
    private int allModelIds;//全部车型  1-全部  2-不是全部
    private List<Long> vehicleModelIds;//车型id列表
    private int activityType; //活动类型
    private int pricingType; //定价类型：1-灵活定价、2-规范定价
    private int discountLatitude; //优惠纬度：1-车辆租金
    private List<FullMinusStandardPricing> fullMinusStandardPricing;//满减规范定价
    private List<FullMinusStandardPricing> fullMinusFlexiblePricing;//报名后满减灵活定价 变成一个固定值
    private List<DiscountStandardPricing> discountStandardPricing;//打折规范定价
    private List<DiscountStandardPricing> discountFlexiblePricing;//报名后打折灵活定价 变成一个固定值

    public GetSignUpProprietaryActivityInfoRes toRes(){
        return GetSignUpProprietaryActivityInfoRes.newBuilder()
                .setId(id)
                .setSignUpId(signUpId)
                .setActivityName(activityName)
                .setOrgCode(orgCode)
                .setStoreId(storeId)
                .setAllModelIds(allModelIds)
                .addAllVehicleModelIds(vehicleModelIds)
                .setActivityType(activityType)
                .setPricingType(pricingType)
                .setDiscountLatitude(discountLatitude)
                .addAllFullMinusStandardPricing(getFullMinusPricing(fullMinusStandardPricing))
                .addAllFullMinusFlexiblePricing(getFullMinusPricing(fullMinusFlexiblePricing))
                .addAllDiscountStandardPricing(getDiscountPricing(discountStandardPricing))
                .addAllDiscountFlexiblePricing(getDiscountPricing(discountFlexiblePricing))
                .build();
    }

    private List<com.saicmobility.evcard.md.mdactservice.api.DiscountStandardPricing> getDiscountPricing(List<DiscountStandardPricing> discountPricing) {
        List<com.saicmobility.evcard.md.mdactservice.api.DiscountStandardPricing> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(discountPricing)){
            for (DiscountStandardPricing item : discountPricing) {
                com.saicmobility.evcard.md.mdactservice.api.DiscountStandardPricing dsp = com.saicmobility.evcard.md.mdactservice.api.DiscountStandardPricing.newBuilder()
                        .setDays(item.getDays())
                        .setDiscount(item.getDiscount())
                        .build();
                list.add(dsp);
            }
        }
        return list;
    }

    private List<com.saicmobility.evcard.md.mdactservice.api.FullMinusStandardPricing> getFullMinusPricing(List<FullMinusStandardPricing> fullMinusPricing) {
        List<com.saicmobility.evcard.md.mdactservice.api.FullMinusStandardPricing> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(fullMinusPricing)){
            for (FullMinusStandardPricing item : fullMinusPricing) {
                com.saicmobility.evcard.md.mdactservice.api.FullMinusStandardPricing fmsp = com.saicmobility.evcard.md.mdactservice.api.FullMinusStandardPricing.newBuilder()
                        .setDays(item.getDays())
                        .setDiscountAmount(item.getDiscountAmount())
                        .build();
                list.add(fmsp);
            }
        }
        return list;
    }

}
