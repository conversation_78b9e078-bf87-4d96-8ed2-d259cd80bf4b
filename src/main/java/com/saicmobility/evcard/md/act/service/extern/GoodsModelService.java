package com.saicmobility.evcard.md.act.service.extern;

import com.saicmobility.evcard.md.act.service.extern.ConfigLoader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 查询商品信息服务
 */
@Service
public class GoodsModelService {

    /*@Resource
    MdGoodsService mdGoodsService;*/

    @Autowired
    ConfigLoader configLoader;

    private static final ConcurrentHashMap<Long, String> goodsModelMap = new ConcurrentHashMap<>();

    public String getGoodsModelNameById(Long goodsModelId) {
        if (null == goodsModelId) {
            return null;
        }

        /*GetGoodsModelDetailReq getGoodsModelDetailReq = GetGoodsModelDetailReq.newBuilder().setId(goodsModelId).build();
        GetGoodsModelDetailRes goodsModelDetailRes = mdGoodsService.getGoodsModelDetail(getGoodsModelDetailReq);
        if (goodsModelDetailRes.getRetCode() != 0) {
            return null;
        }*/
        return configLoader.getGoodsModelName(goodsModelId);
    }

}
