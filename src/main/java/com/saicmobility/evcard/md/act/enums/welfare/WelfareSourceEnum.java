package com.saicmobility.evcard.md.act.enums.welfare;

public enum WelfareSourceEnum {

    SCAN_CODE(1,"扫码"),
    TRANSFER(2,"转增"),
    H5_RECEIVE(3,"H5领取"),
    ;

    private int source;
    private String desc;

    WelfareSourceEnum(int type, String desc) {
        this.source = type;
        this.desc = desc;
    }


    public int getSource() {
        return source;
    }

    public static WelfareSourceEnum getWelfareSourceEnum(int source) {
        WelfareSourceEnum[] values = values();
        for (WelfareSourceEnum welfareTypeEnum : values) {
            if (welfareTypeEnum.getSource() == source) {
                return welfareTypeEnum;
            }
        }
        return null;
    }
}
