package com.saicmobility.evcard.md.act.util;

import com.saicmobility.evcard.md.act.config.CcbConfig;
import com.saicmobility.evcard.md.act.dto.coupon.channel.ccb.CCBReconciliationFileDto;
import org.apache.commons.lang3.StringUtils;

import java.security.MessageDigest;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2025/1/14 11:15
 * @Description: 建行组装工具类
 */
public class CCBUtils {
    /**
     * 建行4.7 生成对账单文件工具栏
     * warn！！！单文件最多支持两万条数据，若超过，请分批传输
     * warn！！！单文件最多支持两万条数据，若超过，请分批传输
     * warn！！！单文件最多支持两万条数据，若超过，请分批传输
     * @param version
     * @param createTime
     * @param beginTime
     * @param endTime
     * @param dtos
     * @return
     */
    public static String buildReconciliationFileContent(String version,String createTime,String beginTime,String endTime,List<CCBReconciliationFileDto> dtos) {
        Integer recordNum = dtos.size();
        Map<String, String> titleMap = new LinkedHashMap<String, String>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE,-1);
        titleMap.put("Version",StringUtils.isBlank(version) ? "2.0" : version);
        titleMap.put("PlatformId", CcbConfig.CCB_PLATFORM_ID);
        titleMap.put("CreateTime", StringUtils.isBlank(createTime) ? DateUtils.dateToString(new Date(),DateUtils.DATE_TYPE4) : createTime);
        titleMap.put("BeginTime",StringUtils.isBlank(beginTime) ? DateUtils.dateToString(calendar.getTime(),DateUtils.DATE_TYPE27) : beginTime);
        titleMap.put("EndTime",StringUtils.isBlank(endTime) ? DateUtils.dateToString(calendar.getTime(),DateUtils.DATE_TYPE28) : endTime);
        titleMap.put("RecordNum",String.valueOf(recordNum));
        StringBuffer titleStrBuffer = buildReconciliationFileTitle(titleMap);
        StringBuffer contentStrBuffer = buildReconciliationFileContent(dtos);

        return titleStrBuffer.append(contentStrBuffer).toString();
    }
    private static StringBuffer buildReconciliationFileTitle(Map<String, String> contentMap) {
        StringBuffer contentStrBuffer = new StringBuffer();

        for (Map.Entry<String, String> entry : contentMap.entrySet()) {
            contentStrBuffer.append(entry.getKey()).append("=").append(entry.getValue()).append("|@|");
        }
        return contentStrBuffer.append("\n");
    }
    private static StringBuffer buildReconciliationFileContent(List<CCBReconciliationFileDto> dtos) {
        StringBuffer contentStrBuffer = new StringBuffer();
        dtos.forEach(dto->{
            contentStrBuffer.append(StringUtils.isBlank(dto.getProductId()) ? "" : dto.getProductId()).append("|@|")
                    .append(StringUtils.isBlank(dto.getCouponCode()) ? "" : dto.getCouponCode()).append("|@|")
                    .append(StringUtils.isBlank(dto.getOperation()) ? "" : dto.getOperation()).append("|@|")
                    .append(StringUtils.isBlank(dto.getUseOrderId()) ? "" : dto.getUseOrderId()).append("|@|")
                    .append(StringUtils.isBlank(dto.getStoreId_2()) ? "" : dto.getStoreId_2()).append("|@|")
                    .append(StringUtils.isBlank(dto.getUseTm()) ? "" : dto.getUseTm()).append("|@|")
                    .append(StringUtils.isBlank(dto.getAcmUsedAmt()) ? "" : dto.getAcmUsedAmt()).append("|@|")
                    .append(StringUtils.isBlank(dto.getUnUsedAmt()) ? "" : dto.getUnUsedAmt()).append("|@|")
                    .append("\n")
            ;
        });
        return contentStrBuffer;
    }


    public static void main(String[] args) {
        List<CCBReconciliationFileDto> ccbReconciliationFileDtos = Arrays.asList(new CCBReconciliationFileDto("1", "1", "1", "1", "1", "1", "1", "1"),
                new CCBReconciliationFileDto("2", "2", "2", "2", "2", "2", "2", "2"),
                new CCBReconciliationFileDto("3", "3", "3", "3", "3", "3", "3", "3"),
                new CCBReconciliationFileDto("4", "4", "4", "4", "4", "4", "4", "4"));
        System.out.println(CCBUtils.buildReconciliationFileContent("2.0",null,null,null,ccbReconciliationFileDtos));
    }
}
