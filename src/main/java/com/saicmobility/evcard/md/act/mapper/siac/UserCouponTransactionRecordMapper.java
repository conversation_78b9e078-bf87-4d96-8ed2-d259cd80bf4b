package com.saicmobility.evcard.md.act.mapper.siac;


import com.saicmobility.evcard.md.act.entity.siac.UserCouponTransactionRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserCouponTransactionRecordMapper {

    List<UserCouponTransactionRecord> selectByTypeAndTime(@Param("type")Integer type, @Param("date") String date);
    UserCouponTransactionRecord selectByTypeAndSeq(@Param("type")Integer type, @Param("userCouponSeq") String userCouponSeq);
}
