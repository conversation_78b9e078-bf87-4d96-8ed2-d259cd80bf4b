package com.saicmobility.evcard.md.act.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.entity.BaseEntity;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivitySignupRelation;
import com.saicmobility.evcard.md.act.enums.IsDeletedEnum;
import com.saicmobility.evcard.md.act.mapper.act.ProprietaryActivitySignupRelationMapper;
import com.saicmobility.evcard.md.act.service.IProprietaryActivitySignupRelationService;
import com.saicmobility.evcard.md.act.service.inner.BaseService;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProprietaryActivitySignupRelationServiceImpl extends BaseService<ProprietaryActivitySignupRelationMapper, ProprietaryActivitySignupRelation>
        implements IProprietaryActivitySignupRelationService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdateSignupRelation(Set<String> orgCodeList, Long activityId, Integer isAllStore, Integer isAllVehicle
            , Long updateOperId, String updateOperName,String vehicleModelIds,String flexiblePricing) throws BusinessException {
        if(CollectionUtils.isEmpty(orgCodeList) || activityId == null || isAllStore == null || isAllVehicle == null  || updateOperId == null ||StringUtils.isBlank(updateOperName)){
            log.info("tid:{},新增or修改活动报名关系异常：orgCodeList:{},activityId:{},isAllStore:{},isAllVehicle:{},updateOperId:{},updateOperName:{}"
                    , Trace.currentTraceId(), JSONObject.toJSONString(orgCodeList),activityId,isAllStore,isAllVehicle,updateOperId,updateOperName);
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "新增活动报名关系异常");
        }
        try {
            List<ProprietaryActivitySignupRelation> signupRelationList = lambdaQuery()
                    .eq(ProprietaryActivitySignupRelation::getActivityId, activityId)
                    .eq(BaseEntity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                    .list();
            Map<String, ProprietaryActivitySignupRelation> signupRelationMap = signupRelationList.stream()
                    .collect(Collectors.toMap(ProprietaryActivitySignupRelation::getOrgCode, Function.identity()));
            List<ProprietaryActivitySignupRelation> addSignupRelationList = new ArrayList<>();
            orgCodeList.forEach(orgCode->{
                // 已报名的机构不在处理
                if (signupRelationMap.containsKey(orgCode)) {
                    return;
                }
                ProprietaryActivitySignupRelation    signupRelation = new ProprietaryActivitySignupRelation();
                signupRelation.setActivityId(activityId);
                signupRelation.setIsAllStore(isAllStore);
                signupRelation.setCreateOperId(updateOperId);
                signupRelation.setCreateOperName(updateOperName);
                signupRelation.setCreateTime(new Date());
                signupRelation.setUpdateOperName(updateOperName);
                signupRelation.setUpdateTime(new Date());
                signupRelation.setUpdateOperId(updateOperId);
                signupRelation.setIsDeleted(IsDeletedEnum.NORMAL.getType());
                signupRelation.setVehicleModelIds(vehicleModelIds);
                signupRelation.setIsAllVehicle(isAllVehicle);
                signupRelation.setFlexiblePricing(flexiblePricing);
                signupRelation.setOrgCode(orgCode);
                addSignupRelationList.add(signupRelation);
            });
            if (!CollectionUtils.isEmpty(addSignupRelationList) ) {
                UserDTO userDTO = new UserDTO();
                userDTO.setId(updateOperId);
                userDTO.setUsername(updateOperName);
                saveBatch(addSignupRelationList,userDTO,new Date());
                log.info("tid:{},新增活动报名关系成功", Trace.currentTraceId());
            }
        }catch (Exception e){
            log.error("tid:{},新增活动报名关系异常:", Trace.currentTraceId(),e);
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "新增活动报名关系异常");
        }
    }

}
