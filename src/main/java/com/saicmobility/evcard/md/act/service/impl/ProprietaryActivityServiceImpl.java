package com.saicmobility.evcard.md.act.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.bo.proprietary.*;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.domain.StoreInfoCombobox;
import com.saicmobility.evcard.md.act.dto.market.TimeRange;
import com.saicmobility.evcard.md.act.dto.proprietary.*;
import com.saicmobility.evcard.md.act.dto.proprietary.DiscountFlexiblePricing;
import com.saicmobility.evcard.md.act.dto.proprietary.DiscountStandardPricing;
import com.saicmobility.evcard.md.act.dto.proprietary.FullMinusFlexiblePricing;
import com.saicmobility.evcard.md.act.dto.proprietary.FullMinusStandardPricing;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivity;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivitySignup;
import com.saicmobility.evcard.md.act.enums.IsDeletedEnum;
import com.saicmobility.evcard.md.act.enums.market.ActType;
import com.saicmobility.evcard.md.act.enums.proprietary.ActivityStatusEnum;
import com.saicmobility.evcard.md.act.enums.proprietary.HolidaysTypeEnum;
import com.saicmobility.evcard.md.act.enums.proprietary.PricingTypeEnum;
import com.saicmobility.evcard.md.act.manager.ProprietaryActivityManager;
import com.saicmobility.evcard.md.act.mapper.act.ProprietaryActivityMapper;
import com.saicmobility.evcard.md.act.mapper.act.ProprietaryActivitySignupMapper;
import com.saicmobility.evcard.md.act.service.IProprietaryActivityService;
import com.saicmobility.evcard.md.act.service.extern.ConfigLoader;
import com.saicmobility.evcard.md.act.service.extern.OrgService;
import com.saicmobility.evcard.md.act.service.extern.StoreService;
import com.saicmobility.evcard.md.act.service.inner.BaseService;
import com.saicmobility.evcard.md.act.util.CommonUtils;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.mdactservice.api.*;
import com.saicmobility.evcard.md.mdstoreservice.api.TopCityInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 自营活动表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@Slf4j
@Service
public class ProprietaryActivityServiceImpl extends BaseService<ProprietaryActivityMapper, ProprietaryActivity> implements IProprietaryActivityService {

    @Resource
    private ProprietaryActivityMapper proprietaryActivityMapper;

    @Resource
    private ProprietaryActivitySignupMapper proprietaryActivitySignupMapper;

    @Resource
    private ProprietaryActivityManager proprietaryActivityManager;

    @Resource
    private ConfigLoader configLoader;

    @Resource
    private OrgService orgService;

    @Resource
    private StoreService storeService;

    @Override
    public void addProprietaryActivity(AddProprietaryActivityDto dto) throws BusinessException {

        //判断该记录是否已经存在
        LambdaQueryWrapper<ProprietaryActivity> queryWrapper = new LambdaQueryWrapper<ProprietaryActivity>()
                .eq(ProprietaryActivity::getActivityName, dto.getActivityName())
                .eq(ProprietaryActivity::getActivityTag, dto.getActivityTag())
                //.eq(dto.getAllOrgCodes() == 1,ProprietaryActivity::getOrgCodes, "-1")
                //.eq(dto.getAllOrgCodes() != 1,ProprietaryActivity::getOrgCodes, CommonUtils.orgCodeStr(dto.getOrgCodes()))
                .eq(dto.getAllModelIds() == 1,ProprietaryActivity::getVehicleModelIds, "-1")
                .eq(dto.getAllModelIds() != 1,ProprietaryActivity::getVehicleModelIds, dto.toStringFromList(dto.getVehicleModelIds()))
                .eq(dto.getAllStore() == 1 ,ProprietaryActivity::getStoreIds , "-1")
                .eq(dto.getAllStore() != 1 ,ProprietaryActivity::getStoreIds , dto.toStringFromList(dto.getStoreIdList()))
                .eq(ProprietaryActivity::getActivityType, dto.getActivityType())
                .eq(ProprietaryActivity::getPricingType, dto.getPricingType())
                .eq(ProprietaryActivity::getDiscountLatitude, dto.getDiscountLatitude())
                .eq(dto.getActivityType() == 1 && dto.getPricingType() == 1, ProprietaryActivity::getFullMinusFlexiblePricing, JSON.toJSONString(dto.getFullMinusFlexiblePricing()))
                .eq(dto.getActivityType() == 1 && dto.getPricingType() == 2, ProprietaryActivity::getFullMinusStandardPricing, JSON.toJSONString(dto.getFullMinusStandardPricing()))
                .eq(dto.getActivityType() == 2 && dto.getPricingType() == 1, ProprietaryActivity::getDiscountFlexiblePricing, JSON.toJSONString(dto.getDiscountFlexiblePricing()))
                .eq(dto.getActivityType() == 2 && dto.getPricingType() == 2, ProprietaryActivity::getDiscountStandardPricing, JSON.toJSONString(dto.getDiscountStandardPricing()))
                .eq(ProprietaryActivity::getMinRentDays, dto.getMinRentDays())
                .eq(ProprietaryActivity::getAvailableOnHolidays, dto.getAvailableOnHolidays())
                .eq(ProprietaryActivity::getSignUpStartDate, dto.getSignUpStartDate())
                .eq(ProprietaryActivity::getSignUpEndDate, dto.getSignUpEndDate())
                .eq(ProprietaryActivity::getPickUpDate, dto.getPickUpDate())
                .eq(ProprietaryActivity::getReturnDate, dto.getReturnDate())
                .eq(ProprietaryActivity::getActivityStartDate, dto.getActivityStartDate())
                .eq(ProprietaryActivity::getActivityEndDate, dto.getActivityEndDate())
                .eq(ProprietaryActivity::getUnavailableDateRanges, JSON.toJSONString(dto.getUnavailableDateRanges()))
                .eq(ProprietaryActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .eq(ProprietaryActivity::getSpecifyDateFlag,dto.getSpecifyDateFlag())
                .eq(ProprietaryActivity::getSpecifyDate,dto.getSpecifyDate())
                .eq(ProprietaryActivity::getBlockHolidayFlag,dto.getBlockHolidayFlag())
                .eq(ProprietaryActivity::getIntersectionFlag,dto.getIntersectionFlag())
                ;

        List<ProprietaryActivity> activitys = proprietaryActivityMapper.selectList(queryWrapper);

        if (CollectionUtils.isNotEmpty(activitys)) {
            for(ProprietaryActivity activity : activitys){
                if(activity.getActivityStatus() == ActivityStatusEnum.NOT_START.getType() ||
                        activity.getActivityStatus() == ActivityStatusEnum.EFFECT.getType()){
                    log.error("该配置内容(未开始、生效中)状态已存在，请不要重复创建！");
                    throw new BusinessException(ErrorEnum.REPEATED_ERROR1.getCode(), ErrorEnum.REPEATED_ERROR1.getMsg());
                }
            }
        }

        ProprietaryActivity proprietaryActivity = setProprietaryActivityData(dto);
        //不存在该记录，新增
        String logContent = "新增";
        proprietaryActivityManager.insertProprietaryActivity(proprietaryActivity, dto.getCurrentUser(), logContent);
    }

    @Override
    public QueryProprietaryActivityListBo queryProprietaryActivityList(QueryProprietaryActivityListDto dto) throws BusinessException {
        //查询
        boolean condition1 = dto.getActivityStatus() == 0;
        boolean condition2 = dto.getActivityType() == 0;
        boolean condition3 = dto.getPricingType() == 0;
        LambdaQueryWrapper<ProprietaryActivity> queryWrapper = new LambdaQueryWrapper<ProprietaryActivity>()
                .like(!StringUtils.isEmpty(dto.getActivityName()),ProprietaryActivity::getActivityName, "%" + dto.getActivityName() + "%")
                .like(!StringUtils.isEmpty(dto.getActivityTag()),ProprietaryActivity::getActivityTag, "%" + dto.getActivityTag() + "%")
                .eq(!condition1,ProprietaryActivity::getActivityStatus, dto.getActivityStatus())
                .eq(!condition2,ProprietaryActivity::getActivityType, dto.getActivityType())
                .eq(!condition3,ProprietaryActivity::getPricingType, dto.getPricingType())
                .ge(dto.getSignUpStartDate() != null ,ProprietaryActivity::getSignUpEndDate, dto.getSignUpStartDate())
                .le(dto.getSignUpEndDate() != null,ProprietaryActivity::getSignUpStartDate, dto.getSignUpEndDate())
                .eq(ProprietaryActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .last("order by field(activity_status, 2,1,3,4), sign_up_start_date desc,id desc");
//                .orderByAsc(ProprietaryActivity::getActivityStatus)
//                .orderByDesc(ProprietaryActivity::getUpdateTime)
//                .orderByDesc(ProprietaryActivity::getId);

        Page<ProprietaryActivity> page = new Page<>(dto.getPageNum(), dto.getPageSize(),true);
        Page<ProprietaryActivity> pageResult = proprietaryActivityMapper.selectPage(page, queryWrapper);

        if (pageResult == null) {
            log.error("未查询到自营活动（分页）");
            throw new BusinessException(ErrorEnum.LIST_ERROR1.getCode(), ErrorEnum.LIST_ERROR1.getMsg());
        }

        //返回参数
        QueryProprietaryActivityListBo bo = new QueryProprietaryActivityListBo();
        List<QueryProprietaryActivityListInfo> list = new ArrayList<>();
        for (ProprietaryActivity act : pageResult.getRecords()) {
            QueryProprietaryActivityListInfo info = new QueryProprietaryActivityListInfo();
            info.setId(act.getId());
            info.setActivityName(act.getActivityName());
            info.setActivityTag(act.getActivityTag());
            info.setActivityType(act.getActivityType());
            info.setSignUpStartDate(act.getSignUpStartDate().toString());
            info.setSignUpEndDate(act.getSignUpEndDate().toString());
            info.setPricingType(act.getPricingType());
            info.setActivityStatus(act.getActivityStatus());
            list.add(info);
        }
        bo.setList(list);
        bo.setTotal((int) pageResult.getTotal());
        return bo;
    }

    @Override
    public GetProprietaryActivityInfoBo getProprietaryActivityInfo(long id) throws BusinessException {
        if(id <= 0){
            log.error("无效的活动id, id = {}", id);
            throw new BusinessException(ErrorEnum.INVALID_ACTIVITY_ID.getCode(), ErrorEnum.INVALID_ACTIVITY_ID.getMsg());
        }
        ProprietaryActivity act = proprietaryActivityMapper.selectById(id);
        if (act == null) {
            log.error("未查询到自营活动详情, id = {}", id);
            throw new BusinessException(ErrorEnum.NO_FOUNT_ACTIVITY_DETAIL.getCode(), ErrorEnum.NO_FOUNT_ACTIVITY_DETAIL.getMsg());
        }
        GetProprietaryActivityInfoBo bo = new GetProprietaryActivityInfoBo();
        try {
            bo.setId(act.getId());
            bo.setActivityName(act.getActivityName());
            bo.setActivityTag(act.getActivityTag());
             if(StringUtils.equals(act.getOrgCodes(),"-1")){
                bo.setAllOrgCodes(1);
                bo.setOrgCodes(new ArrayList<>());
            }else {
                bo.setAllOrgCodes(2);
                bo.setOrgCodes(CommonUtils.orgCodesSplit(act.getOrgCodes()));
            }
            int allStore = StringUtils.equals(act.getStoreIds(),"-1")? 1:2;
            List<Long> storeIdList = StringUtils.equals(act.getStoreIds(),"-1")? new ArrayList<>():CommonUtils.storeIdsSplit(act.getStoreIds());
            bo.setAllStore(allStore);
            bo.setStoreIdList(storeIdList);
            if(StringUtils.equals(act.getVehicleModelIds(),"-1")){
                bo.setAllModelIds(1);
                bo.setVehicleModelIds(new ArrayList<>());
            }else{
                bo.setAllModelIds(2);
                bo.setVehicleModelIds(Arrays.stream(act.getVehicleModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList()));
            }
            bo.setActivityType(act.getActivityType());
            bo.setPricingType(act.getPricingType());
            bo.setActivityStatus(act.getActivityStatus());
            bo.setDiscountLatitude(act.getDiscountLatitude());
            bo.setFullMinusStandardPricing(JSON.parseArray(act.getFullMinusStandardPricing(), FullMinusStandardPricing.class));
            bo.setFullMinusFlexiblePricing(JSON.parseArray(act.getFullMinusFlexiblePricing(), FullMinusFlexiblePricing.class));
            bo.setDiscountStandardPricing(JSON.parseArray(act.getDiscountStandardPricing(), DiscountStandardPricing.class));
            bo.setDiscountFlexiblePricing(JSON.parseArray(act.getDiscountFlexiblePricing(),DiscountFlexiblePricing.class));
            bo.setMinRentDays(act.getMinRentDays());
            bo.setMaxRentDays(act.getMaxRentDays() == null? 0 :act.getMaxRentDays());
            bo.setAvailableOnHolidays(act.getAvailableOnHolidays());
            bo.setSignUpStartDate(act.getSignUpStartDate().toString());
            bo.setSignUpEndDate(act.getSignUpEndDate().toString());
            bo.setPickUpDate(act.getPickUpDate().toString());
            bo.setReturnDate(act.getReturnDate().toString());
            bo.setActivityStartDate(act.getActivityStartDate().toString());
            bo.setActivityEndDate(act.getActivityEndDate().toString());
            bo.setUnavailableDateRanges(JSON.parseArray(act.getUnavailableDateRanges(), TimeRange.class));
            bo.setSignUpOrgCodes(act.getSignUpOrgCodes());
            bo.setSameDayUseFlag(act.getSameDayUseFlag() == null ? 0 : act.getSameDayUseFlag());
            bo.setSpecifyDateFlag(act.getSpecifyDateFlag() == null ? 0 : act.getSpecifyDateFlag());
            bo.setSpecifyDate(act.getSpecifyDate());
            bo.setBlockHolidayFlag(act.getBlockHolidayFlag()== null ? 0 : act.getBlockHolidayFlag());
            bo.setIntersectionFlag(act.getIntersectionFlag());
        } catch (Exception e) {
            log.error("自营活动根据id获取详情失败, id = {}", id);
            throw new BusinessException(ErrorEnum.GET_DETAIL_ERROR2.getCode(), ErrorEnum.GET_DETAIL_ERROR2.getMsg());
        }
        return bo;
    }

    @Override
    public CancelProprietaryActivityRes cancelProprietaryActivity(CancelProprietaryActivityReq req) {
        try {
            CurrentUser currentUser = req.getCurrentUser();
            long id = req.getId();
            if(id <= 0){
                log.error("无效的活动id, id = {}", id);
                throw new BusinessException(ErrorEnum.INVALID_ACTIVITY_ID.getCode(), ErrorEnum.INVALID_ACTIVITY_ID.getMsg());
            }
            ProprietaryActivity act = proprietaryActivityMapper.selectById(id);
            if (act == null) {
                log.error("未查询到自营活动详情, id = {}", id);
                throw new BusinessException(ErrorEnum.NO_FOUNT_ACTIVITY_DETAIL.getCode(), ErrorEnum.NO_FOUNT_ACTIVITY_DETAIL.getMsg());
            }
            if (act.getActivityStatus().equals(ActivityStatusEnum.CANCEL.getType())) {
                log.error("该自营活动已作废,不能重复作废, id = {}", id);
                throw new BusinessException(ErrorEnum.HAS_CANCEL.getCode(), ErrorEnum.HAS_CANCEL.getMsg());
            }

            //删除
            String logContent = "作废自营活动";
            proprietaryActivityManager.cancelProprietaryActivity(id, currentUser, logContent);

            return CancelProprietaryActivityRes.ok();
        } catch (BusinessException e) {
            log.error("cancelProprietaryActivity 作废自营活动失败,req = {}", JSON.toJSONString(req));
            return CancelProprietaryActivityRes.failed(e.getCode(), e.getMessage());
        }

    }

    @Override
    public void updateProprietaryActivity(UpdateProprietaryActivityDto updateDto) throws BusinessException {
        ProprietaryActivity act = proprietaryActivityMapper.selectById(updateDto.getId());
        if (act == null) {
            log.error("未查询到自营活动, id = {}", updateDto.getId());
            throw new BusinessException(ErrorEnum.NO_FOUNT_ACTIVITY_DETAIL.getCode(), ErrorEnum.NO_FOUNT_ACTIVITY_DETAIL.getMsg());
        }

        //只有未开始状态才可修改详情
        if (act.getActivityStatus() != ActivityStatusEnum.NOT_START.getType()) {
            log.error("只有未开始状态才可修改详情,该活动状态 id = {} activityStatus = {}", act.getId(),act.getActivityStatus());
            throw new BusinessException(ErrorEnum.NOT_MODIFY.getCode(), ErrorEnum.NOT_MODIFY.getMsg());
        }

        AddProprietaryActivityDto dto = updateDto.getAddProprietaryActivityDto();
        //判断该记录是否已经存在
        LambdaQueryWrapper<ProprietaryActivity> queryWrapper = new LambdaQueryWrapper<ProprietaryActivity>()
                .eq(ProprietaryActivity::getActivityName, dto.getActivityName())
                .eq(ProprietaryActivity::getActivityTag, dto.getActivityTag())
                /*.eq(dto.getAllOrgCodes() == 1,ProprietaryActivity::getOrgCodes, "-1")
                .eq(dto.getAllOrgCodes() != 1,ProprietaryActivity::getOrgCodes, CommonUtils.orgCodeStr(dto.getOrgCodes()))*/
                .eq(dto.getAllModelIds() == 1,ProprietaryActivity::getVehicleModelIds, "-1")
                .eq(dto.getAllModelIds() != 1,ProprietaryActivity::getVehicleModelIds, dto.toStringFromList(dto.getVehicleModelIds()))
                .eq(dto.getAllStore() == 1 ,ProprietaryActivity::getStoreIds , "-1")
                .eq(dto.getAllStore() != 1 ,ProprietaryActivity::getStoreIds , dto.toStringFromList(dto.getStoreIdList()))
                .eq(ProprietaryActivity::getActivityType, dto.getActivityType())
                .eq(ProprietaryActivity::getPricingType, dto.getPricingType())
                .eq(ProprietaryActivity::getDiscountLatitude, dto.getDiscountLatitude())
                .eq(dto.getActivityType() == 1 && dto.getPricingType() == 1, ProprietaryActivity::getFullMinusFlexiblePricing, JSON.toJSONString(dto.getFullMinusFlexiblePricing()))
                .eq(dto.getActivityType() == 1 && dto.getPricingType() == 2, ProprietaryActivity::getFullMinusStandardPricing, JSON.toJSONString(dto.getFullMinusStandardPricing()))
                .eq(dto.getActivityType() == 2 && dto.getPricingType() == 1, ProprietaryActivity::getDiscountFlexiblePricing, JSON.toJSONString(dto.getDiscountFlexiblePricing()))
                .eq(dto.getActivityType() == 2 && dto.getPricingType() == 2, ProprietaryActivity::getDiscountStandardPricing, JSON.toJSONString(dto.getDiscountStandardPricing()))
                .eq(ProprietaryActivity::getMinRentDays, dto.getMinRentDays())
                .eq(ProprietaryActivity::getMaxRentDays, dto.getMaxRentDays())
                .eq(ProprietaryActivity::getAvailableOnHolidays, dto.getAvailableOnHolidays())
                .eq(ProprietaryActivity::getSignUpStartDate, dto.getSignUpStartDate())
                .eq(ProprietaryActivity::getSignUpEndDate, dto.getSignUpEndDate())
                .eq(ProprietaryActivity::getPickUpDate, dto.getPickUpDate())
                .eq(ProprietaryActivity::getReturnDate, dto.getReturnDate())
                .eq(ProprietaryActivity::getActivityStartDate, dto.getActivityStartDate())
                .eq(ProprietaryActivity::getActivityEndDate, dto.getActivityEndDate())
                .eq(ProprietaryActivity::getUnavailableDateRanges, JSON.toJSONString(dto.getUnavailableDateRanges()))
                .eq(ProprietaryActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .eq(ProprietaryActivity::getSameDayUseFlag, dto.getSameDayUseFlag())
                .eq(ProprietaryActivity::getSpecifyDateFlag, dto.getSpecifyDateFlag())
                .eq(ProprietaryActivity::getSpecifyDate, dto.getSpecifyDate())
                .eq(ProprietaryActivity::getBlockHolidayFlag, dto.getBlockHolidayFlag())
                .eq(ProprietaryActivity::getIntersectionFlag, dto.getIntersectionFlag())
                ;

        ProprietaryActivity activity = proprietaryActivityMapper.selectOne(queryWrapper);

        if (activity != null) {
            log.error("该配置内容已存在，不需要更新！");
            throw new BusinessException(ErrorEnum.REPEATED_ERROR2.getCode(), ErrorEnum.REPEATED_ERROR2.getMsg());
        }

        //更新
        ProprietaryActivity proprietaryActivity = setProprietaryActivityData(dto);
        proprietaryActivity.setId(updateDto.getId());
        String logContent = getLogContent(act, proprietaryActivity);
        proprietaryActivityManager.updateProprietaryActivityInfo(proprietaryActivity, updateDto.getCurrentUser(), logContent, updateDto.getId());
    }

    @Override
    public QuerySignUpProprietaryActivityListBo querySignUpProprietaryActivityListByActivityId(QuerySignUpProprietaryActivityListByActivityIdReq querySignUpProprietaryActivityListByActivityIdReq) throws BusinessException {
        long id = querySignUpProprietaryActivityListByActivityIdReq.getId();
        int pageNum = querySignUpProprietaryActivityListByActivityIdReq.getPageNum();
        int pageSize = querySignUpProprietaryActivityListByActivityIdReq.getPageSize();
        if(id <= 0){
            log.error("无效的活动id, id = {}", id);
            throw new BusinessException(ErrorEnum.INVALID_ACTIVITY_ID.getCode(), ErrorEnum.INVALID_ACTIVITY_ID.getMsg());
        }
        ProprietaryActivity act = proprietaryActivityMapper.selectById(id);
        if (act == null) {
            log.error("未查询到自营活动详情, id = {}", id);
            throw new BusinessException(ErrorEnum.NO_FOUNT_ACTIVITY_DETAIL.getCode(), ErrorEnum.NO_FOUNT_ACTIVITY_DETAIL.getMsg());
        }

        LambdaQueryWrapper<ProprietaryActivitySignup> queryWrapper = new LambdaQueryWrapper<ProprietaryActivitySignup>()
                .eq(ProprietaryActivitySignup::getActivityId, id)
                .eq(ProprietaryActivitySignup::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                ;

        Page<ProprietaryActivitySignup> page = new Page<>(pageNum, pageSize,true);
        Page<ProprietaryActivitySignup> pageResult = proprietaryActivitySignupMapper.selectPage(page, queryWrapper);

        if (pageResult == null) {
            log.error("未查询到报名自营活动详情（分页）");
            throw new BusinessException(ErrorEnum.NO_FOUNT_SIGN_UP_ACTIVITY_DETAIL.getCode(), ErrorEnum.NO_FOUNT_SIGN_UP_ACTIVITY_DETAIL.getMsg());
        }

//        List<ProprietaryActivitySignup> signUpList = proprietaryActivitySignupMapper.selectList(queryWrapper);
//        if (CollectionUtils.isEmpty(signUpList)) {
//            log.error("未查询到报名自营活动详情, id = {}", id);
//            throw new BusinessException(ErrorEnum.NO_FOUNT_SIGN_UP_ACTIVITY_DETAIL.getCode(), ErrorEnum.NO_FOUNT_SIGN_UP_ACTIVITY_DETAIL.getMsg());
//        }

        QuerySignUpProprietaryActivityListBo bo = new QuerySignUpProprietaryActivityListBo();
        List<QuerySignUpProprietaryActivityListInfo> list = new ArrayList<>();
        for(ProprietaryActivitySignup item : pageResult.getRecords()){
            QuerySignUpProprietaryActivityListInfo info = new QuerySignUpProprietaryActivityListInfo();
            info.setId(act.getId());
            info.setSignUpId(item.getId());
            info.setActivityName(act.getActivityName());
            info.setOrgCode(item.getOrgCode());
            info.setOrgName(orgService.getOrgNameByOrgCode(info.getOrgCode()));
            info.setActivityType(act.getActivityType());
            info.setSignUpDate(DateUtil.dateToString(item.getCreateTime(),DateUtil.DATE_TYPE1));
            info.setPricingType(act.getPricingType());
            if(item.getStoreId() != null){
                info.setStoreId(item.getStoreId());
                info.setStoreName(storeService.getStoreNameByStoreId(item.getStoreId()));
            }
            list.add(info);
        }
        bo.setList(list);
        bo.setTotal((int) pageResult.getTotal());
        return bo;
    }

    @Override
    public GetSignUpProprietaryActivityInfoBo getSignUpProprietaryActivityInfo(GetSignUpProprietaryActivityInfoReq req) throws BusinessException {
        long id = req.getId();
        long signUpId = req.getSignUpId();
        if(id <= 0){
            log.error("无效的活动id, id = {}", id);
            throw new BusinessException(ErrorEnum.INVALID_ACTIVITY_ID.getCode(), ErrorEnum.INVALID_ACTIVITY_ID.getMsg());
        }
        if(signUpId <= 0){
            log.error("无效的报名id, signUpId = {}", signUpId);
            throw new BusinessException(ErrorEnum.INVALID_SIGN_UP_ACTIVITY_ID.getCode(), ErrorEnum.INVALID_SIGN_UP_ACTIVITY_ID.getMsg());
        }

        ProprietaryActivity act = proprietaryActivityMapper.selectById(id);
        if (act == null) {
            log.error("未查询到自营活动详情, id = {}", id);
            throw new BusinessException(ErrorEnum.NO_FOUNT_ACTIVITY_DETAIL.getCode(), ErrorEnum.NO_FOUNT_ACTIVITY_DETAIL.getMsg());
        }

        ProprietaryActivitySignup proprietaryActivitySignup = proprietaryActivitySignupMapper.selectById(signUpId);
        if (proprietaryActivitySignup == null) {
            log.error("未查询到报名自营活动详情, signUpId = {}", signUpId);
            throw new BusinessException(ErrorEnum.NO_FOUNT_SIGN_UP_ACTIVITY_DETAIL.getCode(), ErrorEnum.NO_FOUNT_SIGN_UP_ACTIVITY_DETAIL.getMsg());
        }

        GetSignUpProprietaryActivityInfoBo bo = new GetSignUpProprietaryActivityInfoBo();
        try {
            bo.setId(act.getId());
            bo.setSignUpId(proprietaryActivitySignup.getId());
            bo.setActivityName(act.getActivityName());
            bo.setOrgCode(proprietaryActivitySignup.getOrgCode());
            bo.setStoreId(proprietaryActivitySignup.getStoreId());
            if(StringUtils.equals(act.getVehicleModelIds(),"-1")){
                bo.setAllModelIds(1);
                bo.setVehicleModelIds(new ArrayList<>());
            }else{
                bo.setAllModelIds(2);
                bo.setVehicleModelIds(Arrays.stream(act.getVehicleModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList()));
            }
            bo.setActivityType(act.getActivityType());
            bo.setPricingType(act.getPricingType());
            bo.setDiscountLatitude(act.getDiscountLatitude());
            bo.setFullMinusStandardPricing(JSON.parseArray(act.getFullMinusStandardPricing(), FullMinusStandardPricing.class));
            bo.setFullMinusFlexiblePricing(JSON.parseArray(proprietaryActivitySignup.getFullMinusFlexiblePricing(), FullMinusStandardPricing.class));
            bo.setDiscountStandardPricing(JSON.parseArray(act.getDiscountStandardPricing(), DiscountStandardPricing.class));
            bo.setDiscountFlexiblePricing(JSON.parseArray(proprietaryActivitySignup.getDiscountFlexiblePricing(),DiscountStandardPricing.class));
        } catch (Exception e) {
            log.error("获取报名后自营活动详情, id = {}", id);
            throw new BusinessException(ErrorEnum.GET_DETAIL_ERROR2.getCode(), ErrorEnum.GET_DETAIL_ERROR2.getMsg());
        }
        return bo;
    }

    //更新日志
    private String getLogContent(ProprietaryActivity oldAct, ProprietaryActivity newAct) {
        String str = "更新自营活动";

        //活动名称
        if (!StringUtils.equals(oldAct.getActivityName(),newAct.getActivityName())) {
            str += "活动名称由 " + oldAct.getActivityName() + " 更新为 " + newAct.getActivityName() + "; ";
        }
        //活动标签
        if (!StringUtils.equals(oldAct.getActivityTag(),newAct.getActivityTag())) {
            str += "活动标签由 " + oldAct.getActivityTag() + " 更新为 " + newAct.getActivityTag() + "; ";
        }
        //参与门店
        if (!StringUtils.equals(oldAct.getStoreIds(),newAct.getStoreIds())) {
            List<Long> storeIds = CommonUtils.storeIdsSplit(oldAct.getStoreIds());
            List<Long> storeIds1 = CommonUtils.storeIdsSplit(newAct.getStoreIds());
            str += "参与门店由 " + configLoader.listStoreName(storeIds) + " 更新为 " + configLoader.listStoreName(storeIds1) + "; ";
        }
        //门店车型
        if (!StringUtils.equals(oldAct.getVehicleModelIds(),newAct.getVehicleModelIds())) {
            List<Long> ids1 = Arrays.stream(oldAct.getVehicleModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<Long> ids2 = Arrays.stream(newAct.getVehicleModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            str += "门店车型由 " + configLoader.listVehicleName(ids1) + " 更新为 " + configLoader.listVehicleName(ids2);
        }
        //活动类型 定价类型
        if(!Objects.equals(oldAct.getActivityType(), newAct.getActivityType()) && Objects.equals(oldAct.getPricingType(), newAct.getPricingType())){
            str += "活动类型由 " + getActivityTypeName(oldAct.getActivityType()) + " 更新为 " + getActivityTypeName(newAct.getActivityType()) + "; ";

            //满减 规范
            if(Objects.equals(oldAct.getActivityType(),ActType.FULLREDUCTION.getType()) && Objects.equals(oldAct.getPricingType(),PricingTypeEnum.STANDARD_PRICING.getType())){
                str += "由满减规范定价 " + oldAct.getFullMinusStandardPricing() + " 更新为 打折规范定价" + newAct.getDiscountStandardPricing() + "; ";
            }
            //折扣 规范
            if(Objects.equals(oldAct.getActivityType(),ActType.DISCOUNT.getType()) && Objects.equals(oldAct.getPricingType(),PricingTypeEnum.STANDARD_PRICING.getType())){
                str += "由打折规范定价 " + oldAct.getDiscountStandardPricing() + " 更新为 满减规范定价" + newAct.getFullMinusStandardPricing() + "; ";
            }
            //满减 灵活
            if(Objects.equals(oldAct.getActivityType(),ActType.FULLREDUCTION.getType()) && Objects.equals(oldAct.getPricingType(),PricingTypeEnum.FLEXIBLE_PRICING.getType())){
                str += "由满减灵活定价 " + oldAct.getFullMinusFlexiblePricing() + " 更新为 打折灵活定价" + newAct.getDiscountFlexiblePricing() + "; ";
            }
            //折扣 灵活
            if(Objects.equals(oldAct.getActivityType(),ActType.DISCOUNT.getType()) && Objects.equals(oldAct.getPricingType(),PricingTypeEnum.FLEXIBLE_PRICING.getType())){
                str += "由打折灵活定价 " + oldAct.getDiscountFlexiblePricing() + " 更新为 满减灵活定价" + newAct.getFullMinusFlexiblePricing() + "; ";
            }
        }
        //活动类型 定价类型
        if(!Objects.equals(oldAct.getActivityType(), newAct.getActivityType()) && !Objects.equals(oldAct.getPricingType(), newAct.getPricingType())){
            str += "活动类型由 " + getActivityTypeName(oldAct.getActivityType()) + " 更新为 " + getActivityTypeName(newAct.getActivityType()) + "; ";
            str += "定价类型由 " + getPricingTypeName(oldAct.getPricingType()) + " 更新为 " + getPricingTypeName(newAct.getPricingType()) + "; ";

            //满减 规范
            if(Objects.equals(oldAct.getActivityType(),ActType.FULLREDUCTION.getType()) && Objects.equals(oldAct.getPricingType(),PricingTypeEnum.STANDARD_PRICING.getType())){
                str += "由满减规范定价 " + oldAct.getFullMinusStandardPricing() + " 更新为 打折灵活定价" + newAct.getDiscountFlexiblePricing() + "; ";
            }
            //满减 灵活
            if(Objects.equals(oldAct.getActivityType(),ActType.FULLREDUCTION.getType()) && Objects.equals(oldAct.getPricingType(),PricingTypeEnum.FLEXIBLE_PRICING.getType())){
                str += "由满减灵活定价 " + oldAct.getFullMinusFlexiblePricing() + " 更新为 打折规范定价" + newAct.getDiscountStandardPricing() + "; ";
            }
            //折扣 规范
            if(Objects.equals(oldAct.getActivityType(),ActType.DISCOUNT.getType()) && Objects.equals(oldAct.getPricingType(),PricingTypeEnum.STANDARD_PRICING.getType())){
                str += "由打折规范定价 " + oldAct.getDiscountStandardPricing() + " 更新为 满减灵活定价" + newAct.getFullMinusFlexiblePricing() + "; ";
            }
            //折扣 灵活
            if(Objects.equals(oldAct.getActivityType(),ActType.DISCOUNT.getType()) && Objects.equals(oldAct.getPricingType(),PricingTypeEnum.FLEXIBLE_PRICING.getType())){
                str += "由打折灵活定价 " + oldAct.getDiscountFlexiblePricing() + " 更新为 满减规范定价" + newAct.getFullMinusStandardPricing() + "; ";
            }
        }
        //活动类型 定价类型
        if(Objects.equals(oldAct.getActivityType(), newAct.getActivityType()) && !Objects.equals(oldAct.getPricingType(), newAct.getPricingType())){
            str += "定价类型由 " + getPricingTypeName(oldAct.getPricingType()) + " 更新为 " + getPricingTypeName(newAct.getPricingType()) + "; ";

            //满减 规范
            if(Objects.equals(oldAct.getActivityType(),ActType.FULLREDUCTION.getType()) && Objects.equals(oldAct.getPricingType(),PricingTypeEnum.STANDARD_PRICING.getType())){
                str += "由满减规范定价 " + oldAct.getFullMinusStandardPricing() + " 更新为 满减灵活定价" + newAct.getFullMinusFlexiblePricing() + "; ";
            }
            //满减 灵活
            if(Objects.equals(oldAct.getActivityType(),ActType.FULLREDUCTION.getType()) && Objects.equals(oldAct.getPricingType(),PricingTypeEnum.FLEXIBLE_PRICING.getType())){
                str += "由满减灵活定价 " + oldAct.getFullMinusFlexiblePricing() + " 更新为 满减规范定价" + newAct.getFullMinusStandardPricing() + "; ";
            }
            //折扣 规范
            if(Objects.equals(oldAct.getActivityType(),ActType.DISCOUNT.getType()) && Objects.equals(oldAct.getPricingType(),PricingTypeEnum.STANDARD_PRICING.getType())){
                str += "由打折规范定价 " + oldAct.getDiscountStandardPricing() + " 更新为 打折灵活定价" + newAct.getDiscountFlexiblePricing() + "; ";
            }
            //折扣 灵活
            if(Objects.equals(oldAct.getActivityType(),ActType.DISCOUNT.getType()) && Objects.equals(oldAct.getPricingType(),PricingTypeEnum.FLEXIBLE_PRICING.getType())){
                str += "由打折灵活定价 " + oldAct.getDiscountFlexiblePricing() + " 更新为 打折规范定价" + newAct.getDiscountStandardPricing() + "; ";
            }
        }
        //优惠纬度
        if(!Objects.equals(oldAct.getDiscountLatitude(), newAct.getDiscountLatitude())){
            str += "优惠纬度由 " + oldAct.getDiscountLatitude() + " 更新为 " + newAct.getDiscountLatitude() + "; ";
        }
        //最小租期
        if(!Objects.equals(oldAct.getMinRentDays(), newAct.getMinRentDays())){
            str += "最小租期由 " + oldAct.getMinRentDays() + " 更新为 " + newAct.getMinRentDays() + "; ";
        }
        //节假日是否可用
        if(!Objects.equals(oldAct.getAvailableOnHolidays(), newAct.getAvailableOnHolidays())){
            str += "节假日是否可用由 " + getAvailableName(oldAct.getAvailableOnHolidays()) + " 更新为 " + getAvailableName(newAct.getAvailableOnHolidays()) + "; ";
        }
        //报名开始时间
        if (oldAct.getSignUpStartDate().compareTo(newAct.getSignUpStartDate()) != 0) {
            str += "报名开始时间由 " + oldAct.getSignUpStartDate() + " 更新为 " + newAct.getSignUpStartDate() + "; ";
        }
        //报名结束时间
        if (oldAct.getSignUpEndDate().compareTo(newAct.getSignUpEndDate()) != 0) {
            str += "报名结束时间由 " + oldAct.getSignUpEndDate() + " 更新为 " + newAct.getSignUpEndDate() + "; ";
        }
        //取车时间
        if (oldAct.getPickUpDate().compareTo(newAct.getPickUpDate()) != 0) {
            str += "取车时间由 " + oldAct.getPickUpDate() + " 更新为 " + newAct.getPickUpDate() + "; ";
        }
        //还车时间
        if (oldAct.getReturnDate().compareTo(newAct.getReturnDate()) != 0) {
            str += "还车时间由 " + oldAct.getReturnDate() + " 更新为 " + newAct.getReturnDate() + "; ";
        }
        //活动开始时间
        if (oldAct.getActivityStartDate().compareTo(newAct.getActivityStartDate()) != 0) {
            str += "活动开始时间由 " + oldAct.getActivityStartDate() + " 更新为 " + newAct.getActivityStartDate() + "; ";
        }
        //活动结束时间
        if (oldAct.getActivityEndDate().compareTo(newAct.getActivityEndDate()) != 0) {
            str += "活动结束时间由 " + oldAct.getActivityEndDate() + " 更新为 " + newAct.getActivityEndDate() + "; ";
        }
        //不可用时间范围
        if (!StringUtils.equals(oldAct.getUnavailableDateRanges(),newAct.getUnavailableDateRanges())) {
            str += "不可用时间范围由 " + oldAct.getUnavailableDateRanges().replace("startDate", "不可用时间开始时间").replace("endDate", "不可用时间结束时间") +
                    " 更新为 " + newAct.getUnavailableDateRanges().replace("startDate", "不可用时间开始时间").replace("endDate", "不可用时间结束时间") + "; ";
        }
        //指定日期表示
        if (oldAct.getSpecifyDateFlag() != newAct.getSpecifyDateFlag()) {
            str += "指定日期标识由 " + (oldAct.getSpecifyDateFlag() == 0 ? "不限制" : "限制") + " 更新为 " + (newAct.getSpecifyDateFlag() == 0 ? "不限制" : "限制") + "; ";
        }
        //指定日期
        if (!StringUtils.equals(oldAct.getSpecifyDate(),newAct.getSpecifyDate())) {
            str += "指定日期由 " + getContentBySpecifyDate(oldAct.getSpecifyDate()) + " 更新为 " + getContentBySpecifyDate(newAct.getSpecifyDate()) + "; ";
        }
        //屏蔽节假日
        if (oldAct.getBlockHolidayFlag() != newAct.getBlockHolidayFlag()) {
            str += "指定日期是否屏蔽节假日由 " + (oldAct.getBlockHolidayFlag() == 0 ? "不屏蔽" : "屏蔽") + " 更新为 " + (newAct.getBlockHolidayFlag() == 0 ? "不屏蔽" : "屏蔽") + "; ";
        }
        if (str.length() > 1024) {
            str = str.substring(0, 1021) + "...";
        }
        return str;
    }
    //指定日期转为中文
    private String getContentBySpecifyDate(String specifyDate){
        String specifyDateContent = "";
        if (StringUtils.isBlank(specifyDate)){
            return specifyDateContent;
        }
        try {
            String[] specifyDates = specifyDate.split(";");
            Arrays.sort(specifyDates);
            List<Integer> specifyDateList = Arrays.stream(specifyDates).map(Integer::valueOf).collect(Collectors.toList());
            for (int i = 0; i < specifyDateList.size(); i++) {
                Integer specifyDateInt = specifyDateList.get(i);
                if (specifyDateInt == 1){
                    specifyDateContent = specifyDateContent.concat("周一、");
                }else if (specifyDateInt == 2){
                    specifyDateContent = specifyDateContent.concat("周二、");
                }else if (specifyDateInt == 3){
                    specifyDateContent = specifyDateContent.concat("周三、");
                }else if (specifyDateInt == 4){
                    specifyDateContent = specifyDateContent.concat("周四、");
                }else if (specifyDateInt == 5){
                    specifyDateContent = specifyDateContent.concat("周五、");
                }else if (specifyDateInt == 6){
                    specifyDateContent = specifyDateContent.concat("周六、");
                }else if (specifyDateInt == 7){
                    specifyDateContent = specifyDateContent.concat("周日、");
                }
            }
            if (specifyDateContent.endsWith("、")){
                specifyDateContent = specifyDateContent.substring(0,specifyDateContent.length()-1);
            }
            return specifyDateContent;
        }catch (Exception e){
            return "";
        }
    }

    /**节假日是否可用*/
    private String getAvailableName(Integer availableOnHolidays) {
        String str = "";
        if(Objects.equals(availableOnHolidays, HolidaysTypeEnum.AVAILABLE.getType())){
            str =  HolidaysTypeEnum.AVAILABLE.getMsg();
        }
        if(Objects.equals(availableOnHolidays,HolidaysTypeEnum.NOT_AVAILABLE.getType())){
            str =  HolidaysTypeEnum.NOT_AVAILABLE.getMsg();
        }
        return str;
    }

    /**获取定价类型*/
    private String getPricingTypeName(Integer pricingType) {
        String str = "";
        if(Objects.equals(pricingType, PricingTypeEnum.FLEXIBLE_PRICING.getType())){
            str =  PricingTypeEnum.FLEXIBLE_PRICING.getMsg();
        }
        if(Objects.equals(pricingType,PricingTypeEnum.STANDARD_PRICING.getType())){
            str =  PricingTypeEnum.STANDARD_PRICING.getMsg();
        }
        return str;
    }

    /**获取活动类型*/
    private String getActivityTypeName(Integer activityType) {
        String str = "";
        if(Objects.equals(activityType,ActType.FULLREDUCTION.getType())){
            str =  ActType.FULLREDUCTION.getMsg();
        }
        if(Objects.equals(activityType,ActType.DISCOUNT.getType())){
            str =  ActType.DISCOUNT.getMsg();
        }
        return str;
    }

    /**设置表里的数据*/
    private ProprietaryActivity setProprietaryActivityData(AddProprietaryActivityDto addProprietaryActivityDto) {
        ProprietaryActivity act = new ProprietaryActivity();
        if(addProprietaryActivityDto != null){
            AddProprietaryActivityDto dto = addProprietaryActivityDto;
            act.setActivityName(dto.getActivityName());
            act.setActivityTag(dto.getActivityTag());
          /*  if(dto.getAllOrgCodes() == 1){
                act.setOrgCodes("-1");
            }else{
                act.setOrgCodes(CommonUtils.orgCodeStr(dto.getOrgCodes()));
            }*/
            if(dto.getAllModelIds() == 1){
                act.setVehicleModelIds("-1");
            }else{
                act.setVehicleModelIds(dto.toStringFromList(dto.getVehicleModelIds()));
            }
            act.setStoreIds(dto.getAllStore() == 1 ? "-1": dto.toStringFromList(dto.getStoreIdList()));
            if(dto.getAllStore() == 1){
                act.setOrgCodes("-1");
            }else {
                Set<String> orgCodeSet = new HashSet<>();
                dto.getStoreIdList().forEach(storeId->{
                    StoreInfoCombobox store = configLoader.getStore(storeId);
                    if(store != null){
                        orgCodeSet.add(store.getOperOrgCode());
                    }
                });
                act.setOrgCodes(CommonUtils.orgCodeStr(new ArrayList<>(orgCodeSet)));
            }
            act.setActivityType(dto.getActivityType());
            String curStr = DateUtil.dateToString(new Date(), DateUtil.DATE_TYPE5);
            LocalDate curDate = DateUtil.getLocalDateFromStr(curStr, DateUtil.DATE_TYPE5);
            if(dto.getSignUpStartDate().compareTo(curDate) <= 0){
                act.setActivityStatus(ActivityStatusEnum.EFFECT.getType());
            }else{
                act.setActivityStatus(ActivityStatusEnum.NOT_START.getType());
            }
            act.setPricingType(dto.getPricingType());
            act.setDiscountLatitude(dto.getDiscountLatitude());
            if(CollectionUtils.isEmpty(dto.getFullMinusStandardPricing())){
                act.setFullMinusStandardPricing("");
            }else{
                act.setFullMinusStandardPricing(JSON.toJSONString(dto.getFullMinusStandardPricing()));
            }
            if(CollectionUtils.isEmpty(dto.getFullMinusFlexiblePricing())){
                act.setFullMinusFlexiblePricing("");
            }else{
                act.setFullMinusFlexiblePricing(JSON.toJSONString(dto.getFullMinusFlexiblePricing()));
            }
            if(CollectionUtils.isEmpty(dto.getDiscountStandardPricing())){
                act.setDiscountStandardPricing("");
            }else{
                act.setDiscountStandardPricing(JSON.toJSONString(dto.getDiscountStandardPricing()));
            }
            if(CollectionUtils.isEmpty(dto.getDiscountFlexiblePricing())){
                act.setDiscountFlexiblePricing("");
            }else{
                act.setDiscountFlexiblePricing(JSON.toJSONString(dto.getDiscountFlexiblePricing()));
            }
            act.setSameDayUseFlag(dto.getSameDayUseFlag() == null ?  0 : dto.getSameDayUseFlag());
            act.setMinRentDays(dto.getMinRentDays());
            act.setMaxRentDays(dto.getMaxRentDays());
            act.setAvailableOnHolidays(dto.getAvailableOnHolidays());
            act.setSignUpStartDate(dto.getSignUpStartDate());
            act.setSignUpEndDate(dto.getSignUpEndDate());
            act.setPickUpDate(dto.getPickUpDate());
            act.setReturnDate(dto.getReturnDate());
            act.setActivityStartDate(dto.getActivityStartDate());
            act.setActivityEndDate(dto.getActivityEndDate());
            act.setUnavailableDateRanges(JSON.toJSONString(dto.getUnavailableDateRanges()));
            act.setSpecifyDateFlag(dto.getSpecifyDateFlag());
            act.setSpecifyDate(dto.getSpecifyDate());
            act.setBlockHolidayFlag(dto.getBlockHolidayFlag());
            act.setIntersectionFlag(dto.getIntersectionFlag());
            if(StringUtils.isEmpty(dto.getSignUpOrgCodes())){
                act.setSignUpOrgCodes("");
            }else{
                act.setSignUpOrgCodes(dto.getSignUpOrgCodes());
            }
        }
        return act;
    }
}
