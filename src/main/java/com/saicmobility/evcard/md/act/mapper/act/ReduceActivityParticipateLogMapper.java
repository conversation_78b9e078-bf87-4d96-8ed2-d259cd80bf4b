package com.saicmobility.evcard.md.act.mapper.act;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.saicmobility.evcard.md.act.entity.OperateLog;
import com.saicmobility.evcard.md.act.entity.ReduceActivityParticipateLog;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 立减活动 Mapper
 */
public interface ReduceActivityParticipateLogMapper extends BaseMapper<ReduceActivityParticipateLog> {


    Page<ReduceActivityParticipateLog> searchActivityParticipateLog(@Param("storeId") Long storeId,
                                                                    @Param("activityId") Long activityId,
                                                                    @Param("goodsModelId") Long goodsModelId,
                                                                    Page<ReduceActivityParticipateLog> page);

    Page<OperateLog> searchAllParticipateLog(@Param("storeId") Long storeId,
                                             @Param("activityId") Long activityId,
                                             @Param("foreignId") String foreignId,
                                             @Param("goodsModelId") Long goodsModelId,
                                             Page<OperateLog> page);


}
