package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.lang.reflect.Type;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.saicmobility.evcard.md.act.dto.newEnergy.TopCityInfoDto;
import com.saicmobility.evcard.md.act.util.DateUtils;
import com.saicmobility.evcard.md.mdactservice.api.BrandModelActivityInfo;
import com.saicmobility.evcard.md.mdactservice.api.TopCityInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 品牌车型配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_brand_model_activity")
@ApiModel(value="BrandModelActivity对象", description="品牌车型配置表")
public class BrandModelActivity extends Model<BrandModelActivity> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "状态（0=正常 1=已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID")
    private Long createOperId;

    @ApiModelProperty(value = "创建人名称")
    private String createOperName;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人ID")
    private Long updateOperId;

    @ApiModelProperty(value = "更新人名称")
    private String updateOperName;

    @ApiModelProperty(value = "品牌车型名称")
    private String brandModelName;

    @ApiModelProperty(value = "副标题")
    private String subtitleContent;

    @ApiModelProperty(value = "活动状态：1-待发布，2-待上线，3-上线中，4-暂停中，5-已下线")
    private Integer activityStatus;

    @ApiModelProperty(value = "列表页图内slogan")
    private String listpageSlogan;

    @ApiModelProperty(value = "活动城市,-1为全部城市，多城市以英文分号分割：123;234;567;891")
    private String activityCitys;

    @ApiModelProperty(value = "活动开始时间")
    private LocalDateTime activityStartDate;

    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime activityEndDate;

    @ApiModelProperty(value = "首页图片url")
    private String homePagePicUrl;

    @ApiModelProperty(value = "列表页图片url")
    private String listPagePicUrl;

    @ApiModelProperty(value = "详情页图片url")
    private String detailPagePicUrl;

    @ApiModelProperty(value = "是否关联下单 0-否 1-是")
    private Integer relatedOrdersFlag;

    @ApiModelProperty(value = "关联门店车型id")
    private String relatedModelIds;

    public static BrandModelActivityInfo toRes(BrandModelActivity brandModelActivity){
        BrandModelActivityInfo.Builder builder = BrandModelActivityInfo.newBuilder();
        String activityCitysStr = brandModelActivity.getActivityCitys();
        List<TopCityInfo> activityCityList = new ArrayList<>();
        if (StringUtils.isNotBlank(activityCitysStr)){
            Gson gson = new Gson();
            Type listType = new TypeToken<List<TopCityInfoDto>>(){}.getType();
            List<TopCityInfoDto> activityCityDtoList = gson.fromJson(activityCitysStr, listType);
            activityCityList = TopCityInfoDto.listToRes(activityCityDtoList);
        }

        return builder.setId(brandModelActivity.getId())
                .setCreateTime(DateUtils.dateToString(brandModelActivity.getCreateTime(),DateUtils.DATE_TYPE1))
                .setCreateOperId(brandModelActivity.getCreateOperId())
                .setCreateOperName(brandModelActivity.getCreateOperName())
                .setUpdateTime(DateUtils.dateToString(brandModelActivity.getUpdateTime(),DateUtils.DATE_TYPE1))
                .setUpdateOperId(brandModelActivity.getUpdateOperId())
                .setUpdateOperName(brandModelActivity.getUpdateOperName())
                .setBrandModelName(brandModelActivity.brandModelName)
                .setSubtitleContent(brandModelActivity.getSubtitleContent())
                .setActivityStatus(brandModelActivity.getActivityStatus())
                .setListpageSlogan(brandModelActivity.getListpageSlogan())
                .addAllActivityCitys(activityCityList)
                .setActivityStartDate(DateUtils.dateToString(brandModelActivity.getActivityStartDate(),DateUtils.DATE_TYPE1))
                .setActivityEndDate(DateUtils.dateToString(brandModelActivity.getActivityEndDate(),DateUtils.DATE_TYPE1))
                .setHomePagePicUrl(brandModelActivity.getHomePagePicUrl())
                .setListPagePicUrl(brandModelActivity.getListPagePicUrl())
                .setDetailPagePicUrl(brandModelActivity.getDetailPagePicUrl())
                .setRelatedOrdersFlag(brandModelActivity.getRelatedOrdersFlag())
                .setRelatedModelIds(brandModelActivity.getRelatedModelIds())
                .build();
    }

}
