package com.saicmobility.evcard.md.act.service.rest.entity.coupon;

import com.extracme.evcard.rpc.coupon.dto.CouponModelListDto;
import com.saicmobility.evcard.md.mdactservice.api.CouponModelView;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Optional;

@Data
@EqualsAndHashCode(callSuper = false)
public class GetCouponModelViewResponse extends CouponModelListDto {

    public CouponModelView toResBuilder(String mid) {
        CouponModelView.Builder builder = CouponModelView.newBuilder();
        builder.setCouponSeq(Optional.ofNullable(this.getCouponSeq()).orElse(0))
                .setCouponType(Optional.ofNullable(this.getCouponType()).orElse(0))
                .setTimeType(Optional.ofNullable(this.getTimeType()).orElse(0))
                .setStartTime(this.getStartTime())
                .setEndTime(this.getEndTime())
                .setMinAmount(null == this.getMinAmount() ? "0.00" : this.getMinAmount().toString())
                .setCouponValue(null == this.getCouponValue() ? "0.00" : this.getCouponValue().toString())
                .setDes(this.getDes())
                //.setPickUpStoreId()
                //.setPickUpStoreName()
                //.setReturnStoreId()
                //.setReturnStoreName()
                //.setGoodsModelId()
                //.setGoodsModelName()
                .setPickUpCity(this.getPickshopCity())
                .setPickUpCityName(this.getPickshopCityName())
                .setReturnCity(this.getReturnshopCity())
                .setReturnCityName(this.getReturnshopCityName())
                .setDiscountRate(Optional.ofNullable(this.getDiscountRate()).orElse(0))
                .setVehicleNo(this.getVehicleNo())
                .setServiceType(Optional.ofNullable(this.getServiceType()).orElse(0))
                .setCouponName(this.getCouponName())
                .setValidTimeType(Optional.ofNullable(this.getValidTimeType()).orElse(0))
                .setValidDays(Optional.ofNullable(this.getValidDays()).orElse(0))
                .setEffectiveDays(Optional.ofNullable(this.getEffectiveDays()).orElse(0))
                .setStartDate(this.getStartDate())
                .setExpiresDate(this.getExpiresDate())
                .setActivityOverlap(Optional.ofNullable(this.getActivityOverlap()).orElse(0))
                .setPackageIds(this.getPackageIds())
                .setHolidaysAvailable(Optional.ofNullable(this.getHolidaysAvailable()).orElse(0))
                .setAvailableDaysOfWeek(this.getAvailableDaysOfWeek())
                .setRentMethod(this.getRentMethod())
                //.setRentMethodGroup()
                .setUseMethod(this.getUseMethod())
                .setServiceTypeDesc(this.getServiceTypeDesc())
                .setRentMethodDesc(this.getRentMethodDesc())
                //.setRentMethodGroupDesc()
                .setDurationLimit(null == this.getDurationLimit() ? "0.00" : this.getDurationLimit().toString())
                .addAllTags(this.getTags())
                .setVehicleLimitDesc(this.getVehicleLimitDesc())
                .setSpace1(this.getSpace1())
                .addAllSpace2(this.getSpace2())
                .build();
        return builder.build();
    }

}
