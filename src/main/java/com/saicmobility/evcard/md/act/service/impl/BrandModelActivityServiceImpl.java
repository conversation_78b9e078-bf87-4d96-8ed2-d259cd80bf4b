package com.saicmobility.evcard.md.act.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.google.gson.Gson;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.dto.newEnergy.TopCityInfoDto;
import com.saicmobility.evcard.md.act.entity.BrandModelActivity;
import com.saicmobility.evcard.md.act.enums.BrandModelOperStateEnum;
import com.saicmobility.evcard.md.act.enums.IsDeletedEnum;
import com.saicmobility.evcard.md.act.mapper.act.BrandModelActivityMapper;
import com.saicmobility.evcard.md.act.service.IBrandModelActivityService;
import com.saicmobility.evcard.md.act.service.IBrandModelLogService;
import com.saicmobility.evcard.md.act.util.CityNameSpecialHandle;
import com.saicmobility.evcard.md.act.util.DateUtils;
import com.saicmobility.evcard.md.mdactservice.api.CurrentUser;
import com.saicmobility.evcard.md.mdactservice.api.*;
import com.saicmobility.evcard.md.mdstockservice.api.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 品牌车型配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Slf4j
@Service
public class BrandModelActivityServiceImpl extends ServiceImpl<BrandModelActivityMapper, BrandModelActivity> implements IBrandModelActivityService {

    @Autowired
    private IBrandModelLogService brandModelLogService;
    @Autowired
    private MdStockService mdStockService;

    @Override
    public SaveOrUpdateBrandModelActivityRes insertOrUpdate(SaveOrUpdateBrandModelActivityReq req) {
        try {
            BrandModelActivity brandModelActivity = new BrandModelActivity();
            Long id = req.getId();
            int operType = req.getOperType();
            Integer activityStatus;
            CurrentUser currentUser = req.getCurrentUser();
            BrandModelOperStateEnum operStateEnum = BrandModelOperStateEnum.OPER_INSERT;

            if (id == null || id == 0 ){
                //没有主键id，做新增操作
                brandModelActivity.setCreateOperId(currentUser.getUserId());
                brandModelActivity.setCreateOperName(currentUser.getUserName());
                if (BrandModelOperStateEnum.OPER_INSERT.getType() == operType){
                    //新增-保存操作，状态为待发布
                    activityStatus = BrandModelOperStateEnum.STATE_TOBE_RELEASED.getType();
                } else if (BrandModelOperStateEnum.OPER_ONLINE.getType() == operType) {
                    //新增-上线操作，状态为待上线、上线中
                    LocalDate nowDate = DateUtils.dateToLocalDate(new Date(), DateUtils.DATE_TYPE1);
                    LocalDate startDate = DateUtils.dateToLocalDate(req.getActivityStartDate(), DateUtils.DATE_TYPE1);
                    if (nowDate.isBefore(startDate)){
                        activityStatus = BrandModelOperStateEnum.STATE_TOBE_ONLINE.getType();
                    }else {
                        activityStatus = BrandModelOperStateEnum.STATE_ONLINEING.getType();
                    }
                }else {
                    activityStatus = BrandModelOperStateEnum.STATE_TOBE_RELEASED.getType();
                }
            }else {
                //存在主键id，做更新操作
                brandModelActivity.setId(id);
                activityStatus = req.getActivityStatus();

                operStateEnum = BrandModelOperStateEnum.OPER_UPDATE;
            }
            String activityCityStr = null;
            List<TopCityInfo> activityCityList = req.getActivityCitysList();
            if (CollectionUtil.isNotEmpty(activityCityList)){
                Gson gson = new Gson();
                activityCityStr = gson.toJson(TopCityInfoDto.listToDto(activityCityList));
            }
            LocalDateTime startDate = DateUtils.getDateFromStr(req.getActivityStartDate(), DateUtils.DATE_TYPE1);
            LocalDateTime endDate = DateUtils.getDateFromStr(req.getActivityEndDate(), DateUtils.DATE_TYPE1);
            brandModelActivity.setActivityStatus(activityStatus);
            brandModelActivity.setUpdateOperId(currentUser.getUserId());
            brandModelActivity.setUpdateOperName(currentUser.getUserName());
            brandModelActivity.setBrandModelName(req.getBrandModelName());
            brandModelActivity.setSubtitleContent(req.getSubtitleContent());
            brandModelActivity.setListpageSlogan(req.getListpageSlogan());
            brandModelActivity.setActivityCitys(activityCityStr);
            brandModelActivity.setActivityStartDate(startDate);
            brandModelActivity.setActivityEndDate(endDate);
            brandModelActivity.setHomePagePicUrl(req.getHomePagePicUrl());
            brandModelActivity.setListPagePicUrl(req.getListPagePicUrl());
            brandModelActivity.setDetailPagePicUrl(req.getDetailPagePicUrl());
            brandModelActivity.setRelatedOrdersFlag(req.getRelatedOrdersFlag());
            brandModelActivity.setRelatedModelIds(req.getRelatedModelIds());
            boolean saveOrUpdateFlag = saveOrUpdate(brandModelActivity);
            //记录日志
            if (saveOrUpdateFlag){
                brandModelLogService.saveOperateLog(brandModelActivity.getId(), operStateEnum,operStateEnum.getMsg(),currentUser);
                return SaveOrUpdateBrandModelActivityRes.ok();
            }else {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "保存失败");

            }
        }catch (BusinessException e){
            return SaveOrUpdateBrandModelActivityRes.failed(e.getCode(),e.getMessage());
        }
    }

    @Override
    public UpdateBrandModelActivityStatusRes updateBrandModelActivityStatus(UpdateBrandModelActivityStatusReq req) {
        try{
            CurrentUser currentUser = req.getCurrentUser();
            long id = req.getId();
            int operType = req.getOperType();
            BrandModelOperStateEnum activityStatusEnum = null;

            BrandModelOperStateEnum newEnum = BrandModelOperStateEnum.getEnumByType(1,operType);
            if (id < 0 ){
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "活动id不能为空");
            }
            if (newEnum == null){
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "未查询到合适操作");
            }
            BrandModelActivity brandModelActivity = this.getById(id);
            if (ObjectUtils.isEmpty(brandModelActivity)){
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "未查询到对应的活动信息");
            }
            Integer oldState = brandModelActivity.getActivityStatus();
            BrandModelOperStateEnum oldStateEnum = BrandModelOperStateEnum.getEnumByType(2,oldState);

            //操作判断
            if (BrandModelOperStateEnum.STATE_TOBE_RELEASED.getType() == oldState){
                //待发布
                if (BrandModelOperStateEnum.OPER_ONLINE.getType() == operType){
                    //上线操作，状态为待上线、上线中
                    LocalDateTime nowDate = DateUtils.getSystemDate();
                    LocalDateTime startDate = brandModelActivity.getActivityStartDate();
                    if (nowDate.isBefore(startDate)){
                        activityStatusEnum = BrandModelOperStateEnum.STATE_TOBE_ONLINE;
                    } else {
                        activityStatusEnum = BrandModelOperStateEnum.STATE_ONLINEING;
                    }
                } else if (BrandModelOperStateEnum.OPER_OFFLINE.getType() == operType) {
                    //下线操作
                    activityStatusEnum = BrandModelOperStateEnum.STATE_OFFLINED;
                }
            } else if (BrandModelOperStateEnum.STATE_TOBE_ONLINE.getType() == oldState) {
                //待上线
                if (BrandModelOperStateEnum.OPER_PAUSE.getType() == operType) {
                    //暂停操作
                    activityStatusEnum = BrandModelOperStateEnum.STATE_PAUSEING;
                }else if (BrandModelOperStateEnum.OPER_OFFLINE.getType() == operType) {
                    //下线操作
                    activityStatusEnum = BrandModelOperStateEnum.STATE_OFFLINED;
                }
            } else if (BrandModelOperStateEnum.STATE_ONLINEING.getType() == oldState) {
                //上线中
                if (BrandModelOperStateEnum.OPER_PAUSE.getType() == operType) {
                    //暂停操作
                    activityStatusEnum = BrandModelOperStateEnum.STATE_PAUSEING;
                }else if (BrandModelOperStateEnum.OPER_OFFLINE.getType() == operType) {
                    //下线操作
                    activityStatusEnum = BrandModelOperStateEnum.STATE_OFFLINED;
                }
            }else if (BrandModelOperStateEnum.STATE_PAUSEING.getType() == oldState) {
                //暂停中
                if (BrandModelOperStateEnum.OPER_ONLINE.getType() == operType) {
                    //上线操作
                    activityStatusEnum = BrandModelOperStateEnum.STATE_ONLINEING;
                }else if (BrandModelOperStateEnum.OPER_OFFLINE.getType() == operType) {
                    //下线操作
                    activityStatusEnum = BrandModelOperStateEnum.STATE_OFFLINED;
                }else if (BrandModelOperStateEnum.CANCEL_PAUSE.getType() == operType) {
                    //取消暂停操作
                    LocalDateTime nowDate = DateUtils.getSystemDate();
                    LocalDateTime startDate = brandModelActivity.getActivityStartDate();
                    if (nowDate.isBefore(startDate)){
                        activityStatusEnum = BrandModelOperStateEnum.STATE_TOBE_ONLINE;
                    } else {
                        activityStatusEnum = BrandModelOperStateEnum.STATE_ONLINEING;
                    }
                }
            }else if (BrandModelOperStateEnum.STATE_OFFLINED.getType() == oldState) {
                //已下线

            }
            if (activityStatusEnum == null){
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "未匹配合适的操作");
            }
            brandModelActivity.setActivityStatus(activityStatusEnum.getType());
            boolean saveOrUpdateFlag = saveOrUpdate(brandModelActivity);
            if (saveOrUpdateFlag){
                //日志记录
                brandModelLogService.saveOperateLog(id, newEnum,"原状态为："+oldStateEnum.getMsg()+" -> 现状态为："+activityStatusEnum.getMsg(),currentUser);
                return UpdateBrandModelActivityStatusRes.ok();
            }else {
                return UpdateBrandModelActivityStatusRes.failed(ErrorEnum.REQUEST_ERROR.getCode(), "未成功更新品牌车型");
            }
        }catch (BusinessException e){
            return UpdateBrandModelActivityStatusRes.failed(e.getCode(),e.getMessage());
        }
    }

    @Override
    public QueryBrandModelActivityListRes queryBrandModelActivityList(QueryBrandModelActivityListReq req) {
        try {
            QueryBrandModelActivityListRes.Builder res = QueryBrandModelActivityListRes.newBuilder();
            List<BrandModelActivityInfo> list=new ArrayList<>();
            long id = req.getId();
            String activityCitysStr = req.getActivityCitys();
            String activityStatusesStr = req.getActivityStatuses();
            String brandModelName = req.getBrandModelName();
            String activityStartDateStr = req.getActivityStartDate();
            String activityEndDateStr = req.getActivityEndDate();
            int effectiveCityFlag = req.getEffectiveCityFlag();

            Page<BrandModelActivity> page = new Page<>(req.getPageNum(), req.getPageSize(),true);
            LambdaQueryWrapper<BrandModelActivity> queryWrapper = new LambdaQueryWrapper<BrandModelActivity>()
                    .eq(BrandModelActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType());
            if (id > 0){
                queryWrapper.like(BrandModelActivity::getId,"%"+id+"%");
            }
            if (StringUtils.isNotBlank(activityCitysStr)){
                //查询城市
                String[] activityCitys = activityCitysStr.split(";");
                queryWrapper.and(o -> {
                    o.like(BrandModelActivity::getActivityCitys, "%\"cityId\":-1,%");
                    for (String activityCity : activityCitys) {
                        o.or(o1 -> o1.like(BrandModelActivity::getActivityCitys, "%\"cityId\":" + activityCity + ",%"));
                    }
                });

            }
            if (StringUtils.isNotBlank(activityStatusesStr)){
                //查询状态
                String[] activityStatuses = activityStatusesStr.split(";");
                List<String> activityStatusList = Arrays.asList(activityStatuses);
                queryWrapper.in(BrandModelActivity::getActivityStatus,activityStatusList);
            }
            if (StringUtils.isNotBlank(brandModelName)){
                //品牌车型名称
                queryWrapper.like(BrandModelActivity::getBrandModelName,"%"+brandModelName+"%");
            }

            if (StringUtils.isNotBlank(activityStartDateStr)){
                //ge >=
                //le <=
                queryWrapper.le(BrandModelActivity::getActivityStartDate,activityStartDateStr);

            }
            if (StringUtils.isNotBlank(activityEndDateStr)){
                queryWrapper.ge(BrandModelActivity::getActivityEndDate,activityEndDateStr);
            }
            queryWrapper.orderByAsc(BrandModelActivity::getActivityStatus).orderByDesc(BrandModelActivity::getActivityStartDate).orderByDesc(BrandModelActivity::getCreateTime);

            Page<BrandModelActivity> result = this.page(page, queryWrapper);
            if (CollectionUtil.isEmpty(result.getRecords())) {
                return res.build();
            }else {
                List<BrandModelActivity> records = result.getRecords();
                List<Long> relatedModelIdList = records.stream()
                        .map(BrandModelActivity::getRelatedModelIds) // 提取 codes 字段
                        .filter(relatedModelId -> StringUtils.isNotBlank(relatedModelId)) // 过滤掉 null 和空字符串
                        .flatMap(relatedModelId -> Arrays.stream(relatedModelId.split(";"))) // 拆分并扁平化
                        .map(Long::parseLong)
                        .distinct()
                        .collect(Collectors.toList());
                //调用门店查询批量接口
                Map<Long, List<CityInfo>> cityInfoMap = new HashMap<>();
                Map<Integer, Set<Long>> cityVehicleModelMap = new HashMap<>();
                if (effectiveCityFlag > 0){
                    GetCityListByStoreVehicleModelIdRes storeVehicleModelIdRes = mdStockService.getCityListByStoreVehicleModelId(GetCityListByStoreVehicleModelIdReq.newBuilder().addAllStoreVehicleModelId(relatedModelIdList).build());
                    if (storeVehicleModelIdRes.getRetCode()== 0) {
                        List<CityByStoreVehicleModel> cityByStoreVehicleModelList = storeVehicleModelIdRes.getListList();
                        cityInfoMap = cityByStoreVehicleModelList.stream().collect(
                                Collectors.toMap(CityByStoreVehicleModel::getStoreVehicleModelId, CityByStoreVehicleModel::getCityListList));
                        cityVehicleModelMap = buildCityVehicleModelMap(cityByStoreVehicleModelList);
                    }
                }

                for (BrandModelActivity record : records) {
                    //todo 校验门店车型对应城市与配置的活动城市集合
                    BrandModelActivityInfo resDto = BrandModelActivity.toRes(record);
                    //查询有效跳转城市
                    List<TopCityInfo> effectiveCitys = new ArrayList<>();
                    List<TopCityInfo> activityCitysList = resDto.getActivityCitysList();
                    String relatedModelIds = resDto.getRelatedModelIds();
                    if (effectiveCityFlag > 0 && resDto.getRelatedOrdersFlag() == 1 && StringUtils.isNotBlank(relatedModelIds)){
                        //查询有效跳转城市标志位为1，且 关联订单下单才会判断
                        String[] relatedModelIdsArray = relatedModelIds.split(";");
                        Set<Long> recordRelatedModelIds = Arrays.stream(relatedModelIdsArray).map(Long::parseLong).collect(Collectors.toSet());
                        Set<Integer> cityIds = new HashSet<>();
                        for (String relatedModelId : relatedModelIdsArray) {
                            List<CityInfo> cityInfoList = cityInfoMap.get(Long.valueOf(relatedModelId));
                            if (CollectionUtil.isNotEmpty(cityInfoList)){
                                for (CityInfo cityInfo : cityInfoList) {
                                    for (TopCityInfo activityCity : activityCitysList) {
                                        if (cityInfo.getCityId() == activityCity.getCityId() || activityCity.getCityId() == -1){
                                            if (!cityIds.contains(cityInfo.getCityId())) {
                                                TopCityInfo topCityInfo = TopCityInfoDto.cityInfoToRes(cityInfo, cityVehicleModelMap, recordRelatedModelIds);
                                                effectiveCitys.add(topCityInfo);
                                                cityIds.add(cityInfo.getCityId());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    resDto = resDto.toBuilder()
                            .addAllEffectiveCitys(effectiveCitys)
                            .build();
                    list.add(resDto);
                }
                return res.addAllBrandModelActivityInfos(list)
                        .setTotal(result.getTotal())
                        .setSize(result.getSize())

                        .build();
            }
        }catch (Exception e){
            return QueryBrandModelActivityListRes.failed(ErrorEnum.REQUEST_ERROR.getCode(), "查询异常");
        }
    }

    @Override
    public QueryBrandModelActivityDetailRes queryBrandModelActivityDetail(QueryBrandModelActivityDetailReq req) {
        try {
            QueryBrandModelActivityDetailRes.Builder builder = QueryBrandModelActivityDetailRes.newBuilder();
            long id = req.getId();
            if (id > 0 ){
                LambdaQueryWrapper<BrandModelActivity> queryWrapper = new LambdaQueryWrapper<BrandModelActivity>()
                        .eq(BrandModelActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                        .eq(BrandModelActivity::getId,id);
                BrandModelActivity brandModelActivity = this.getById(id);
                if (brandModelActivity != null){
                    builder.setBrandModelActivityInfo(BrandModelActivity.toRes(brandModelActivity));
                }
            }

            return builder.build();
        }catch (Exception e){
            return QueryBrandModelActivityDetailRes.failed(ErrorEnum.REQUEST_ERROR.getCode(), "查询异常");
        }
    }


    /**
     * 转为<城市，门店车型Id列表> 结构
     * @param cityByStoreVehicleModelList
     * @return
     */
    private Map<Integer, Set<Long>> buildCityVehicleModelMap(List<CityByStoreVehicleModel> cityByStoreVehicleModelList) {
        return cityByStoreVehicleModelList.stream()
                .flatMap(cityByStoreVehicleModel -> cityByStoreVehicleModel.getCityListList()
                        .stream()
                        .map(cityInfo -> new AbstractMap.SimpleEntry<>(cityInfo.getCityId(), cityByStoreVehicleModel.getStoreVehicleModelId()))
                )
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toSet())
                ));
    }

}
