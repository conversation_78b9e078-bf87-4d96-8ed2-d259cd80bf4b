package com.saicmobility.evcard.md.act.dto.proprietary;

import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.mdactservice.api.QueryProprietaryActivityListReq;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/1/4 14:41
 */
@Data
@Slf4j
public class QueryProprietaryActivityListDto {
    private String activityName; //活动名称
    private String activityTag; //活动标签
    private int activityStatus;//活动状态：1-未开始、2-生效中、3-已过期、4-已作废
    private int activityType; //活动类型：1-满减、2-打折
    private int pricingType;//定价类型：1-灵活定价、2-规范定价
    private LocalDate signUpStartDate; //报名开始时间 yyyy-MM-dd
    private LocalDate signUpEndDate; //报名结束时间 yyyy-MM-dd

    private int pageNum; // 页码
    private int pageSize; // 每页大小

    /**
     * req->dto 入参为空校验
     * 日期范围校验
     * @param req
     * @return
     */
    public QueryProprietaryActivityListDto parse(QueryProprietaryActivityListReq req) throws BusinessException{
        QueryProprietaryActivityListDto dto  = new QueryProprietaryActivityListDto();

        dto.setActivityName(req.getActivityName());
        dto.setActivityTag(req.getActivityTag());
        dto.setActivityStatus(req.getActivityStatus());
        dto.setActivityType(req.getActivityType());
        dto.setPricingType(req.getPricingType());

        //报名开始时间、报名结束时间
        if(!StringUtils.isEmpty(req.getSignUpStartDate()) && !StringUtils.isEmpty(req.getSignUpEndDate())){
            LocalDate signUpStartDate = DateUtil.getLocalDateFromStr(req.getSignUpStartDate(), DateUtil.DATE_TYPE5);
            LocalDate signUpEndDate = DateUtil.getLocalDateFromStr(req.getSignUpEndDate(), DateUtil.DATE_TYPE5);
            if (signUpStartDate.compareTo(signUpEndDate) > 0) {
                log.error("报名结束时间小于报名开始时间, 请检查, signUpStartDate= {}, signUpEndDate = {}", req.getSignUpStartDate(), req.getSignUpEndDate());
                throw new BusinessException(ErrorEnum.DATE_ERROR.getCode(), ErrorEnum.DATE_ERROR.getMsg());
            }
            dto.setSignUpStartDate(signUpStartDate);
            dto.setSignUpEndDate(signUpEndDate);
        }
        if (!StringUtils.isEmpty(req.getSignUpStartDate())) {
            LocalDate signUpStartDate = DateUtil.getLocalDateFromStr(req.getSignUpStartDate(), DateUtil.DATE_TYPE5);
            dto.setSignUpStartDate(signUpStartDate);
        }
        if (!StringUtils.isEmpty(req.getSignUpEndDate())) {
            LocalDate signUpEndDate = DateUtil.getLocalDateFromStr(req.getSignUpEndDate(), DateUtil.DATE_TYPE5);
            dto.setSignUpEndDate(signUpEndDate);
        }
        if (req.getPageNum() <= 0) {
            log.error("当前页码缺失");
            throw new BusinessException(ErrorEnum.PAGENUM_ERROR.getCode(), ErrorEnum.PAGENUM_ERROR.getMsg());
        }
        dto.setPageNum(req.getPageNum());
        if (req.getPageSize() <= 0) {
            log.error("每页显示条数缺失");
            throw new BusinessException(ErrorEnum.PAGESIZE_ERROR.getCode(), ErrorEnum.PAGESIZE_ERROR.getMsg());
        }
        dto.setPageSize(req.getPageSize());

        return dto;
    }
}
