package com.saicmobility.evcard.md.act.domain.common;

import java.io.Serializable;
import java.util.List;

public class PageResult <T> implements Serializable {
    private static final long serialVersionUID = 4910211148065786702L;
    private List<T> datas;
    private String result;
    private Long total;
    private Long pages;
    private Long pageSize;
    private Long currentPage;

    public PageResult() {
    }

    public List<T> getDatas() {
        return this.datas;
    }

    public void setDatas(List<T> datas) {
        this.datas = datas;
    }

    public String getResult() {
        return this.result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Long getTotal() {
        return this.total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Long getPages() {
        return this.pages;
    }

    public void setPages(Long pages) {
        this.pages = pages;
    }

    public Long getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(Long pageSize) {
        this.pageSize = pageSize;
    }

    public Long getCurrentPage() {
        return this.currentPage;
    }

    public void setCurrentPage(Long currentPage) {
        this.currentPage = currentPage;
    }
}

