package com.saicmobility.evcard.md.act.dto.coupon.channel.ccb;

import com.saicmobility.evcard.md.act.dto.coupon.channel.CouponModelDto;
import com.saicmobility.evcard.md.act.dto.coupon.channel.ReconciliationFileDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CCBReconciliationFileDto {
    //产品编码
    private String productId;
    //券码编号
    private String couponCode;
    //当前状态
    private String operation;
    //优惠券核销时的使用订单号，最后一次使用/退单的用券订单号
    private String useOrderId;
    //行外核销门店ID
    private String storeId_2;
    //优惠券状态最后一次变动的时间 yyyyMMddhh24miss
    private String useTm;
    //累计已使用金额，operation为011032、011033时该字段必填 非必填
    private String acmUsedAmt;
    //剩余未使用金额，operation为011032、011033时该字段必填
    private String unUsedAmt;
}
