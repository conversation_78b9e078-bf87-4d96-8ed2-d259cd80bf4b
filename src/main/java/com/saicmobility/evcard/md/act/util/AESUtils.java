package com.saicmobility.evcard.md.act.util;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

@Slf4j
public class AESUtils {
    private static final String ENCODING = "utf-8";
    private static final String KEY_ALGORITHM = "AES";

    /**
     * AES 解密操作
     *
     * @param content
     * @param key base64编码后
     * @return
     */
    public static String ecbDecrypt(String content, String key) {
        try {
            byte[] contentBytes = Base64.decodeBase64(content);
            byte[] keyBytes = Base64.decodeBase64(key);
            byte[] data = ecbDecrypt(contentBytes, keyBytes);
            return new String(data, ENCODING);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static byte[] ecbDecrypt(byte[] contentBytes, byte[] key) {
        byte[] data = null;
        try {
            data = encryptOrDecrypt(Cipher.DECRYPT_MODE, contentBytes, key, null, EncodeType.AES_ECB_PKCS5Padding);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return data;
    }

    /**
     * AES 加密操作
     *
     * @param content 待加密内容
     * @param key     加密密码
     * @return 返回Base64转码后的加密数据
     */
    public static String ecbEncrypt(String content, String key) {
        byte[] data = null;
        try {
            byte[] contentBytes = content.getBytes(ENCODING);
            byte[] keyBytes = Base64.decodeBase64(key);
            data = ecbEncrypt(contentBytes, keyBytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return data == null ? null : Base64.encodeBase64String(data);
    }


    /**
     * AES 加密操作
     *
     * @param contentBytes 待加密内容
     * @param key     加密密码
     * @return 返回Base64转码后的加密数据
     */
    public static byte[] ecbEncrypt(byte[] contentBytes, byte[] key) {
        byte[] data = null;
        try {
            data = encryptOrDecrypt(Cipher.ENCRYPT_MODE, contentBytes, key, null, EncodeType.AES_ECB_PKCS5Padding);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return data;
    }


    /**
     * 根据时间戳13位，生成aes cbc 加密向量 vi
     * 生成规则先把时间戳反向，用零补足16位
     *
     * @param timestamp
     * @return
     */
    public static String genTimestampIV(String timestamp) {
        return StringUtils.reverse(timestamp) + "000";
    }

    private static byte[] encryptOrDecrypt(int mode, byte[] contentBytes, byte[] keyBytes, String iv, String modeAndPadding) throws InvalidKeyException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, NoSuchPaddingException, BadPaddingException, IllegalBlockSizeException, UnsupportedEncodingException {
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, KEY_ALGORITHM);
        Cipher cipher = Cipher.getInstance(modeAndPadding);// 创建密码器
        if (null != iv) {
            //指定一个初始化向量 (Initialization vector，IV)， IV 必须是16位
            byte[] ivBytes = iv.getBytes(ENCODING);
            cipher.init(mode, keySpec, new IvParameterSpec(ivBytes));
        } else {
            cipher.init(mode, keySpec);
        }
        return cipher.doFinal(contentBytes);
    }

    public class EncodeType {
        //    算法/模式/填充                 16字节加密后数据长度       不满16字节加密后长度
        //    AES/CBC/NoPadding                   16                          不支持
        //    AES/CBC/PKCS5Padding                32                          16
        //    AES/CBC/ISO10126Padding             32                          16
        //    AES/CFB/NoPadding                   16                          原始数据长度
        //    AES/CFB/PKCS5Padding                32                          16
        //    AES/CFB/ISO10126Padding             32                          16
        //    AES/ECB/NoPadding                   16                          不支持
        //    AES/ECB/PKCS5Padding                32                          16
        //    AES/ECB/ISO10126Padding             32                          16
        //    AES/OFB/NoPadding                   16                          原始数据长度
        //    AES/OFB/PKCS5Padding                32                          16
        //    AES/OFB/ISO10126Padding             32                          16
        //    AES/PCBC/NoPadding                  16                          不支持
        //    AES/PCBC/PKCS5Padding               32                          16
        //    AES/PCBC/ISO10126Padding            32                          16
        //    默认为 ECB/PKCS5Padding
        public final static String AES_DEFAULT = "AES";
        public final static String AES_CBC_NoPadding = "AES/CBC/NoPadding";
        public final static String AES_CBC_PKCS5Padding = "AES/CBC/PKCS5Padding";
        public final static String AES_CBC_ISO10126Padding = "AES/CBC/ISO10126Padding";
        public final static String AES_CFB_NoPadding = "AES/CFB/NoPadding";
        public final static String AES_CFB_PKCS5Padding = "AES/CFB/PKCS5Padding";
        public final static String AES_CFB_ISO10126Padding = "AES/CFB/ISO10126Padding";
        public final static String AES_ECB_NoPadding = "AES/ECB/NoPadding";
        public final static String AES_ECB_PKCS5Padding = "AES/ECB/PKCS5Padding";
        public final static String AES_ECB_ISO10126Padding = "AES/ECB/ISO10126Padding";
        public final static String AES_OFB_NoPadding = "AES/OFB/NoPadding";
        public final static String AES_OFB_PKCS5Padding = "AES/OFB/PKCS5Padding";
        public final static String AES_OFB_ISO10126Padding = "AES/OFB/ISO10126Padding";
        public final static String AES_PCBC_NoPadding = "AES/PCBC/NoPadding";
        public final static String AES_PCBC_PKCS5Padding = "AES/PCBC/PKCS5Padding";
        public final static String AES_PCBC_ISO10126Padding = "AES/PCBC/ISO10126Padding";
    }
}