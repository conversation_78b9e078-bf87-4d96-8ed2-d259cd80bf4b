package com.saicmobility.evcard.md.act.service.rest;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.service.rest.entity.activity.OfferThirdCouponRequest;
import com.saicmobility.evcard.md.act.service.rest.entity.activity.OfferThirdCouponsResponse;
import com.saicmobility.evcard.md.act.service.rest.entity.coupon.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class CouponRestClient extends AbstractRestClient {

    public OrderCouponsResponse orderCoupons(OrderCouponsRequest request) throws BusinessException {
        return postForObject(mdRestApiConfig.getOrderCoupons(), request, OrderCouponsResponse.class);
    }

    public BatchOrderCouponsResponse batchOrderCoupons(BatchOrderCouponsRequest request) throws BusinessException {
        return postForObjectNoLog(mdRestApiConfig.getBatchOrderCoupons(), request, BatchOrderCouponsResponse.class);
    }

    public CheckOrderCouponResponse checkOrderCoupon(CheckOrderCouponRequest request) throws BusinessException {
        return postForObject(mdRestApiConfig.getCheckOrderCoupon(), request, CheckOrderCouponResponse.class);
    }

    public GetCouponModelViewResponse getCouponModelView(Integer couponSeq) throws BusinessException {
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.add("Content-Type", "application/json");
        HttpEntity<Integer> requestEntity = new HttpEntity<Integer>(couponSeq, requestHeaders);
        return postForObject(mdRestApiConfig.getGetCouponModelView(), requestEntity, GetCouponModelViewResponse.class);
    }

    public GetCouponModelListViewResponse getCouponModelListView(List<Integer> couponSeqList) throws BusinessException {
        return postForObject(mdRestApiConfig.getGetCouponModelListView(), couponSeqList, GetCouponModelListViewResponse.class);
    }

    public UseCouponResponse useCoupons(UseCouponRequest request) throws BusinessException {
        return postForObject(mdRestApiConfig.getUseCoupon(), request, UseCouponResponse.class);
    }

    public OfferThirdCouponsResponse offerThirdCoupons(OfferThirdCouponRequest request) throws BusinessException {
        return postForObject(mdRestApiConfig.getOfferThirdCoupons(), request, OfferThirdCouponsResponse.class);
    }

    public GetCouponViewResponse getCouponView(GetCouponViewRequest request) throws BusinessException {
        return postForObject(mdRestApiConfig.getGetCouponDes(), request, GetCouponViewResponse.class);
    }

    public ExchangeResponse exchange(ExchangeRequest request) throws BusinessException {
        return postForObject(mdRestApiConfig.getExchange(), request, ExchangeResponse.class);
    }

}
