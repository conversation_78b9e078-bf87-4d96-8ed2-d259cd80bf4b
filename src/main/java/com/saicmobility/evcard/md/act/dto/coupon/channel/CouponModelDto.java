package com.saicmobility.evcard.md.act.dto.coupon.channel;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CouponModelDto {

    /************************* 来自mmp_third_coupon的数据 *************************/

    /**
     * 优惠券模板ID
     */
    private Long id;

    /**
     * 活动送券配置ID对应
     */
    private Long thirdActivityId;

    /**
     * 优惠券模板编号
     */
    private Long couponSeq;

    /**
     * 优惠券发放张数
     */
    private Integer offerQuantity;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 优惠券到账几天有效
     */
    private Integer effectiveDays;

    /**
     * 优惠券有效时长，优惠券的有效期为发券时间 + 有效时长。当VALID_TIME_TYPE字段值为1时，该字段值为0
     */
    private Integer validDays;

    /**
     * 优惠券有效期开始时间 YYYY-MM-DD
     */
    private String startDate;

    /**
     * 优惠券有效结束时间  YYYY-MM-DD
     */
    private String expiresDate;

    /**
     * 优惠券有效期类型（1起止时间 2时长）
     */
    private Integer validTimeType;

    /**
     * 发放机构ID
     */
    private String orgId;

    /**
     * 优惠券类型 1:直扣 2：折扣 3:非收入券 4:购买类
     */
    private Integer couponType;

    /**
     * 优惠券限制（0-全国券 1-地域券）
     */
    private Integer couponLimit;

    /**
     * 发券目标对象，0缺省 1仅老用户 2仅新用户
     */
    private Integer couponTarget;

    /**
     * 发券时机，0缺省， 对于邀新活动 1注册 2审核通过 3订单完成
     */
    private Integer offerTiming;

    /************************* 来自mmp_pack_night_activity的数据 *************************/

    /**
     *活动名称
     */
    private String activityName;

    /**
     *活动状态(0:待发布 1:待上线 2:进行中 3:已停止 4:暂停中)
     */
    private Long activityStatus;

    /**
     *活动开始日期
     */
    private String activityStartDate;

    /**
     *活动结束日期
     */
    private String activityEndDate;

    /**
     *活动开始时间
     */
    private String activityStartTime;

    /**
     *活动结束时间
     */
    private String activityEndTime;

    /**
     *备注
     */
    private String remark;

    /************************* 来自coupon_def的数据 *************************/

    /**
     * 优惠券面值（如果是打折券 该值表示最大抵扣值）
     */
    private Double couponValue;


    /************************* 来自mmp_third_activity的数据 *************************/

    /**
     * 兑换码有效期结束时间
     */
    private LocalDateTime cdkExpiresTime;
}
