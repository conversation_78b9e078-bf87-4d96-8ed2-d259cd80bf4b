package com.saicmobility.evcard.md.act.service.suixiangcard;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saicmobility.evcard.md.act.entity.iss.SuixiangCardBase;
import com.saicmobility.evcard.md.act.entity.iss.SuixiangCardPrice;
import com.saicmobility.evcard.md.act.entity.iss.SuixiangCardRentDays;
import com.saicmobility.evcard.md.act.mapper.iss.SuixiangCardBaseMapper;
import com.saicmobility.evcard.md.act.mapper.iss.SuixiangCardPriceMapper;
import com.saicmobility.evcard.md.act.mapper.iss.SuixiangCardRentDaysMapper;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.mdactservice.api.GetSuiXiangCardBaseInfoByRentDayIdRes;
import com.saicmobility.evcard.md.mdactservice.api.SuixiangCardBaseInfo;
import com.saicmobility.evcard.md.mdactservice.api.SuixiangCardPriceInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 随享卡基础表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Service
public class SuixiangCardBaseServiceImpl extends ServiceImpl<SuixiangCardBaseMapper, SuixiangCardBase> implements ISuixiangCardBaseService {
    @Autowired
    private SuixiangCardRentDaysMapper suixiangCardRentDaysMapper;
    @Autowired
    private SuixiangCardBaseMapper suixiangCardBaseMapper;
    @Autowired
    private SuixiangCardPriceMapper suixiangCardPriceMapper;


    @Override
    public GetSuiXiangCardBaseInfoByRentDayIdRes getSuixiangCardBaseByRentDayId(Long rentDayId){
        if (rentDayId == null){
            return GetSuiXiangCardBaseInfoByRentDayIdRes.ok();
        }
        SuixiangCardRentDays suixiangCardRentDays = suixiangCardRentDaysMapper.selectByPrimaryKey(rentDayId);
        if (suixiangCardRentDays != null){
            Long cardBaseId = suixiangCardRentDays.getCardBaseId();
            //获取基本信息
            SuixiangCardBase suixiangCardBase = suixiangCardBaseMapper.selectByPrimaryKey(cardBaseId);
            SuixiangCardBaseInfo suixiangCardBaseInfo = SuixiangCardBaseInfo.newBuilder()
                    .setCardBaseId(cardBaseId)
                    .setCardName(suixiangCardBase.getCardName())
                    .setOrgId(suixiangCardBase.getOrgId())
                    .setCityId(suixiangCardBase.getCityId())
                    .setAdvanceNoticeTime(suixiangCardBase.getAdvanceNoticeTime() == null ? null : DateUtil.dateToString(suixiangCardBase.getAdvanceNoticeTime(), DateUtil.DATE_TYPE1))
                    .setSaleStartTime(suixiangCardBase.getSaleStartTime() == null ? null : DateUtil.dateToString(suixiangCardBase.getSaleStartTime(), DateUtil.DATE_TYPE1))
                    .setSaleEndTime(suixiangCardBase.getSaleEndTime() == null ? null : DateUtil.dateToString(suixiangCardBase.getSaleEndTime(), DateUtil.DATE_TYPE1))
                    .setEffectiveDays(suixiangCardBase.getEffectiveDays())
                    .setValidDaysType(suixiangCardBase.getValidDaysType())
                    .setInitStock(suixiangCardBase.getInitStock())
                    .setStock(suixiangCardBase.getStock())
                    .setSales(suixiangCardBase.getSales())
                    .setDisplayFlag(suixiangCardBase.getDisplayFlag())
                    .setSingleOrderDuration(suixiangCardBase.getSingleOrderDuration().toPlainString())
                    .setStyleType(suixiangCardBase.getStyleType())
                    .setBackUrl(suixiangCardBase.getBackUrl())
                    .setRules(suixiangCardBase.getRules())
                    .setHolidayAvailable(suixiangCardBase.getHolidayAvailable())
                    .setUnavailableDate(suixiangCardBase.getUnavailableDate())
                    .setPurchaseLimitNum(suixiangCardBase.getPurchaseLimitNum())
                    .setMergeFlag(suixiangCardBase.getMergeFlag())
                    .setLandingPageFlag(suixiangCardBase.getLandingPageFlag())
                    .setVehicleBrandIds(suixiangCardBase.getVehicleBrandIds())
                    .setCardStatus(suixiangCardBase.getCardStatus())
                    .setIsDeleted(suixiangCardBase.getIsDeleted())
                    .build();

            //获取价格信息
            List<SuixiangCardPrice> suixiangCardPrices = suixiangCardPriceMapper.selectInfoByBaseId(cardBaseId);
            List<SuixiangCardPriceInfo> suixiangCardPriceInfos = SuixiangCardPrice.listRoRes(suixiangCardPrices);
            return GetSuiXiangCardBaseInfoByRentDayIdRes.newBuilder()
                    .setRentDayId(rentDayId)
                    .setRentDays(suixiangCardRentDays.getRentDays())
                    .setServiceFees(suixiangCardRentDays.getServiceFees())
                    .setTotalServiceFeesAmout(suixiangCardRentDays.getTotalServiceFeesAmout().toPlainString())
                    .setSuixiangCardBaseInfo(suixiangCardBaseInfo)
                    .addAllSuixiangCardPriceInfo(suixiangCardPriceInfos)
                    .build();
        }
        return GetSuiXiangCardBaseInfoByRentDayIdRes.ok();
    }
}
