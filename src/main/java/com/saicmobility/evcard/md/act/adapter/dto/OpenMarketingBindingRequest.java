package com.saicmobility.evcard.md.act.adapter.dto;

import lombok.Data;

import java.util.List;

@Data
public class OpenMarketingBindingRequest {
    /**
     * [直连商家]营销信息id
     */
    private String marketId;
    /**
     * 门店列表  第一次同步的时候可以不传，后续增量以及更新必传
     * 如果选了该门店下所有车型，那isAll字段就是1，VehicleModelRequest中marketStoreId就是对应的门店id，vehicleModelId为0。
     * 如果该门店下没有车型也可以这么传，后续即使新增车型也没问题
     * 如果选了该门店下部分车型，那isAll字段就是0，VehicleModelRequest中marketStoreId就是对应的门店id，vehicleModelId对应的车型id
     */
    private List<MarketStoreRequest> marketStoreList;
    /**
     * 车型id列表  第一次同步的时候可以不传，后续增量以及更新必传
     */
    private List<VehicleModelRequest> vehicleModelList;


}
