package com.saicmobility.evcard.md.act.domain.coupon;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 优惠券交易记录
 */
@Data
public class SynchronousCouponAccountDto implements Serializable {
    private static final long serialVersionUID = 3842480934636318099L;

    /** 个人会员查询传入authid,企业账户传入agencyId (必填) */
    private String userId;
    /** 用户类型 1 个人 2 企业 */
    private Integer userType;
    /** 操作类型 1 发放 2 使用 3 过期  4 系统作废 5 退款作废 6 兑换 (必填)*/
    private Integer operateType;
    /** 优惠券编号 (必填) */
    private Long userCouponSeq;
    /** 操作人 (必填) */
    private String operator;
    /** 操作时间 */
    private Date operateDateTime;
    /** 使用备注和描述 */
    private String desc;



    public SynchronousCouponAccountDto() {

    }

    public SynchronousCouponAccountDto(String userId, Integer operateType, Long userCouponSeq, String operator,Integer userType) {
        this.userId = userId;
        this.operateType = operateType;
        this.userCouponSeq = userCouponSeq;
        this.operator = operator;
        this.userType = userType;
    }

}
