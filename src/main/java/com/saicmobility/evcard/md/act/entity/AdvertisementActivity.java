package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_advertisement_activity")
@ApiModel(value="AdvertisementActivity对象", description="")
public class AdvertisementActivity extends Model<AdvertisementActivity> {

    @ApiModelProperty(value = "主键")
    @TableId( value = "id",type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "渠道唯一标识")
    private String source;

    @ApiModelProperty(value = "应用唯一标识")
    private String appid;

    @ApiModelProperty(value = "设备类型（1=IOS、2=Android)")
    private Integer devicePlatform;

    @ApiModelProperty(value = "设备标识符类型（idfa、imei等）")
    private Integer deviceType;

    @ApiModelProperty(value = "设备标识符")
    private String deviceCode;

    @ApiModelProperty(value = "回调地址")
    private String callbackUrl;

    @ApiModelProperty(value = "回调结果（0=未回调成功，1=回调成功）")
    private Integer callbackResult;

    @ApiModelProperty(value = "回调成功时间")
    private LocalDateTime callbackTime;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人")
    private String createOperName;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人id")
    private Long updateOperId;

    @ApiModelProperty(value = "修改人")
    private String updateOperName;

    @ApiModelProperty(value = "状态（0=正常 1=已删除）")
    @TableLogic
    private Integer isDeleted;

}
