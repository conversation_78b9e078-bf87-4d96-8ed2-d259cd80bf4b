package com.saicmobility.evcard.md.act;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.common.bpe.FlowRes;
import com.saicmobility.evcard.md.act.bo.market.CacheChannelActBo;
import com.saicmobility.evcard.md.act.bo.market.GetChannelActDetailBo;
import com.saicmobility.evcard.md.act.bo.market.GetChannelActDetailFromDiscountCodeBo;
import com.saicmobility.evcard.md.act.bo.market.ListChannelActBo;
import com.saicmobility.evcard.md.act.bo.proprietary.GetProprietaryActivityInfoBo;
import com.saicmobility.evcard.md.act.bo.proprietary.GetSignUpProprietaryActivityInfoBo;
import com.saicmobility.evcard.md.act.bo.proprietary.QueryProprietaryActivityListBo;
import com.saicmobility.evcard.md.act.bo.proprietary.QuerySignUpProprietaryActivityListBo;
import com.saicmobility.evcard.md.act.bo.suixiangcard.SuiXiangCardThirdSaleInfoBo;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.domain.activity.*;
import com.saicmobility.evcard.md.act.dto.market.AddChannelActDto;
import com.saicmobility.evcard.md.act.dto.market.DeleteChannelActDto;
import com.saicmobility.evcard.md.act.dto.market.ListChannelActDto;
import com.saicmobility.evcard.md.act.dto.market.UpdateChannelActDto;
import com.saicmobility.evcard.md.act.dto.promotion.PromotionDto;
import com.saicmobility.evcard.md.act.dto.proprietary.AddProprietaryActivityDto;
import com.saicmobility.evcard.md.act.dto.proprietary.QueryProprietaryActivityListDto;
import com.saicmobility.evcard.md.act.dto.proprietary.UpdateProprietaryActivityDto;
import com.saicmobility.evcard.md.act.dto.suixiangcard.SuiXiangCardThirdSaleInfoInput;
import com.saicmobility.evcard.md.act.dto.welfare.GetWelfareInfoInput;
import com.saicmobility.evcard.md.act.dto.welfare.ReceiveWelfareInput;
import com.saicmobility.evcard.md.act.entity.BrandModelLog;
import com.saicmobility.evcard.md.act.enums.market.CostBearingPartyEnum;
import com.saicmobility.evcard.md.act.mq.SynActivityListener;
import com.saicmobility.evcard.md.act.service.*;
import com.saicmobility.evcard.md.act.service.coupun.CouponService;
import com.saicmobility.evcard.md.act.service.impl.StoreReduceActivityServiceImpl;
import com.saicmobility.evcard.md.act.service.impl.coupon.channel.ccb.ICCBCouponManagerService;
import com.saicmobility.evcard.md.act.service.promotion.IPromotionService;
import com.saicmobility.evcard.md.act.service.suixiangcard.ISuixiangCardBaseService;
import com.saicmobility.evcard.md.act.service.suixiangcard.ISuixiangCardService;
import com.saicmobility.evcard.md.act.service.welfare.WelfareServiceAdapter;
import com.saicmobility.evcard.md.mdactservice.api.*;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Component
public class MdActServiceImpl implements MdActService {

    @Autowired
    private OperateLogService operateLogService;

    @Resource
    private PackageConfigurationService packageConfigurationService;

    @Resource
    private StoreReduceActivityServiceImpl storeReduceActivityServiceImpl;

    @Resource
    private ReduceActivityService reduceActivityService;

    @Resource
    private CouponService couponService;

    @Resource
    private MarketingModuleService marketingModuleService;

    @Resource
    private IProprietaryActivityService proprietaryActivityService;

    @Resource
    private IProprietaryActivitySignupService signupActivityService;

    @Autowired
    private IPromotionService promotionServiceImpl;

    @Autowired
    private ISynActivityService synActivityService;
    @Autowired
    private ISuixiangCardBaseService suixiangCardBaseService;
    @Autowired
    private IBrandModelActivityService brandModelActivityService;
    @Autowired
    private IBrandModelLogService brandModelLogService;
    @Autowired
    private ICCBCouponManagerService ccbCouponManagerServiceImpl;

    @Autowired
    private ISuixiangCardService suixiangCardService;

    @Override
    public GetDictRes getDict(GetDictReq getDictReq) {
        return GetDictRes.ok();
    }

    @Override
    public SearchOperateLogRes searchOperateLog(SearchOperateLogReq req) {
        return operateLogService.searchOperateLog(req);
    }

    @Override
    public SearchPackageRes searchPackage(SearchPackageReq req) {
        return packageConfigurationService.searchPackage(req);
    }

    @Override
    public GetPackageByIdRes getPackageDetail(GetPackageByIdReq req) {
        return packageConfigurationService.getPackageDetail(req);
    }

    @Override
    public AddPackageRes addPackage(AddPackageReq req) {
        return packageConfigurationService.addPackage(req);
    }

    @Override
    public OfflinePackageRes offlinePackage(OfflinePackageReq req) {
        return packageConfigurationService.offlinePackage(req);
    }

    @Override
    public SearchPackageNameRes searchPackageName(SearchPackageNameReq req) {
        return packageConfigurationService.searchPackageName(req);
    }

    @Override
    public SearchReduceActivityRes searchReduceActivity(SearchReduceActivityReq req) {
        return reduceActivityService.searchReduceActivity(req);
    }

    @Override
    public GetReduceActivityWithStoreRes getReduceActivityWithStore(GetReduceActivityWithStoreReq req) {
        return reduceActivityService.getReduceActivityWithStore(req);
    }

    @Override
    public AddReduceActivityRes addReduceActivity(AddReduceActivityReq req) {
        return reduceActivityService.addReduceActivity(req);
    }

    @Override
    public UpdateReduceActivityRes updateReduceActivity(UpdateReduceActivityReq req) {
        return reduceActivityService.updateReduceActivity(req);
    }

    @Override
    public OfflineReduceActivityRes offlineReduceActivity(OfflineReduceActivityReq req) {
        return reduceActivityService.offlineReduceActivity(req);
    }

    @Override
    public SearchStoreUnParticipateActivityRes searchStoreUnParticipateActivity(SearchStoreUnParticipateActivityReq req) {
        return storeReduceActivityServiceImpl.searchStoreUnParticipateActivity(req);
    }

    @Override
    public SearchStoreParticipateActivityRes searchStoreParticipateActivity(SearchStoreParticipateActivityReq req) {
        return storeReduceActivityServiceImpl.searchStoreParticipateActivity(req);
    }

    @Override
    public GetReduceActivityRes getReduceActivity(GetReduceActivityReq req) {
        return reduceActivityService.getReduceActivity(req);
    }

    @Override
    public ParticipateStoreReduceActivityRes participateStoreReduceActivity(ParticipateStoreReduceActivityReq req) {
        return storeReduceActivityServiceImpl.participateStoreReduceActivity(req);
    }

    @Override
    public CancelStoreReduceActivityRes cancelStoreReduceActivity(CancelStoreReduceActivityReq req) {
        return storeReduceActivityServiceImpl.cancelStoreReduceActivity(req);
    }

    @Override
    public SearchActivityParticipateLogRes searchActivityParticipateLog(SearchActivityParticipateLogReq req) {
        return operateLogService.searchActivityParticipateLog(req);
    }

    @Override
    public SearchReduceActivityNameRes searchReduceActivityName(SearchReduceActivityNameReq req) {
        return reduceActivityService.searchReduceActivityName(req);
    }

    @Override
    public GetReduceActivityWithGoodModelNameRes getReduceActivityWithGoodModelName(GetReduceActivityWithGoodModelNameReq req) {
        return reduceActivityService.getReduceActivityWithGoodModelName(req);
    }

    @Override
    public GetPackageByIdRes getPackageById(GetPackageByIdReq req) {
        return packageConfigurationService.getPackageById(req);
    }

    @Override
    public SearchAvailablePackageRes searchAvailablePackage(SearchAvailablePackageReq req) {
        return packageConfigurationService.searchAvailablePackage(req);
    }

    @Override
    public SearchAllPackageRes searchAllPackage(SearchAllPackageReq req) {
        return packageConfigurationService.searchAllPackage(req);
    }

    @Override
    public GetStoreAvailableReduceActivityRes getStoreAvailableReduceActivity(GetStoreAvailableReduceActivityReq req) {
        return storeReduceActivityServiceImpl.getStoreAvailableReduceActivity(req);
    }

    @Override
    public SearchAllReduceActivityRes searchAllReduceActivity(SearchAllReduceActivityReq req) {
        return reduceActivityService.searchAllReduceActivity(req);
    }

    @Override
    public GetCouponRes getCoupon(GetCouponReq req) {
        return couponService.getCoupon(req);
    }

    @Override
    public OrderCouponRes orderCoupons(OrderCouponReq orderCouponReq) {
        return couponService.orderCoupons(orderCouponReq);
    }

    @Override
    public BatchOrderCouponRes batchOrderCoupons(BatchOrderCouponReq batchOrderCouponReq) {
        return couponService.batchOrderCoupons(batchOrderCouponReq);
    }

    @Override
    public CheckOrderCouponRes checkOrderCoupon(CheckOrderCouponReq checkOrderCouponReq) {
        return couponService.checkOrderCoupon(checkOrderCouponReq);
    }

    @Override
    public UseCouponRes useCoupon(UseCouponReq req) {
        return couponService.useCouponRest(req);
    }

    @Override
    public FrozenCouponRes frozenCoupon(FrozenCouponReq req) {
        return couponService.frozenCoupon(req);
    }

    @Override
    public GetCouponViewRes getCouponView(GetCouponViewReq req) {
        return couponService.getCouponView(req);
    }

    @Override
    public GetCouponModelViewRes getCouponModelView(GetCouponModelReq req) {
        return couponService.getCouponModelView(req);
    }

    @Override
    public GetCouponModelListViewRes getCouponModelListView(GetCouponModelListReq req) {
        return couponService.getCouponModelListView(req);
    }

    @Override
    public UserOrderOfferCouponsRes userOrderOfferCoupons(UserOrderOfferCouponsReq req) {
        return couponService.userOrderOfferCoupons(req);
    }

    @Override
    public GetFirstOrderFlagRes getFirstOrderFlag(GetFirstOrderFlagReq req) {
        return couponService.getFirstOrderFlag(req);
    }

    @Override
    public OfferThirdCouponsRes offerThirdCoupons(OfferThirdCouponsReq req) {
        return couponService.offerThirdCoupons(req);
    }

    @Override
    public GetMmpCouponListRes getMmpCouponList(GetMmpCouponListReq req) {
        return couponService.getMmpCouponList(req);
    }

    @Override
    public GetActivityBasicInfoRes getActivityBasicInfo(GetActivityBasicInfoReq req) {
        return couponService.getActivityBasicInfo(req);
    }

    @Override
    public ModifyOrderOperateCouponRes modifyOrderOperateCoupon(ModifyOrderOperateCouponReq req) {
        return couponService.unFrozenThenFrozenCoupon(req);
    }

    @Override
    public AddChannelActRes addChannelAct(AddChannelActReq req) {
        //处理入参
        AddChannelActDto dto = new AddChannelActDto();

        try {
            dto = dto.getDTO(req);
            marketingModuleService.addChannelAct(dto);
        } catch (BusinessException e) {
            log.error("新增营销活动业务失败,dto = {}", JSON.toJSONString(dto),e);
            return AddChannelActRes.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("新增营销活动失败,dto = {}", JSON.toJSONString(dto),e);
            return AddChannelActRes.failed(ErrorEnum.REQUEST_ERROR.getCode(),ErrorEnum.REQUEST_ERROR.getMsg());
        }
        return AddChannelActRes.ok();
    }

    @Override
    public UpdateChannelActRes updateChannelAct(UpdateChannelActReq req) {
        //处理入参
        UpdateChannelActDto dto = new UpdateChannelActDto();

        try {
            dto = dto.getDTO(req);
            marketingModuleService.updateChannelAct(dto);
        } catch (BusinessException e) {
            log.error("修改营销活动业务失败,dto = {}", JSON.toJSONString(dto),e);
            return UpdateChannelActRes.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("修改营销活动失败,dto = {}", JSON.toJSONString(dto),e);
            return UpdateChannelActRes.failed(ErrorEnum.REQUEST_ERROR.getCode(),ErrorEnum.REQUEST_ERROR.getMsg());
        }
        return UpdateChannelActRes.ok();
    }

    @Override
    public ListChannelActRes listChannelAct(ListChannelActReq req) {
        //处理入参
        ListChannelActDto dto = new ListChannelActDto();
        ListChannelActBo bo;
        try {
            dto.setChannel(req.getChannel());
            dto.setDiscountCode(req.getDiscountCode());
            dto.setActName(req.getActName());
            dto.setActStatus(req.getActStatus());
            dto.setPageNum(req.getPageNum());
            dto.setPageSize(req.getPageSize());
            bo = marketingModuleService.listChannelAct(dto);
        } catch (BusinessException e) {
            log.error("listChannelAct 营销活动业务失败,dto = {}", JSON.toJSONString(dto),e);
            return ListChannelActRes.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("listChannelAct 营销活动失败,dto = {}", JSON.toJSONString(dto),e);
            return ListChannelActRes.failed(ErrorEnum.REQUEST_ERROR.getCode(),ErrorEnum.REQUEST_ERROR.getMsg());
        }

        return ListChannelActRes.newBuilder()
                .addAllList(bo.toListChannelAct(bo.getList()))
                .setTotal(bo.getTotal())
                .build();
    }

    @Override
    public GetChannelActDetailRes getChannelActDetail(GetChannelActDetailReq req) {
        long id = req.getId();//报名id
        int type = req.getType();
        //自营活动
        if(type == 1){
            //先通过报名活动id查询自营活动id
            GetSignupProprietaryActivityDetailRes res  = getSignupProprietaryActivityDetail(GetSignupProprietaryActivityDetailReq.newBuilder().setId(id).build());
            if(res.getRetCode() != 0){
                log.error("根据报文id未查询到自营活动id");
                return GetChannelActDetailRes.failed(ErrorEnum.FAILED_QUERY1.getCode(),ErrorEnum.FAILED_QUERY1.getMsg());
            }
            long activityId = res.getInfo().getActivityId();
            //根据id查自营活动
            GetProprietaryActivityInfoRes res1 = getProprietaryActivityInfo(GetProprietaryActivityInfoReq.newBuilder().setId(activityId).build());
            if(res1.getRetCode() != 0){
                log.error("调用getProprietaryActivityInfo() 失败");
                return GetChannelActDetailRes.failed(ErrorEnum.FAILED_QUERY2.getCode(),ErrorEnum.FAILED_QUERY2.getMsg());
            }
            return GetChannelActDetailRes.newBuilder()
                    .setActName(res1.getActivityName())
                    .setActType(res1.getActivityType())
                    .setCostBearingParty(CostBearingPartyEnum.MERCHANT.getType())
                    .addAllOrgCodes(res1.getOrgCodesList())
                    .build();
        }
        //擎路活动
        GetChannelActDetailBo bo;
        try {
            bo = marketingModuleService.getChannelActDetail(id);
        } catch (BusinessException e) {
            log.error("getChannelActDetail 营销活动业务失败,id = {}", id,e);
            return GetChannelActDetailRes.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("getChannelActDetail 营销活动失败,id = {}", id,e);
            return GetChannelActDetailRes.failed(ErrorEnum.REQUEST_ERROR.getCode(),ErrorEnum.REQUEST_ERROR.getMsg());
        }

        String amount = new BigDecimal(bo.getMaxDiscountAmount()).compareTo(new BigDecimal("-1")) != 0 ? bo.getMaxDiscountAmount() : "0";
        return GetChannelActDetailRes.newBuilder()
                .setId(bo.getId())
                .setChannelText(bo.getChannelText())
                .setChannel(bo.getAppKey())
                .setDiscountCode(bo.getDiscountCode())
                .addAllOrgCodes(bo.getOrgCodes())
                .setActName(bo.getActName())
                .setActStatus(bo.getActStatus())
                .addAllStoreIdList(bo.getStoreIds())
                .addAllCarIdList(bo.getCatIds())
                .setActType(bo.getActType())
                .setDiscountLatitude(bo.getDiscountLatitude())
                .setDiscountMethod(bo.getDiscountMethod())
                .setDiscountConditional1(bo.getDiscountConditional1())
                .setDiscountConditional2(bo.getDiscountConditional2())
                .setRestrictDiscounts(bo.getRestrictDiscounts())
                //有无限制
                .setMaxDiscountAmount(amount)
                .setMaxRentDays(bo.getMaxRentDays())
                .setMinRentDays(bo.getMinRentDays())
                .setActStartDate(bo.getActStartDate())
                .setActEndDate(bo.getActEndDate())
                .setPickupStartDate(bo.getPickupStartDate())
                .setPickupEndDate(bo.getPickupEndDate())
                .setReturnStartDate(bo.getReturnStartDate())
                .setReturnEndDate(bo.getReturnEndDate())
                .addAllUnavailableDateRanges(bo.toUnavailableList(bo.getUnavailableDateRanges()))
                .setCostBearingParty(bo.getCostBearingParty())
                .setCostAllocationMethod(bo.getCostAllocationMethod())
                .setMerchantBear(bo.getMerchantBear())
                .setPlatformBear(bo.getPlatformBear())
                .setIsAllStore(bo.getIsAllStore())
                .setIsAllVehicle(bo.getIsAllVehicle())
                .setIntersectionFlag(bo.getIntersectionFlag())
                .build();
    }

    @Override
    public DeleteChannelActRes deleteChannelAct(DeleteChannelActReq req) {
        //处理入参
        DeleteChannelActDto dto = new DeleteChannelActDto();
        dto.setId(req.getId());
        dto.setCurrentUser(req.getCurrentUser());

        //删除
        try {
            marketingModuleService.deleteChannelAct(dto);
        } catch (BusinessException e) {
            log.error("deleteChannelAct 营销活动业务失败,dto = {}", JSON.toJSONString(dto),e);
            return DeleteChannelActRes.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("deleteChannelAct 营销活动失败,dto = {}", JSON.toJSONString(dto),e);
            return DeleteChannelActRes.failed(ErrorEnum.REQUEST_ERROR.getCode(),ErrorEnum.REQUEST_ERROR.getMsg());
        }
        return DeleteChannelActRes.ok();
    }

    @Override
    public GetChannelActDetailFromDiscountCodeRes getChannelActDetailFromDiscountCode(GetChannelActDetailFromDiscountCodeReq req) {
        String discountCode = req.getDiscountCode();
        GetChannelActDetailFromDiscountCodeBo bo;

        try {
            bo = marketingModuleService.getChannelActDetailFromDiscountCode(discountCode);
        } catch (BusinessException e) {
            log.error("getChannelActDetailFromDiscountCode 营销活动业务失败,discountCode = {}", discountCode,e);
            return GetChannelActDetailFromDiscountCodeRes.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("getChannelActDetailFromDiscountCode 营销活动失败,discountCode = {}", discountCode,e);
            return GetChannelActDetailFromDiscountCodeRes.failed(ErrorEnum.REQUEST_ERROR.getCode(),ErrorEnum.REQUEST_ERROR.getMsg());
        }
        String amount = new BigDecimal(bo.getMaxDiscountAmount()).compareTo(new BigDecimal("-1")) != 0 ? bo.getMaxDiscountAmount() : "0";
        return GetChannelActDetailFromDiscountCodeRes.newBuilder()
                .setId(bo.getId())
                .setChannelText(bo.getChannelText())
                .setChannel(bo.getAppKey())
                .setDiscountCode(bo.getDiscountCode())
                .addAllOrgCodes(bo.getOrgCodes())
                .setActName(bo.getActName())
                .setActStatus(bo.getActStatus())
                .addAllStoreIdList(bo.getStoreIds())
                .addAllCarIdList(bo.getCatIds())
                .setActType(bo.getActType())
                .setDiscountLatitude(bo.getDiscountLatitude())
                .setDiscountMethod(bo.getDiscountMethod())
                .setDiscountConditional1(bo.getDiscountConditional1())
                .setDiscountConditional2(bo.getDiscountConditional2())
                .setRestrictDiscounts(bo.getRestrictDiscounts())
                //有无限制
                .setMaxDiscountAmount(amount)
                .setMaxRentDays(bo.getMaxRentDays())
                .setMinRentDays(bo.getMinRentDays())
                .setActStartDate(bo.getActStartDate())
                .setActEndDate(bo.getActEndDate())
                .setPickupStartDate(bo.getPickupStartDate())
                .setPickupEndDate(bo.getPickupEndDate())
                .setReturnStartDate(bo.getReturnStartDate())
                .setReturnEndDate(bo.getReturnEndDate())
                .addAllUnavailableDateRanges(bo.toUnavailableList(bo.getUnavailableDateRanges()))
                .setCostBearingParty(bo.getCostBearingParty())
                .setCostAllocationMethod(bo.getCostAllocationMethod())
                .setMerchantBear(bo.getMerchantBear())
                .setPlatformBear(bo.getPlatformBear())
                .build();
    }

    @Override
    public CacheChannelActRes cacheChannelAct(CacheChannelActReq req) {
        //处理入参
        CacheChannelActBo bo;
        try {
            bo = marketingModuleService.cacheChannelAct(req.getChannel(), req.getCfgMd5());
        } catch (BusinessException e) {
            log.error("cacheChannelAct 营销活动失败,Channel = {}", req.getChannel(),e);
            return CacheChannelActRes.failed(e.getCode(), e.getMessage());
        }
        return CacheChannelActRes.newBuilder()
                .addAllDetail(bo.toChannelActDetailList())
                .setCfgMd5(bo.getMd5())
                .build();
    }

    @Override
    public AddProprietaryActivityRes addProprietaryActivity(AddProprietaryActivityReq addProprietaryActivityReq) {
        //处理入参
        AddProprietaryActivityDto dto = new AddProprietaryActivityDto();

        try {
            dto = dto.parse(addProprietaryActivityReq);
            proprietaryActivityService.addProprietaryActivity(dto);
        } catch (BusinessException e) {
            log.error("新增自营活动失败,dto = {}", JSON.toJSONString(dto));
            return AddProprietaryActivityRes.failed(e.getCode(), e.getMessage());
        }
        return AddProprietaryActivityRes.ok();

    }

    @Override
    public QueryProprietaryActivityListRes queryProprietaryActivityList(QueryProprietaryActivityListReq queryProprietaryActivityListReq) {
        //处理入参
        QueryProprietaryActivityListDto dto = new QueryProprietaryActivityListDto();
        QueryProprietaryActivityListBo bo;
        try {
            dto = dto.parse(queryProprietaryActivityListReq);
            bo = proprietaryActivityService.queryProprietaryActivityList(dto);
        } catch (BusinessException e) {
            log.error("queryProprietaryActivityList 查询自营活动失败,dto = {}", JSON.toJSONString(dto));
            return QueryProprietaryActivityListRes.failed(e.getCode(), e.getMessage());
        }
        return QueryProprietaryActivityListRes.newBuilder()
                .addAllList(bo.toList(bo.getList()))
                .setTotal(bo.getTotal())
                .build();
    }

    @Override
    public SignupProprietaryActivityRes signupProprietaryActivity(SignupProprietaryActivityReq req) {
        SignupProprietaryActivityDto dto = SignupProprietaryActivityDto.parse(req);

        try {
            signupActivityService.signupProprietaryActivity(dto);
            return SignupProprietaryActivityRes.ok();
        } catch (BusinessException e) {
            log.error("signupProprietaryActivity 营销活动报名失败,dto = {}", JSON.toJSONString(dto));
            return SignupProprietaryActivityRes.failed(e.getCode(), e.getMessage());
        } finally {

        }
    }

    @Override
    public QuitProprietaryActivityRes quitProprietaryActivity(QuitProprietaryActivityReq req) {
        return signupActivityService.quitProprietaryActivity(req);
    }

    @Override
    public GetSignupProprietaryActivityListRes getSignupProprietaryActivityList(GetSignupProprietaryActivityListReq req) {
        GetSignupProprietaryActivityListDto dto = GetSignupProprietaryActivityListDto.parse(req);
        try {
            return signupActivityService.getSignupProprietaryActivityList(dto);
        } catch (BusinessException e) {
            log.error("getSignupProprietaryActivityDetail 获取自营活动报名列表失败, dto = {}", JSON.toJSONString(dto));
            return GetSignupProprietaryActivityListRes.failed(e.getCode(), e.getMessage());
        }
    }

    @Override
    public UpdateSignupProprietaryActivityRes updateSignupProprietaryActivity(UpdateSignupProprietaryActivityReq req) {
        UpdateSignupProprietaryActivityDto dto = UpdateSignupProprietaryActivityDto.parse(req);
        try {
            return signupActivityService.updateSignupProprietaryActivity(dto);
        } catch (BusinessException e) {
            log.error("updateSignupProprietaryActivity 编辑报名活动失败, dto = {}", JSON.toJSONString(dto));
            return UpdateSignupProprietaryActivityRes.failed(e.getCode(), e.getMessage());
        }
    }

    @Override
    public GetSignupProprietaryActivityDetailRes getSignupProprietaryActivityDetail(GetSignupProprietaryActivityDetailReq req) {
        try {
            GetSignupProprietaryActivityDetailBo bo = signupActivityService.getSignupProprietaryActivityDetail(req.getId());
            return GetSignupProprietaryActivityDetailRes.newBuilder().setInfo(bo.toRes()).build();
        } catch (BusinessException e) {
            log.error("getSignupProprietaryActivityDetail 获取自营活动详情失败, id = {}", req.getId());
            return GetSignupProprietaryActivityDetailRes.failed(e.getCode(), e.getMessage());
        }
    }

    @Override
    public GetProprietaryActivityInfoRes getProprietaryActivityInfo(GetProprietaryActivityInfoReq getProprietaryActivityInfoReq) {
        long id = getProprietaryActivityInfoReq.getId();
        GetProprietaryActivityInfoBo bo;
        try {
            bo = proprietaryActivityService.getProprietaryActivityInfo(id);
        } catch (BusinessException e) {
            log.error("getProprietaryActivityInfo 获取自营活动详情失败,id = {}", id);
            return GetProprietaryActivityInfoRes.failed(e.getCode(), e.getMessage());
        }
        return bo.toRes();
    }

    @Override
    public UpdateProprietaryActivityRes updateProprietaryActivity(UpdateProprietaryActivityReq updateProprietaryActivityReq) {
        //处理入参
        UpdateProprietaryActivityDto dto = new UpdateProprietaryActivityDto();

        try {
            dto = dto.parse(updateProprietaryActivityReq);
            proprietaryActivityService.updateProprietaryActivity(dto);
        } catch (BusinessException e) {
            log.error("修改自营活动失败,dto = {}", JSON.toJSONString(dto));
            return UpdateProprietaryActivityRes.failed(e.getCode(), e.getMessage());
        }
        return UpdateProprietaryActivityRes.ok();
    }

    @Override
    public CancelProprietaryActivityRes cancelProprietaryActivity(CancelProprietaryActivityReq cancelProprietaryActivityReq) {
        return proprietaryActivityService.cancelProprietaryActivity(cancelProprietaryActivityReq);
    }

    @Override
    public QuerySignUpProprietaryActivityListByActivityIdRes querySignUpProprietaryActivityListByActivityId(QuerySignUpProprietaryActivityListByActivityIdReq querySignUpProprietaryActivityListByActivityIdReq) {
        QuerySignUpProprietaryActivityListBo bo;
        try {
            bo = proprietaryActivityService.querySignUpProprietaryActivityListByActivityId(querySignUpProprietaryActivityListByActivityIdReq);
        } catch (BusinessException e) {
            log.error("querySignUpProprietaryActivityListByActivityId 根据活动id 查询报名自营活动列表,querySignUpProprietaryActivityListByActivityIdReq = {}", JSON.toJSONString(querySignUpProprietaryActivityListByActivityIdReq));
            return QuerySignUpProprietaryActivityListByActivityIdRes.failed(e.getCode(), e.getMessage());
        }
        return QuerySignUpProprietaryActivityListByActivityIdRes.newBuilder()
                .addAllList(bo.toList(bo.getList()))
                .setTotal(bo.getTotal())
                .build();
    }

    @Override
    public GetSignUpProprietaryActivityInfoRes getSignUpProprietaryActivityInfo(GetSignUpProprietaryActivityInfoReq getSignUpProprietaryActivityInfoReq) {
        GetSignUpProprietaryActivityInfoBo bo;
        try {
            bo = proprietaryActivityService.getSignUpProprietaryActivityInfo(getSignUpProprietaryActivityInfoReq);
        } catch (BusinessException e) {
            log.error("getSignUpProprietaryActivityInfo 获取报名后自营活动详情,req = {}", JSON.toJSONString(getSignUpProprietaryActivityInfoReq));
            return GetSignUpProprietaryActivityInfoRes.failed(e.getCode(), e.getMessage());
        }
        return bo.toRes();
    }

    @Override
    public GetSignUpProprietaryActivityByVehicleModelIdsRes getSignUpProprietaryActivityByVehicleModelIds(GetSignUpProprietaryActivityByVehicleModelIdsReq req) {
        GetSignUpProprietaryActivityByVehicleModelIdsBo bo = GetSignUpProprietaryActivityByVehicleModelIdsBo.parse(req);
        try {
            return signupActivityService.getSignUpProprietaryActivityByVehicleModelIds(bo);
        } catch (BusinessException e) {
            log.error("getSignUpProprietaryActivityByVehicleModelIds 根据车型列表批量查询活报名动信息,req = {}", JSON.toJSONString(bo));
            return GetSignUpProprietaryActivityByVehicleModelIdsRes.failed(e.getCode(), e.getMessage());
        }
    }

    @Override
    public GetActSignUpDetailByVehicleModelIdRes getActSignUpDetailByVehicleModelId(GetActSignUpDetailByVehicleModelIdReq req) {
        try {
            return signupActivityService.getActSignUpDetailByVehicleModelId(req);
        } catch (BusinessException e) {
            log.error("getActSignUpDetailByVehicleModelId 根据车型查询活报名动信息, orgCode = {}, planPickupDateTime = {}, storeVehicleModelId = {}", req.getOrgCode(), req.getPlanPickupDateTime(), req.getStoreVehicleModelId());
            return GetActSignUpDetailByVehicleModelIdRes.failed(e.getCode(), e.getMessage());
        }
    }

    @Override
    public CheckPromotionByYoumiRes checkPromotionByYoumi(CheckPromotionByYoumiReq checkPromotionByYoumiReq) {
        // 有米推广中，设备平台只为iOS，设备码类型为idfa
        PromotionDto promotionDto = new PromotionDto();
        promotionDto.setSource(checkPromotionByYoumiReq.getSource());
        promotionDto.setAppid(checkPromotionByYoumiReq.getAppid());
        promotionDto.setDeviceCode(checkPromotionByYoumiReq.getIdfa());
        promotionDto.setDevicePlatform(1);
        promotionDto.setDeviceType(1);
        FlowRes flowRes = promotionServiceImpl.checkPromotion(promotionDto);
        return CheckPromotionByYoumiRes.newBuilder()
                .setRetCode(flowRes.retCode())
                .setRetMsg(flowRes.retMsg())
                .build();
    }

    @Override
    public ClickPromotionByYoumiRes clickPromotionByYoumi(ClickPromotionByYoumiReq clickPromotionByYoumiReq) {
        PromotionDto promotionDto = new PromotionDto();
        promotionDto.setSource(clickPromotionByYoumiReq.getSource());
        promotionDto.setAppid(clickPromotionByYoumiReq.getAppid());
        promotionDto.setDeviceCode(clickPromotionByYoumiReq.getIdfa());
        promotionDto.setDevicePlatform(1);
        promotionDto.setDeviceType(1);
        promotionDto.setCallbackUrl(clickPromotionByYoumiReq.getCallbackUrl());
        FlowRes flowRes = promotionServiceImpl.clickPromotion(promotionDto);
        return ClickPromotionByYoumiRes.newBuilder()
                .setRetCode(flowRes.retCode())
                .setRetMsg(flowRes.retMsg())
                .build();
    }

    @Override
    public CallPromotionByYoumiRes callPromotionByYoumi(CallPromotionByYoumiReq callPromotionByYoumiReq) {
        PromotionDto promotionDto = new PromotionDto();
        promotionDto.setDevicePlatform(1);
        promotionDto.setDeviceType(1);
        promotionDto.setAppid(callPromotionByYoumiReq.getAppid());
        promotionDto.setDeviceCode(callPromotionByYoumiReq.getIdfa());
        FlowRes flowRes = promotionServiceImpl.callPromotion(promotionDto);
        return CallPromotionByYoumiRes.newBuilder()
                .setRetCode(flowRes.retCode())
                .setRetMsg(flowRes.retMsg())
                .build();
    }

    @Override
    public SyncActivityVehicleRes syncActivityVehicle(SyncActivityVehicleReq req) {
        try {
            synActivityService.doSyncActivityVehicle(req.getStoreId(), req.getMdModelIdsList(),req.getOrgCode()
                    ,req.getSecondAppKeyList().contains(SynActivityListener.EV_CARD),req.getSecondAppKeyList());
        }catch (Exception e){
            log.error("tid:{},同步活动车型异常!e: ", Trace.currentTraceId(),e);
            return SyncActivityVehicleRes.failed(ErrorEnum.REQUEST_ERROR.getCode(),ErrorEnum.REQUEST_ERROR.getMsg());
        }
        return SyncActivityVehicleRes.ok();
    }

    @Override
    public SyncActivityStoreRes syncActivityStore(SyncActivityStoreReq req) {
        try {
            synActivityService.doSyncActivityStore(req.getStoreId(),req.getSecondAppKeyList().contains(SynActivityListener.EV_CARD)
                    , req.getSecondAppKeyList(), req.getOrgCode());
        }catch (Exception e){
            log.error("tid:{},同步活动门店异常!e: ", Trace.currentTraceId(),e);
            return SyncActivityStoreRes.failed(ErrorEnum.REQUEST_ERROR.getCode(),ErrorEnum.REQUEST_ERROR.getMsg());
        }
        return SyncActivityStoreRes.ok();
    }

    @Autowired
    private WelfareServiceAdapter welfareServiceAdapter;
    @Override
    public GetWelfareDetailInfoRes getWelfareDetailInfo(GetWelfareDetailInfoReq getWelfareDetailInfoReq) {
        GetWelfareInfoInput getWelfareInfoInput = new GetWelfareInfoInput();
        getWelfareInfoInput.setWelfareType(getWelfareDetailInfoReq.getWelfareType());
        getWelfareInfoInput.setKey(getWelfareDetailInfoReq.getKey());
        getWelfareInfoInput.setSource(getWelfareDetailInfoReq.getSource());
        return welfareServiceAdapter.getWelfareDetailInfo(getWelfareInfoInput);
    }

    @Override
    public ReceiveWelfareRes receiveWelfare(ReceiveWelfareReq receiveWelfareReq) {
        ReceiveWelfareInput input = new ReceiveWelfareInput();
        input.setMobile(receiveWelfareReq.getMobile());
        input.setMid(receiveWelfareReq.getMid());
        input.setType(receiveWelfareReq.getType());

        // 设置 source=3 时的新参数
        if (receiveWelfareReq.getSource() == 3) {
            input.setAuthcode(receiveWelfareReq.getAuthcode());
            input.setMembershipPolicyVersion(receiveWelfareReq.getMembershipPolicyVersion());
            input.setPrivacyPolicyVersion(receiveWelfareReq.getPrivacyPolicyVersion());
        }

        GetWelfareInfoInput getWelfareInfoInput = new GetWelfareInfoInput();
        getWelfareInfoInput.setWelfareType(receiveWelfareReq.getWelfareType());
        getWelfareInfoInput.setKey(receiveWelfareReq.getKey());
        getWelfareInfoInput.setSource(receiveWelfareReq.getSource());
        input.setGetWelfareInfoInput(getWelfareInfoInput);
        return welfareServiceAdapter.receiveWelfare(input);
    }

    @Override
    public GetPrimeActByCityRes getPrimeActByCity(GetPrimeActByCityReq getPrimeActByCityReq) {
        return signupActivityService.getPrimeActByCity(getPrimeActByCityReq);
    }

    @Override
    public GetSuiXiangCardBaseInfoByRentDayIdRes getSuiXiangCardBaseInfoByRentDayId(GetSuiXiangCardBaseInfoByRentDayIdReq getSuiXiangCardBaseInfoByRentDayIdReq) {
        return suixiangCardBaseService.getSuixiangCardBaseByRentDayId(getSuiXiangCardBaseInfoByRentDayIdReq.getRentDayId());
    }

    @Override
    public GetCouponDetailForCCBRes getCouponDetailForCCB(GetCouponDetailForCCBReq getCouponDetailForCCBReq) {
        return ccbCouponManagerServiceImpl.getCouponDetailForCCB(getCouponDetailForCCBReq);
    }

    @Override
    public GetCouponStockForCCBRes getCouponStockForCCB(GetCouponStockForCCBReq getCouponStockForCCBReq) {
        return ccbCouponManagerServiceImpl.getCouponStockForCCB(getCouponStockForCCBReq);
    }

    @Override
    public OfferCouponForCCBRes offerCouponForCCB(OfferCouponForCCBReq offerCouponForCCBReq) {
        return ccbCouponManagerServiceImpl.offCouponForCCB(offerCouponForCCBReq);
    }

    @Override
    public GetChannelCouponStatusForCCBRes getChannelCouponStatusForCCB(GetChannelCouponStatusForCCBReq req) {
        return ccbCouponManagerServiceImpl.getChannelCouponStatusForCCB(req);
    }

    @Override
    public CallBackCouponStatusToCCBRes callBackCouponStatusToCCB(CallBackCouponStatusToCCBReq callBackCouponStatusToCCBReq) {
        return ccbCouponManagerServiceImpl.callBackCouponStatusToCCB(callBackCouponStatusToCCBReq);
    }

    @Override
    public InvalidChannelCouponForCCBRes invalidChannelCouponForCCB(InvalidChannelCouponForCCBReq req) {
        return ccbCouponManagerServiceImpl.invalidChannelCouponForCCB(req);
    }

    @Override
    public ReconciliationFileToRes reconciliationFileToCCB(ReconciliationFileToReq reconciliationFileToReq) {
        return ccbCouponManagerServiceImpl.reconciliationFileToCCB(reconciliationFileToReq);
    }

    @Override
    public StatementOfAccountCallBackForCCBRes statementOfAccountCallBackForCCB(StatementOfAccountCallBackForCCBReq req) {
        return ccbCouponManagerServiceImpl.statementOfAccountCallBackForCCB(req);
    }


    @Override
    public RemoveChannelCouponStockRes removeChannelCouponStock(RemoveChannelCouponStockReq removeChannelCouponStockReq) {
        return ccbCouponManagerServiceImpl.removeChannelCouponStock(removeChannelCouponStockReq);
    }


    @Override
    public SaveOrUpdateBrandModelActivityRes saveOrUpdateBrandModelActivity(SaveOrUpdateBrandModelActivityReq saveOrUpdateBrandModelActivityReq) {
        return brandModelActivityService.insertOrUpdate(saveOrUpdateBrandModelActivityReq);
    }

    @Override
    public UpdateBrandModelActivityStatusRes updateBrandModelActivityStatus(UpdateBrandModelActivityStatusReq updateBrandModelActivityStatusReq) {
        return brandModelActivityService.updateBrandModelActivityStatus(updateBrandModelActivityStatusReq);
    }

    @Override
    public QueryBrandModelActivityListRes queryBrandModelActivityList(QueryBrandModelActivityListReq queryBrandModelActivityListReq) {
        return brandModelActivityService.queryBrandModelActivityList(queryBrandModelActivityListReq);
    }

    @Override
    public QueryBrandModelLogsByActIdRes queryBrandModelLogsByActId(QueryBrandModelLogsByActIdReq queryBrandModelLogsByActIdReq) {
        return brandModelLogService.queryBrandModelLogsByActId(queryBrandModelLogsByActIdReq);
    }

    @Override
    public QueryBrandModelActivityDetailRes queryBrandModelActivityDetail(QueryBrandModelActivityDetailReq queryBrandModelActivityDetailReq) {
        return brandModelActivityService.queryBrandModelActivityDetail(queryBrandModelActivityDetailReq);
    }

    @Override
    public GetSuiXiangCardThirdSaleInfoRes getSuiXiangCardThirdSaleInfo(GetSuiXiangCardThirdSaleInfoReq req) {
        try {
            SuiXiangCardThirdSaleInfoInput input = new SuiXiangCardThirdSaleInfoInput(req);
            SuiXiangCardThirdSaleInfoBo suiXiangCardThirdSaleInfo = suixiangCardService.getSuiXiangCardThirdSaleInfo(input);
            return suiXiangCardThirdSaleInfo.toGetSuiXiangCardThirdSaleInfoRes();
        } catch (BusinessException e) {
            log.error("获取随行卡第三方销售信息失败，tid={}",Trace.currentTraceId(),e);
            return GetSuiXiangCardThirdSaleInfoRes.failed(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("获取随行卡第三方销售信息异常，tid={}",Trace.currentTraceId(),e);
            return GetSuiXiangCardThirdSaleInfoRes.failed(-1,"获取失败");
        }
    }

    @Override
    public GetH5WelfareDetailInfoRes getH5WelfareDetailInfo(GetH5WelfareDetailInfoReq getH5WelfareDetailInfoReq) {
        return null;
    }
}
