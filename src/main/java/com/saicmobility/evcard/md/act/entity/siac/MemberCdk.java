package com.saicmobility.evcard.md.act.entity.siac;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("member_cdk")
@ApiModel(value="MemberCdk对象", description="")
public class MemberCdk extends Model<MemberCdk> {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "兑换码功能 1：一码多券")
    private Integer type;

    @ApiModelProperty(value = "兑换码")
    private String cdkey;

    @ApiModelProperty(value = "0：未兑换 1：已兑换 2：冻结 3:已过期")
    private Integer status;

    @ApiModelProperty(value = "活动id")
    private String actionId;

    @ApiModelProperty(value = "兑换方式 0：输入兑换码 1：扫码")
    private Integer activatedWay;

    @ApiModelProperty(value = "兑换人MID，兑换后才有值")
    private String activatedMid;

    @ApiModelProperty(value = "兑换人姓名，兑换后才有值")
    private String activatedUserName;

    @ApiModelProperty(value = "兑换人手机号，兑换后才有值")
    private String activatedUserMobile;

    @ApiModelProperty(value = "兑换时间，兑换后才有值")
    private Date activatedTime;

    @ApiModelProperty(value = "兑换码有效期开始时间")
    private Date validityStartTime;

    @ApiModelProperty(value = "兑换码有效期结束时间")
    private Date validityEndTime;

    @ApiModelProperty(value = "微信小程序兑换二维码图片地址")
    private String wechatCdkQrUrl;

    @ApiModelProperty(value = "优惠券数目")
    private Integer couponNum;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人")
    private String createOperName;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "修改人id")
    private Long updateOperId;

    @ApiModelProperty(value = "修改人")
    private String updateOperName;

    @ApiModelProperty(value = "删除标记（0：正常 1：删除）")
    private Integer isDeleted;

}
