package com.saicmobility.evcard.md.act.dto.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PromotionDto {
    // 主键
    private Long id;

    // 渠道唯一标识
    private String source;

    // 应用唯一标识
    private String appid;

    // 设备类型（1=IOS、2=Android)
    private Integer devicePlatform;

    // 设备标识符类型（idfa、imei等）
    private Integer deviceType;

    // 设备标识符
    private String deviceCode;

    // 回调地址
    private String callbackUrl;

    // 回调结果（0=未回调成功，1=回调成功）
    private Integer callbackResult;

    // 回调成功时间
    private LocalDateTime callbackTime;
}
