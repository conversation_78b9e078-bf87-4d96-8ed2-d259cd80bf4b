package com.saicmobility.evcard.md.act.mapper.act;

import com.saicmobility.evcard.md.act.entity.ProprietaryActivity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 自营活动表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
public interface ProprietaryActivityMapper extends BaseMapper<ProprietaryActivity> {


    /**
     * 批量修改门店id （只更新门店id）
     * @param updateList
     */
    int updateBatchStoreId(@Param("list") List<ProprietaryActivity> updateList);


    List<ProprietaryActivity> queryProprietaryActByStoreIds(@Param("list") List<Long> storeIds);
}
