package com.saicmobility.evcard.md.act.dto.welfare;

import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.mdactservice.api.GetWelfareDetailInfoRes;
import com.saicmobility.evcard.md.mdactservice.api.SuiXiangCardInfo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class SuiXiangCardWelfareInfo implements Serializable {

    private String name; // 随享卡名称
    private int totalDays; // 随享卡总天数
    private int validDaysType; // 有效期类型 有效期：1-一个月、2-两个月、3-三个月、6-六个月、12-一年
    private String tip; // 使用注意事项

    public static SuiXiangCardInfo toRes(SuiXiangCardWelfareInfo welfareInfo) {
        SuiXiangCardInfo suiXiangCardInfo = SuiXiangCardInfo
                .newBuilder()
                .setName(welfareInfo.getName())
                .setTotalDays(welfareInfo.getTotalDays())
                .setValidDaysType(welfareInfo.getValidDaysType())
                .setTip(welfareInfo.getTip())
                .build();
        return suiXiangCardInfo;
    }

    public static GetWelfareDetailInfoRes listToRes(List<SuiXiangCardWelfareInfo> welfareInfoList){
        if (CollectionUtils.isNotEmpty(welfareInfoList)) {
            List<SuiXiangCardInfo> suiXiangCardInfos = welfareInfoList.stream().map(SuiXiangCardWelfareInfo::toRes).collect(Collectors.toList());
            return GetWelfareDetailInfoRes.newBuilder().addAllSuiXiangCardInfos(suiXiangCardInfos).build();
        }
        return GetWelfareDetailInfoRes.failed(ErrorEnum.NOT_FOUND_WELFARE_INFO.getCode(), ErrorEnum.NOT_FOUND_WELFARE_INFO.getMsg());
    }
}
