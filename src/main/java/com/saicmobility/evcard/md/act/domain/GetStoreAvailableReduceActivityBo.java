package com.saicmobility.evcard.md.act.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 获取门店可用的立减活动(app接口)bo
 * @Author: lidong
 * @Date: 2022/06/11
 */
@Data
public class GetStoreAvailableReduceActivityBo implements Serializable {

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 用户可参与次数
     */
    private Integer userParticipateNumber;

    /**
     * 生效开始时间
     */
    private String activityStartTime;

    /**
     * 生效结束时间
     */
    private String activityEndTime;

    /**
     * 用户注册开始时间
     */
    private String registerStartTime;

    /**
     * 用户注册结束时间
     */
    private String registerEndTime;

    /**
     * 是否仅首单可用 1 是  2 否
     */
    private Integer firstOrderAvailable;

    /**
     * 活动立减金额
     */
    private BigDecimal activityDiscount;

    /**
     * 活动规则说明
     */
    private String activityRuleDescription;

    /**
     * 活动图片
     */
    private String activityPicUrl;

}
