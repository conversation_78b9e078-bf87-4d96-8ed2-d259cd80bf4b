package com.saicmobility.evcard.md.act.constant;

import com.saicmobility.evcard.md.act.enums.ChannelType;
import com.saicmobility.evcard.md.act.enums.ConfigStateEnum;
import com.saicmobility.evcard.md.act.enums.market.*;
import com.saicmobility.evcard.md.act.enums.proprietary.ActivityStatusEnum;
import com.saicmobility.evcard.md.act.enums.proprietary.HolidaysTypeEnum;
import com.saicmobility.evcard.md.act.enums.proprietary.PricingTypeEnum;

import java.util.Arrays;
import java.util.List;

public class BusinessConst {
    // 机构配置，store_id 的默认值
    public static final Long RENT_DEFAULT_STORE_ID = -1L;

    //public static final Date DEFAULT_CONFIG_END_TIME = DateUtil.getDateFromDateStr("9998-01-01 00:00:00", DateUtil.DATE_TYPE1);

    /**
     * 日志类型： 1.套餐配置 2.立减活动配置
     */
    public static final Integer LOG_TYPE_PACKAGE = 1;
    public static final Integer LOG_TYPE_REDUCE_ACTIVITY = 2;

    /**
     * 操作日志类别：1普通配置  2状态变更
     */
    public static final Integer LOG_OPT_NORMAL = 1;
    public static final Integer LOG_OPT_CHANGE = 2;

    /**
     * "配置状态 1待生效 2生效中 3已下线"
     */
    public static final Integer CONFIG_STATUS_PENDING = 1;
    public static final Integer CONFIG_STATUS_ONLINE = 2;
    public static final Integer CONFIG_STATUS_OFFLINE = 3;

    /**
     * 门店营销服务远程调用coupon服务
     */
    public static final Integer CALL_SERVICE_TYPE = 2;

    /**
     * 取还车点限制类别 1网点 2门店
     **/
    public static final Integer STORE_TYPE = 2;

    /**
     * 是否可用早鸟套餐 1是 2否
     **/
    public static final Integer EARLY_PACKAGE = 1;

    /**
     * 是否可用早鸟套餐 1是 2否
     **/
    public static final Integer NOT_EARLY_PACKAGE = 2;

    /**
     * 所有渠道
     */
    public static List<Integer> ALL_CHANNEL_TYPE = Arrays.asList(ChannelType.XIANXIA.getType(), ChannelType.XIECHENG.getType(), ChannelType.FEIZHU.getType(), ChannelType.HALUO.getType(),
            ChannelType.ZUZUCHE.getType(), ChannelType.WUKONG.getType(), ChannelType.XIECHENGFENXIAO.getType());

    /**
     * 所有活动状态
     */
    public static List<Integer> ALL_ACT_STATUS = Arrays.asList(ConfigStateEnum.VALID.getType(), ConfigStateEnum.EXPIRE.getType());

    /**
     * 活动类型
     */
    public static List<Integer> ALL_ACT_TYPE = Arrays.asList(ActType.FULLREDUCTION.getType(), ActType.DISCOUNT.getType(), ActType.DECREASE.getType());

    /**
     * 优惠纬度
     */
    public static List<Integer> ALL_DISCOUNT_LATITUDE = Arrays.asList(DiscountLatitudeEnum.CARRENT.getType(), DiscountLatitudeEnum.WHOLEORDER.getType());

    /**
     * 优惠方式
     */
    public static List<Integer> ALL_DISCOUNT_METHOD = Arrays.asList(DiscountMethodEnum.AMOUNT.getType(), DiscountMethodEnum.RENT_DAYS.getType(),
            DiscountMethodEnum.DAY_MINUS_DAY.getType(), DiscountMethodEnum.DAY_MINUS_AMOUNT.getType());

    /**
     * 是否优惠限制
     * 1--有限制 2--无限制
     */
    public static List<Integer> IS_RESTRICT_DISCOUNT = Arrays.asList(RestrictDiscount.RESTRICT.getType(), RestrictDiscount.NOT_RESTRICT.getType());

    /**
     * 成本承担方
     */
    public static List<Integer> ALL_COST_BEAR_PARTY = Arrays.asList(CostBearingPartyEnum.PLATFORM.getType(), CostBearingPartyEnum.MERCHANT.getType(),
            CostBearingPartyEnum.TOGETHER.getType());

    /**
     * 成本分摊方式
     */
    public static List<Integer> ALL_COST_ALLOCATION_METHOD = Arrays.asList(CostAllocationMethodEnum.PERCENT.getType(), CostAllocationMethodEnum.AMOUNT.getType());

    /**
     * 擎路平台id 100
     **/
    public static final String QINGLU_PLATFORMID = "100";

    /**
     * 自营活动类型
     */
    public static List<Integer> ALL_PROPRIETARY_ACTIVITY_TYPE = Arrays.asList(ActType.FULLREDUCTION.getType(), ActType.DISCOUNT.getType());

    /**
     * 自营活动定价类型
     */
    public static List<Integer> ALL_PROPRIETARY_PRICING_TYPE = Arrays.asList(PricingTypeEnum.FLEXIBLE_PRICING.getType(), PricingTypeEnum.STANDARD_PRICING.getType());

    /**
     * 节假日是否可用
     */
    public static List<Integer> ALL_HOLIDAYS_TYPE = Arrays.asList(HolidaysTypeEnum.AVAILABLE.getType(), HolidaysTypeEnum.NOT_AVAILABLE.getType());
    /**
     * 自营活动 活动状态
     */
    public static List<Integer> ALL_PROPRIETARY_ACTIVITY_STATUS = Arrays.asList(ActivityStatusEnum.NOT_START.getType(), ActivityStatusEnum.EFFECT.getType(), ActivityStatusEnum.EXPIRE.getType(), ActivityStatusEnum.CANCEL.getType());

    /**
     * 哈啰二级渠道 second_hello
     */
    public static final String HELLO_SECOND_CHANNEL = "second_hello";

    /**
     * 铁行二级渠道
     */
    public static final String TX_SECOND_CHANNEL = "second_tiexing";

    /**
     * 支付宝车生活二级渠道
     */
    public static final String SECOND_ALI_PAY_CAR_LIFE_CHANNEL = "second_ALI_PAY_CAR_LIFE";


    public static final String TONGCHENGZUCHE_APP_KEY = "tongchengzuche";
    public static final int TONGCHENGZUCHE_PLATFORM_ID = 113;

    /**
     * 同程租车二级渠道
     */
    public static final String SECOND_TONGCHENG_ZUCHE_CHANNEL = "second_tongchengzuche";


    /**
     * 不同步给 擎路 的二级渠道key
     *
     */
    public static List<String> NOT_SYNC_SECOND_APP_KEY_LIST = Arrays.asList(BusinessConst.HELLO_SECOND_CHANNEL, BusinessConst.TX_SECOND_CHANNEL, BusinessConst.SECOND_ALI_PAY_CAR_LIFE_CHANNEL, BusinessConst.SECOND_TONGCHENG_ZUCHE_CHANNEL);
}
