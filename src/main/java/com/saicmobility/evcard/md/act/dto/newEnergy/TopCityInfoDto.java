package com.saicmobility.evcard.md.act.dto.newEnergy;

import com.saicmobility.evcard.md.act.util.CityNameSpecialHandle;
import com.saicmobility.evcard.md.mdactservice.api.TopCityInfo;
import com.saicmobility.evcard.md.mdstockservice.api.CityInfo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/21 9:56
 * @Description: 城市实体类
 */
@Data
public class TopCityInfoDto {
    private Long  id; // id ，全部城市，id为-1
    private Long  cityId; // 运营城市id，全部城市，id为-1
    private String  cityName; // 运营城市名称，全部城市，name为全部
    private Long  provinceId; // 所属省份id
    private double lon; // 经度
    private double lat; // 纬度
    private String  abbrCityName;//城市简称

    public static TopCityInfo cityInfoToRes(CityInfo dto){
        return TopCityInfo.newBuilder()
                .setCityId(dto.getCityId())
                .setCityName(dto.getCityName())
                .setLon(Double.parseDouble(dto.getLon()))
                .setLat(Double.parseDouble(dto.getLat()))
                .setAbbrCityName(CityNameSpecialHandle.getAbbrCityName(dto.getCityName()))
                .build();
    }

    public static TopCityInfo cityInfoToRes(CityInfo dto, Map<Integer, Set<Long>> cityVehicleModelMap, Set<Long> relatedModelIds){
        Set<Long> cityVehicleModelIds = cityVehicleModelMap.getOrDefault(dto.getCityId(), new HashSet<>());
        return TopCityInfo.newBuilder()
                .setCityId(dto.getCityId())
                .setCityName(dto.getCityName())
                .setLon(Double.parseDouble(dto.getLon()))
                .setLat(Double.parseDouble(dto.getLat()))
                .setAbbrCityName(CityNameSpecialHandle.getAbbrCityName(dto.getCityName()))
                .addAllModelIds(relatedModelIds.stream().filter(cityVehicleModelIds::contains).collect(Collectors.toList()))
                .build();
    }

    public static TopCityInfo toRes(TopCityInfoDto dto){
        return TopCityInfo.newBuilder()
                .setId(dto.getId())
                .setCityId(dto.getCityId())
                .setCityName(dto.getCityName())
                .setProvinceId(dto.getProvinceId())
                .setLon(dto.getLon())
                .setLat(dto.getLat())
                .setAbbrCityName(dto.getAbbrCityName())
                .build();
    }
    public static List<TopCityInfo> listToRes(List<TopCityInfoDto> dtos){
        List<TopCityInfo> topCityInfos= new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dtos)){
            dtos.forEach(dto->{
                topCityInfos.add(toRes(dto));
            });
        }
        return topCityInfos;
    }
    public static TopCityInfoDto toDto(TopCityInfo res){
        TopCityInfoDto topCityInfoDto = new TopCityInfoDto();
        topCityInfoDto.setId(res.getId());
        topCityInfoDto.setCityId(res.getCityId());
        topCityInfoDto.setCityName(res.getCityName());
        topCityInfoDto.setProvinceId(res.getProvinceId());
        topCityInfoDto.setLon(res.getLon());
        topCityInfoDto.setLat(res.getLat());
        topCityInfoDto.setAbbrCityName(res.getAbbrCityName());
        return topCityInfoDto;
    }
    public static List<TopCityInfoDto> listToDto(List<TopCityInfo> list){
        List<TopCityInfoDto> topCityInfoDtos= new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)){
            list.forEach(res->{
                topCityInfoDtos.add(toDto(res));
            });
        }
        return topCityInfoDtos;
    }

}


