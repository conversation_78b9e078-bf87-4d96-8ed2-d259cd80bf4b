package com.saicmobility.evcard.md.act.job;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saicmobility.evcard.md.act.constant.SystemConst;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.entity.OperateLog;
import com.saicmobility.evcard.md.act.entity.PackageConfiguration;
import com.saicmobility.evcard.md.act.mapper.act.OperateLogMapper;
import com.saicmobility.evcard.md.act.mapper.act.PackageConfigurationMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 更新套餐状态
 * 套餐到指定日期时分秒 自动生效,失效
 */
@Slf4j
@Component
@JobHandler("UpdatePackageStatusJob")
public class UpdatePackageStatusJob extends IJobHandler {

    @Autowired
    private PackageConfigurationMapper packageConfigurationMapper;

    @Autowired
    private OperateLogMapper operateLogMapper;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("============> 开始执行 更新套餐状态 定时任务 <============");
        LocalDateTime nowTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String nowTimeStr = nowTime.format(formatter);

        UserDTO jobUser = SystemConst.ELASTIC_JOB_USER;

        // 将待生效的变为生效中
        LambdaQueryWrapper<PackageConfiguration> waitWrapper = new LambdaQueryWrapper<PackageConfiguration>()
                .eq(PackageConfiguration::getConfigState, 2)
                .eq(PackageConfiguration::getIsDeleted, 0)
                .le(PackageConfiguration::getStartTime, nowTimeStr);
        List<PackageConfiguration> waitPackageList = packageConfigurationMapper.selectList(waitWrapper);
        if (CollectionUtil.isNotEmpty(waitPackageList)) {
            for (PackageConfiguration packageConfiguration : waitPackageList) {
                packageConfiguration.setConfigState(1);
                packageConfiguration.setUpdateOperId(jobUser.getId());
                packageConfiguration.setUpdateOperName(jobUser.getName());
                packageConfigurationMapper.updateById(packageConfiguration);
                //插入日志
                com.saicmobility.evcard.md.act.entity.OperateLog operateLog = new OperateLog();
                operateLog.setForeignId(String.valueOf(packageConfiguration.getId()));
                operateLog.setOperateType(1);
                operateLog.setOperateContent("套餐由待生效变为生效中");
                operateLog.setCreateOperId(jobUser.getId());
                operateLog.setCreateOperName(jobUser.getName());
                operateLog.setCreateOperOrgName("环球车享汽车租赁有限公司");
                operateLogMapper.insert(operateLog);
            }
        }

        //将生效中的变为已下线
        LambdaQueryWrapper<PackageConfiguration> effectWrapper = new LambdaQueryWrapper<PackageConfiguration>()
                .eq(PackageConfiguration::getConfigState, 1)
                .eq(PackageConfiguration::getIsDeleted, 0);
        List<PackageConfiguration> effectPackageList = packageConfigurationMapper.selectList(effectWrapper);
        if (CollectionUtil.isNotEmpty(effectPackageList)) {
            for (PackageConfiguration effectPackage : effectPackageList) {
                String endTime = effectPackage.getEndTime();
                if (StringUtils.isBlank(endTime)) {
                    continue;
                }
                LocalDateTime endDateTime = LocalDateTime.parse(endTime, formatter);
                //套餐天数
                Integer daysNumber = effectPackage.getDaysNumber();
                //套餐实际还剩的天数区间
                int remainDays = getDaysNum(nowTime, endDateTime);
                if (daysNumber > remainDays || endDateTime.isBefore(nowTime)) {
                    //变为失效
                    effectPackage.setConfigState(3);
                    effectPackage.setUpdateOperId(jobUser.getId());
                    effectPackage.setUpdateOperName(jobUser.getName());
                    packageConfigurationMapper.updateById(effectPackage);
                    //插入日志
                    com.saicmobility.evcard.md.act.entity.OperateLog operateLog = new OperateLog();
                    operateLog.setForeignId(String.valueOf(effectPackage.getId()));
                    operateLog.setOperateType(1);
                    operateLog.setOperateContent("套餐由生效中变为已失效");
                    operateLog.setCreateOperId(jobUser.getId());
                    operateLog.setCreateOperName(jobUser.getName());
                    operateLog.setCreateOperOrgName("环球车享汽车租赁有限公司");
                    operateLogMapper.insert(operateLog);
                }

            }
        }

        log.info("============> 结束执行 更新套餐状态 定时任务 <============");
        return ReturnT.SUCCESS;
    }

    private int getDaysNum(LocalDateTime startTime, LocalDateTime endTime) {
        Duration days = Duration.between(startTime, endTime);
        return (int) days.toDays();
    }
}
