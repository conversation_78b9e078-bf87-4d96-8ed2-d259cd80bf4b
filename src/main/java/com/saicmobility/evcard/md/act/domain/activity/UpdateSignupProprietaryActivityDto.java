package com.saicmobility.evcard.md.act.domain.activity;

import com.alibaba.fastjson.annotation.JSONField;
import com.saicmobility.evcard.md.mdactservice.api.CurrentUser;
import com.saicmobility.evcard.md.mdactservice.api.UpdateSignupProprietaryActivityReq;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class UpdateSignupProprietaryActivityDto {
    private long id; // 报名id
    private String orgCode; // 活动报名机构
    private List<Long> vehicleModelIds; // 车型id列表
    private List<SignupFullMinusFlexiblePricing> fullMinusFlexiblePricing; // 满减灵活定价
    private List<SignupDiscountFlexiblePricing> discountFlexiblePricing; // 打折灵活定价
    private Long storeId;//活动报名门店
    @JSONField(serialize = false)
    private CurrentUser currentUser; // 当前用户
    private Integer isAllVehicle; // 是否全部车型  0否  1是

    public static UpdateSignupProprietaryActivityDto parse(UpdateSignupProprietaryActivityReq req) {
        UpdateSignupProprietaryActivityDto dto = new UpdateSignupProprietaryActivityDto();
        dto.setId(req.getId());
        dto.setOrgCode(req.getOrgCode());
        dto.setStoreId(req.getStoreId());
        dto.setIsAllVehicle(req.getIsAllVehicle());
        // 车型id列表
        if (!CollectionUtils.isEmpty(req.getVehicleModelIdsList())) {
            dto.setVehicleModelIds(req.getVehicleModelIdsList());
        }
        // 满减灵活定价
        if (!CollectionUtils.isEmpty(req.getFullMinusFlexiblePricingList())) {
            List<SignupFullMinusFlexiblePricing> list = req.getFullMinusFlexiblePricingList().stream().map( o -> {
                SignupFullMinusFlexiblePricing price = new SignupFullMinusFlexiblePricing();
                price.setDays(o.getDays());
                price.setDiscountAmount(o.getDiscountAmount());
                price.setMinDiscountAmount(o.getMinDiscountAmount());
                price.setMaxDiscountAmount(o.getMaxDiscountAmount());
                return price;
            }).collect(Collectors.toList());
            dto.setFullMinusFlexiblePricing(list);
        }
        // 打折灵活定价
        if (!CollectionUtils.isEmpty(req.getDiscountFlexiblePricingList())) {
            List<SignupDiscountFlexiblePricing> list = req.getDiscountFlexiblePricingList().stream().map( o -> {
                SignupDiscountFlexiblePricing price = new SignupDiscountFlexiblePricing();
                price.setDays(o.getDays());
                price.setDiscount(o.getDiscount());
                price.setMinDiscount(o.getMinDiscount());
                price.setMaxDiscount(o.getMaxDiscount());
                return price;
            }).collect(Collectors.toList());
            dto.setDiscountFlexiblePricing(list);
        }
        // 当前用户
        dto.setCurrentUser(req.getCurrentUser());
        return dto;
    }
}
