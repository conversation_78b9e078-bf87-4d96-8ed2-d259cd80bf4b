package com.saicmobility.evcard.md.act.dto.proprietary;

import com.alibaba.fastjson.annotation.JSONField;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.constant.BusinessConst;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.dto.market.TimeRange;
import com.saicmobility.evcard.md.act.enums.market.DiscountLatitudeEnum;
import com.saicmobility.evcard.md.act.service.extern.ConfigLoader;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.mdactservice.api.AddProprietaryActivityReq;
import com.saicmobility.evcard.md.mdactservice.api.CurrentUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/3 10:40
 */
@Data
@Slf4j
public class AddProprietaryActivityDto {
    private String activityName;//活动名称
    private String activityTag; //活动标签
    private List<String> orgCodes;//机构列表
    private List<Long> vehicleModelIds;//车型id列表
    private int allOrgCodes; //全部机构  1-全部  2-不是全部
    private int allModelIds;////全部车型  1-全部  2-不是全部
    private int activityType; //活动类型：1-满减、2-打折
    private int pricingType;//定价类型：1-灵活定价、2-规范定价
    private int discountLatitude; //优惠纬度：1-车辆租金
    private List<FullMinusStandardPricing> fullMinusStandardPricing;//满减规范定价
    private List<FullMinusFlexiblePricing> fullMinusFlexiblePricing;//满减灵活定价
    private List<DiscountStandardPricing> discountStandardPricing;//打折规范定价
    private List<DiscountFlexiblePricing> discountFlexiblePricing;//打折灵活定价
    private int minRentDays; //最小租期
    private int maxRentDays; //最大租期
    private int availableOnHolidays; //节假日是否可用：1-可用、2-不可用
    private LocalDate signUpStartDate; //报名开始时间 yyyy-MM-dd
    private LocalDate signUpEndDate; //报名结束时间 yyyy-MM-dd
    private LocalDate pickUpDate; //取车时间 yyyy-MM-dd
    private LocalDate returnDate; //还车时间 yyyy-MM-dd
    private LocalDate activityStartDate; //活动开始时间 yyyy-MM-dd
    private LocalDate activityEndDate; //活动结束时间 yyyy-MM-dd
    private List<TimeRange> unavailableDateRanges; //不可用时间范围
    private String signUpOrgCodes;//报名机构集合
    private Integer sameDayUseFlag;//仅限下单当日取车  0-非当日使用 1-当日使用
    private int allStore;//全部门店  1-全部  2-不是全部
    private List<Long> storeIdList;//门店id列表

    private int specifyDateFlag;//是否指定下单日期 0-不限制 1-限制
    private String specifyDate;//指定日期，格式以数字，英文分号分割。空代表未选择： 1;3-周一，周三
    private int blockHolidayFlag; //屏蔽节假日 0-不屏蔽 1-屏蔽
    private Integer intersectionFlag; //取还车时间交集开关，1=取还车都必须在时间范围内 2=取还车任一时间在时间范围内

    @JSONField(serialize = false)
    private CurrentUser currentUser; // 当前用户

    /**
     * req->dto 入参为空校验
     * 日期范围校验
     * 不可用时间在取车时间范围内
     * @param req
     * @return
     */
    public AddProprietaryActivityDto parse(AddProprietaryActivityReq req) throws BusinessException {
        AddProprietaryActivityDto dto = new AddProprietaryActivityDto();

        //activityName
        if (StringUtils.isEmpty(req.getActivityName())) {
            log.error("活动名称为空，请输入");
            throw new BusinessException(ErrorEnum.INVALID_ACTNAME.getCode(), ErrorEnum.INVALID_ACTNAME.getMsg());
        }
        if (req.getActivityName().length() > 100) {
            throw new BusinessException(ErrorEnum.CHAR_TOO_LONG.getCode(), ErrorEnum.CHAR_TOO_LONG.getMsg());
        }
        dto.setActivityName(req.getActivityName());
        //activityTag
        if (StringUtils.isEmpty(req.getActivityTag())) {
            log.error("活动标签为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_ACTIVITY_TAG.getCode(), ErrorEnum.EMPTY_ACTIVITY_TAG.getMsg());
        }
        if (req.getActivityTag().length() > 100) {
            throw new BusinessException(ErrorEnum.CHAR_TOO_LONG.getCode(), ErrorEnum.CHAR_TOO_LONG.getMsg());
        }
        dto.setActivityTag(req.getActivityTag());
        //orgCodes
      /*   dto.setAllOrgCodes(req.getAllOrgCodes());
        //全部机构  1-全部  2-不是全部
       if(req.getAllOrgCodes() != 1){
            if (CollectionUtils.isEmpty(req.getOrgCodesList())) {
                log.error("参与机构为空，请输入");
                throw new BusinessException(ErrorEnum.INVALID_ORGCODE.getCode(), ErrorEnum.INVALID_ORGCODE.getMsg());
            }
            dto.setOrgCodes(req.getOrgCodesList());
        }*/
        //vehicleModelIds
        dto.setAllModelIds(req.getAllModelIds());
        if(req.getAllModelIds() != 1){
            if (CollectionUtils.isEmpty(req.getVehicleModelIdsList())) {
                log.error("车型id列表为空，请输入");
                throw new BusinessException(ErrorEnum.EMPTY_VEHICLE_MODEL_IDS.getCode(), ErrorEnum.EMPTY_VEHICLE_MODEL_IDS.getMsg());
            }
            dto.setVehicleModelIds(req.getVehicleModelIdsList());
        }
        dto.setAllStore(req.getAllStore());
        if(dto.getAllStore() != 1 ){
            if(CollectionUtils.isEmpty(req.getStoreIdListList())){
                log.error("门店id列表为空，请输入");
                throw new BusinessException(ErrorEnum.EMPTY_STORE_IDS.getCode(), ErrorEnum.EMPTY_STORE_IDS.getMsg());
            }
            dto.setStoreIdList(req.getStoreIdListList());
        }
        //活动类型
        if (!BusinessConst.ALL_PROPRIETARY_ACTIVITY_TYPE.contains(req.getActivityType())) {
            log.error("活动类型不合法，请检查，activityType = {}", req.getActivityType());
            throw new BusinessException(ErrorEnum.INVALID_ACTTYPE.getCode(), ErrorEnum.INVALID_ACTTYPE.getMsg());
        }
        dto.setActivityType(req.getActivityType());
        //定价类型
        if (!BusinessConst.ALL_PROPRIETARY_PRICING_TYPE.contains(req.getPricingType())) {
            log.error("定价类型不合法，请检查，pricingType = {}", req.getPricingType());
            throw new BusinessException(ErrorEnum.INVALID_PRICING_TYPE.getCode(), ErrorEnum.INVALID_PRICING_TYPE.getMsg());
        }
        dto.setPricingType(req.getPricingType());
        //优惠纬度
        if (!DiscountLatitudeEnum.CARRENT.getType().equals(req.getDiscountLatitude())) {
            log.error("优惠纬度不合法，请检查，discountLatitude = {}", req.getDiscountLatitude());
            throw new BusinessException(ErrorEnum.INVALID_DIS_LATITUDE.getCode(), ErrorEnum.INVALID_DIS_LATITUDE.getMsg());
        }
        dto.setDiscountLatitude(req.getDiscountLatitude());

        //满减灵活定价
        if(req.getActivityType() == 1 && req.getPricingType() == 1){
            //fullMinusFlexiblePricing
            if (CollectionUtils.isEmpty(req.getFullMinusFlexiblePricingList())) {
                log.error("满减灵活定价为空，请输入");
                throw new BusinessException(ErrorEnum.EMPTY_FULL_MINUS_FLEXIBLE_PRICING.getCode(), ErrorEnum.EMPTY_FULL_MINUS_FLEXIBLE_PRICING.getMsg());
            }
            List<FullMinusFlexiblePricing> list = req.getFullMinusFlexiblePricingList().stream().map(item ->{
                FullMinusFlexiblePricing fullMinusFlexiblePricing = new FullMinusFlexiblePricing();
                fullMinusFlexiblePricing.setDays(item.getDays());
                fullMinusFlexiblePricing.setMinDiscountAmount(item.getMinDiscountAmount());
                fullMinusFlexiblePricing.setMaxDiscountAmount(item.getMaxDiscountAmount());
                return  fullMinusFlexiblePricing;
            }).collect(Collectors.toList());
            for (FullMinusFlexiblePricing item: list){
                if(StringUtils.equals(item.getDays(),"0")){
                    log.error("天数不合法，必须大于0");
                    throw new BusinessException(ErrorEnum.INVALID_ZERO_DAYS.getCode(), ErrorEnum.INVALID_ZERO_DAYS.getMsg());
                }
            }
            if(list.size() > 1){
                list.sort(Comparator.comparing(o -> Integer.parseInt(o.getDays()))); // 根据天数从小到大排序（升序）
                for (int i = 0; i < list.size()-1; i++) {
                    //天数有相同的
                    if(StringUtils.equals(list.get(i).getDays(),list.get(i+1).getDays())){
                        log.error("存在天数相同的数据");
                        throw new BusinessException(ErrorEnum.INVALID_SAME_DAYS.getCode(), ErrorEnum.INVALID_SAME_DAYS.getMsg());
                    }
                    //最小优惠金额 后一项必须大于等于前一项
                    if(new BigDecimal(list.get(i).getMinDiscountAmount()).compareTo(new BigDecimal(list.get(i+1).getMinDiscountAmount())) > 0){
                        log.error("满减灵活定价最小优惠金额，后一项必须大于等于前一项");
                        throw new BusinessException(ErrorEnum.INVALID_DISCOUNT_AMOUNT.getCode(), ErrorEnum.INVALID_DISCOUNT_AMOUNT.getMsg());
                    }
                }
            }
            dto.setFullMinusFlexiblePricing(list);
        }
        //满减规范定价
        if(req.getActivityType() == 1 && req.getPricingType() == 2){
            //fullMinusStandardPricing
            if (CollectionUtils.isEmpty(req.getFullMinusStandardPricingList())) {
                log.error("满减规范定价为空，请输入");
                throw new BusinessException(ErrorEnum.EMPTY_FULL_MINUS_STANDARD_PRICING.getCode(), ErrorEnum.EMPTY_FULL_MINUS_STANDARD_PRICING.getMsg());
            }
            List<FullMinusStandardPricing> list = req.getFullMinusStandardPricingList().stream().map(item ->{
                FullMinusStandardPricing fullMinusStandardPricing = new FullMinusStandardPricing();
                fullMinusStandardPricing.setDays(item.getDays());
                fullMinusStandardPricing.setDiscountAmount(item.getDiscountAmount());
                return  fullMinusStandardPricing;
            }).collect(Collectors.toList());
            for (FullMinusStandardPricing item: list){
                if(StringUtils.equals(item.getDays(),"0")){
                    log.error("天数不合法，必须大于0");
                    throw new BusinessException(ErrorEnum.INVALID_ZERO_DAYS.getCode(), ErrorEnum.INVALID_ZERO_DAYS.getMsg());
                }
            }
            if(list.size() > 1){
                list.sort(Comparator.comparing(o -> Integer.parseInt(o.getDays()))); // 根据天数从小到大排序（升序）
                for (int i = 0; i < list.size()-1; i++) {
                    //天数有相同的
                    if(StringUtils.equals(list.get(i).getDays(),list.get(i+1).getDays())){
                        log.error("存在天数相同的数据");
                        throw new BusinessException(ErrorEnum.INVALID_SAME_DAYS.getCode(), ErrorEnum.INVALID_SAME_DAYS.getMsg());
                    }
                    //优惠金额 后一项必须大于等于前一项
                    if(new BigDecimal(list.get(i).getDiscountAmount()).compareTo(new BigDecimal(list.get(i+1).getDiscountAmount())) > 0){
                        log.error("满减规范定价优惠金额，后一项必须大于等于前一项");
                        throw new BusinessException(ErrorEnum.INVALID_DISCOUNT_AMOUNT.getCode(), ErrorEnum.INVALID_DISCOUNT_AMOUNT.getMsg());
                    }
                }
            }
            dto.setFullMinusStandardPricing(list);
        }
        //打折灵活定价
        if(req.getActivityType() == 2 && req.getPricingType() == 1){
            //discountFlexiblePricing
            if (CollectionUtils.isEmpty(req.getDiscountFlexiblePricingList())) {
                log.error("打折灵活定价为空，请输入");
                throw new BusinessException(ErrorEnum.EMPTY_DISCOUNT_FLEXIBLE_PRICING.getCode(), ErrorEnum.EMPTY_DISCOUNT_FLEXIBLE_PRICING.getMsg());
            }
            List<DiscountFlexiblePricing> list = req.getDiscountFlexiblePricingList().stream().map(item ->{
                DiscountFlexiblePricing discountFlexiblePricing = new DiscountFlexiblePricing();
                discountFlexiblePricing.setDays(item.getDays());
                discountFlexiblePricing.setMinDiscount(item.getMinDiscount());
                discountFlexiblePricing.setMaxDiscount(item.getMaxDiscount());
                return  discountFlexiblePricing;
            }).collect(Collectors.toList());
            for (DiscountFlexiblePricing item: list){
                if(StringUtils.equals(item.getDays(),"0")){
                    log.error("天数不合法，必须大于0");
                    throw new BusinessException(ErrorEnum.INVALID_ZERO_DAYS.getCode(), ErrorEnum.INVALID_ZERO_DAYS.getMsg());
                }
            }
            if(list.size() > 1){
                list.sort(Comparator.comparing(o -> Integer.parseInt(o.getDays()))); // 根据天数从小到大排序（升序）
                for (int i = 0; i < list.size()-1; i++) {
                    //天数有相同的
                    if(StringUtils.equals(list.get(i).getDays(),list.get(i+1).getDays())){
                        log.error("存在天数相同的数据");
                        throw new BusinessException(ErrorEnum.INVALID_SAME_DAYS.getCode(), ErrorEnum.INVALID_SAME_DAYS.getMsg());
                    }
                    //最小折扣 后一项必须小于等于前一项
                    if(new BigDecimal(list.get(i).getMinDiscount()).compareTo(new BigDecimal(list.get(i+1).getMinDiscount())) < 0){
                        log.error("打折灵活定价最小折扣，后一项必须小于等于前一项");
                        throw new BusinessException(ErrorEnum.INVALID_DISCOUNT.getCode(), ErrorEnum.INVALID_DISCOUNT.getMsg());
                    }
                }
            }
            dto.setDiscountFlexiblePricing(list);
        }
        //打折规范定价
        if(req.getActivityType() == 2 && req.getPricingType() == 2){
            //discountStandardPricing
            if (CollectionUtils.isEmpty(req.getDiscountStandardPricingList())) {
                log.error("打折规范定价为空，请输入");
                throw new BusinessException(ErrorEnum.EMPTY_DISCOUNT_STANDARD_PRICING.getCode(), ErrorEnum.EMPTY_DISCOUNT_STANDARD_PRICING.getMsg());
            }
            List<DiscountStandardPricing> list = req.getDiscountStandardPricingList().stream().map(item ->{
                DiscountStandardPricing discountStandardPricing = new DiscountStandardPricing();
                discountStandardPricing.setDays(item.getDays());
                discountStandardPricing.setDiscount(item.getDiscount());
                return  discountStandardPricing;
            }).collect(Collectors.toList());
            for (DiscountStandardPricing item: list){
                if(StringUtils.equals(item.getDays(),"0")){
                    log.error("天数不合法，必须大于0");
                    throw new BusinessException(ErrorEnum.INVALID_ZERO_DAYS.getCode(), ErrorEnum.INVALID_ZERO_DAYS.getMsg());
                }
            }
            if(list.size() > 1){
                list.sort(Comparator.comparing(o -> Integer.parseInt(o.getDays()))); // 根据天数从小到大排序（升序）
                for (int i = 0; i < list.size()-1; i++) {
                    //天数有相同的
                    if(StringUtils.equals(list.get(i).getDays(),list.get(i+1).getDays())){
                        log.error("存在天数相同的数据");
                        throw new BusinessException(ErrorEnum.INVALID_SAME_DAYS.getCode(), ErrorEnum.INVALID_SAME_DAYS.getMsg());
                    }
                    //最小折扣 后一项必须小于等于前一项
                    if(new BigDecimal(list.get(i).getDiscount()).compareTo(new BigDecimal(list.get(i+1).getDiscount())) < 0){
                        log.error("打折规范定价最小折扣，后一项必须小于等于前一项");
                        throw new BusinessException(ErrorEnum.INVALID_DISCOUNT.getCode(), ErrorEnum.INVALID_DISCOUNT.getMsg());
                    }
                }
            }
            dto.setDiscountStandardPricing(list);
        }

        //最小租期
        if (req.getMinRentDays() < 0) {
            log.error("最小租期有误，请输入");
//            throw new BusinessException(ErrorEnum.EMPTY_MIN_RENT_DAYS.getCode(), ErrorEnum.EMPTY_MIN_RENT_DAYS.getMsg());
            throw new BusinessException(ErrorEnum.ZERO_RENT_DAYS_ERROR.getCode(), ErrorEnum.ZERO_RENT_DAYS_ERROR.getMsg());
        }
        dto.setMinRentDays(req.getMinRentDays());
        //最大租期
        if (req.getMaxRentDays() <0){
            log.error("最大租期为小于0的整数，输入有误");
            throw new BusinessException(ErrorEnum.ZERO_RENT_DAYS_ERROR.getCode(), ErrorEnum.ZERO_RENT_DAYS_ERROR.getMsg());
        }else if (req.getMaxRentDays() > 0 && req.getMaxRentDays() < req.getMinRentDays()){
            log.error("最大租期小于最小租期，输入有误");
            throw new BusinessException(ErrorEnum.MAX_RENT_DAYS_COMPARE_ERROR.getCode(), ErrorEnum.MAX_RENT_DAYS_COMPARE_ERROR.getMsg());
        }
        dto.setMaxRentDays(req.getMaxRentDays());
        //节假日是否可用
        if (!BusinessConst.ALL_HOLIDAYS_TYPE.contains(req.getAvailableOnHolidays())) {
            log.error("节假日是否可用类型不合法，请检查，availableOnHolidays = {}", req.getAvailableOnHolidays());
            throw new BusinessException(ErrorEnum.INVALID_HOLIDAYS_TYPE.getCode(), ErrorEnum.INVALID_HOLIDAYS_TYPE.getMsg());
        }
        dto.setAvailableOnHolidays(req.getAvailableOnHolidays());

        //报名开始时间、报名结束时间
        if (StringUtils.isEmpty(req.getSignUpStartDate())) {
            log.error("报名开始时间为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_SIGN_UP_START_DATE.getCode(), ErrorEnum.EMPTY_SIGN_UP_START_DATE.getMsg());
        }
        LocalDate signUpStartDate = DateUtil.getLocalDateFromStr(req.getSignUpStartDate(), DateUtil.DATE_TYPE5);
        dto.setSignUpStartDate(signUpStartDate);
        if (StringUtils.isEmpty(req.getSignUpEndDate())) {
            log.error("报名结束时间为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_SIGN_UP_END_DATE.getCode(), ErrorEnum.EMPTY_SIGN_UP_END_DATE.getMsg());
        }
        LocalDate signUpEndDate = DateUtil.getLocalDateFromStr(req.getSignUpEndDate(), DateUtil.DATE_TYPE5);
        dto.setSignUpEndDate(signUpEndDate);
        if (signUpStartDate.compareTo(signUpEndDate) > 0) {
            log.error("报名结束时间小于报名开始时间, 请检查, signUpStartDate= {}, signUpEndDate = {}", req.getSignUpStartDate(), req.getSignUpEndDate());
            throw new BusinessException(ErrorEnum.DATE_ERROR.getCode(), ErrorEnum.DATE_ERROR.getMsg());
        }
        String curStr = DateUtil.dateToString(new Date(), DateUtil.DATE_TYPE5);
        LocalDate curDate = DateUtil.getLocalDateFromStr(curStr, DateUtil.DATE_TYPE5);
        // 当前日期 <= 报名结束日期
        if (signUpEndDate.compareTo(curDate) < 0) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "报名结束时间小于当前时间");
        }

        //取车时间 还车时间
        if (StringUtils.isEmpty(req.getPickUpDate())) {
            log.error("取车时间为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_PICK_UP_DATE.getCode(), ErrorEnum.EMPTY_PICK_UP_DATE.getMsg());
        }
        LocalDate pickUpDate = DateUtil.getLocalDateFromStr(req.getPickUpDate(), DateUtil.DATE_TYPE5);
        dto.setPickUpDate(pickUpDate);
        if (StringUtils.isEmpty(req.getReturnDate())) {
            log.error("还车时间为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_RETURN_DATE.getCode(), ErrorEnum.EMPTY_RETURN_DATE.getMsg());
        }
        LocalDate returnDate = DateUtil.getLocalDateFromStr(req.getReturnDate(), DateUtil.DATE_TYPE5);
        dto.setReturnDate(returnDate);
        if (pickUpDate.compareTo(returnDate) > 0) {
            log.error("还车时间小于取车时间, 请检查, pickUpDate= {}, returnDate = {}", req.getPickUpDate(), req.getReturnDate());
            throw new BusinessException(ErrorEnum.DATE_ERROR.getCode(), ErrorEnum.DATE_ERROR.getMsg());
        }
        // 当前日期 <= 还车时间日期
        if (returnDate.compareTo(curDate) < 0) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "还车时间小于当前时间");
        }

        //活动开始时间、活动结束时间
        if (StringUtils.isEmpty(req.getActivityStartDate())) {
            log.error("活动开始时间为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_ACT_START_DATE.getCode(), ErrorEnum.EMPTY_ACT_START_DATE.getMsg());
        }
        LocalDate activityStartDate = DateUtil.getLocalDateFromStr(req.getActivityStartDate(), DateUtil.DATE_TYPE5);
        dto.setActivityStartDate(activityStartDate);
        if (StringUtils.isEmpty(req.getActivityEndDate())) {
            log.error("活动结束时间为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_ACT_END_DATE.getCode(), ErrorEnum.EMPTY_ACT_END_DATE.getMsg());
        }
        LocalDate activityEndDate = DateUtil.getLocalDateFromStr(req.getActivityEndDate(), DateUtil.DATE_TYPE5);
        dto.setActivityEndDate(activityEndDate);
        if (activityStartDate.compareTo(activityEndDate) > 0) {
            log.error("活动结束时间小于活动开始时间, 请检查, activityStartDate= {}, activityEndDate = {}", req.getActivityStartDate(), req.getActivityEndDate());
            throw new BusinessException(ErrorEnum.DATE_ERROR.getCode(), ErrorEnum.DATE_ERROR.getMsg());
        }
        // 当前日期 <= 活动结束日期
        if (activityEndDate.compareTo(curDate) < 0) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "活动结束时间小于当前时间");
        }
        //是否仅限下单当日取车
//        if (req.getSameDayUseFlag() == null){
//            throw new BusinessException(ErrorEnum.EMPTY_SAME_DAY_USE_FLAG_ERROR.getCode(), ErrorEnum.EMPTY_SAME_DAY_USE_FLAG_ERROR.getMsg());
//        }
        dto.setSameDayUseFlag(req.getSameDayUseFlag());
        //不可用时间范围
        dto.setUnavailableDateRanges(toTimeRangeList(req.getUnavailableDateRangesList(), pickUpDate, returnDate));

        dto.setCurrentUser(req.getCurrentUser());

        dto.setSpecifyDateFlag(req.getSpecifyDateFlag());
        dto.setSpecifyDate(req.getSpecifyDate());
        dto.setBlockHolidayFlag(req.getBlockHolidayFlag());
        dto.setIntersectionFlag(req.getIntersectionFlag());
        return dto;
    }

    /**
     * 将自动生成的TimeRange列表转成自定义的TimeRange列表
     * 列表长度不超过10
     * 不可用时间要在取车时间范围内
     *
     * @param unavailableDateRangesList
     * @return
     */
    private List<TimeRange> toTimeRangeList(List<com.saicmobility.evcard.md.mdactservice.api.TimeRange> unavailableDateRangesList, LocalDate pickUpDate, LocalDate returnDate) throws BusinessException {
        if (unavailableDateRangesList.size() > 10) {
            log.error("不可用时间不可超过10条，size = {}", unavailableDateRangesList.size());
            throw new BusinessException(ErrorEnum.OVER_SIZE_UNAVAILABLE_LIST.getCode(), ErrorEnum.OVER_SIZE_UNAVAILABLE_LIST.getMsg());
        }
        List<TimeRange> list = new ArrayList<>();
        for (com.saicmobility.evcard.md.mdactservice.api.TimeRange timeRange : unavailableDateRangesList) {
            LocalDate startDate = DateUtil.getLocalDateFromStr(timeRange.getStartDate(), DateUtil.DATE_TYPE5);
            LocalDate endDate = DateUtil.getLocalDateFromStr(timeRange.getEndDate(), DateUtil.DATE_TYPE5);
            // 该条不可用时间数据的合法性
            if (startDate.compareTo(endDate) > 0) {
                log.error("该条不可用结束时间小于开始时间, 请检查, startDate= {}, endDate = {}", timeRange.getStartDate(), timeRange.getEndDate());
                throw new BusinessException(ErrorEnum.DATE_ERROR.getCode(), ErrorEnum.DATE_ERROR.getMsg());
            }
            // 该条不可用时间是否在取车时间范围内
            if (startDate.compareTo(pickUpDate) < 0 || startDate.compareTo(returnDate) > 0 || endDate.compareTo(pickUpDate) < 0 || endDate.compareTo(returnDate) > 0) {
                log.error("该不可用时间超出取车时间范围");
                throw new BusinessException(ErrorEnum.UNAVAILABLETIME_OVER.getCode(), ErrorEnum.UNAVAILABLETIME_OVER.getMsg());
            }
            TimeRange tm = new TimeRange();
            tm.setStartDate(timeRange.getStartDate());
            tm.setEndDate(timeRange.getEndDate());
            list.add(tm);
        }
        return list;
    }

    public String toStringFromList(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }

        String str = "";
        for (Long num : list) {
            str += String.valueOf(num) + ",";
        }
        return str.substring(0, str.length() - 1);
    }

    public String toStringFromStrList(List<String> list) {
        String str = "";
        for (String str1 : list) {
            str += str1 + ",";
        }
        return str.substring(0, str.length() - 1);
    }
}
