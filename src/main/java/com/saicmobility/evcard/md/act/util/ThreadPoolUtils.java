package com.saicmobility.evcard.md.act.util;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池工具类
 * <AUTHOR>
 * @since 2017/6/21
 */
public class ThreadPoolUtils {

    /** 自定义线程池 */
    public static final ExecutorService executor =  new ThreadPoolExecutor(4, 9, 60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(5),new ThreadPoolExecutor.CallerRunsPolicy());

}
