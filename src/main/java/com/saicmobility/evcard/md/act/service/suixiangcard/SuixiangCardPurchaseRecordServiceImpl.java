package com.saicmobility.evcard.md.act.service.suixiangcard;

import com.saicmobility.evcard.md.act.entity.iss.SuixiangCardPurchaseRecord;
import com.saicmobility.evcard.md.act.mapper.iss.SuixiangCardPurchaseRecordMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saicmobility.evcard.md.act.service.suixiangcard.ISuixiangCardPurchaseRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 随享卡购买记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Service
public class SuixiangCardPurchaseRecordServiceImpl extends ServiceImpl<SuixiangCardPurchaseRecordMapper, SuixiangCardPurchaseRecord> implements ISuixiangCardPurchaseRecordService {

}
