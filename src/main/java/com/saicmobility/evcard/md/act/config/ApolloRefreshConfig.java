package com.saicmobility.evcard.md.act.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigChangeListener;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.saicmobility.evcard.md.act.service.coupun.IChannelCouponManagerService;
import com.saicmobility.evcard.md.act.service.impl.coupon.channel.ccb.ICCBCouponManagerService;
import com.saicmobility.evcard.md.act.util.RedisLock;
import com.saicmobility.evcard.md.act.util.RedisLockUtil;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
@Slf4j
public class ApolloRefreshConfig {

    @ApolloConfig
    private Config config;

    @Autowired
    private IChannelCouponManagerService ccbCouponManagerServiceImpl;

    @Autowired
    private RedisLockUtil redisLockUtil;

    @ApolloConfigChangeListener(interestedKeys = {"ccb.mmpThirdCoupon.id"})
    public void listenerCCBMmpThirdCouponId(ConfigChangeEvent changeEvent) {
        ConfigChange change = changeEvent.getChange("ccb.mmpThirdCoupon.id");
        String oldValue = change.getOldValue();
        String newValue = change.getNewValue();
        log.info("tid:{}, apollo changed, oldValue:{}, newValue:{}", Trace.currentTraceId(), oldValue, newValue);
        if (StringUtils.isNotBlank(newValue)) {
            String[] newValues = newValue.split(",");
            List<String> oldValues = StringUtils.isNotBlank(oldValue) ? Arrays.asList(oldValue.split(",")) : new ArrayList<>();
            for (String value : newValues) {
                if (!oldValues.contains(value)) {
                    RedisLock apolloInitRedisKeyLock = null;
                    // 多台机器只初始化一次
                    try {
                        long mmpThirdCouponId = Long.parseLong(value);
                        apolloInitRedisKeyLock = redisLockUtil.acquireLock2(ccbCouponManagerServiceImpl.getApolloInitRedisKey(mmpThirdCouponId), 60 * 1000);
                        ccbCouponManagerServiceImpl.initCouponStock(mmpThirdCouponId);
                    } catch (Exception e) {
                        log.warn("tid:{}, initCouponStock error, mmpThirdCouponId:{}", Trace.currentTraceId(), value, e);
                    } finally {
                        if (apolloInitRedisKeyLock != null) {
                            redisLockUtil.releaseLock(apolloInitRedisKeyLock);
                        }
                    }
                }
            }
        }
    }

}
