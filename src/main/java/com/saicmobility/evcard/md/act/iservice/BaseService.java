package com.saicmobility.evcard.md.act.iservice;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;

import javax.validation.constraints.NotEmpty;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface BaseService<T> extends IService<T> {
    boolean deleteLogic(@NotEmpty List<Long> ids, UserDTO user, Date dateTime);

    boolean changeStatus(@NotEmpty List<Long> ids, Integer status, UserDTO user, Date dateTime);

    boolean save(T entity, UserDTO user, Date dateTime);

    boolean updateById(T entity, UserDTO user, Date dateTime);

    boolean update(T entity, UserDTO user, Date dateTime, Wrapper<T> updateWrapper);

    boolean saveBatch(Collection<T> entityList, UserDTO user, Date dateTime);
    boolean saveBatch(Collection<T> entityList, int batchSize, UserDTO user, Date dateTime);
}

