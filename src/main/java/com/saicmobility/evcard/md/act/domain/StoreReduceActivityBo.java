package com.saicmobility.evcard.md.act.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.saicmobility.evcard.md.act.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店参与活动信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StoreReduceActivityBo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "运营公司id")
    private String orgCode;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "报名截止时间")
    private String signUpDeadline;

    @ApiModelProperty(value = "生效开始时间")
    private String activityStartTime;

    @ApiModelProperty(value = "生效结束时间")
    private String activityEndTime;

    @ApiModelProperty(value = "用户注册开始时间")
    private String registerStartTime;

    @ApiModelProperty(value = "用户注册结束时间")
    private String registerEndTime;

    @ApiModelProperty(value = "状态 1 待生效 2 生效中 3 已下线")
    private Integer activityStatus;

    @ApiModelProperty(value = "商品车型id,已参与活动接口返回")
    private Long goodsModelId;

    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @ApiModelProperty(value = "门店运营机构id")
    private String operOrgCode;
}
