package com.saicmobility.evcard.md.act.service.rest.entity.activity;

import com.extracme.evcard.activity.dto.ThirdCouponOfferDto;
import com.saicmobility.evcard.md.mdactservice.api.OfferThirdCouponsReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class OfferThirdCouponRequest extends ThirdCouponOfferDto {

    public static OfferThirdCouponRequest fromRes(OfferThirdCouponsReq req){
        OfferThirdCouponRequest request = new OfferThirdCouponRequest();
        request.setThirdCouponId(req.getActivityCouponId());
        request.setOrgId(req.getOrgCode());
        request.setOriginRefSeq(req.getOriginRefSeq());
        request.setOptUser(req.getOptUser());
        request.setCouponName(req.getCouponName());
        //此场景仅允许发放一张优惠券
        request.setOfferQuantity(1);
        request.setStartDate(req.getStartDate());
        request.setExpiresDate(req.getExpiresDate());
        return request;
    }

}
