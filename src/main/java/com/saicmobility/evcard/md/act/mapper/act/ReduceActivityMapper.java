package com.saicmobility.evcard.md.act.mapper.act;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.saicmobility.evcard.md.act.domain.ReduceActivityBo;
import com.saicmobility.evcard.md.act.domain.ReduceActivityNameBo;
import com.saicmobility.evcard.md.act.entity.ReduceActivity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 立减活动 Mapper
 */
public interface ReduceActivityMapper extends BaseMapper<ReduceActivity> {

    Page<ReduceActivityBo> queryReduceActivity(@Param("orgCode") String orgCode,
                                               @Param("activityStatus") Integer activityStatus,
                                               @Param("activityName") String activityName,
                                               @Param("activityId") Long activityId,
                                               Page<ReduceActivity> page);

    ReduceActivityBo getByActivityId(@Param("activityId") Long activityId);

    List<ReduceActivityNameBo> searchReduceActivityName();

    List<ReduceActivityBo> getAllReduceActivity();

    /**
     *查询下线状态(未手动下线的)的立减活动
     * @return
     */
    List<ReduceActivityBo> queryOfflineReduceActivity();


}
