package com.saicmobility.evcard.md.act.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.adapter.QingluAdapter;
import com.saicmobility.evcard.md.act.adapter.dto.MarketStoreRequest;
import com.saicmobility.evcard.md.act.adapter.dto.MarketUnserviceableTimeRequest;
import com.saicmobility.evcard.md.act.adapter.dto.MarketingCampaignInfoRequest;
import com.saicmobility.evcard.md.act.adapter.dto.VehicleModelRequest;
import com.saicmobility.evcard.md.act.bo.market.*;
import com.saicmobility.evcard.md.act.constant.BusinessConst;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.domain.QlSecondChannelBo;
import com.saicmobility.evcard.md.act.dto.external.StoreStoreVehicleModelInfoBo;
import com.saicmobility.evcard.md.act.dto.external.StoreVehicleModelInfoBo;
import com.saicmobility.evcard.md.act.dto.market.*;
import com.saicmobility.evcard.md.act.entity.ChannelActivity;
import com.saicmobility.evcard.md.act.enums.ConfigStateEnum;
import com.saicmobility.evcard.md.act.enums.IsDeletedEnum;
import com.saicmobility.evcard.md.act.enums.market.*;
import com.saicmobility.evcard.md.act.enums.proprietary.ActivityStatusEnum;
import com.saicmobility.evcard.md.act.manager.MarketingModuleManager;
import com.saicmobility.evcard.md.act.mapper.act.ChannelActivityMapper;
import com.saicmobility.evcard.md.act.service.ExternalSystemFacade;
import com.saicmobility.evcard.md.act.service.MarketingModuleService;
import com.saicmobility.evcard.md.act.service.extern.ConfigLoader;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.act.util.Md5Utils;
import com.saicmobility.evcard.md.mdstockservice.api.MdStockService;
import com.saicmobility.evcard.md.mdstockservice.api.QueryStoreVehicleModelByStoreIdReq;
import com.saicmobility.evcard.md.mdstockservice.api.StoreStoreVehicleModelInfo;
import com.saicmobility.evcard.md.mdstockservice.api.StoreVehicleModelInfo;
import com.saicmobility.evcard.md.mdstoreservice.api.GetChannelSaasStoreListReq;
import com.saicmobility.evcard.md.mdstoreservice.api.GetChannelSaasStoreListRes;
import com.saicmobility.evcard.md.mdstoreservice.api.MdStoreService;
import com.saicmobility.evcard.md.mdstoreservice.api.SaasStoreInfo;
import com.saicmobility.evcard.md.mduserservice.api.MdUserService;
import com.saicmobility.evcard.md.mduserservice.api.QueryBySecondChannelReq;
import com.saicmobility.evcard.md.mduserservice.api.QueryBySecondChannelRes;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MarketingModuleServiceImpl implements MarketingModuleService {

    @Resource
    private ChannelActivityMapper channelActivityMapper;

    @Resource
    private MarketingModuleManager marketingModuleManager;

    @Resource
    private ConfigLoader configLoader;

    @Resource
    private QingluAdapter qingluAdapter;

    @Resource
    private ExternalSystemFacade externalSystemFacade;

    @Resource
    private MdStockService mdStockService;

    @Resource
    private MdStoreService mdStoreService;

    @Autowired
    private MdUserService mdUserService;

    @Override
    public void addChannelAct(AddChannelActDto dto) throws BusinessException {
        //入参合法校验
        if (StringUtils.isEmpty(dto.getAppKey())) {//channel
            log.error("二级渠道为空，请重新输入，secondAppKey = {}", dto.getAppKey());
            throw new BusinessException(ErrorEnum.INVALID_APPKEY.getCode(), ErrorEnum.INVALID_APPKEY.getMsg());
        }
        if (dto.getActName().length() > 100) {//act name
            log.error("活动名称长度超长，请重新输入，length = {}", dto.getActName().length());
            throw new BusinessException(ErrorEnum.INVALID_ACTNAME.getCode(), ErrorEnum.INVALID_ACTNAME.getMsg());
        }
        if (!BusinessConst.ALL_ACT_TYPE.contains(dto.getActType())) {//act type
            log.error("活动类型不合法，请重新输入，actType = {}", dto.getActType());
            throw new BusinessException(ErrorEnum.INVALID_ACTTYPE.getCode(), ErrorEnum.INVALID_ACTTYPE.getMsg());
        }
        if (!BusinessConst.ALL_DISCOUNT_LATITUDE.contains(dto.getDiscountLatitude())) {//discount latitude
            log.error("优惠纬度不合法，请重新输入，discountLatitude = {}", dto.getDiscountLatitude());
            throw new BusinessException(ErrorEnum.INVALID_DIS_LATITUDE.getCode(), ErrorEnum.INVALID_DIS_LATITUDE.getMsg());
        }
        if (!BusinessConst.ALL_DISCOUNT_METHOD.contains(dto.getDiscountMethod())) {//discount method
            log.error("优惠方式不合法，请重新输入，discountMethod = {}", dto.getDiscountMethod());
            throw new BusinessException(ErrorEnum.INVALID_DIS_METHOD.getCode(), ErrorEnum.INVALID_DIS_METHOD.getMsg());
        }
        if (!BusinessConst.ALL_COST_BEAR_PARTY.contains(dto.getCostBearingParty())) {//cost bear party
            log.error("成本承担方不合法，请重新输入, costBearParty = {}", dto.getCostBearingParty());
            throw new BusinessException(ErrorEnum.INVALID_COST_BEAR_PARTY.getCode(), ErrorEnum.INVALID_COST_BEAR_PARTY.getMsg());
        }
        //只有共同承担时需要成本分摊方式
        if (dto.getCostBearingParty() == CostBearingPartyEnum.TOGETHER.getType() && !BusinessConst.ALL_COST_ALLOCATION_METHOD.contains(dto.getCostAllocationMethod())) {//cost allocation
            log.error("成本分摊方式不合法，请重新输入, costAllocationMethod = {}", dto.getCostAllocationMethod());
            throw new BusinessException(ErrorEnum.INVALID_COST_ALLOCATION.getCode(), ErrorEnum.INVALID_COST_ALLOCATION.getMsg());
        }
        //优惠方式不合法
        if (!isValidDiscountMethod(dto.getActType(), dto.getDiscountMethod(), dto.getDiscountConditional1(), dto.getDiscountConditional2())) {
            log.error("优惠条件不合法,请检查 活动类型、优惠方式、优惠条件1、优惠条件2");
            throw new BusinessException(ErrorEnum.DISCOUNT_ERROR.getCode(), ErrorEnum.DISCOUNT_ERROR.getMsg());
        }

        String discountCode = dto.getDiscountCode();
        if ((!discountCode.startsWith("EV")) || discountCode.length() > 50) {
            log.error("优惠码不符合条件，discountCode={}", discountCode);
            throw new BusinessException(ErrorEnum.DISCOUNT_CODE_ILLEAGL.getCode(), ErrorEnum.DISCOUNT_CODE_ILLEAGL.getMsg());
        }

        // 判断新增营销活动的 二级渠道 下 是否有相同优惠码的   ；不允许
        List<ChannelActivity> channelActivities = channelActivityMapper.selectList(new LambdaQueryWrapper<ChannelActivity>()
                .eq(ChannelActivity::getSecondAppKey, dto.getAppKey())
                .eq(ChannelActivity::getDiscountCode, discountCode)
                .eq(ChannelActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
        );

        if (CollectionUtils.isNotEmpty(channelActivities)) {
            log.error("存在相同渠道、相同优惠码，discountCode={},channelActivities={}", discountCode, JSON.toJSONString(channelActivities));
            throw new BusinessException(ErrorEnum.DISCOUNT_CODE_REPEAT.getCode(), ErrorEnum.DISCOUNT_CODE_REPEAT.getMsg());
        }

        //判断该记录是否已经存在
        LambdaQueryWrapper<ChannelActivity> queryWrapper = new LambdaQueryWrapper<ChannelActivity>()
                .eq(ChannelActivity::getSecondAppKey, dto.getAppKey())
                .eq(ChannelActivity::getActivityName, dto.getActName())
                .eq(ChannelActivity::getOrgCodes, dto.toStringFromStrList(dto.getOrgCodes()))
                .eq(ChannelActivity::getStoreIds, dto.toStringFromList(dto.getStoreIds()))
                .eq(ChannelActivity::getCarModelIds, dto.toStringFromList(dto.getCatIds()))
                .eq(ChannelActivity::getActivityType, dto.getActType())
                .eq(ChannelActivity::getDiscountLatitude, dto.getDiscountLatitude())
                .eq(ChannelActivity::getDiscountMethod, dto.getDiscountMethod())
                .eq(ChannelActivity::getDiscountCondition1, dto.getDiscountConditional1())
                .eq(ChannelActivity::getDiscountCondition2, dto.getDiscountConditional2())
                .eq(ChannelActivity::getRestrictDiscounts, dto.getRestrictDiscounts())
                .eq(dto.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType(), ChannelActivity::getMaxDiscountAmount, dto.getMaxDiscountAmout())
                .eq(dto.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType(), ChannelActivity::getMaxRentDays, dto.getMaxRentDays())
                .eq(dto.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType(), ChannelActivity::getMinRentDays, dto.getMinRentDays())
                .eq(ChannelActivity::getActivityStartDate, dto.getActStartDate())
                .eq(ChannelActivity::getActivityEndDate, dto.getActEndDate())
                .eq(ChannelActivity::getPickUpStartDate, dto.getPickupStartDate())
                .eq(ChannelActivity::getPickUpEndDate, dto.getPickupEndDate())
                .eq(ChannelActivity::getReturnStartDate, dto.getReturnStartDate())
                .eq(ChannelActivity::getReturnEndDate, dto.getReturnEndDate())
                .eq(ChannelActivity::getUnavailableDateRanges, JSON.toJSONString(dto.getUnavailableDateRanges()))
                .eq(ChannelActivity::getCostBearingParty, dto.getCostBearingParty())
                .eq(dto.getCostBearingParty() == CostBearingPartyEnum.TOGETHER.getType(), ChannelActivity::getCostAllocationMethod, dto.getCostAllocationMethod())
                .eq(dto.getCostBearingParty() == CostBearingPartyEnum.TOGETHER.getType(), ChannelActivity::getMerchantBear, dto.getMerchantBear())
                .eq(dto.getCostBearingParty() == CostBearingPartyEnum.TOGETHER.getType(), ChannelActivity::getPlatformBear, dto.getPlatformBear())
                .eq(ChannelActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .eq(ChannelActivity::getDiscountCode, discountCode)
                .eq(ChannelActivity::getIntersectionFlag, dto.getIntersectionFlag());


        ChannelActivity activity = channelActivityMapper.selectOne(queryWrapper);

        if (activity != null) {
            log.error("该配置内容已存在，请不要重复创建！");
            throw new BusinessException(ErrorEnum.REPEATED_ERROR1.getCode(), ErrorEnum.REPEATED_ERROR1.getMsg());
        }

        //如果是哈啰，铁行活动 增加校验
        if(BusinessConst.NOT_SYNC_SECOND_APP_KEY_LIST.contains(dto.getAppKey())){
            isValidHelloActivity(dto);
        }

        //不存在该记录，新增
        String logContent = "新增";
        ChannelActivity act = setBaseChannelActivity(dto, null);
        marketingModuleManager.insertChannelActivityInfo(act, dto.getCurrentUser(), logContent);
        //哈啰, 铁行同步
        if(BusinessConst.NOT_SYNC_SECOND_APP_KEY_LIST.contains(dto.getAppKey())){

        }else{//擎路同步
            externalSystemFacade.notifyChannelActivitySync(act, false);
        }
    }

    /**
     * 新增时 对Hello， 铁行活动的额外数据校验
     * @param dto
     */
    private void isValidHelloActivity(AddChannelActDto dto) throws BusinessException {
        //Hello活动的活动类型不能是减至
        if(dto.getActType() == ActType.DECREASE.getType()){
            log.error("Hello/铁行活动的活动类型不能是减至！");
            throw new BusinessException(ErrorEnum.HELLO_ERROR1.getCode(), ErrorEnum.HELLO_ERROR1.getMsg());
        }
        //Hello活动为满减时 只能是 满天减...金额
        if(dto.getActType() == ActType.FULLREDUCTION.getType() && dto.getDiscountMethod() != DiscountMethodEnum.DAY_MINUS_AMOUNT.getType()){
            log.error("Hello/铁行活动为满减时 只能是 满天减...金额！");
            throw new BusinessException(ErrorEnum.HELLO_ERROR5.getCode(), ErrorEnum.HELLO_ERROR5.getMsg());
        }
        //Hello活动为打折时 只能是 满天打...折
        if(dto.getActType() == ActType.DISCOUNT.getType() && dto.getDiscountMethod() != DiscountMethodEnum.RENT_DAYS.getType()){
            log.error("Hello/铁行活动为满减时 只能是 满天打...折！");
            throw new BusinessException(ErrorEnum.HELLO_ERROR6.getCode(), ErrorEnum.HELLO_ERROR6.getMsg());
        }
        //Hello活动的优惠纬度不能是订单整单
        if(dto.getDiscountLatitude() == DiscountLatitudeEnum.WHOLEORDER.getType()){
            log.error("Hello/铁行活动的优惠纬度不能是订单整单！");
            throw new BusinessException(ErrorEnum.HELLO_ERROR2.getCode(), ErrorEnum.HELLO_ERROR2.getMsg());
        }
        //Hello活动的优惠方式不能是针对金额
        if(dto.getDiscountMethod() == DiscountMethodEnum.AMOUNT.getType()){
            log.error("Hello/铁行活动的优惠方式不能是针对金额！");
            throw new BusinessException(ErrorEnum.HELLO_ERROR3.getCode(), ErrorEnum.HELLO_ERROR3.getMsg());
        }
        //Hello活动的成本承担方必须是商家全部承担
        if(dto.getCostBearingParty() != CostBearingPartyEnum.MERCHANT.getType()){
            log.error("Hello/铁行活动的成本承担方必须是商家全部承担！");
            throw new BusinessException(ErrorEnum.HELLO_ERROR4.getCode(), ErrorEnum.HELLO_ERROR4.getMsg());
        }
    }

    /**
     * 更新时 对Hello活动的额外数据校验
     * @param dto
     * @throws BusinessException
     */
    private void isValidHelloActivity(UpdateChannelActDto dto) throws BusinessException {
        //Hello活动的活动类型不能是减至
        if(dto.getActType() == ActType.DECREASE.getType()){
            log.error("Hello/铁行活动的活动类型不能是减至！");
            throw new BusinessException(ErrorEnum.HELLO_ERROR1.getCode(), ErrorEnum.HELLO_ERROR1.getMsg());
        }
        //Hello活动为满减时 只能是 满天减...金额
        if(dto.getActType() == ActType.FULLREDUCTION.getType() && dto.getDiscountMethod() != DiscountMethodEnum.DAY_MINUS_AMOUNT.getType()){
            log.error("Hello/铁行活动为满减时 只能是 满天减...金额！");
            throw new BusinessException(ErrorEnum.HELLO_ERROR5.getCode(), ErrorEnum.HELLO_ERROR5.getMsg());
        }
        //Hello活动为打折时 只能是 满天打...折
        if(dto.getActType() == ActType.DISCOUNT.getType() && dto.getDiscountMethod() != DiscountMethodEnum.RENT_DAYS.getType()){
            log.error("Hello/铁行活动为满减时 只能是 满天打...折！");
            throw new BusinessException(ErrorEnum.HELLO_ERROR6.getCode(), ErrorEnum.HELLO_ERROR6.getMsg());
        }
        //Hello活动的优惠纬度不能是订单整单
        if(dto.getDiscountLatitude() == DiscountLatitudeEnum.WHOLEORDER.getType()){
            log.error("Hello/铁行活动的优惠纬度不能是订单整单！");
            throw new BusinessException(ErrorEnum.HELLO_ERROR2.getCode(), ErrorEnum.HELLO_ERROR2.getMsg());
        }
        //Hello活动的优惠方式不能是针对金额
        if(dto.getDiscountMethod() == DiscountMethodEnum.AMOUNT.getType()){
            log.error("Hello/铁行活动的优惠方式不能是针对金额！");
            throw new BusinessException(ErrorEnum.HELLO_ERROR3.getCode(), ErrorEnum.HELLO_ERROR3.getMsg());
        }
        //Hello活动的成本承担方必须是商家全部承担
        if(dto.getCostBearingParty() != CostBearingPartyEnum.MERCHANT.getType()){
            log.error("Hello/铁行活动的成本承担方必须是商家全部承担！");
            throw new BusinessException(ErrorEnum.HELLO_ERROR4.getCode(), ErrorEnum.HELLO_ERROR4.getMsg());
        }
    }
    /**
     * 判断入参的actType, discountMethod, condition1, condition2是否合法
     *
     * @param actType
     * @param discountMethod
     * @param condition1
     * @param condition2
     * @return true--合法 false--不合法
     */
    private boolean isValidDiscountMethod(int actType, int discountMethod, String condition1, String condition2) {
        if (actType == ActType.FULLREDUCTION.getType() && discountMethod == DiscountMethodEnum.RENT_DAYS.getType()) {
            log.error("当活动类型为满减时，优惠方式不能传针对租期:{}", DiscountMethodEnum.RENT_DAYS.getType());
            return false;
        }
        if (actType == ActType.DISCOUNT.getType()) {
            if (discountMethod == DiscountMethodEnum.DAY_MINUS_DAY.getType() || discountMethod == DiscountMethodEnum.DAY_MINUS_AMOUNT.getType()) {
                log.error("当活动类型为打折时，优惠方式不能为{},只能为针对金额或针对租期", discountMethod);
                return false;
            }
            BigDecimal decimal1 = new BigDecimal(condition1);
            BigDecimal decimal2 = new BigDecimal(condition2);
            if (decimal1.compareTo(new BigDecimal(0)) < 0) {
                log.error("优惠条件1不合法, 必须大于0", condition1);
                return false;
            }
            if (decimal2.compareTo(new BigDecimal(0)) < 0 || decimal2.compareTo(new BigDecimal(100)) > 0) {
                log.error("优惠条件2不合法, 必须在0-100", condition2);
                return false;
            }
            if (decimal2.scale() > 0) {
                log.error("优惠条件2不为整数", condition1);
                return false;
            }
        }
        if (actType == ActType.DECREASE.getType()) {//减至的时候只能是针对金额
            if (discountMethod != DiscountMethodEnum.AMOUNT.getType()) {
                log.error("优惠方式错误，减至的时候只能是针对金额");
                return false;
            }
        }
        return true;
    }

    /**
     * 设置ChannelActivity属性
     *
     * @param dto1,dto2
     * @return
     */
    ChannelActivity setBaseChannelActivity(AddChannelActDto dto1, UpdateChannelActDto dto2) throws BusinessException {
        ChannelActivity act = new ChannelActivity();
        if (dto1 == null) {
            UpdateChannelActDto dto = dto2;
            //20231121 优惠码可输入，废弃根据id找到对应的优惠码
            //String discountCode = channelActivityMapper.selectById(dto.getId()).getDiscountCode();
            int channel = getThirdId(dto.getAppKey());
            act.setChannel(channel);
            act.setSecondAppKey(dto.getAppKey());
            act.setDiscountCode(dto.getDiscountCode());
            act.setActivityName(dto.getActName());
            act.setOrgCodes(dto.toStringFromStrList(dto.getOrgCodes()));
            act.setActivityStatus(ConfigStateEnum.VALID.getType());
            act.setStoreIds(dto.toStringFromList(dto.getStoreIds()));
            act.setCarModelIds(dto.toStringFromList(dto.getCatIds()));
            act.setActivityType(dto.getActType());
            act.setDiscountLatitude(dto.getDiscountLatitude());
            act.setDiscountMethod(dto.getDiscountMethod());
            act.setDiscountCondition1(dto.getDiscountConditional1());
            act.setDiscountCondition2(dto.getDiscountConditional2());
            act.setRestrictDiscounts(dto.getRestrictDiscounts());
            BigDecimal amount = act.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType() ? dto.getMaxDiscountAmout() : new BigDecimal(-1);
            act.setMaxDiscountAmount(amount);
            int maxDays = act.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType() ? dto.getMaxRentDays() : -1;
            act.setMaxRentDays(maxDays);
            int minDays = act.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType() ? dto.getMinRentDays() : -1;
            act.setMinRentDays(minDays);
            act.setActivityStartDate(dto.getActStartDate());
            act.setActivityEndDate(dto.getActEndDate());
            act.setPickUpStartDate(dto.getPickupStartDate());
            act.setPickUpEndDate(dto.getPickupEndDate());
            act.setReturnStartDate(dto.getReturnStartDate());
            act.setReturnEndDate(dto.getReturnEndDate());
            act.setUnavailableDateRanges(JSON.toJSONString(dto.getUnavailableDateRanges()));
            act.setCostBearingParty(dto.getCostBearingParty());
            act.setCostAllocationMethod(dto.getCostAllocationMethod());
            act.setMerchantBear(dto.getMerchantBear());
            act.setPlatformBear(dto.getPlatformBear());
            act.setIsAllVehicle(dto.getIsAllVehicle());
            act.setIsAllStore(dto.getIsAllStore());
            act.setIntersectionFlag(dto.getIntersectionFlag());
        } else {
            // 新增
            AddChannelActDto dto = dto1;

            int channel = getThirdId(dto.getAppKey());
            // discountCode = generateDiscountCode(dto.getAppKey());
            act.setChannel(channel);
            act.setSecondAppKey(dto.getAppKey());
            // 用户输入
            act.setDiscountCode(dto.getDiscountCode());
            act.setActivityName(dto.getActName());
            act.setOrgCodes(dto.toStringFromStrList(dto.getOrgCodes()));
            act.setActivityStatus(ConfigStateEnum.VALID.getType());
            act.setStoreIds(dto.toStringFromList(dto.getStoreIds()));
            act.setCarModelIds(dto.toStringFromList(dto.getCatIds()));
            act.setActivityType(dto.getActType());
            act.setDiscountLatitude(dto.getDiscountLatitude());
            act.setDiscountMethod(dto.getDiscountMethod());
            act.setDiscountCondition1(dto.getDiscountConditional1());
            act.setDiscountCondition2(dto.getDiscountConditional2());
            act.setRestrictDiscounts(dto.getRestrictDiscounts());
            BigDecimal amount = act.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType() ? dto.getMaxDiscountAmout() : new BigDecimal(-1);
            act.setMaxDiscountAmount(amount);
            int maxDays = act.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType() ? dto.getMaxRentDays() : -1;
            act.setMaxRentDays(maxDays);
            int minDays = act.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType() ? dto.getMinRentDays() : -1;
            act.setMinRentDays(minDays);
            act.setActivityStartDate(dto.getActStartDate());
            act.setActivityEndDate(dto.getActEndDate());
            act.setPickUpStartDate(dto.getPickupStartDate());
            act.setPickUpEndDate(dto.getPickupEndDate());
            act.setReturnStartDate(dto.getReturnStartDate());
            act.setReturnEndDate(dto.getReturnEndDate());
            act.setUnavailableDateRanges(JSON.toJSONString(dto.getUnavailableDateRanges()));
            act.setCostBearingParty(dto.getCostBearingParty());
            act.setCostAllocationMethod(dto.getCostAllocationMethod());
            act.setMerchantBear(dto.getMerchantBear());
            act.setPlatformBear(dto.getPlatformBear());
            act.setIsAllVehicle(dto.getIsAllVehicle());
            act.setIsAllStore(dto.getIsAllStore());
            act.setIntersectionFlag(dto.getIntersectionFlag());
        }
        return act;
    }

    /**
     * 生成优惠码
     *
     * @param secondAppKey
     * @return
     */
    /*String generateDiscountCode(String secondAppKey) throws BusinessException {
        // 查询 优惠码前缀
        String pre = configLoader.getDisCountCode(secondAppKey);
        if (StringUtils.isEmpty(pre)) {
            log.error("查询优惠码为空secondAppKey = {}", secondAppKey);
            throw new BusinessException(ErrorEnum.DISCOUNT_CODE_NULL.getCode(), ErrorEnum.DISCOUNT_CODE_NULL.getMsg());
        }

        //模糊搜索当前渠道的所有优惠码
        LambdaQueryWrapper<ChannelActivity> queryWrapper = new LambdaQueryWrapper<ChannelActivity>()
                .like(ChannelActivity::getDiscountCode, "%" + pre + "%");
        List<ChannelActivity> acts = channelActivityMapper.selectList(queryWrapper);
        if (acts.isEmpty()) {
            return pre + "1";
        }
        List<Integer> codes = new ArrayList<>();
        for (ChannelActivity act : acts) {
            Integer code = Integer.parseInt(act.getDiscountCode().replace(pre, ""));
            codes.add(code);
        }
        String discountCode = pre + String.valueOf(Collections.max(codes) + 1);
        if (discountCode.length() > 50) {
            log.error("优惠码超过50个字符, 优惠码 = {}", discountCode);
            throw new BusinessException(ErrorEnum.DISCOUNT_CODE_OVERSIZE.getCode(), ErrorEnum.DISCOUNT_CODE_OVERSIZE.getMsg());
        }
        return discountCode;
    }*/

    @Override
    public void updateChannelAct(UpdateChannelActDto dto) throws BusinessException {
        //入参合法校验
        if (dto.getActName().length() > 100) {//act name
            log.error("活动名称长度超长，请重新输入，length = {}", dto.getActName().length());
            throw new BusinessException(ErrorEnum.INVALID_ACTNAME.getCode(), ErrorEnum.INVALID_ACTNAME.getMsg());
        }
        if (!BusinessConst.ALL_ACT_TYPE.contains(dto.getActType())) {//act type
            log.error("活动类型不合法，请重新输入，actType = {}", dto.getActType());
            throw new BusinessException(ErrorEnum.INVALID_ACTTYPE.getCode(), ErrorEnum.INVALID_ACTTYPE.getMsg());
        }
        if (!BusinessConst.ALL_DISCOUNT_LATITUDE.contains(dto.getDiscountLatitude())) {//discount latitude
            log.error("优惠纬度不合法，请重新输入，discountLatitude = {}", dto.getDiscountLatitude());
            throw new BusinessException(ErrorEnum.INVALID_DIS_LATITUDE.getCode(), ErrorEnum.INVALID_DIS_LATITUDE.getMsg());
        }
        if (!BusinessConst.ALL_DISCOUNT_METHOD.contains(dto.getDiscountMethod())) {//discount method
            log.error("优惠方式不合法，请重新输入，discountMethod = {}", dto.getDiscountMethod());
            throw new BusinessException(ErrorEnum.INVALID_DIS_METHOD.getCode(), ErrorEnum.INVALID_DIS_METHOD.getMsg());
        }
        if (!BusinessConst.ALL_COST_BEAR_PARTY.contains(dto.getCostBearingParty())) {//cost bear party
            log.error("成本承担方不合法，请重新输入, costBearParty = {}", dto.getCostBearingParty());
            throw new BusinessException(ErrorEnum.INVALID_COST_BEAR_PARTY.getCode(), ErrorEnum.INVALID_COST_BEAR_PARTY.getMsg());
        }
        //只有共同承担时需要成本分摊方式
        if (dto.getCostBearingParty() == CostBearingPartyEnum.TOGETHER.getType() && !BusinessConst.ALL_COST_ALLOCATION_METHOD.contains(dto.getCostAllocationMethod())) {//cost allocation
            log.error("成本分摊方式不合法，请重新输入, costAllocationMethod = {}", dto.getCostAllocationMethod());
            throw new BusinessException(ErrorEnum.INVALID_COST_ALLOCATION.getCode(), ErrorEnum.INVALID_COST_ALLOCATION.getMsg());
        }
        //优惠方式不合法
        if (!isValidDiscountMethod(dto.getActType(), dto.getDiscountMethod(), dto.getDiscountConditional1(), dto.getDiscountConditional2())) {
            log.error("优惠条件不合法,请检查 活动类型、优惠方式、优惠条件1、优惠条件2");
            throw new BusinessException(ErrorEnum.DISCOUNT_ERROR.getCode(), ErrorEnum.DISCOUNT_ERROR.getMsg());
        }

        String discountCode = dto.getDiscountCode();
        if ((!discountCode.startsWith("EV")) || discountCode.length() > 50) {
            log.error("优惠码不符合条件，discountCode={}", discountCode);
            throw new BusinessException(ErrorEnum.DISCOUNT_CODE_ILLEAGL.getCode(), ErrorEnum.DISCOUNT_CODE_ILLEAGL.getMsg());
        }

        //如果是哈啰，铁行活动 增加校验
        if(BusinessConst.NOT_SYNC_SECOND_APP_KEY_LIST.contains(dto.getAppKey())){
            isValidHelloActivity(dto);
        }

        //判断该记录是否已经存在
        LambdaQueryWrapper<ChannelActivity> queryWrapper = new LambdaQueryWrapper<ChannelActivity>()
                .eq(ChannelActivity::getSecondAppKey, dto.getAppKey())
                .eq(ChannelActivity::getActivityName, dto.getActName())
                .eq(ChannelActivity::getOrgCodes, dto.toStringFromStrList(dto.getOrgCodes()))
                .eq(ChannelActivity::getStoreIds, dto.toStringFromList(dto.getStoreIds()))
                .eq(ChannelActivity::getCarModelIds, dto.toStringFromList(dto.getCatIds()))
                .eq(ChannelActivity::getActivityType, dto.getActType())
                .eq(ChannelActivity::getDiscountLatitude, dto.getDiscountLatitude())
                .eq(ChannelActivity::getDiscountMethod, dto.getDiscountMethod())
                .eq(ChannelActivity::getDiscountCondition1, dto.getDiscountConditional1())
                .eq(ChannelActivity::getDiscountCondition2, dto.getDiscountConditional2())
                .eq(ChannelActivity::getRestrictDiscounts, dto.getRestrictDiscounts())
                .eq(dto.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType(), ChannelActivity::getMaxDiscountAmount, dto.getMaxDiscountAmout())
                .eq(dto.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType(), ChannelActivity::getMaxRentDays, dto.getMaxRentDays())
                .eq(dto.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType(), ChannelActivity::getMinRentDays, dto.getMinRentDays())
                .eq(ChannelActivity::getActivityStartDate, dto.getActStartDate())
                .eq(ChannelActivity::getActivityEndDate, dto.getActEndDate())
                .eq(ChannelActivity::getPickUpStartDate, dto.getPickupStartDate())
                .eq(ChannelActivity::getPickUpEndDate, dto.getPickupEndDate())
                .eq(ChannelActivity::getReturnStartDate, dto.getReturnStartDate())
                .eq(ChannelActivity::getReturnEndDate, dto.getReturnEndDate())
                .eq(ChannelActivity::getUnavailableDateRanges, JSON.toJSONString(dto.getUnavailableDateRanges()))
                .eq(ChannelActivity::getCostBearingParty, dto.getCostBearingParty())
                .eq(dto.getCostBearingParty() == CostBearingPartyEnum.TOGETHER.getType(), ChannelActivity::getCostAllocationMethod, dto.getCostAllocationMethod())
                .eq(dto.getCostBearingParty() == CostBearingPartyEnum.TOGETHER.getType(), ChannelActivity::getMerchantBear, dto.getMerchantBear())
                .eq(dto.getCostBearingParty() == CostBearingPartyEnum.TOGETHER.getType(), ChannelActivity::getPlatformBear, dto.getPlatformBear())
                .eq(ChannelActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .eq(ChannelActivity::getDiscountCode, discountCode)
                .eq(ChannelActivity::getIntersectionFlag, dto.getIntersectionFlag());

        ChannelActivity activity = channelActivityMapper.selectOne(queryWrapper);

        if (activity != null) {
            log.error("该配置内容已存在，不需要更新！");
            throw new BusinessException(ErrorEnum.REPEATED_ERROR2.getCode(), ErrorEnum.REPEATED_ERROR2.getMsg());
        }

        // 修改时 判断营销活动的 二级渠道 下 是否有相同优惠码的 生效中的 记录 ；不允许
        List<ChannelActivity> channelActivities = channelActivityMapper.selectList(new LambdaQueryWrapper<ChannelActivity>()
                .eq(ChannelActivity::getSecondAppKey, dto.getAppKey())
                .eq(ChannelActivity::getDiscountCode, discountCode)
                .ne(ChannelActivity::getId, dto.getId())
                .eq(ChannelActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
        );

        if (CollectionUtils.isNotEmpty(channelActivities)) {
            log.error("修改时，存在相同渠道、相同优惠码，discountCode={},channelActivities={}", discountCode, JSON.toJSONString(channelActivities));
            throw new BusinessException(ErrorEnum.DISCOUNT_CODE_REPEAT.getCode(), ErrorEnum.DISCOUNT_CODE_REPEAT.getMsg());
        }
        //渠道不可改
        activity = channelActivityMapper.selectById(dto.getId());
        if (!activity.getSecondAppKey().equals(dto.getAppKey())) {
            log.error("渠道不可更改,secondAppKey = {}", activity.getSecondAppKey());
            throw new BusinessException(ErrorEnum.UPDATE_ERROR1.getCode(), ErrorEnum.UPDATE_ERROR1.getMsg());
        }
        //活动类型不可改
        if (activity.getActivityType() != dto.getActType()) {
            log.error("活动类型不可更改,actType = {}", activity.getActivityType());
            throw new BusinessException(ErrorEnum.UPDATE_ERROR2.getCode(), ErrorEnum.UPDATE_ERROR2.getMsg());
        }
        //优惠纬度不可改
        if (activity.getDiscountLatitude() != dto.getDiscountLatitude()) {
            log.error("优惠纬度不可更改,discountLatitude = {}", activity.getDiscountLatitude());
            throw new BusinessException(ErrorEnum.UPDATE_ERROR3.getCode(), ErrorEnum.UPDATE_ERROR3.getMsg());
        }
        //优惠方式不可改
        if (activity.getDiscountMethod() != dto.getDiscountMethod() || !activity.getDiscountCondition1().equals(dto.getDiscountConditional1()) || !activity.getDiscountCondition2().equals(dto.getDiscountConditional2())) {
            log.error("优惠方式不可更改,discountMethod = {}, discountCondition1 = {}, discountCondition2 = {}", dto.getDiscountMethod(), activity.getDiscountCondition1(), activity.getDiscountCondition2());
            throw new BusinessException(ErrorEnum.UPDATE_ERROR4.getCode(), ErrorEnum.UPDATE_ERROR4.getMsg());
        }
        //是否限制优惠不可改
        if (activity.getRestrictDiscounts() != dto.getRestrictDiscounts()) {
            log.error("是否限制优惠不可更改,restrictDiscount = {}, maxDiscountAmount = {}, maxRentDays = {}, minRentDays = {}", dto.getRestrictDiscounts(), activity.getMaxDiscountAmount(), activity.getMaxRentDays(), activity.getMinRentDays());
            throw new BusinessException(ErrorEnum.UPDATE_ERROR5.getCode(), ErrorEnum.UPDATE_ERROR5.getMsg());
        } else {//有限制时 最高优惠金额、最大最小租期不可更改
            // 去掉一个判断，数据库 存的是-1，前端给的是0
            if (activity.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType() && (/*activity.getMaxDiscountAmount().compareTo(dto.getMaxDiscountAmout()) != 0
                    ||*/ activity.getMaxRentDays() != dto.getMaxRentDays() || activity.getMinRentDays() != dto.getMinRentDays())) {
                log.error("是否限制优惠不可更改, maxDiscountAmount = {}, maxRentDays = {}, minRentDays = {}", activity.getMaxDiscountAmount(), activity.getMaxRentDays(), activity.getMinRentDays());
                throw new BusinessException(ErrorEnum.UPDATE_ERROR5.getCode(), ErrorEnum.UPDATE_ERROR5.getMsg());
            }
        }
        //成本承担方不可改
        if (activity.getCostBearingParty() != dto.getCostBearingParty()) {
            log.error("成本承担方不可更改,costBearingParty = {}", dto.getCostBearingParty());
            throw new BusinessException(ErrorEnum.UPDATE_ERROR6.getCode(), ErrorEnum.UPDATE_ERROR6.getMsg());
        }
        //成本分摊方式不可更改
        if (activity.getCostAllocationMethod() != dto.getCostAllocationMethod() || activity.getMerchantBear().compareTo(dto.getMerchantBear()) != 0 || activity.getPlatformBear().compareTo(dto.getPlatformBear()) != 0) {
            log.error("成本分摊方式不可更改,costAllocationMethod = {}, MerchantBear = {}, platformBear = {}", activity.getCostAllocationMethod(), activity.getMerchantBear(), activity.getPlatformBear());
            throw new BusinessException(ErrorEnum.UPDATE_ERROR9.getCode(), ErrorEnum.UPDATE_ERROR9.getMsg());
        }
        //更新
        ChannelActivity act = setBaseChannelActivity(null, dto);
        String logContent = getLogContent(activity, act);
        marketingModuleManager.updateChannelActivityInfo(act, dto.getCurrentUser(), logContent, dto.getId());
        if (BusinessConst.NOT_SYNC_SECOND_APP_KEY_LIST.contains(dto.getAppKey())) {

        }else{
            //擎路同步
            // 新纪录同步
            externalSystemFacade.notifyChannelActivitySync(act, false);
            activity = channelActivityMapper.selectById(dto.getId());
            // 老纪录同步
            externalSystemFacade.notifyChannelActivitySync(activity, false);
        }
    }

    String getLogContent(ChannelActivity oldAct, ChannelActivity newAct) {
        String str = "更新";
        QlSecondChannelBo qlSecondChannelBo = configLoader.getQlSecondChannelBo(oldAct.getSecondAppKey());
        if (qlSecondChannelBo != null) {
            str += qlSecondChannelBo.getSecondChannelName();
        }
        str += "渠道活动";
        //channel
       /* switch (oldAct.getChannel()) {
            case 1:
                str += "线下渠道活动：";
                break;
            case 2:
                str += "携程渠道活动：";
                break;
            case 3:
                str += "飞猪渠道活动：";
                break;
            case 4:
                str += "哈罗渠道活动：";
                break;
            case 5:
                str += "租租车渠道活动：";
                break;
            case 6:
                str += "悟空渠道活动：";
                break;
            case 10:
                str += "携程分销渠道活动：";
                break;
        }*/
        //优惠码
        if (!oldAct.getDiscountCode().equals(newAct.getDiscountCode())) {
            str += "优惠码由 " + oldAct.getDiscountCode() + " 更新为 " + newAct.getDiscountCode() + "; ";
        }

        //活动名称
        if (!oldAct.getActivityName().equals(newAct.getActivityName())) {
            str += "活动名称由 " + oldAct.getActivityName() + " 更新为 " + newAct.getActivityName() + "; ";
        }
        //参与机构
        if (!oldAct.getOrgCodes().equals(newAct.getOrgCodes())) {
            List<String> codes1 = Arrays.stream(oldAct.getOrgCodes().split(",")).collect(Collectors.toList());
            List<String> codes2 = Arrays.stream(newAct.getOrgCodes().split(",")).collect(Collectors.toList());
            str += "参与机构由 " + configLoader.listOrgName(codes1) + " 更新为 " + configLoader.listOrgName(codes2) + "; ";
        }
        //适用的门店车型
        if (!JSON.toJSONString(oldAct.getStoreIds()).equals(JSON.toJSONString(newAct.getStoreIds()))) {
            List<Long> ids1 = Arrays.stream(oldAct.getStoreIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<Long> ids2 = Arrays.stream(newAct.getStoreIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            str += "适用的门店由 " + configLoader.listStoreName(ids1) + " 更新为 " + configLoader.listStoreName(ids2);
        }
        if (!JSON.toJSONString(oldAct.getCarModelIds()).equals(JSON.toJSONString(newAct.getCarModelIds()))) {
            List<Long> ids1 = Arrays.stream(oldAct.getCarModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<Long> ids2 = Arrays.stream(newAct.getCarModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            str += "适用的车型由 " + configLoader.listVehicleName(ids1) + " 更新为 " + configLoader.listVehicleName(ids2);
        }
        //活动开始时间
        if (oldAct.getActivityStartDate().compareTo(newAct.getActivityStartDate()) != 0) {
            str += "活动开始时间由 " + oldAct.getActivityStartDate() + " 更新为 " + newAct.getActivityStartDate() + "; ";
        }
        //活动结束时间
        if (oldAct.getActivityEndDate().compareTo(newAct.getActivityEndDate()) != 0) {
            str += "活动结束时间由 " + oldAct.getActivityEndDate() + " 更新为 " + newAct.getActivityEndDate() + "; ";
        }
        //取车开始时间
        if (oldAct.getPickUpStartDate().compareTo(newAct.getPickUpStartDate()) != 0) {
            str += "取车开始时间由 " + oldAct.getPickUpStartDate() + " 更新为 " + newAct.getPickUpStartDate() + "; ";
        }
        //取车结束时间
        if (oldAct.getPickUpStartDate().compareTo(newAct.getPickUpStartDate()) != 0) {
            str += "取车结束时间由 " + oldAct.getPickUpStartDate() + " 更新为 " + newAct.getPickUpStartDate() + "; ";
        }
        //还车开始时间
        if (oldAct.getReturnStartDate().compareTo(newAct.getReturnStartDate()) != 0) {
            str += "还车开始时间由 " + oldAct.getReturnStartDate() + " 更新为 " + newAct.getReturnStartDate() + "; ";
        }
        //还车结束时间
        if (oldAct.getReturnEndDate().compareTo(newAct.getReturnEndDate()) != 0) {
            str += "还车结束时间由 " + oldAct.getReturnEndDate() + " 更新为 " + newAct.getReturnEndDate() + "; ";
        }
        //不可用时间范围
        if (!oldAct.getUnavailableDateRanges().equals(newAct.getUnavailableDateRanges())) {
            str += "不可用时间范围由 " + oldAct.getUnavailableDateRanges().replace("startDate", "不可用时间开始时间").replace("endDate", "不可用时间结束时间").replace("unavailableDateRanges", "不可用时间范围") + " 更新为 " + newAct.getUnavailableDateRanges().replace("startDate", "不可用时间开始时间").replace("endDate", "不可用时间结束时间").replace("unavailableDateRanges", "不可用时间范围") + "; ";
        }
        if (!oldAct.getIntersectionFlag().equals(newAct.getIntersectionFlag())) {
            str += "取还车时间【包含/交集】由 " + (oldAct.getIntersectionFlag() == 1 ? "包含" : "交集") + " 更新为 " + (newAct.getIntersectionFlag() == 1 ? "包含" : "交集") + "; ";
        }
        if (str.length() > 1024) {
            str = str.substring(0, 1021) + "...";
        }
        return str;
    }

    /**
     * 根据二级渠道查对应的channel
     * @param appKey
     * @return
     */
    int getThirdId(String appKey){
        if(appKey.equals(BusinessConst.HELLO_SECOND_CHANNEL)){
            return 4;
        }
        else if (appKey.equals(BusinessConst.TX_SECOND_CHANNEL)){
            return 20;
        }else if (appKey.equals(BusinessConst.SECOND_ALI_PAY_CAR_LIFE_CHANNEL)){
            return 21;
        }else if (appKey.equals(BusinessConst.SECOND_TONGCHENG_ZUCHE_CHANNEL)){
            return 22;
        }
        else{
            return (int) configLoader.getThirdId(appKey);
        }
    }
    @Override
    public ListChannelActBo listChannelAct(ListChannelActDto dto) throws BusinessException {
        //参数校验
        if (!BusinessConst.ALL_ACT_STATUS.contains(dto.getActStatus()) && dto.getActStatus() != 0) {
            log.error("活动状态不合法，请重新输入");
            throw new BusinessException(ErrorEnum.INVALID_ACTSTATUS.getCode(), ErrorEnum.INVALID_ACTSTATUS.getMsg());
        }
        if (dto.getPageNum() == 0) {
            log.error("当前页码缺失");
            throw new BusinessException(ErrorEnum.PAGENUM_ERROR.getCode(), ErrorEnum.PAGENUM_ERROR.getMsg());
        }
        if (dto.getPageSize() == 0) {
            log.error("每页显示条数缺失");
            throw new BusinessException(ErrorEnum.PAGESIZE_ERROR.getCode(), ErrorEnum.PAGESIZE_ERROR.getMsg());
        }

        //查询
        boolean condition = dto.getActStatus() == 0;
        LambdaQueryWrapper<ChannelActivity> queryWrapper = new LambdaQueryWrapper<ChannelActivity>()
                .eq(!StringUtils.isEmpty(dto.getChannel()), ChannelActivity::getSecondAppKey, dto.getChannel())
                .like(!StringUtils.isEmpty(dto.getDiscountCode()), ChannelActivity::getDiscountCode, "%" + dto.getDiscountCode() + "%")
                .like(!StringUtils.isEmpty(dto.getActName()), ChannelActivity::getActivityName, "%" + dto.getActName() + "%")
                .eq(!condition, ChannelActivity::getActivityStatus, dto.getActStatus())
                .eq(ChannelActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .orderByAsc(ChannelActivity::getActivityStatus)
                .orderByDesc(ChannelActivity::getUpdateTime)
                .orderByDesc(ChannelActivity::getId);

        Page page = new Page(dto.getPageNum(), dto.getPageSize());
        Page<ChannelActivity> pageResult = channelActivityMapper.selectPage(page, queryWrapper);

        if (pageResult == null) {
            log.error("未查询到营销活动（分页）");
            throw new BusinessException(ErrorEnum.LIST_ERROR1.getCode(), ErrorEnum.LIST_ERROR1.getMsg());
        }

        //返回参数
        ListChannelActBo bo = new ListChannelActBo();
        List<ChannelActInfo> list = new ArrayList<>();
        for (ChannelActivity act : pageResult.getRecords()) {
            ChannelActInfo info = new ChannelActInfo();
            info.setId(act.getId());
            info.setDiscountCode(act.getDiscountCode());
            info.setActName(act.getActivityName());
            info.setActType(act.getActivityType());
            info.setStartDate(act.getActivityStartDate().toString());
            info.setEndDate(act.getActivityEndDate().toString());
            info.setActStatus(act.getActivityStatus());
            info.setChannel(act.getChannel());
            info.setChannelText(getChannelName(act.getSecondAppKey()));
            list.add(info);
        }
        bo.setList(list);
        bo.setTotal((int) pageResult.getTotal());
        return bo;
    }

    @Override
    public CacheChannelActBo cacheChannelAct(String secondAppKey, String md5) throws BusinessException {
        try {
            //查询
            LambdaQueryWrapper<ChannelActivity> queryWrapper = new LambdaQueryWrapper<ChannelActivity>()
                    .eq(StringUtils.isNotBlank(secondAppKey), ChannelActivity::getSecondAppKey, secondAppKey)
                    .orderByAsc(ChannelActivity::getId);
            List<ChannelActivity> result = channelActivityMapper.selectList(queryWrapper);

            //返回参数
            CacheChannelActBo bo = new CacheChannelActBo();
            List<GetChannelActDetailBo> list = new ArrayList<>();
            for (ChannelActivity act : result) {
                GetChannelActDetailBo channelActDetailBo = GetChannelActDetailBo.getInstanceFromDb(act);
                channelActDetailBo.setChannelText(getChannelName(act.getSecondAppKey()));
                list.add(channelActDetailBo);
            }
            String newMd5 = Md5Utils.toMd5(list);
            if (StringUtils.isNotEmpty(md5) && md5.equals(newMd5)) {
                bo.setMd5(md5);
            }else{
                bo.setList(list);
                bo.setMd5(newMd5);
            }
            return bo;
        } catch (Exception e) {
            log.error("缓存查询所有渠道活动异常,secondAppKey={},md5={}", secondAppKey,md5,e);
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(),ErrorEnum.REQUEST_ERROR.getMsg());
        }
    }

    @Override
    public GetChannelActDetailBo getChannelActDetail(long id) throws BusinessException {
        ChannelActivity act = channelActivityMapper.selectById(id);
        if (act == null) {
            log.error("未查询到渠道活动详情, id = {}", id);
            throw new BusinessException(ErrorEnum.GET_DETAIL_ERROR1.getCode(), ErrorEnum.GET_DETAIL_ERROR1.getMsg());
        }
        try {
            GetChannelActDetailBo bo = GetChannelActDetailBo.getInstanceFromDb(act);
            bo.setChannelText(getChannelName(act.getSecondAppKey()));
            return bo;
        } catch (Exception e) {
            log.error("营销活动根据id获取详情失败, id = {}", id,e);
            throw new BusinessException(ErrorEnum.GET_DETAIL_ERROR2.getCode(), ErrorEnum.GET_DETAIL_ERROR2.getMsg());
        }
    }

    public String getChannelName(String secondAppKey) {
        try {
            QlSecondChannelBo qlSecondChannelBo = configLoader.getQlSecondChannelBo(secondAppKey);
            if (qlSecondChannelBo != null) {
                return qlSecondChannelBo.getSecondChannelName();
            }else{
                return "渠道活动";
            }
        } catch (Exception e) {
            log.error("获取channel异常, secondAppKey", secondAppKey, e);
        }
        return "";
    }

    @Override
    public void deleteChannelAct(DeleteChannelActDto dto) throws BusinessException {
        //判断该id能否查询到数据
        ChannelActivity act = channelActivityMapper.selectById(dto.getId());
        if (act == null) {
            log.error("根据id未查询到数据, 请重新输入, id = {}", dto.getId());
            throw new BusinessException(ErrorEnum.DELETE_ERROR1.getCode(), ErrorEnum.DELETE_ERROR1.getMsg());
        }
        //删除
        String logContent = "删除渠道营销规则";
        marketingModuleManager.deleteChannelActivityInfo(dto.getId(), dto.getCurrentUser(), logContent);
        //擎路同步
        act = channelActivityMapper.selectById(dto.getId());
        externalSystemFacade.notifyChannelActivitySync(act, false);
    }

    /**
     * 将ChannelActivity转成擎路入参MarketingCampaignInfoRequest
     *
     * @param activity
     * @return
     */
    @Override
    public MarketingCampaignInfoRequest dataSync(ChannelActivity activity) throws BusinessException {
        log.info("tid:{},dataSync start activity={}",  Trace.currentTraceId(),JSON.toJSONString(activity));
        MarketingCampaignInfoRequest req = new MarketingCampaignInfoRequest();

        Integer activityType = activity.getActivityType();
        Integer discountMethod = activity.getDiscountMethod();

        req.setMarketId(activity.getId().toString());
        req.setChannelId((long) activity.getChannel());
        req.setStatus(activity.getActivityStatus());
        req.setMarketName(activity.getActivityName());
        req.setMarketCode(activity.getDiscountCode());
        req.setMarketType(activityType);
        req.setDimension(activity.getDiscountLatitude());
        //优惠方式
        if (activityType == ActType.FULLREDUCTION.getType()
                && (discountMethod == DiscountMethodEnum.DAY_MINUS_DAY.getType() || discountMethod == DiscountMethodEnum.DAY_MINUS_AMOUNT.getType())) {

            req.setDiscountMode(DiscountMethodEnum.RENT_DAYS.getType());
            // 擎路特殊处理 优惠方式 0：无，1：满天减天，2：满天减金额
            int qingluDiscountSubMode = discountMethod;
            if (discountMethod == DiscountMethodEnum.DAY_MINUS_DAY.getType()) {
                qingluDiscountSubMode = 1;
            } else if (discountMethod == DiscountMethodEnum.DAY_MINUS_AMOUNT.getType()) {
                qingluDiscountSubMode = 2;
            }
            req.setDiscountSubMode(qingluDiscountSubMode);
        } else {
            req.setDiscountMode(discountMethod);
            req.setDiscountSubMode(0);
        }
        //元->分
        if (activityType == ActType.FULLREDUCTION.getType()) {
            //满减-针对金额 满...元减...元
            if (discountMethod == DiscountMethodEnum.AMOUNT.getType()) {
                req.setSatisfy(new BigDecimal(activity.getDiscountCondition1()).movePointRight(2).intValue());
                req.setDiscount(new BigDecimal(activity.getDiscountCondition2()).movePointRight(2).intValue());
            } else if (discountMethod == DiscountMethodEnum.DAY_MINUS_AMOUNT.getType()) {//满减-针对租期-满天减金额 满...天减...元
                req.setSatisfy(Integer.valueOf(activity.getDiscountCondition1()));
                req.setDiscount(new BigDecimal((activity.getDiscountCondition2())).movePointRight(2).intValue());
            } else {//满减-针对租期-满天减天  满...天减...天
                req.setSatisfy(Integer.valueOf(activity.getDiscountCondition1()));
                req.setDiscount(Integer.valueOf(activity.getDiscountCondition2()));
            }
        } else if (activityType == ActType.DISCOUNT.getType() && discountMethod == DiscountMethodEnum.AMOUNT.getType()) {//打折-针对金额 满...元打...%
            req.setSatisfy(new BigDecimal(activity.getDiscountCondition1()).movePointRight(2).intValue());
            req.setDiscount(Integer.valueOf(activity.getDiscountCondition2()));
        } else if (activityType == ActType.DECREASE.getType()) {// 减至 满...元直接减至...元
            req.setSatisfy(new BigDecimal(activity.getDiscountCondition1()).movePointRight(2).intValue());
            req.setDiscount(new BigDecimal(activity.getDiscountCondition2()).movePointRight(2).intValue());
        } else {
            req.setSatisfy(Integer.valueOf(activity.getDiscountCondition1()));
            req.setDiscount(Integer.valueOf(activity.getDiscountCondition2()));
        }
        //限制优惠
        if (activity.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType()) {
            req.setMaxMoney(activity.getMaxDiscountAmount().movePointRight(2).intValue());
            req.setMaxLeaseTerm(activity.getMaxRentDays());
            req.setMinLeaseTerm(activity.getMinRentDays());
        } else {
            req.setMaxMoney(-1);
        }
        //时间范围
        req.setPlaceOrderStart(Timestamp.valueOf(activity.getActivityStartDate().atTime(00, 00, 00)).getTime());
        req.setPlaceOrderEnd(Timestamp.valueOf(activity.getActivityEndDate().atTime(23, 59, 59)).getTime());
        req.setPickStart(Timestamp.valueOf(activity.getPickUpStartDate().atTime(00, 00, 00)).getTime());
        req.setPickEnd(Timestamp.valueOf(activity.getPickUpEndDate().atTime(23, 59, 59)).getTime());
        req.setReturnStart(Timestamp.valueOf(activity.getReturnStartDate().atTime(00, 00, 00)).getTime());
        req.setReturnEnd(Timestamp.valueOf(activity.getReturnEndDate().atTime(23, 59, 59)).getTime());
        //成本分摊
        req.setCostParty(activity.getCostBearingParty());
        req.setCostPartyType(activity.getCostAllocationMethod());
        //只有共同承担时需要设置以下参数
        if (activity.getCostBearingParty() == CostBearingPartyEnum.TOGETHER.getType()) {
            if (activity.getCostAllocationMethod() == CostAllocationMethodEnum.PERCENT.getType()) {//百分比设置
                req.setMerchantCostPartyValue(activity.getMerchantBear().intValue());
                req.setPlatformCostPartyValue(activity.getPlatformBear().intValue());
            } else {//按固定金额设置
                if (activity.getMerchantBear().compareTo(BigDecimal.ZERO) == 0) {
                    req.setPlatformCostPartyValue(activity.getPlatformBear().movePointRight(2).intValue());
                } else {
                    req.setMerchantCostPartyValue(activity.getMerchantBear().movePointRight(2).intValue());
                }
            }
        }
        //门店列表
        //车型id列表
        List<MarketStoreRequest> storeList = new ArrayList<>();
        List<VehicleModelRequest> modelList = new ArrayList<>();

        //List<String> stores = Arrays.asList(activity.getStoreIds().split(","));
        List<String> vehicleIds = Arrays.asList(activity.getCarModelIds().split(","));


        // 查询同步给擎路的门店集合
        List<String> storeIds2 = Arrays.stream(activity.getStoreIds().split(",")).collect(Collectors.toList());
        GetChannelSaasStoreListRes getChannelSaasStoreListRes = mdStoreService.getChannelSaasStoreList(GetChannelSaasStoreListReq.newBuilder()
                .setPlatform(BusinessConst.QINGLU_PLATFORMID)
                .addAllStoreId(storeIds2)
                .setStoreType("1,2")
                .build());
        if (getChannelSaasStoreListRes.getRetCode() != 0) {
            log.error("tid:{},营销活动同步给擎路时，获取擎路门店集合时，异常,code={},msg={}", Trace.currentTraceId(), getChannelSaasStoreListRes.getRetCode(), getChannelSaasStoreListRes.getRetMsg());
            throw new BusinessException(ErrorEnum.SYNC_QINGLU_FAIL.getCode(), ErrorEnum.SYNC_QINGLU_FAIL.getMsg());
        }
        List<SaasStoreInfo> infoList = getChannelSaasStoreListRes.getInfoList();
        Set<Long> storeIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(infoList)) {
            storeIds = infoList.stream().map(s -> Long.parseLong(s.getStoreId())).collect(Collectors.toSet());
            log.info("tid:{},去重后的已经同步给擎路的门店有storeIds={}", Trace.currentTraceId(), JSON.toJSONString(storeIds));
        } else {
            log.error("tid:{},未查询到同步给擎路的有效门店", Trace.currentTraceId());
            throw new BusinessException(ErrorEnum.SYNC_QINGLU_FAIL.getCode(), ErrorEnum.SYNC_QINGLU_FAIL.getMsg());
        }

        //根据门店ids拿到 该门店下的所有车型信息
        List<StoreStoreVehicleModelInfo> storeStoreVehicleModelInfos = mdStockService.queryStoreVehicleModelByStoreId(QueryStoreVehicleModelByStoreIdReq.newBuilder().addAllStoreId(storeIds).build()).getStoreStoreVehicleModelInfoList();
        List<StoreStoreVehicleModelInfoBo> storeStoreVehicleModelInfoBos = toStoreStoreVehicleModelInfoList(storeStoreVehicleModelInfos);
        log.info("tid:{},查询该门店下，车型，storeIds={},storeStoreVehicleModelInfoBos={}", Trace.currentTraceId(), JSON.toJSONString(storeIds), JSON.toJSONString(storeStoreVehicleModelInfoBos));
        // key 门店id value 门店下的车型list
        Map<Long, List<StoreVehicleModelInfoBo>> map = storeStoreVehicleModelInfoBos.stream()
                .collect(Collectors.toMap(StoreStoreVehicleModelInfoBo::getId, StoreStoreVehicleModelInfoBo::getStoreVehicleModelInfoList, (key1, key2) -> key1));

        //根据二级渠道查询一级渠道信息
        QueryBySecondChannelRes queryBySecondChannelRes = mdUserService.queryBySecondChannel(QueryBySecondChannelReq.newBuilder().setSecondChannelKey(activity.getSecondAppKey()).build());
        if (queryBySecondChannelRes.getRetCode() != 0) {
            log.error("tid:{},营销活动同步给擎路时，获取一级渠道，异常,code={},msg={}", Trace.currentTraceId(), queryBySecondChannelRes.getRetCode(), queryBySecondChannelRes.getRetMsg());
            throw new BusinessException(ErrorEnum.SYNC_QINGLU_FAIL.getCode(), ErrorEnum.SYNC_QINGLU_FAIL.getMsg());
        }
        String firstChannelKey = queryBySecondChannelRes.getFirstChannelKey();
        for (Long id : storeIds) {
            MarketStoreRequest marketStoreRequest = new MarketStoreRequest();
            String storeId = "MD" + id;
            marketStoreRequest.setStoreId(storeId);
            //拿到这个门店id下对应的车型id列表
            List<String> vehicles2 = new ArrayList<>();
            for (StoreVehicleModelInfoBo info : map.get(id)) {
                //判断该门店下面的车型是否与该渠道绑定
                if(!configLoader.containsVehicleModeValue(info.getVehicleId(),firstChannelKey)){
                    continue;
                }
                vehicles2.add(String.valueOf(info.getVehicleId()));
            }
            //判断是否全选 门店下车型为空, 2个没有交集代表没有车型(!CollectionUtils.containsAny(vehicles2,vehicleIds))
            if (vehicleIds.containsAll(vehicles2) || (!CollectionUtils.containsAny(vehicles2,vehicleIds))) {
                marketStoreRequest.setIsAll(IsAllEnum.ALL.getType());
            } else {
                marketStoreRequest.setIsAll(IsAllEnum.NOT_ALL.getType());
            }
            storeList.add(marketStoreRequest);
            // 找到vehicles2和vehicles中的相同元素
            vehicles2.retainAll(vehicleIds);

            // 门店下没有车型 或者 门店下选中全部车型  则车型id设为0
            if (marketStoreRequest.getIsAll() == IsAllEnum.ALL.getType() || CollectionUtils.isEmpty(vehicles2)) {
                VehicleModelRequest vehicleModelRequest = new VehicleModelRequest();
                vehicleModelRequest.setMarketStoreId(storeId);
                vehicleModelRequest.setVehicleModelId("0");
                modelList.add(vehicleModelRequest);
            } else if (marketStoreRequest.getIsAll() == IsAllEnum.NOT_ALL.getType()) {
                // 遍历组装数据
                for (String vehicle : vehicles2) {
                    VehicleModelRequest vehicleModelRequest = new VehicleModelRequest();
                    vehicleModelRequest.setMarketStoreId(storeId);
                    vehicleModelRequest.setVehicleModelId(vehicle);
                    modelList.add(vehicleModelRequest);
                }
            }
        }
        req.setMarketStoreList(storeList);
        req.setVehicleModelList(modelList);

        //不可用时间范围
        List<MarketUnserviceableTimeRequest> timeList = new ArrayList<>();
        List<TimeRange> timeRanges = JSON.parseObject(activity.getUnavailableDateRanges(), UnavailableDateRanges.class).getUnavailableDateRanges();
        for (TimeRange timeRange : timeRanges) {
            MarketUnserviceableTimeRequest time = new MarketUnserviceableTimeRequest();
            time.setUnserviceableStart(Timestamp.valueOf(DateUtil.getLocalDateFromStr(timeRange.getStartDate(), DateUtil.DATE_TYPE5).atTime(00, 00, 00)).getTime());
            time.setUnserviceableEnd(Timestamp.valueOf(DateUtil.getLocalDateFromStr(timeRange.getEndDate(), DateUtil.DATE_TYPE5).atTime(23, 59, 59)).getTime());
            timeList.add(time);
        }
        req.setMarketUnserviceableTimeList(timeList);
        req.setDelFlg(activity.getIsDeleted() == IsDeletedEnum.IS_DELETE.getType());
        return req;
    }

    public List<StoreStoreVehicleModelInfoBo> toStoreStoreVehicleModelInfoList(List<StoreStoreVehicleModelInfo> infos) {
        List<StoreStoreVehicleModelInfoBo> list = new ArrayList<>();
        for (StoreStoreVehicleModelInfo info : infos) {
            StoreStoreVehicleModelInfoBo storeStoreVehicleModelInfo = new StoreStoreVehicleModelInfoBo();
            storeStoreVehicleModelInfo.setId(info.getStoreId());
            storeStoreVehicleModelInfo.setStoreVehicleModelInfoList(toStoreVehicleModelInfoList(info.getStoreVehicleModelInfoList()));
            list.add(storeStoreVehicleModelInfo);
        }
        return list;
    }

    private List<StoreVehicleModelInfoBo> toStoreVehicleModelInfoList(List<StoreVehicleModelInfo> storeVehicleModelInfoList) {
        List<StoreVehicleModelInfoBo> list = new ArrayList<>();
        for (StoreVehicleModelInfo info : storeVehicleModelInfoList) {
            StoreVehicleModelInfoBo storeVehicleModelInfo = new StoreVehicleModelInfoBo();
            storeVehicleModelInfo.setVehicleId(info.getStoreVehicleModelId());
            storeVehicleModelInfo.setVehicleName(info.getStoreVehicleModelName());
            list.add(storeVehicleModelInfo);
        }
        return list;
    }

    @Override
    public GetChannelActDetailFromDiscountCodeBo getChannelActDetailFromDiscountCode(String discountCode) throws BusinessException {
        LambdaQueryWrapper queryWrapper = new LambdaQueryWrapper<ChannelActivity>()
                .eq(ChannelActivity::getDiscountCode, discountCode)
                .eq(ChannelActivity::getActivityStatus, ConfigStateEnum.VALID.getType())
                .eq(ChannelActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType());
        ChannelActivity act = channelActivityMapper.selectOne(queryWrapper);

        if (act == null) {
            // 查询 id最大的
            queryWrapper = new LambdaQueryWrapper<ChannelActivity>()
                    .eq(ChannelActivity::getDiscountCode, discountCode)
                    .orderByDesc(ChannelActivity::getId)
                    .last("limit 1");
            act = channelActivityMapper.selectOne(queryWrapper);
            if (act == null) {
                log.error("未查询到渠道活动详情, discountCode = {}", discountCode);
                throw new BusinessException(ErrorEnum.GET_DETAIL_ERROR1.getCode(), ErrorEnum.GET_DETAIL_ERROR1.getMsg());
            }
        }
        GetChannelActDetailFromDiscountCodeBo bo = new GetChannelActDetailFromDiscountCodeBo();
        bo.setId(act.getId());
        bo.setChannelText(getChannelName(act.getSecondAppKey()));
        bo.setAppKey(act.getSecondAppKey());
        bo.setDiscountCode(act.getDiscountCode());
        bo.setOrgCodes(Arrays.stream(act.getOrgCodes().split(",")).collect(Collectors.toList()));
        bo.setActName(act.getActivityName());
        bo.setActStatus(act.getActivityStatus());
        bo.setStoreIds(Arrays.stream(act.getStoreIds().split(",")).map(Long::parseLong).collect(Collectors.toList()));
        bo.setCatIds(Arrays.stream(act.getCarModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList()));
        bo.setActType(act.getActivityType());
        bo.setDiscountLatitude(act.getDiscountLatitude());
        bo.setDiscountMethod(act.getDiscountMethod());
        bo.setDiscountConditional1(act.getDiscountCondition1());
        bo.setDiscountConditional2(act.getDiscountCondition2());
        bo.setRestrictDiscounts(act.getRestrictDiscounts());
        bo.setMaxDiscountAmount(act.getMaxDiscountAmount().toString());
        bo.setMaxRentDays(act.getMaxRentDays());
        bo.setMinRentDays(act.getMinRentDays());
        bo.setActStartDate(act.getActivityStartDate().toString());
        bo.setActEndDate(act.getActivityEndDate().toString());
        bo.setPickupStartDate(act.getPickUpStartDate().toString());
        bo.setPickupEndDate(act.getPickUpEndDate().toString());
        bo.setReturnStartDate(act.getReturnStartDate().toString());
        bo.setReturnEndDate(act.getReturnEndDate().toString());
        bo.setUnavailableDateRanges(JSON.parseObject(act.getUnavailableDateRanges(), UnavailableDateRanges.class).getUnavailableDateRanges());
        bo.setCostBearingParty(act.getCostBearingParty());
        bo.setCostAllocationMethod(act.getCostAllocationMethod());
        bo.setMerchantBear(act.getMerchantBear().toString());
        bo.setPlatformBear(act.getPlatformBear().toString());
        return bo;
    }

}
