package com.saicmobility.evcard.md.act.bo.market;

import com.saicmobility.evcard.md.act.dto.market.TimeRange;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class GetChannelActDetailFromDiscountCodeBo {
    private long id;
    private String channelText; //渠道类型：1-携程、2-哈罗、3-飞猪、4-悟空、5-高德、6-滴滴
    private String discountCode; //优惠码
    private List<String> orgCodes; //机构
    private String actName; //活动名称
    private int actStatus; //活动状态：1-生效、2-已过期
    private List<Long> storeIds;
    private List<Long> catIds;
    private int actType; //活动类型：1-满减、2-打折、3-减至
    private int discountLatitude; //优惠纬度：1-车辆租金、2-订单整单
    private int discountMethod; //优惠方式：1-针对金额、2-针对租期、3-满天减天、4-满天减金额
    private String discountConditional1; //优惠条件1 满...
    private String discountConditional2; //优惠条件2 减至...元//减至...天//打...折
    private int restrictDiscounts; //是否限制优惠：1-有限制、2-无限制
    private String maxDiscountAmount; //最高优惠金额，当restrictDiscounts=1才需要设置此字段
    private int maxRentDays; //最大租期，当restrictDiscounts=1才需要设置此字段
    private int minRentDays; //最小租期，当restrictDiscounts=1才需要设置此字段
    private String actStartDate; //活动开始时间
    private String actEndDate; //活动结束时间
    private String pickupStartDate; //取车开始时间
    private String pickupEndDate; //取车结束时间
    private String returnStartDate; //还车开始时间
    private String returnEndDate; //还车结束时间
    private List<TimeRange> unavailableDateRanges; //不可用时间范围
    private int costBearingParty; //成本承担方：1-平台全部承担、2-商家全部承担、3-共同承担
    private int costAllocationMethod; //成本分摊方式：1-百分比设置、2-按固定金额设置
    private String merchantBear; //商户承担(百分比或金额)
    private String platformBear; //平台承担(百分比或金额)
    private String appKey; //二级渠道Key

    public List<com.saicmobility.evcard.md.mdactservice.api.TimeRange> toUnavailableList(List<TimeRange> unavailableDateRanges) {
        List<com.saicmobility.evcard.md.mdactservice.api.TimeRange> list = new ArrayList<>();
        for (TimeRange timeRange : unavailableDateRanges) {
            com.saicmobility.evcard.md.mdactservice.api.TimeRange tr = com.saicmobility.evcard.md.mdactservice.api.TimeRange.newBuilder()
                    .setStartDate(timeRange.getStartDate())
                    .setEndDate(timeRange.getEndDate())
                    .build();
            list.add(tr);
        }
        return list;
    }

}
