package com.saicmobility.evcard.md.act.enums.channelcoupon;

/**
 * 操作类型 1=渠道发放 2=渠道用户手动退 3=渠道过期自动退
 */
public enum CCBOperationEnum {

    CHANNEL_GIVE(1, "渠道发放"),
    CHANNEL_USER_MANUAL_RETURN(2, "渠道用户手动退"),
    GETCHANNEL_EXPIRE_AUTO_RETURN(3, "渠道过期自动退"),
    ;
    private Integer type;
    private String msg;

    CCBOperationEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }


    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

}
