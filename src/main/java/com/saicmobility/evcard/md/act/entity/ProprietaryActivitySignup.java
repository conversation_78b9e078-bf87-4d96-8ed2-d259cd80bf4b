package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 自营活动报名表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_proprietary_activity_signup")
@ApiModel(value = "ProprietaryActivitySignup对象", description = "自营活动报名表")
public class ProprietaryActivitySignup extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "报名状态  1：未参加  2：已参加")
    private Integer signupStatus;

    @ApiModelProperty(value = "报名门店运营机构id")
    private String orgCode;

    @ApiModelProperty(value = "报名门店运营机构id")
    private Long storeId;

    @ApiModelProperty(value = "车型id集合")
    private String vehicleModelIds;

    @ApiModelProperty(value = "是否全部车型(0:否,1:是)")
    private Integer isAllVehicle;

    @ApiModelProperty(value = "满减灵活定价，格式如：[{\"days\":\"3\",\"discountAmount\":\"50\"},{\"days\":\"3\",\"discountAmount\":\"50\"}]")
    private String fullMinusFlexiblePricing;

    @ApiModelProperty(value = "打折灵活定价，格式如：[{\"days\":\"3\",\"discount\":\"50\"},{\"days\":\"3\",\"discount\":\"50\"}]")
    private String discountFlexiblePricing;
}
