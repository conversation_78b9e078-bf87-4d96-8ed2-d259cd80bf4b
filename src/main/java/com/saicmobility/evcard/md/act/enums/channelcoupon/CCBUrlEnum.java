package com.saicmobility.evcard.md.act.enums.channelcoupon;

import com.saicmobility.evcard.md.act.config.CcbConfig;

public enum CCBUrlEnum {
    CALL_BACK_COUPON_STATUS(CcbConfig.callBackCouponStatusCode, CcbConfig.callBackCouponStatusUrl, "4.5-回推优惠券状态"),
    RECONCILIATION_FILE(CcbConfig.reconciliationFileTransCode, CcbConfig.reconciliationFileUrl, "4.7-推送对账文件"),


    ;


    CCBUrlEnum(String transCode, String url, String remark) {
        this.transCode = transCode;
        this.url = url;
        this.remark = remark;
    }

    // 交易码
    private String transCode;

    //url
    private String url;
    //备注
    private String remark;


    public String getTransCode() {
        return transCode;
    }


    public String getUrl() {
        return url;
    }


    public String getRemark() {
        return remark;
    }

}
