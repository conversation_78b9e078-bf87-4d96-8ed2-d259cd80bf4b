package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 活动送券关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mmp_third_coupon")
@ApiModel(value="MmpThirdCoupon对象", description="活动送券关联表")
public class MmpThirdCoupon extends Model<MmpThirdCoupon> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关联主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "活动送券配置ID对应 mmp_third_activity.id")
    private Long thirdActivityId;

    @ApiModelProperty(value = "优惠券模板编号")
    private Long couponSeq;

    @ApiModelProperty(value = "优惠券发放张数")
    private Integer offerQuantity;

    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    @ApiModelProperty(value = "优惠券到账几天有效")
    @TableField("EFFECTIVE_DAYS")
    private Integer effectiveDays;

    @ApiModelProperty(value = "优惠券有效时长，优惠券的有效期为发券时间 + 有效时长。当VALID_TIME_TYPE字段值为1时，该字段值为0")
    @TableField("VALID_DAYS")
    private Integer validDays;

    @ApiModelProperty(value = "优惠券有效期开始时间 YYYY-MM-DD")
    @TableField("START_DATE")
    private String startDate;

    @ApiModelProperty(value = "优惠券有效结束时间  YYYY-MM-DD")
    @TableField("EXPIRES_DATE")
    private String expiresDate;

    @ApiModelProperty(value = "优惠券有效期类型（1起止时间 2时长）")
    @TableField("VALID_TIME_TYPE")
    private Integer validTimeType;

    @ApiModelProperty(value = "发放机构ID")
    private String orgId;

    @ApiModelProperty(value = "优惠券类型 1:直扣 2：折扣 3:非收入券 4:购买类")
    private Integer couponType;

    @ApiModelProperty(value = "优惠券限制（0-全国券 1-地域券）")
    private Integer couponLimit;

    @ApiModelProperty(value = "发券目标对象，0缺省 1仅老用户 2仅新用户")
    private Integer couponTarget;

    @ApiModelProperty(value = "发券时机，0缺省， 对于邀新活动 1注册 2审核通过 3订单完成")
    private Integer offerTiming;


}
