package com.saicmobility.evcard.md.act.service.impl.coupon;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.coupon.dto.CouponListDto;
import com.extracme.evcard.rpc.coupon.dto.CouponModelListDto;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.constant.BusinessConst;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.domain.ActivityCouponDTO;
import com.saicmobility.evcard.md.act.domain.coupon.CouponConditionDto;
import com.saicmobility.evcard.md.act.dto.welfare.CouponWelfareInfo;
import com.saicmobility.evcard.md.act.entity.MmpThirdCoupon;
import com.saicmobility.evcard.md.act.entity.siac.MemberCdk;
import com.saicmobility.evcard.md.act.entity.siac.MemberCdkRelation;
import com.saicmobility.evcard.md.act.entity.siac.MmpPackNightActivity;
import com.saicmobility.evcard.md.act.mapper.siac.*;
import com.saicmobility.evcard.md.act.service.coupun.CouponService;
import com.saicmobility.evcard.md.act.service.coupun.IMmpThirdCouponService;
import com.saicmobility.evcard.md.act.service.extern.MemberService;
import com.saicmobility.evcard.md.act.service.extern.StoreService;
import com.saicmobility.evcard.md.act.service.inner.CouponModelService;
import com.saicmobility.evcard.md.act.service.rest.CouponRestClient;
import com.saicmobility.evcard.md.act.service.rest.entity.activity.OfferThirdCouponRequest;
import com.saicmobility.evcard.md.act.service.rest.entity.activity.OfferThirdCouponsResponse;
import com.saicmobility.evcard.md.act.service.rest.entity.coupon.*;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.act.util.NumberUtils;
import com.saicmobility.evcard.md.mdactservice.api.*;
import com.saicmobility.evcard.md.mduserservice.api.MdUserService;
import com.saicmobility.evcard.md.mduserservice.api.MemberBasicInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class CouponServiceImpl implements CouponService {

    @Resource
    private MdUserService mdUserService;

    @Resource
    private OrgInfoMapper orgInfoMapper;

    @Resource
    private UserCouponListMapper userCouponListMapper;

    @Resource
    private UserCouponAccountMapper userCouponAccountMapper;

    @Resource
    private UserCouponTransactionRecordMapper userCouponTransactionRecordMapper;

    @Autowired
    private CouponRestClient couponRestClient;

    @Autowired
    private MemberService memberService;

    @Resource
    private StoreService storeService;

    @Resource
    private MmpPackNightActivityMapper mmpPackNightActivityMapper;

    @Resource
    private MmpUserTagMapper mmpUserTagMapper;

    @Resource
    private MemberCdkMapper memberCdkMapper;
    @Resource
    private MemberCdkRelationMapper memberCdkRelationMapper;
    @Resource
    private IMmpThirdCouponService mmpThirdCouponService;

    @Override
    public UseCouponRes useCouponRest(UseCouponReq req) {
        if (StringUtils.isBlank(req.getMid())) {
            return UseCouponRes.failed(ErrorEnum.PARAMS_ERR_LACK_MID.getCode(), ErrorEnum.PARAMS_ERR_LACK_MID.getMsg());
        }
        if (req.getUserCouponSeq() <= 0) {
            return UseCouponRes.failed(ErrorEnum.PARAMS_ERR_LACK_MID.getCode(), ErrorEnum.PARAMS_ERR_LACK_MID.getMsg());
        }

        if (StringUtils.isBlank(req.getOrderOrgCode())) {
            return UseCouponRes.failed(-1000, "核销优惠券的订单所属机构不能为空");
        }
        if (StringUtils.isBlank(req.getOperatorName()) || req.getOperatorId() <= 0) {
            return UseCouponRes.failed(-1000, "操作人不能为空");
        }
        MemberBasicInfo member = memberService.getMemberByMId(req.getMid());
        if (member == null) {
            return UseCouponRes.failed(ErrorEnum.MEMBER_NOT_FOUND.getCode(), ErrorEnum.MEMBER_NOT_FOUND.getMsg());
        }
        UseCouponRequest request = UseCouponRequest.fromRes(req, member.getAuthId());
        UseCouponResponse response = null;
        try {
            response = couponRestClient.useCoupons(request);
        } catch (BusinessException e) {
            return UseCouponRes.failed(ErrorEnum.REQUEST_ERROR.getCode(), ErrorEnum.REQUEST_ERROR.getMsg());
        }
        UseCouponRes res = UseCouponRes.newBuilder().setRetCode(response.getCode())
                .setRetMsg(response.getMessage()).build();
        return res;
    }

    @Override
    public OrderCouponRes orderCoupons(OrderCouponReq req) {
        OrderCouponCondition orderCouponCondition = req.getOrderCouponCondition();
        if (StringUtils.isBlank(orderCouponCondition.getPickupTime())) {
            return OrderCouponRes.failed(-1000, "取车时间不能为空");
        }

        if (StringUtils.isBlank(orderCouponCondition.getPickUpStoreId())) {
            return OrderCouponRes.failed(-1000, "取车门店不能为空");
        }

        if (StringUtils.isBlank(orderCouponCondition.getMid())) {
            return OrderCouponRes.failed(ErrorEnum.PARAMS_ERR_LACK_MID.getCode(), ErrorEnum.PARAMS_ERR_LACK_MID.getMsg());
        }
        MemberBasicInfo member = memberService.getMemberByMId(orderCouponCondition.getMid());
        if (member == null) {
            return OrderCouponRes.failed(ErrorEnum.MEMBER_NOT_FOUND.getCode(), ErrorEnum.MEMBER_NOT_FOUND.getMsg());
        }
        OrderCouponsRequest request = OrderCouponsRequest.fromRes(req);
        if (StringUtils.isBlank(orderCouponCondition.getPickUpCity())) {
            Long storeCity = storeService.getStoreCityByStoreId(Long.valueOf(orderCouponCondition.getPickUpStoreId()));
            request.setPickshopCity(null == storeCity ? "" : storeCity.toString());
        }
        if (StringUtils.isBlank(orderCouponCondition.getReturnCity())) {
            Long storeCity = storeService.getStoreCityByStoreId(Long.valueOf(orderCouponCondition.getReturnStoreId()));
            request.setReturnshopCity(null == storeCity ? "" : storeCity.toString());
        }
        request.setAuthId(member.getAuthId());
        try {
            OrderCouponsResponse response = couponRestClient.orderCoupons(request);
            OrderCouponRes res = response.toResBuilder(orderCouponCondition.getMid()).build();
            return res;
        } catch (BusinessException e) {
            return OrderCouponRes.failed(ErrorEnum.REQUEST_ERROR.getCode(), ErrorEnum.REQUEST_ERROR.getMsg());
        }
    }

    @Override
    public BatchOrderCouponRes batchOrderCoupons(BatchOrderCouponReq req) {
        // 入参校验
        OrderCouponCondition orderCouponCondition = req.getOrderCouponCondition();
        if (StringUtils.isBlank(orderCouponCondition.getPickupTime())) {
            return BatchOrderCouponRes.failed(-1000, "取车时间不能为空");
        }

        if (StringUtils.isBlank(orderCouponCondition.getPickUpStoreId())) {
            return BatchOrderCouponRes.failed(-1000, "取车门店不能为空");
        }

        if (StringUtils.isBlank(orderCouponCondition.getPickUpCity())) {
            return BatchOrderCouponRes.failed(-1001, "取车门店城市不能为空");
        }

        if (StringUtils.isBlank(orderCouponCondition.getReturnCity())) {
            return BatchOrderCouponRes.failed(-1001, "还车门店城市不能为空");
        }

        if (StringUtils.isBlank(orderCouponCondition.getMid())) {
            return BatchOrderCouponRes.failed(ErrorEnum.PARAMS_ERR_LACK_MID.getCode(), ErrorEnum.PARAMS_ERR_LACK_MID.getMsg());
        }

        MemberBasicInfo member = memberService.getMemberByMId(orderCouponCondition.getMid());
        if (member == null) {
            return BatchOrderCouponRes.failed(ErrorEnum.MEMBER_NOT_FOUND.getCode(), ErrorEnum.MEMBER_NOT_FOUND.getMsg());
        }

        BatchOrderCouponsRequest request = BatchOrderCouponsRequest.fromRes(req);
        request.getOrderCouponDto().setAuthId(member.getAuthId());
        try {
            BatchOrderCouponsResponse response = couponRestClient.batchOrderCoupons(request);
            BatchOrderCouponRes res = response.toResBuilder(orderCouponCondition.getMid());
            return res;
        } catch (BusinessException e) {
            return BatchOrderCouponRes.failed(ErrorEnum.REQUEST_ERROR.getCode(), ErrorEnum.REQUEST_ERROR.getMsg());
        }
    }

    public CouponViewDes convertCouponViewDes(CouponListDto couponDto, String mid) {
        CouponViewDes.Builder builder = CouponViewDes.newBuilder();
        CouponInfo.Builder couponInfoBuilder = CouponInfo.newBuilder();
        //处理商品车型(多个)
        String goodsVehicleModel = couponDto.getGoodsVehicleModel();
        couponInfoBuilder.setGoodsModelId(goodsVehicleModel);
        String goodsModelIdToDes = couponModelService.goodsModelIdToDes(goodsVehicleModel);
        couponInfoBuilder.setGoodsModelName(goodsModelIdToDes);

        //TODO 此门店信息组织在coupons已经需要
        //处理取车门店(多个)
       /* String pickupStoreIds = couponDto.getPickupStoreIds();
        String pickUpStoreNames = couponModelService.storeIdToDes(pickupStoreIds);
        couponInfoBuilder.setPickUpStoreId(pickupStoreIds);
        couponInfoBuilder.setPickUpStoreName(pickUpStoreNames);*/

        //处理还车门店(多个)
       /* String returnStoreIds = couponDto.getReturnStoreIds();
        couponInfoBuilder.setReturnStoreId(returnStoreIds);
        if (StringUtils.equals(pickupStoreIds, returnStoreIds)) {
            couponInfoBuilder.setReturnStoreName(pickUpStoreNames);
        } else {
            String returnStoreNames = couponModelService.storeIdToDes(returnStoreIds);
            couponInfoBuilder.setReturnStoreName(returnStoreNames);
        }*/

        //城市编号与名称是统一的，不需要另外获取
        couponInfoBuilder.setPickUpCity(couponDto.getPickshopCity());
        couponInfoBuilder.setPickUpCityName(couponDto.getPickshopCityName());
        couponInfoBuilder.setReturnCity(couponDto.getReturnshopCity());
        couponInfoBuilder.setReturnCityName(couponDto.getReturnshopCityName());

        if (couponDto.getTransactionType() == null) {
            couponInfoBuilder.setTransactionType(couponDto.getCouponType());
        }
        couponInfoBuilder.setUserCouponSeq(couponDto.getUserCouponSeq())
                .setMid(mid)
                .setCouponSeq(Optional.ofNullable(couponDto.getCouponSeq()).orElse(0L))
                .setStartDate(couponDto.getStartDate())
                .setExpiresDate(couponDto.getExpiresDate())
                .setStatus(Optional.ofNullable(couponDto.getStatus()).orElse(0))
                .setCouponOrigin(couponDto.getCouponOrigin())
                .setCouponValue(NumberUtils.getStr(couponDto.getCouponValue()))
                .setDes(couponDto.getDes())
                .setCouponType(couponDto.getCouponType())
                .setServiceType(couponDto.getServiceType())
                .setMinAmount(NumberUtils.getStr(couponDto.getMinAmount()))
                .setTimeType(Optional.ofNullable(couponDto.getTimeType()).orElse(0))
                .setStartTime(couponDto.getStartTime())
                .setEndTime(couponDto.getEndTime())
                .setVehicleNo(couponDto.getVehicleNo())
                .setDiscountRate(Optional.ofNullable(couponDto.getDiscountRate()).orElse(0))
                .setOrgCode(null == couponDto.getOrgSeq() ? "" : couponDto.getOrgSeq().toString())
                .setOrderNo(couponDto.getOrderSeq())
                .setDiscount(null == couponDto.getDiscount() ? "0.00" : couponDto.getDiscount().toString())
                //.setActivityOverlap(Optional.ofNullable(couponDto.getActivityOverlap()).orElse(0))
                //.setPackageIds(couponDto.getPackageIds())
                //.setHolidaysAvailable(Optional.ofNullable(couponDto.getHolidaysAvailable()).orElse(0))
                //.setAvailableDaysOfWeek(couponDto.getAvailableDaysOfWeek())
                .setRentMethod(couponDto.getRentMethod())
                .setRentMethodGroup(couponDto.getRentMethodGroup())
                .setUseMethod(couponDto.getUseMethod())
                .setDurationLimit(NumberUtils.getStr(couponDto.getDurationLimit()));
        //.setAgencyId(couponDto.getAgencyId())
        //.setActionId(couponDto.getActionId());
        builder.setCoupon(couponInfoBuilder.build())
                .addAllTags(couponDto.getTags())
                .setSpace1(couponDto.getSpace1())
                .addAllSpace2(couponDto.getSpace2())
                .addAllSpace3(couponDto.getSpace3())
                .setAvailability(couponDto.getAvailability())
                .setRemainTimeTag(couponDto.getRemainTimeTag())
                .setVehicleLimitDesc(couponDto.getVehicleLimitDesc());
        return builder.build();
    }

    @Override
    public CheckOrderCouponRes checkOrderCoupon(CheckOrderCouponReq req) {
        CheckOrderCouponRes.Builder builder = CheckOrderCouponRes.newBuilder();
        OrderCouponCondition orderCouponCondition = req.getOrderCouponCondition();

        /*if (StrUtil.isBlank(orderCouponCondition.getPickUpStoreId())) {
            return CheckOrderCouponRes.failed(-1001, "取车门店不能为空");
        }

        if (StrUtil.isBlank(orderCouponCondition.getReturnStoreId())) {
            return CheckOrderCouponRes.failed(-1001, "还车门店不能为空");
        }*/

        if (req.getUserCouponSeq() <= 0) {
            return CheckOrderCouponRes.failed(ErrorEnum.INVALID_COUPON_SEQ.getCode(), ErrorEnum.INVALID_COUPON_SEQ.getMsg());
        }

        if (StringUtils.isBlank(orderCouponCondition.getMid())) {
            return CheckOrderCouponRes.failed(ErrorEnum.PARAMS_ERR_LACK_MID.getCode(), ErrorEnum.PARAMS_ERR_LACK_MID.getMsg());
        }
        MemberBasicInfo member = memberService.getMemberByMId(orderCouponCondition.getMid());
        if (member == null) {
            return CheckOrderCouponRes.failed(ErrorEnum.MEMBER_NOT_FOUND.getCode(), ErrorEnum.MEMBER_NOT_FOUND.getMsg());
        }
        CheckOrderCouponRequest request = CheckOrderCouponRequest.fromRes(req, member.getAuthId());
        CheckOrderCouponResponse response = null;
        try {
            response = couponRestClient.checkOrderCoupon(request);
        } catch (Exception e) {
            return CheckOrderCouponRes.failed(ErrorEnum.REQUEST_ERROR.getCode(), ErrorEnum.REQUEST_ERROR.getMsg());
        }
        return builder.setRetCode(response.getCode()).setRetMsg(response.getMessage()).build();
    }

    @Resource
    private CouponModelService couponModelService;

    @Override
    public GetCouponRes getCoupon(GetCouponReq req) {
        GetCouponRes.Builder builder = GetCouponRes.newBuilder();
        CouponInfo.Builder couponInfoBuilder = CouponInfo.newBuilder();
        long userCouponSeq = req.getUserCouponSeq();
        String mid = req.getMid();
        if (userCouponSeq == 0) {
            return builder.setRetCode(-1000).setRetMsg("优惠券id不能为空").build();
        }
        if (userCouponSeq == -1) {
            return builder.build();
        }

        //将mid转化为authId
        String authId = "";
        if (StrUtil.isNotBlank(mid)){
            authId = memberService.getMemberAuthId(mid);
        }

        CouponConditionDto couponDto = userCouponListMapper.findCouponByAuthIdAndUserCouponSeq(authId, userCouponSeq);
        if (couponDto == null) {
            return builder.build();
        }

        //处理商品车型(多个)
        String goodsVehicleModel = couponDto.getGoodsVehicleModel();
        couponInfoBuilder.setGoodsModelId(goodsVehicleModel);
        String goodsModelIdToDes = couponModelService.goodsModelIdToDes(goodsVehicleModel);
        couponInfoBuilder.setGoodsModelName(goodsModelIdToDes);

        /*//处理取车门店(多个)
        String pickupStoreIds = couponDto.getPickupStoreIds();
        String pickUpStoreNames = couponModelService.storeIdToDes(pickupStoreIds);
        couponInfoBuilder.setPickUpStoreId(pickupStoreIds);
        couponInfoBuilder.setPickUpStoreName(pickUpStoreNames);

        //处理还车门店(多个)
        String returnStoreIds = couponDto.getReturnStoreIds();
        couponInfoBuilder.setReturnStoreId(returnStoreIds);
        if (StringUtils.equals(pickupStoreIds, returnStoreIds)) {
            couponInfoBuilder.setReturnStoreName(pickUpStoreNames);
        } else {
            String returnStoreNames = couponModelService.storeIdToDes(returnStoreIds);
            couponInfoBuilder.setReturnStoreName(returnStoreNames);
        }*/

        //处理取车门店/网点所在城市(多个)
        String pickShopCity = couponDto.getPickshopCity();
        //String pickShopCityName = couponModelService.shopCityToDes(pickShopCity);
        couponInfoBuilder.setPickUpCity(pickShopCity);
        //couponInfoBuilder.setPickUpCityName(pickShopCityName);

        //处理还车门店/网点所在城市(多个)
        String returnShopCity = couponDto.getReturnshopCity();
        couponInfoBuilder.setReturnCity(returnShopCity);
        //取还车城市一致
        /*if (StringUtils.equals(returnShopCity, pickShopCity)) {
            couponInfoBuilder.setReturnCityName(pickShopCityName);
        } else {
            String returnShopCityNames = couponModelService.shopCityToDes(returnShopCity);
            couponInfoBuilder.setReturnCityName(returnShopCityNames);
        }*/

        couponInfoBuilder.setTransactionType(Optional.ofNullable(couponDto.getTransactionType()).orElse(couponDto.getCouponType()));
        couponInfoBuilder.setUserCouponSeq(couponDto.getUserCouponSeq())
                .setMid(mid)
                .setCouponSeq(Optional.ofNullable(couponDto.getCouponSeq()).orElse(0L))
                .setStartDate(couponDto.getStartDate())
                .setExpiresDate(couponDto.getExpiresDate())
                .setStatus(Optional.ofNullable(couponDto.getStatus()).orElse(0))
                .setCouponOrigin(couponDto.getCouponOrigin())
                .setCouponValue(NumberUtils.getStr(couponDto.getCouponValue()))
                .setDes(couponDto.getDes())
                .setCouponType(Optional.ofNullable(couponDto.getCouponType()).orElse(1))
                .setServiceType(Optional.ofNullable(couponDto.getServiceType()).orElse(1))
                .setMinAmount(NumberUtils.getStr(couponDto.getMinAmount()))
                .setTimeType(Optional.ofNullable(couponDto.getTimeType()).orElse(0))
                .setStartTime(couponDto.getStartTime())
                .setEndTime(couponDto.getEndTime())
                .setVehicleNo(couponDto.getVehicleNo())
                .setDiscountRate(Optional.ofNullable(couponDto.getDiscountRate()).orElse(100))
                .setOrgCode(null == couponDto.getOrgSeq() ? "" : couponDto.getOrgSeq().toString())
                .setOrderNo(couponDto.getOrderSeq())
                .setDiscount(NumberUtils.getStr(couponDto.getDiscount()))
                .setActivityOverlap(Optional.ofNullable(couponDto.getActivityOverlap()).orElse(0))
                .setPackageIds(couponDto.getPackageIds())
                .setHolidaysAvailable(Optional.ofNullable(couponDto.getHolidaysAvailable()).orElse(1))
                .setAvailableDaysOfWeek(couponDto.getAvailableDaysOfWeek())
                .setRentMethod(couponDto.getRentMethod())
                .setRentMethodGroup(couponDto.getRentMethodGroup())
                .setUseMethod(couponDto.getUseMethod())
                .setDurationLimit(NumberUtils.getStr(couponDto.getDurationLimit()))
                .setAgencyId(couponDto.getAgencyId())
                .setActionId(couponDto.getActionId());

        return builder.setCoupon(couponInfoBuilder.build()).build();
    }

    @Override
    public FrozenCouponRes frozenCoupon(FrozenCouponReq req) {
        FrozenCouponRes.Builder builder = FrozenCouponRes.newBuilder();
        long userCouponSeq = req.getUserCouponSeq();
        int frozen = req.getFrozen();
        String orderNo = req.getOrderNo();
        if (0 == userCouponSeq || StringUtils.isBlank(orderNo)) {
            return builder.setRetCode(-1000).setRetMsg("优惠券编号不能为空").build();
        }
        String updateTime = DateUtil.dateToString(LocalDateTime.now(), DateUtil.DATE_TYPE3);
        int result = userCouponListMapper.frozenCoupon(userCouponSeq, frozen, orderNo, updateTime);
        if (result > 0) {
            return FrozenCouponRes.ok();
        } else {
            return builder.setRetCode(-1000).setRetMsg("冻结优惠券失败").build();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ModifyOrderOperateCouponRes unFrozenThenFrozenCoupon(ModifyOrderOperateCouponReq req) {
        ModifyOrderOperateCouponRes.Builder builder = ModifyOrderOperateCouponRes.newBuilder();
        long oldUserCouponSeq = req.getOldUserCouponSeq();
        String oldOrderNo = req.getOldOrderNo();

        String newOrderNo = req.getNewOrderNo();
        long newUserCouponSeq = req.getNewUserCouponSeq();
        if (0 == oldUserCouponSeq || StringUtils.isBlank(oldOrderNo) || 0 == oldUserCouponSeq || StringUtils.isBlank(oldOrderNo)) {
            return builder.setRetCode(-1000).setRetMsg("优惠券编号不能为空").build();
        }
        String updateTime = DateUtil.dateToString(LocalDateTime.now(), DateUtil.DATE_TYPE3);

        int result1 = userCouponListMapper.frozenCoupon(oldUserCouponSeq, 0, oldOrderNo, updateTime);
        if (result1 > 0) {
            int result2 = userCouponListMapper.frozenCoupon(newUserCouponSeq, 1, newOrderNo, updateTime);
            if (result2 > 0) {
                return ModifyOrderOperateCouponRes.ok();
            } else {
                return builder.setRetCode(-1000).setRetMsg("冻结优惠券失败").build();
            }
        } else {
            return builder.setRetCode(-1000).setRetMsg("解冻优惠券失败").build();
        }
    }

    @Override
    public GetCouponViewRes getCouponView(GetCouponViewReq req) {
        if (req.getUserCouponSeq() <= 0) {
            return GetCouponViewRes.failed(ErrorEnum.INVALID_COUPON_SEQ.getCode(), ErrorEnum.INVALID_COUPON_SEQ.getMsg());
        }
        if (StringUtils.isBlank(req.getMid())) {
            return GetCouponViewRes.failed(ErrorEnum.PARAMS_ERR_LACK_MID.getCode(), ErrorEnum.PARAMS_ERR_LACK_MID.getMsg());
        }
        String authId = memberService.getMemberAuthId(req.getMid());
        if (StringUtils.isBlank(authId)) {
            return GetCouponViewRes.failed(ErrorEnum.MEMBER_NOT_FOUND.getCode(), ErrorEnum.MEMBER_NOT_FOUND.getMsg());
        }
        GetCouponViewResponse response = null;
        try {
            response = couponRestClient.getCouponView(GetCouponViewRequest.from(req, authId));
            if(response == null) {
                return GetCouponViewRes.ok();
            }
        } catch (BusinessException e) {
            return GetCouponViewRes.failed(ErrorEnum.REQUEST_ERROR.getCode(), ErrorEnum.REQUEST_ERROR.getMsg());
        }
        return response.toRes(req.getMid());
    }

    @Override
    public GetCouponModelViewRes getCouponModelView(GetCouponModelReq req) {
        if (req.getCouponSeq() <= 0) {
            return GetCouponModelViewRes.failed(-1000, "优惠券模板编号不能为空");
        }

        GetCouponModelViewResponse response = null;
        try {
            response = couponRestClient.getCouponModelView(req.getCouponSeq());
        } catch (BusinessException e) {
            return GetCouponModelViewRes.failed(ErrorEnum.REQUEST_ERROR.getCode(), ErrorEnum.REQUEST_ERROR.getMsg());
        }
        GetCouponModelViewRes.Builder builder = GetCouponModelViewRes.newBuilder();
        if (ObjectUtil.isNotNull(response)) {
            CouponModelView couponModelView = convertCouponModelView(response);
            builder.setCouponModel(couponModelView);
        }
        return builder.build();
    }

    public CouponModelView convertCouponModelView(CouponModelListDto dto) {
        CouponModelView.Builder builder = CouponModelView.newBuilder();
        builder.setCouponSeq(Optional.ofNullable(dto.getCouponSeq()).orElse(0))
                .setCouponType(Optional.ofNullable(dto.getCouponType()).orElse(0))
                .setTimeType(Optional.ofNullable(dto.getTimeType()).orElse(0))
                .setStartTime(dto.getStartTime())
                .setEndTime(dto.getEndTime())
                .setMinAmount(null == dto.getMinAmount() ? "0.00" : dto.getMinAmount().toString())
                .setCouponValue(null == dto.getCouponValue() ? "0.00" : dto.getCouponValue().toString())
                .setDes(dto.getDes())
                //.setPickUpStoreId()
                //.setPickUpStoreName()
                //.setReturnStoreId()
                //.setReturnStoreName()
                //.setGoodsModelId()
                //.setGoodsModelName()
                .setPickUpCity(dto.getPickshopCity())
                .setPickUpCityName(dto.getPickshopCityName())
                .setReturnCity(dto.getReturnshopCity())
                .setReturnCityName(dto.getReturnshopCityName())
                .setDiscountRate(Optional.ofNullable(dto.getDiscountRate()).orElse(0))
                .setVehicleNo(dto.getVehicleNo())
                .setServiceType(Optional.ofNullable(dto.getServiceType()).orElse(0))
                .setCouponName(dto.getCouponName())
                .setValidTimeType(Optional.ofNullable(dto.getValidTimeType()).orElse(0))
                .setValidDays(Optional.ofNullable(dto.getValidDays()).orElse(0))
                .setEffectiveDays(Optional.ofNullable(dto.getEffectiveDays()).orElse(0))
                .setStartDate(dto.getStartDate())
                .setExpiresDate(dto.getExpiresDate())
                .setActivityOverlap(Optional.ofNullable(dto.getActivityOverlap()).orElse(0))
                .setPackageIds(dto.getPackageIds())
                .setHolidaysAvailable(Optional.ofNullable(dto.getHolidaysAvailable()).orElse(0))
                .setAvailableDaysOfWeek(dto.getAvailableDaysOfWeek())
                .setRentMethod(dto.getRentMethod())
                .setRentMethodGroup(dto.getRentMethodGroup())
                .setUseMethod(dto.getUseMethod())
                .setServiceTypeDesc(dto.getServiceTypeDesc())
                .setRentMethodDesc(dto.getRentMethodDesc())
                //.setRentMethodGroupDesc()
                .setDurationLimit(null == dto.getDurationLimit() ? "0.00" : dto.getDurationLimit().toString())
                .setVehicleLimitDesc(dto.getVehicleLimitDesc())
                .setSpace1(dto.getSpace1());
        if (BusinessConst.STORE_TYPE.equals(dto.getShopLimitType())) {
            builder.setPickUpStoreId(dto.getPickshopSeq())
                    .setPickUpStoreName(dto.getPickshopName())
                    .setReturnStoreId(dto.getReturnshopSeq())
                    .setReturnStoreName(dto.getReturnshopName());
        }
        if (CollectionUtil.isNotEmpty(dto.getTags())) {
            builder.addAllTags(dto.getTags());
        }
        if (CollectionUtil.isNotEmpty(dto.getSpace2())) {
            builder.addAllSpace2(dto.getSpace2());
        }

        return builder.build();

    }

    @Override
    public GetCouponModelListViewRes getCouponModelListView(GetCouponModelListReq req) {
        List<Integer> couponSeqList = req.getCouponSeqList();
        if (CollectionUtil.isEmpty(couponSeqList)) {
            return GetCouponModelListViewRes.failed(-1000, "优惠券模板编号不能为空");
        }
        GetCouponModelListViewResponse response = null;
        try {
            response = couponRestClient.getCouponModelListView(couponSeqList);
        } catch (BusinessException e) {
            return GetCouponModelListViewRes.failed(ErrorEnum.REQUEST_ERROR.getCode(), ErrorEnum.REQUEST_ERROR.getMsg());
        }
        GetCouponModelListViewRes.Builder builder = GetCouponModelListViewRes.newBuilder();
        if (ObjectUtil.isNotNull(response)) {
            List<CouponModelView> list = response.stream().map(this::convertCouponModelView).collect(Collectors.toList());
            builder.addAllCouponModel(list);
        }

        return builder.build();
    }

    @Override
    public OfferThirdCouponsRes offerThirdCoupons(OfferThirdCouponsReq req) {
        if (StringUtils.isBlank(req.getMid()) || StringUtils.isBlank(req.getOptUser())) {
            OfferThirdCouponsRes.failed(ErrorEnum.PARAM_LACK.getCode(), "用户信息不能为空");
        }

        if (req.getActivityCouponId() <= 0) {
            OfferThirdCouponsRes.failed(ErrorEnum.PARAM_LACK.getCode(), "活动券模版本id不能为空");
        }

        String authId = memberService.getMemberAuthId(req.getMid());
        if (StringUtils.isBlank(authId)) {
            return OfferThirdCouponsRes.failed(ErrorEnum.MEMBER_NOT_FOUND.getCode(), ErrorEnum.MEMBER_NOT_FOUND.getMsg());
        }

        OfferThirdCouponRequest request = OfferThirdCouponRequest.fromRes(req);
        request.setAuthId(authId);
        OfferThirdCouponsResponse response = null;
        try {
            response = couponRestClient.offerThirdCoupons(request);
        } catch (BusinessException e) {
            return OfferThirdCouponsRes.failed(ErrorEnum.REQUEST_ERROR.getCode(), ErrorEnum.REQUEST_ERROR.getMsg());
        }
        return response.toRes();
    }

    @Override
    public GetActivityBasicInfoRes getActivityBasicInfo(GetActivityBasicInfoReq req) {
        GetActivityBasicInfoRes.Builder builder = GetActivityBasicInfoRes.newBuilder();
        List<Long> activityIdList = req.getActivityIdList();
        if (CollectionUtils.isEmpty(activityIdList)) {
            return builder.build();
        }
        List<MmpPackNightActivity> activities = mmpPackNightActivityMapper.selectActivitiesByIds(activityIdList);
        if (CollectionUtils.isNotEmpty(activities)) {
            List<ActivityBasicInfo> list = activities.stream().map(this::convertActivityBasicInfo).collect(Collectors.toList());
            builder.addAllActivity(list);
        }
        return builder.build();
    }

    private ActivityBasicInfo convertActivityBasicInfo(MmpPackNightActivity activity) {
        ActivityBasicInfo.Builder builder = ActivityBasicInfo.newBuilder();
        return builder.setActivityId(activity.getId())
                .setActivityName(activity.getActivityName())
                .setType(Optional.ofNullable(activity.getType()).orElse(0))
                .setOrgCode(activity.getOrgId())
                .setQuotaOrgCode(activity.getQuotaOrgId())
                .setActivityStartDate(activity.getActivityStartDate())
                .setActivityEndDate(activity.getActivityEndDate())
                .setActivityStatus(activity.getActivityStatus().intValue())
                .setThirdActivityId(null == activity.getThirdActivityId() ? 0 : activity.getThirdActivityId())
                .build();
    }

    @Override
    public GetMmpCouponListRes getMmpCouponList(GetMmpCouponListReq req) {
        GetMmpCouponListRes.Builder builder = GetMmpCouponListRes.newBuilder();
        List<Long> couponIdsList = req.getActivityCouponIdsList();
        if (CollectionUtils.isEmpty(couponIdsList)) {
            return builder.build();
        }
        Set<Long> ids = new HashSet<>();
        couponIdsList.forEach(id -> {
            if (id != null) {
                ids.add(id);
            }
        });
        List<ActivityCouponDTO> activityCoupons = mmpPackNightActivityMapper.selectCouponByMmpIds(ids);
        if (CollectionUtils.isNotEmpty(activityCoupons)) {
            List<ActivityCouponModel> list = activityCoupons.stream().map(this::convertActivityCouponModel).collect(Collectors.toList());
            builder.addAllCouponModelList(list);
        }
        return builder.build();
    }

    @Override
    public UserOrderOfferCouponsRes userOrderOfferCoupons(UserOrderOfferCouponsReq req) {
        String expiresDate = null;
        String mid = req.getMid();
        String orderNo = req.getOrderNo();
        //status 0:未使用 1:已使用  2：已经作废  3:已过期  4：全部
        int status = req.getStatus();
        if (status != 4) {
            if (status == 3) {
                status = 2;
            } else if (status == 2) {
                status = 3;
                expiresDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -1), "yyyy-MM-dd");
            }
        }

        if (StringUtils.isBlank(mid) || StringUtils.isBlank(orderNo)) {
            return UserOrderOfferCouponsRes.ok();
        }
        MemberBasicInfo member = memberService.getMemberByMId(mid);
        if (member == null || StrUtil.isBlank(member.getAuthId())) {
            return UserOrderOfferCouponsRes.failed(ErrorEnum.MEMBER_NOT_FOUND.getCode(), ErrorEnum.MEMBER_NOT_FOUND.getMsg());
        }

        UserOrderOfferCouponsRes.Builder builder = UserOrderOfferCouponsRes.newBuilder();
        List<CouponConditionDto> userAllCoupons = userCouponListMapper.findUserCouponsByRemark(member.getAuthId(), orderNo, status, expiresDate);

        if (CollectionUtil.isNotEmpty(userAllCoupons)) {
            List<UserOfferCoupon> list = userAllCoupons.stream().map(dto -> convertUserOfferCoupon(dto, mid)).collect(Collectors.toList());
            builder.addAllCoupon(list);
        }
        return builder.build();
    }

    @Override
    public GetFirstOrderFlagRes getFirstOrderFlag(GetFirstOrderFlagReq req) {
        if (StrUtil.isBlank(req.getMid())){
            GetFirstOrderFlagRes.failed(-2534101,"用户mid不能为空");
        }
        MemberBasicInfo member = memberService.getMemberByMId(req.getMid());
        if (member == null || StrUtil.isBlank(member.getAuthId())) {
            return GetFirstOrderFlagRes.failed(-2534102,"未查询到用户信息");
        }
        Integer countFirstOrder = mmpUserTagMapper.countFirstOrderByAuthId(member.getAuthId());
        GetFirstOrderFlagRes.Builder builder = GetFirstOrderFlagRes.newBuilder();
        if (countFirstOrder > 0){
            //有首单记录，说明不是首单
            builder.setFirstOrderFlag(2);
        }else {
            builder.setFirstOrderFlag(1);
        }

        return builder.build();
    }

    private UserOfferCoupon convertUserOfferCoupon(CouponConditionDto couponDto, String mid) {
        UserOfferCoupon.Builder builder = UserOfferCoupon.newBuilder();
        Date nowDate = DateUtils.truncate(new Date(), Calendar.DATE);
        if (StringUtils.isNotBlank(couponDto.getExpiresDate())) {
            Date expires = DateUtil.parse(couponDto.getExpiresDate(), "yyyy-MM-dd");
            if (couponDto.getStatus() == 0 && nowDate.after(expires)) {
                builder.setStatus(3);
            }
        }
        if (BusinessConst.STORE_TYPE.equals(couponDto.getShopLimitType())){
            builder.setPickUpStoreId(couponDto.getPickshopSeq())
                    .setReturnStoreId(couponDto.getReturnshopSeq());
        }
        return builder.setUserCouponSeq(couponDto.getUserCouponSeq())
                .setMid(mid)
                .setCouponSeq(Optional.ofNullable(couponDto.getCouponSeq()).orElse(0L))
                .setStartDate(couponDto.getStartDate())
                .setExpiresDate(couponDto.getExpiresDate())
                .setStatus(Optional.ofNullable(couponDto.getStatus()).orElse(0))
                .setCouponOrigin(couponDto.getCouponOrigin())
                .setCouponValue(NumberUtils.getStr(couponDto.getCouponValue()))
                .setDes(couponDto.getDes())
                .setCouponType(Optional.ofNullable(couponDto.getCouponType()).orElse(1))
                .setServiceType(Optional.ofNullable(couponDto.getServiceType()).orElse(1))
                .setMinAmount(NumberUtils.getStr(couponDto.getMinAmount()))
                .setTimeType(Optional.ofNullable(couponDto.getTimeType()).orElse(0))
                .setStartTime(couponDto.getStartTime())
                .setEndTime(couponDto.getEndTime())
                .setVehicleNo(couponDto.getVehicleNo())
                .setDiscountRate(Optional.ofNullable(couponDto.getDiscountRate()).orElse(100))
                .setOrgCode(null == couponDto.getOrgSeq() ? "" : couponDto.getOrgSeq().toString())
                .setOrderNo(couponDto.getOrderSeq())
                .setDiscount(NumberUtils.getStr(couponDto.getDiscount()))
                .setActivityOverlap(Optional.ofNullable(couponDto.getActivityOverlap()).orElse(0))
                .setPackageIds(couponDto.getPackageIds())
                .setHolidaysAvailable(Optional.ofNullable(couponDto.getHolidaysAvailable()).orElse(1))
                .setAvailableDaysOfWeek(couponDto.getAvailableDaysOfWeek())
                .setRentMethod(couponDto.getRentMethod())
                .setRentMethodGroup(couponDto.getRentMethodGroup())
                .setUseMethod(couponDto.getUseMethod())
                .setDurationLimit(NumberUtils.getStr(couponDto.getDurationLimit()))
                .setAgencyId(couponDto.getAgencyId())
                .setActionId(couponDto.getActionId())
                .setPickUpCity(couponDto.getPickshopCity())
                .setReturnCity(couponDto.getReturnshopCity())
                .setGoodsModelId(couponDto.getGoodsVehicleModel())
                .build();
    }

    private ActivityCouponModel convertActivityCouponModel(ActivityCouponDTO dto) {
        ActivityCouponModel.Builder builder = ActivityCouponModel.newBuilder();
        return builder.setActivityCouponId(dto.getId())
                .setActivityId(dto.getActivityId())
                .setActivityStatus(dto.getActivityStatus().intValue())
                .setCouponSeq(dto.getCouponSeq())
                .setCouponName(dto.getCouponName())
                .setOrgCode(dto.getOrgId())
                .setActivityType(dto.getActivityType())
                .setValidTimeType(dto.getValidTimeType())
                .setValidDays(dto.getValidDays())
                .setEffectiveDays(dto.getEffectiveDays())
                .setStartDate(dto.getStartDate())
                .setExpiresDate(dto.getExpiresDate())
                .setOfferQuantity(dto.getOfferQuantity())
                .setCouponTarget(dto.getCouponTarget())
                .setCouponType(dto.getCouponType())
                .setCouponValue(NumberUtils.getStr(dto.getCouponValue()))
                .setDiscountRate(dto.getDiscountRate())
                .setMinAmount(NumberUtils.getStr(dto.getMinAmount()))
                .setDurationLimit(NumberUtils.getStr(dto.getDurationLimit()))
                .build();
    }


    @Override
    public CouponWelfareInfo getCouponWelfareInfoByCode(String couponCode) {
        CouponConditionDto couponConditionDto = userCouponListMapper.findByCouponCode(couponCode);
        if (couponConditionDto == null) {
            log.error("getCouponWelfareInfoByCode 通过兑换码未查询到记录 couponCode={}",couponCode);
            return null;
        }
        MmpPackNightActivity mmpPackNightActivity = mmpPackNightActivityMapper.selectById(Long.valueOf(couponConditionDto.getActionId()));
        return getCouponWelfareInfo(couponConditionDto,mmpPackNightActivity);
    }


    /**
     * 封装参数
     * @param couponConditionDto
     * @param mmpPackNightActivity
     * @return
     */
    public CouponWelfareInfo getCouponWelfareInfo(CouponConditionDto couponConditionDto,MmpPackNightActivity mmpPackNightActivity){
        CouponWelfareInfo couponWelfareInfo = new CouponWelfareInfo();
        if (couponConditionDto != null) {
            BigDecimal couponValue = couponConditionDto.getCouponValue();
            Integer discountRate = couponConditionDto.getDiscountRate();
            Integer couponType = couponConditionDto.getCouponType();

            couponWelfareInfo.setType(couponType);
            couponWelfareInfo.setDisCountAmount(couponValue != null ? couponValue.stripTrailingZeros().toPlainString() : "0");
            couponWelfareInfo.setDisCount(discountRate != null ? new BigDecimal(discountRate).divide(new BigDecimal(10)).stripTrailingZeros().toPlainString() : "0");

            // 优惠券使用条件
            StringBuffer sb = new StringBuffer();
            if (couponType == 1) {
                BigDecimal minAmount = couponConditionDto.getMinAmount();
                if (minAmount != null && minAmount.compareTo(BigDecimal.ZERO) > 0) {
                    sb.append("满").append(minAmount.stripTrailingZeros().toPlainString()).append("元可用");
                    sb.append("，");
                }
                BigDecimal durationLimit = couponConditionDto.getDurationLimit();
                if (durationLimit != null && durationLimit.compareTo(BigDecimal.ZERO) > 0) {
                    sb.append("时长").append(durationLimit.stripTrailingZeros().toPlainString()).append("小时可用");
                    sb.append("，");
                }

                if (sb.length() > 0) {
                    //去除末尾逗号
                    sb.deleteCharAt(sb.length() - 1);
                }
            } else if (couponType == 2) {
                if (couponValue != null && couponValue.compareTo(BigDecimal.ZERO) > 0) {
                    sb.append("最多抵").append(couponValue.stripTrailingZeros().toPlainString()).append("元租金");
                    sb.append("，");
                }

                BigDecimal minAmount = couponConditionDto.getMinAmount();
                if (minAmount != null && minAmount.compareTo(BigDecimal.ZERO) > 0) {
                    sb.append("满").append(minAmount.stripTrailingZeros().toPlainString()).append("元可用");
                    sb.append("，");
                }
                if (sb.length() > 0) {
                    //去除末尾逗号
                    sb.deleteCharAt(sb.length() - 1);
                }
            }
            couponWelfareInfo.setConditionDesc(sb.toString());
        }

        if (mmpPackNightActivity != null) {
            Long thirdActivityId = mmpPackNightActivity.getThirdActivityId();
            List<MmpThirdCoupon> list = mmpThirdCouponService.lambdaQuery()
                    .eq(MmpThirdCoupon::getThirdActivityId, thirdActivityId)
                    .eq(MmpThirdCoupon::getCouponSeq, couponConditionDto.getCouponSeq())
                    .list();
            if (CollectionUtils.isNotEmpty(list)) {
                MmpThirdCoupon mmpThirdCoupon = list.get(0);
                couponWelfareInfo.setName(mmpThirdCoupon.getCouponName());
                try {
                    Integer validTimeType = mmpThirdCoupon.getValidTimeType();
                    StringBuffer sb = new StringBuffer();
                    // 1起止时间 2时长
                    if (validTimeType == 1) {
                        // 把yyyy-MM-dd 这个格式的时间字符 转换成 yyyy.MM.dd
                        String startDate = mmpThirdCoupon.getStartDate().replace("-",".");
                        String expireDate = mmpThirdCoupon.getExpiresDate().replace("-",".");
                        couponWelfareInfo.setStartDate(startDate);
                        couponWelfareInfo.setEndDate(expireDate);
                        sb.append(startDate);
                        sb.append("-");
                        sb.append(expireDate);
                    } else {
                        Integer effectiveDays = mmpThirdCoupon.getEffectiveDays();
                        Integer validDays = mmpThirdCoupon.getValidDays();
                        if (effectiveDays == 0) {
                            if (validDays > 0) {
                                sb.append("领取后").append(validDays).append("天内有效");
                            }
                        } else {
                            if (validDays > 0) {
                                sb.append("领取后第").append(effectiveDays).append("天至第").append(effectiveDays + validDays).append("天有效");
                            } else {
                                sb.append("领取后").append(effectiveDays).append("天内有效");
                            }
                        }
                    }
                    couponWelfareInfo.setExpirationDateTip(sb.toString());
                } catch (Exception e) {
                    log.error("获取优惠券有效期异常,mmpThirdCoupon={}", JSON.toJSONString(mmpThirdCoupon),e);
                }

            }
        }
        couponWelfareInfo.setTip("* 优惠券可用于抵扣租金，具体使用规则以下单结算时为准。");
        return couponWelfareInfo;
    }


    /**
     *
     * @param couponConditionDtos
     * @param mmpPackNightActivitys
     * @return
     */
    public List<CouponWelfareInfo> getCouponWelfareInfo(List<CouponConditionDto> couponConditionDtos,List<MmpPackNightActivity> mmpPackNightActivitys){
        List<CouponWelfareInfo> result = new ArrayList<>();
        Map<Long,MmpPackNightActivity> mmpPackNightActivityMap = mmpPackNightActivitys.stream().collect(Collectors.toMap(MmpPackNightActivity::getId, k1->k1, (k1, k2) -> k1));
        for (CouponConditionDto couponConditionDto : couponConditionDtos) {
            String actionId = couponConditionDto.getActionId();
            if (StringUtils.isNotBlank(actionId)) {
                MmpPackNightActivity mmpPackNightActivity = mmpPackNightActivityMap.get(Long.valueOf(actionId));
                CouponWelfareInfo couponWelfareInfo = getCouponWelfareInfo(couponConditionDto, mmpPackNightActivity);
                result.add(couponWelfareInfo);
            }
        }
        return result;
    }


    @Override
    public List<CouponWelfareInfo> getCouponWelfareInfosByCdk(String cdk) {
        List<CouponWelfareInfo> result = new ArrayList<>();
        MemberCdk memberCdk = memberCdkMapper.selectByMemberCdk(cdk);
        if (memberCdk != null) {
            Integer memberCdkId = memberCdk.getId();
            List<MemberCdkRelation> memberCdkRelations = memberCdkRelationMapper.selectListByMembermemberCdkId(memberCdkId);
            if (CollectionUtils.isNotEmpty(memberCdkRelations)) {
                // 优惠券id集合
                List<Long> userCouponSeqs = memberCdkRelations.stream().map(b-> Long.valueOf(b.getRelationId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userCouponSeqs)) {
                    // 优惠券集合
                    List<CouponConditionDto> couponConditionDtos = userCouponListMapper.selectListByIds(userCouponSeqs);
                    if (CollectionUtils.isNotEmpty(couponConditionDtos)) {
                        // 活动id集合
                        List<Long> actionIds = couponConditionDtos.stream().map(b->Long.valueOf(b.getActionId())).distinct().collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(actionIds)) {
                            // 活动集合
                            List<MmpPackNightActivity> mmpPackNightActivities = mmpPackNightActivityMapper.selectActivitiesByIds(actionIds);
                            result = getCouponWelfareInfo(couponConditionDtos,mmpPackNightActivities);
                        }
                    }
                }
            }
        }
        return result;
    }
}
