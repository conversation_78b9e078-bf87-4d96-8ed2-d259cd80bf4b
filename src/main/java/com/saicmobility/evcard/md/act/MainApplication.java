package com.saicmobility.evcard.md.act;

import com.saicmobility.common.envconfig.EnvConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.annotation.MapperScans;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;


@SpringBootApplication
@MapperScans({
		@MapperScan(value = "com.saicmobility.evcard.md.act.mapper.act", sqlSessionFactoryRef = "actSqlSessionFactory", sqlSessionTemplateRef = "actSqlSessionTemplate"),
		@MapperScan(value = "com.saicmobility.evcard.md.act.mapper.siac", sqlSessionFactoryRef = "siacSqlSessionFactory", sqlSessionTemplateRef = "siacSqlSessionTemplate"),
		@MapperScan(value = "com.saicmobility.evcard.md.act.mapper.siacPlus", sqlSessionFactoryRef = "siacPlusSqlSessionFactory", sqlSessionTemplateRef = "siacPlusSqlSessionTemplate"),
		@MapperScan(value = "com.saicmobility.evcard.md.act.mapper.iss", sqlSessionFactoryRef = "issSqlSessionFactory", sqlSessionTemplateRef = "issSqlSessionTemplate")
})
public class MainApplication {

	public static void main(String[] args) {
		EnvConfig.initEnv();
		SpringApplication.run(MainApplication.class, args);

	}
}
