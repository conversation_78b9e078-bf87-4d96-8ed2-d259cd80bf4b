package com.saicmobility.evcard.md.act.enums.proprietary;

/**
 * <AUTHOR>
 * @date 2024/1/4 9:09
 */
public enum PricingTypeEnum {
    FLEXIBLE_PRICING(1, "灵活定价"),
    STANDARD_PRICING(2, "规范定价");

    private Integer type;
    private String msg;

    PricingTypeEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }
}
