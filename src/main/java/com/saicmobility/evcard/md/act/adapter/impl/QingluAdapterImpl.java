package com.saicmobility.evcard.md.act.adapter.impl;

import com.alibaba.fastjson.JSON;
import com.saicmobility.evcard.md.act.adapter.QingluAdapter;
import com.saicmobility.evcard.md.act.util.SignUtils;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Slf4j
@Service
public class QingluAdapterImpl implements QingluAdapter {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${qinglu.merchantId}")
    private String merchantId;

    @Value("${qinglu.appKey}")
    private String appKey;

    @Value("${qinglu.appSecret}")
    private String appSecret;

    @Value("${qinglu.url}")
    private String url;


    @Override
    public String callQinglu(String method, String bizRequest) {

        HttpEntity<MultiValueMap<String, Object>> entity = null;
        MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
        try {
            long currentTimeMillis = System.currentTimeMillis();
            Map<String, Object> signData = new TreeMap<>();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            formData.add("method", method);
            signData.put("method", method);

            formData.add("merchantId", merchantId);
            signData.put("merchantId", merchantId);
            formData.add("appKey", appKey);
            signData.put("appKey", appKey);
            formData.add("timestamp", currentTimeMillis);
            signData.put("timestamp", currentTimeMillis);
            formData.add("v", "1.0");
            signData.put("v", "1.0");
            formData.add("lang", "zh_CN");
            signData.put("lang", "zh_CN");
            formData.add("bizRequest", bizRequest);
            signData.put("bizRequest", bizRequest);
            String sign = SignUtils.sign(signData, appSecret);
            formData.add("sign", sign);

            entity = new HttpEntity<>(formData);

            log.info("tid:{},rest请求url[{}],method:{},参数：{}", Trace.currentTraceId(), url, method,JSON.toJSONString(entity));
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            log.info("tid:{},rest应答参数：response{},sign{}", Trace.currentTraceId(), JSON.toJSONString(response),sign);

            if (response.getStatusCodeValue() == HttpStatus.OK.value()) {
                return response.getBody();
            } else {
                log.error("tid:{},rest请求失败！url[{}],method:{},request[{}],response[{}]", Trace.currentTraceId(), url,method, JSON.toJSONString(entity), JSON.toJSONString(response));
            }
        } catch (Exception e) {
            log.error("tid:{},rest请求异常！url[{}],method:{},request[{}]", Trace.currentTraceId(), url,method, JSON.toJSONString(entity), e);
        }
        return null;
    }

}
