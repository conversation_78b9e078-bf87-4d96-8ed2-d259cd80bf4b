package com.saicmobility.evcard.md.act.iservice;

import com.saicmobility.evcard.md.act.entity.Task;
import com.saicmobility.evcard.md.act.iservice.BaseService;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * TASK表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
public interface ITaskService extends BaseService<Task> {

    void updateToFinished(Integer taskType, String taskParam, String lastRunMsg);

    void updateToFinished(Integer taskType, String taskParam, String lastRunMsg, Long id);

    void updateToFailed(Integer taskType, String taskParam, String lastRunMsg);

    void updateToFailed(Integer taskType, String taskParam, String lastRunMsg, Long id);

    void updateNextRunTime(Integer taskType, String taskParam, Date nextRunTime, String lastRunMsg);

    void updateNextRunTime(Integer taskType, String taskParam, Date nextRunTime, String lastRunMsg, Long id);

    List<Task> listPendingTasks();
}
