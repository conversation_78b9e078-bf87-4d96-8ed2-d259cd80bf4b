package com.saicmobility.evcard.md.act.service.rest;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.service.rest.entity.messagepush.SendEmailRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MessagePushRestClient extends AbstractRestClient {
    public BaseResponse syncSendEmail(SendEmailRequest request) throws BusinessException {
        return postForObject(mdRestApiConfig.getSyncSendEmail(), request, BaseResponse.class);
    }
}
