package com.saicmobility.evcard.md.act.service.promotion;

import com.saicmobility.common.bpe.FlowReq;
import com.saicmobility.common.bpe.FlowRes;
import com.saicmobility.evcard.md.act.dto.promotion.PromotionDto;

/**
 * 广告推广
 */
public interface IPromotionService {
    FlowRes checkPromotion(PromotionDto promotionDto);

    FlowRes clickPromotion(PromotionDto promotionDto);

    FlowRes callPromotion(PromotionDto promotionDto);
}
