package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 渠道营销活动表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_channel_activity")
@ApiModel(value = "ChannelActivity对象", description = "渠道营销活动表")
public class ChannelActivity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "渠道类型：渠道id 1.线下 2.携程 3.飞猪 4.哈啰 5.租租车 6.悟空 10.携程分销")
    private Integer channel;

    @ApiModelProperty(value = "二级渠道appKey")
    private String secondAppKey;

    @ApiModelProperty(value = "门店id集合")
    private String storeIds;

    @ApiModelProperty(value = "车型id集合")
    private String carModelIds;
    @ApiModelProperty(value = "是否全部门店 0否，1是")
    private Integer isAllStore;
    @ApiModelProperty(value = "是否全部车型 0否，1是")
    private Integer isAllVehicle;

    @ApiModelProperty(value = "优惠码")
    private String discountCode;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "参与机构")
    @TableField("orgCodes")
    private String orgCodes;

    @ApiModelProperty(value = "活动状态：1-生效、2-已过期")
    private Integer activityStatus;

    @ApiModelProperty(value = "活动类型：1-满减、2-打折、3-减至")
    private Integer activityType;

    @ApiModelProperty(value = "优惠纬度：1-车辆租金、2-订单整单")
    private Integer discountLatitude;

    @ApiModelProperty(value = "优惠方式：1-针对金额、2-针对租期、3-满天减天、4-满天减金额")
    private Integer discountMethod;

    @ApiModelProperty(value = "优惠条件1")
    private String discountCondition1;

    @ApiModelProperty(value = "优惠条件2")
    private String discountCondition2;

    @ApiModelProperty(value = "是否限制优惠：1-有限制、2-无限制")
    private Integer restrictDiscounts;

    @ApiModelProperty(value = "最高优惠金额，当restrict_discounts=1才需要设置此字段")
    private BigDecimal maxDiscountAmount;

    @ApiModelProperty(value = "最大租期，当restrict_discounts=1才需要设置此字段")
    private Integer maxRentDays;

    @ApiModelProperty(value = "最小租期，当restrict_discounts=1才需要设置此字段")
    private Integer minRentDays;

    @ApiModelProperty(value = "活动开始时间")
    private LocalDate activityStartDate;

    @ApiModelProperty(value = "活动结束时间")
    private LocalDate activityEndDate;

    @ApiModelProperty(value = "取车开始时间")
    private LocalDate pickUpStartDate;

    @ApiModelProperty(value = "取车结束时间")
    private LocalDate pickUpEndDate;

    @ApiModelProperty(value = "还车开始时间")
    private LocalDate returnStartDate;

    @ApiModelProperty(value = "还车结束时间")
    private LocalDate returnEndDate;

    @ApiModelProperty(value = "不可用时间范围")
    private String unavailableDateRanges;

    @ApiModelProperty(value = "成本承担方：1-平台全部承担、2-商家全部承担、3-共同承担")
    private Integer costBearingParty;

    @ApiModelProperty(value = "成本分摊方式：1-百分比设置、2-按固定金额设置")
    private Integer costAllocationMethod;

    @ApiModelProperty(value = "商户承担（百分比或金额）")
    private BigDecimal merchantBear;

    @ApiModelProperty(value = "平台承担（百分比或金额）")
    private BigDecimal platformBear;

    @ApiModelProperty(value = "状态（0=正常 1=已删除）")
    private Integer isDeleted;

    /*@ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID")
    private Long createOperId;

    @ApiModelProperty(value = "创建人名称")
    private String createOperName;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人ID")
    private Long updateOperId;

    @ApiModelProperty(value = "更新人名称")
    private String updateOperName;*/

    @ApiModelProperty(value = "取还车时间交集开关，1=取还车都必须在时间范围内 2=取还车任一时间在时间范围内")
    private Integer intersectionFlag;
}
