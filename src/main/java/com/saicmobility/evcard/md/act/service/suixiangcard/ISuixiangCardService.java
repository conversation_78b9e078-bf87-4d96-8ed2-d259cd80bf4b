package com.saicmobility.evcard.md.act.service.suixiangcard;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.bo.suixiangcard.SuiXiangCardThirdSaleInfoBo;
import com.saicmobility.evcard.md.act.dto.suixiangcard.SuiXiangCardThirdSaleInfoInput;
import com.saicmobility.evcard.md.act.dto.welfare.SuiXiangCardWelfareInfo;


public interface ISuixiangCardService {
    SuiXiangCardWelfareInfo getSuiXiangCardWelfareInfo(String cdkey);


    SuiXiangCardThirdSaleInfoBo getSuiXiangCardThirdSaleInfo(SuiXiangCardThirdSaleInfoInput input) throws BusinessException;
}
