package com.saicmobility.evcard.md.act.enums.channelcoupon;

/**
 * 1 – 订单处理中，适用于权益异步发放的场景
 * 2 – 订单处理失败
 * 3 – 订单处理成功，此时code应答码必须为0000，需返回下面的字段信息
 * 4 – 订单状态不确定
 */
public enum CCBCouponOfferHandlerStatusEnum {
    ORDER_PROCESSING(1, "订单处理中"),
    ORDER_PROCESSING_FAILED(2, "订单处理失败"),
    ORDER_PROCESSING_SUCCESS(3, "订单处理成功"),
    ORDER_STATUS_UNCERTAIN(4, "订单状态不确定")
    ;


    private Integer status;

    private String msg;

    CCBCouponOfferHandlerStatusEnum(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
