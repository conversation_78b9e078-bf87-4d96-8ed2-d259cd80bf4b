package com.saicmobility.evcard.md.act.dto.coupon.channel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReconciliationFileDto {
    private String mmpThirdActivityId;

    private String CouponCode;
    //前一天状态 0：未使用 1：已使用 2：已作废 3: 已过期
    private BigDecimal beforeStatus;
    //0：未使用 1：已使用 2：已作废 3: 已过期
    private BigDecimal status;
    //核销优惠券
    private String orderSeq;
    //核销优惠券的取车门店Id
    private String pickUpStoreId;

    private String useTime;
}
