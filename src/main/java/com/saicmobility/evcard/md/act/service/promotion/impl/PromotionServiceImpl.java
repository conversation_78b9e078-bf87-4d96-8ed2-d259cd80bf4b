package com.saicmobility.evcard.md.act.service.promotion.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.common.bpe.FlowRes;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.dto.promotion.PromotionDto;
import com.saicmobility.evcard.md.act.entity.AdvertisementActivity;
import com.saicmobility.evcard.md.act.mapper.act.AdvertisementActivityMapper;
import com.saicmobility.evcard.md.act.service.inner.IAdvertisementActivityService;
import com.saicmobility.evcard.md.act.service.promotion.IPromotionService;
import com.saicmobility.evcard.md.act.service.rest.entity.CallbackResponse;
import com.saicmobility.evcard.md.act.util.RedisLock;
import com.saicmobility.evcard.md.act.util.RedisLockUtil;
import com.saicmobility.evcard.md.mduserservice.api.CheckDeviceCodeExistReq;
import com.saicmobility.evcard.md.mduserservice.api.CheckDeviceCodeExistRes;
import com.saicmobility.evcard.md.mduserservice.api.MdUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.data.redis.connection.jedis.JedisUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class PromotionServiceImpl implements IPromotionService {

    @Autowired
    protected RestTemplate restTemplate;

    @Autowired
    private AdvertisementActivityMapper advertisementActivityMapper;

    @Autowired
    private RedisLockUtil redisLockUtil;

    @Autowired
    private MdUserService mdUserService;

    @Override
    public FlowRes checkPromotion(PromotionDto promotionDto) {
        // 设备码被推广过下载过app就不参加活动，故不用考虑渠道
        if (StringUtils.isEmpty(promotionDto.getAppid())) {
            return FlowRes.failed(-2534001, "应用唯一标识不能为空");
        }
        if (StringUtils.isEmpty(promotionDto.getDeviceCode())) {
            return FlowRes.failed(-2534001, "设备标识符不能为空");
        }
        if (promotionDto.getDevicePlatform() == null) {
            return FlowRes.failed(-2534001, "设备平台不能为空");
        }
        if (promotionDto.getDeviceType() == null) {
            return FlowRes.failed(-2534001, "设备标识符类型不能为空");
        }
        CheckDeviceCodeExistReq checkDeviceCodeExistReq = CheckDeviceCodeExistReq.newBuilder()
                .setDeviceCode(promotionDto.getDeviceCode())
                .setDevicePlatform(promotionDto.getDevicePlatform())
                .setDeviceType(promotionDto.getDeviceType())
                .build();
        CheckDeviceCodeExistRes checkDeviceCodeExistRes = mdUserService.checkDeviceCodeExist(checkDeviceCodeExistReq);
        if (checkDeviceCodeExistRes.getRetCode() != 0) {
           log.error("有米查重异常，调用mdUserService.checkDeviceCodeExist异常，req:{}，res:{}", checkDeviceCodeExistReq, checkDeviceCodeExistRes);
           return FlowRes.failed(-2534226, "查重异常!");
        }
        if (checkDeviceCodeExistRes.getIsExist()) {
            log.warn("有米查重失败，设备标识符已存在app登录设备库记录中，source:{}, appid:{}, idfa:{}",promotionDto.getSource(), promotionDto.getAppid(), promotionDto.getDeviceCode());
            return FlowRes.failed(-25342261, "该设备标识符已存在");
        }
        List<AdvertisementActivity> advertisementActivities = advertisementActivityMapper.selectList(new LambdaQueryWrapper<AdvertisementActivity>()
                .eq(AdvertisementActivity::getDeviceCode, promotionDto.getDeviceCode())
                .eq(AdvertisementActivity::getDeviceType, promotionDto.getDeviceType())
                .eq(AdvertisementActivity::getDevicePlatform, promotionDto.getDevicePlatform())
                .eq(AdvertisementActivity::getAppid, promotionDto.getAppid()));
        if (!CollectionUtil.isEmpty(advertisementActivities)) {
            log.warn("有米查重失败，设备标识符已存在推广库中，source:{}, appid:{}, idfa:{}",promotionDto.getSource(), promotionDto.getAppid(), promotionDto.getDeviceCode());
            return FlowRes.failed(-25342261, "该设备标识符已存在");
        }
        log.info("有米查重成功，设备标识符不存在，source:{}, appid:{}, idfa:{}",promotionDto.getSource(), promotionDto.getAppid(), promotionDto.getDeviceCode());
        return FlowRes.ok();
    }

    @Override
    public FlowRes clickPromotion(PromotionDto promotionDto) {
        if (StringUtils.isEmpty(promotionDto.getSource())) {
            return FlowRes.failed(-2534001, "渠道唯一标识不能为空");
        }
        if (StringUtils.isEmpty(promotionDto.getAppid())) {
            return FlowRes.failed(-2534001, "应用唯一标识不能为空");
        }
        if (StringUtils.isEmpty(promotionDto.getDeviceCode())) {
            return FlowRes.failed(-2534001, "设备标识符不能为空");
        }
        if (promotionDto.getDevicePlatform() == null) {
            return FlowRes.failed(-2534001, "设备平台不能为空");
        }
        if (promotionDto.getDeviceType() == null) {
            return FlowRes.failed(-2534001, "设备标识符类型不能为空");
        }
        if (StringUtils.isEmpty(promotionDto.getCallbackUrl())) {
            return FlowRes.failed(-2534001, "回调地址不能为空");
        }
        // 防重
        RedisLock redisLock = null;
        try {
            redisLock = redisLockUtil.acquireLock2("2534_ACT_YOUMI_CLICK_IDFA:" + promotionDto.getDeviceCode());
            // 只校验是否已经点击过
            List<AdvertisementActivity> advertisementActivities = advertisementActivityMapper.selectList(new LambdaQueryWrapper<AdvertisementActivity>()
                    .eq(AdvertisementActivity::getDeviceCode, promotionDto.getDeviceCode())
                    .eq(AdvertisementActivity::getDeviceType, promotionDto.getDeviceType())
                    .eq(AdvertisementActivity::getDevicePlatform, promotionDto.getDevicePlatform())
                    .eq(AdvertisementActivity::getAppid, promotionDto.getAppid()));
            if (!CollectionUtil.isEmpty(advertisementActivities)) {
                log.warn("有米点击失败，设备标识符已存在推广库中，source:{}, appid:{}, idfa:{}",promotionDto.getSource(), promotionDto.getAppid(), promotionDto.getDeviceCode());
                return FlowRes.failed(-25342261, "该设备标识符已存在");
            }
            AdvertisementActivity advertisementActivity = new AdvertisementActivity();
            BeanUtils.copyProperties(promotionDto, advertisementActivity);
            if (advertisementActivityMapper.insert(advertisementActivity) >= 1) {
                log.info("有米点击成功，source:{}, appid:{}, idfa:{}, url:{}", promotionDto.getSource(), promotionDto.getAppid(), promotionDto.getDeviceCode(), promotionDto.getCallbackUrl());
                return FlowRes.ok();
            }
        } catch (Exception e) {
            log.error("有米点击失败，source:{}, appid:{}, idfa:{}, url:{}", promotionDto.getSource(), promotionDto.getAppid(), promotionDto.getDeviceCode(), promotionDto.getCallbackUrl(), e);
            return FlowRes.failed(-2534227, "请勿频繁点击");
        } finally {
            if (redisLock != null) {
                redisLockUtil.releaseLock(redisLock);
            }
        }
        log.error("有米点击失败，source:{}, appid:{}, idfa:{}, url:{}",promotionDto.getSource(), promotionDto.getAppid(), promotionDto.getDeviceCode(), promotionDto.getCallbackUrl());
        return FlowRes.failed(-2534227, "保存点击信息失败");
    }

    @Override
    public FlowRes callPromotion(PromotionDto promotionDto) {
        if (StringUtils.isEmpty(promotionDto.getAppid())) {
            return FlowRes.failed(-2534001, "应用唯一标识不能为空");
        }
        if (StringUtils.isEmpty(promotionDto.getDeviceCode())) {
            return FlowRes.failed(-2534001, "设备标识符不能为空");
        }
        if (promotionDto.getDevicePlatform() == null) {
            return FlowRes.failed(-2534001, "设备平台不能为空");
        }
        if (promotionDto.getDeviceType() == null) {
            return FlowRes.failed(-2534001, "设备标识符类型不能为空");
        }
        log.error("有米发起回调，appid:{}, idfa:{}", promotionDto.getAppid(), promotionDto.getDeviceCode());
        List<AdvertisementActivity> advertisementActivities = advertisementActivityMapper.selectList(new LambdaQueryWrapper<AdvertisementActivity>()
                .eq(AdvertisementActivity::getDeviceCode, promotionDto.getDeviceCode())
                .eq(AdvertisementActivity::getDeviceType, promotionDto.getDeviceType())
                .eq(AdvertisementActivity::getDevicePlatform, promotionDto.getDevicePlatform())
                .eq(AdvertisementActivity::getCallbackResult, 0)
                .eq(AdvertisementActivity::getAppid, promotionDto.getAppid()));
        if (CollectionUtils.isEmpty(advertisementActivities)) {
            return FlowRes.failed(0, "不存在该设备号的点击信息或已经使用");
        }
        AdvertisementActivity advertisementActivity = advertisementActivities.get(0);
        String callbackUrl = advertisementActivity.getCallbackUrl();
        if (StringUtils.isEmpty(callbackUrl)) {
            log.error("有米回调失败，请求地址为空，appid:{}, idfa:{}", promotionDto.getAppid(), promotionDto.getDeviceCode());
        }
        try {
            CallbackResponse callbackResponse = callbackUrl(callbackUrl);
            if (callbackResponse.getCode() == 0) {
                advertisementActivity.setCallbackResult(1);
                advertisementActivity.setCallbackTime(LocalDateTime.now());
                int i = advertisementActivityMapper.updateById(advertisementActivity);
                log.info("有米回调成功，source:{}, appid:{}, idfa:{}, url:{}, 入库结果:{}",
                        promotionDto.getSource(), promotionDto.getAppid(), promotionDto.getDeviceCode(), promotionDto.getCallbackUrl(), i > 0);
            }
            else {
                log.error("有米回调失败，接口请求失败，请求地址:{}，返回结果:{}", callbackUrl, JSON.toJSON(callbackResponse));
            }
            return new FlowRes(callbackResponse.getCode(), callbackResponse.getMsg());
        } catch (Exception e) {
            return FlowRes.failed(-2534228, "请求回调地址异常");
        }
    }

    public CallbackResponse callbackUrl(String url) throws BusinessException {
        try {
            CallbackResponse resp = restTemplate.getForObject(url, CallbackResponse.class);
            log.info("回调有米接口，GET请求完成，path={}，请求结果:{}.", url, JSON.toJSON(resp));
            return resp;
        }catch (Exception e) {
            log.error("回调有米接口，GET请求异常，path={}", url, e);
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), e.getMessage());
        }
    }
}
