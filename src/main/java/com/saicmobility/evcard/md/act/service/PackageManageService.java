/*
package com.saicmobility.evcard.md.act.service;

import com.saicmobility.evcard.md.act.domain.OperateLogBo;
import com.saicmobility.evcard.md.act.domain.common.PageResult;
import com.saicmobility.evcard.md.act.domain.packages.AddPackageConfigDto;
import com.saicmobility.evcard.md.act.domain.packages.PackageConfigBo;
import com.saicmobility.evcard.md.act.domain.packages.QueryPackageConfigInput;
import com.saicmobility.evcard.md.act.domain.packages.QueryPackageConfigLogInput;
import com.saicmobility.evcard.md.act.exception.BusinessException;
import org.springframework.stereotype.Component;

*/
/**
 * 套餐配置管理
 * @Author: fsh
 * @Date: 2022/4/15 14:44
 *//*

@Component
public interface PackageManageService {

    */
/**
     * 新增套餐配置
     * @param configDto
     * @return
     * @throws BusinessException
     *//*

    Boolean addPackageConfig(AddPackageConfigDto configDto) throws BusinessException;

    */
/**
     * 查询套餐配置列表
     * @param queryPackageConfigInput
     * @return
     *//*

    PageResult<PackageConfigBo> getPackageConfigList(QueryPackageConfigInput queryPackageConfigInput);

    */
/**
     * 下线套餐配置
     * @param id
     * @param userName
     * @return
     * @throws BusinessException
     *//*

    Boolean offlinePackageConfig(Long id,String userName) throws BusinessException;

    */
/**
     * 查询操作日志
     * @param input
     * @return
     *//*

    PageResult<OperateLogBo> queryOperateLog(QueryPackageConfigLogInput input);

}
*/
