package com.saicmobility.evcard.md.act.enums.market;

public enum RestrictDiscount {
    RESTRICT(1, "有限制"),
    NOT_RESTRICT(2, "无限制");

    private Integer type;
    private String msg;

    RestrictDiscount(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }
}
