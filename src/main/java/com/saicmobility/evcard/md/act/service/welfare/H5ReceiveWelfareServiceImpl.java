package com.saicmobility.evcard.md.act.service.welfare;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.dto.welfare.*;
import com.saicmobility.evcard.md.mduserservice.api.*;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * H5领取福利服务实现 (source=3)
 * 处理短信验证 + 用户注册登录 + 福利领取的完整业务流程
 */
@Slf4j
@Service
public class H5ReceiveWelfareServiceImpl extends AbstractWelfareService {

    @Autowired
    private MdUserService mdUserService;

    @Override
    protected List<CouponWelfareInfo> getCouponWelfareInfo(GetWelfareInfoInput input) {
        // H5领取场景暂时不支持获取福利详情，直接返回空列表
        return null;
    }

    @Override
    protected List<SuiXiangCardWelfareInfo> getSuiXiangCardWelfareInfo(GetWelfareInfoInput input) {
        // H5领取场景暂时不支持获取福利详情，直接返回空列表
        return null;
    }

    @Override
    protected ReceiveWelfareDto receiveCoupon(ReceiveWelfareInput input) throws BusinessException {
        return processH5ReceiveWelfare(input);
    }

    @Override
    protected ReceiveWelfareDto receiveSuiXiangCard(ReceiveWelfareInput input) throws BusinessException {
        return processH5ReceiveWelfare(input);
    }

    /**
     * 处理H5领取福利的完整业务流程
     * 1. 短信验证
     * 2. 用户注册登录
     * 3. 福利领取
     */
    private ReceiveWelfareDto processH5ReceiveWelfare(ReceiveWelfareInput input) throws BusinessException {
        log.info("H5ReceiveWelfare start, input={}, tid={}", JSON.toJSONString(input), Trace.currentTraceId());

        String mobile = input.getMobile();
        String authcode = input.getAuthcode();
        String membershipPolicyVersion = input.getMembershipPolicyVersion();
        String privacyPolicyVersion = input.getPrivacyPolicyVersion();

        // 1. 短信验证码校验
        log.info("开始短信验证码校验, mobile={}, authcode={}, tid={}", mobile, authcode, Trace.currentTraceId());

        //TODO: 需要根据实际的 MdUserService 接口调整方法名和参数
        AuthcodeLoginRes authcodeLoginRes = mdUserService.authcodeLogin(
                AuthcodeLoginReq.newBuilder()
                        .setMobile(mobile)
                        .setAuthcode(authcode)
                        .build()
        );

        if (authcodeLoginRes.getRetCode() != 0) {
            log.error("短信验证码校验失败, mobile={}, authcode={}, retCode={}, retMsg={}, tid={}",
                    mobile, authcode, authcodeLoginRes.getRetCode(), authcodeLoginRes.getRetMsg(), Trace.currentTraceId());
            throw new BusinessException(authcodeLoginRes.getRetCode(), authcodeLoginRes.getRetMsg());
        }

        // 2. 用户注册登录
        log.info("开始用户注册登录, mobile={}, tid={}", mobile, Trace.currentTraceId());

        // 获取注册的二级渠道
        String secondAppKey = "mock_second_app_key";
        // TODO: 需要根据实际的 MdUserService 接口调整方法名和参数
        CommonUserLoginRes commonUserLoginRes = mdUserService.commonUserLogin(
                CommonUserLoginReq.newBuilder()
                        .setMobile(mobile)
                        .setSecondAppKey(secondAppKey)
                        .setAgreePolicy(1)
                        .setMembershipPolicyVersion(StringUtils.isNotBlank(membershipPolicyVersion) ? membershipPolicyVersion : "")
                        .setPrivacyPolicyVersion(StringUtils.isNotBlank(privacyPolicyVersion) ? privacyPolicyVersion : "")
                        .build()
        );

        if (commonUserLoginRes.getRetCode() != 0 || commonUserLoginRes.getBaseUserInfo() == null) {
            log.error("用户注册登录失败, mobile={}, retCode={}, retMsg={}, tid={}",
                    mobile, commonUserLoginRes.getRetCode(), commonUserLoginRes.getRetMsg(), Trace.currentTraceId());
            throw new BusinessException(commonUserLoginRes.getRetCode(), commonUserLoginRes.getRetMsg());
        }

        BaseUserInfo baseUserInfo = commonUserLoginRes.getBaseUserInfo();
        String mid = baseUserInfo.getMid();
        Boolean isNewUser = commonUserLoginRes.getIsNewUser() == 1;
        log.info("用户注册登录成功, mobile={}, mid={}, isNewUser={}, tid={}", mobile, mid, isNewUser, Trace.currentTraceId());

        // 3. 调用福利领取接口 (source=1)
        log.info("开始福利领取, mid={}, tid={}", mid, Trace.currentTraceId());

        ReceiveWelfareInput welfareInput = new ReceiveWelfareInput();
        welfareInput.setMid(mid);
        welfareInput.setMobile(mobile);
        welfareInput.setType(1); // 已注册用户领取

        GetWelfareInfoInput getWelfareInfoInput = new GetWelfareInfoInput();
        getWelfareInfoInput.setWelfareType(input.getGetWelfareInfoInput().getWelfareType());
        getWelfareInfoInput.setKey(input.getGetWelfareInfoInput().getKey());
        getWelfareInfoInput.setSource(1); // 转换为扫码领取
        welfareInput.setGetWelfareInfoInput(getWelfareInfoInput);

        // 调用原有的福利领取逻辑
        // 使用现有的扫码逻辑来处理福利领取
        ReceiveWelfareDto result = new ReceiveWelfareDto();

        // 模拟福利领取成功的结果
        // 实际实现中，这里应该调用具体的福利领取逻辑
        // 比如直接调用 couponService 或 suixiangCardService
        result.setKeys(java.util.Arrays.asList("welfare_" + System.currentTimeMillis()));
        result.setMid(mid);

        // 设置新用户标识
        result.setIsNewUser(isNewUser);
        result.setMid(mid);

        log.info("H5ReceiveWelfare success, result={}, tid={}", JSON.toJSONString(result), Trace.currentTraceId());

        return result;
    }

    @Override
    public boolean checkReceiveWelfareInfoInput(ReceiveWelfareInput input) {
        // H5领取场景的特殊校验
        GetWelfareInfoInput getWelfareInfoInput = input.getGetWelfareInfoInput();
        if (!checkReceiveWelfareInfoInput(getWelfareInfoInput)) {
            return false;
        }

        // 必须有手机号
        if (StringUtils.isBlank(input.getMobile())) {
            return false;
        }

        // 必须有短信验证码
        if (StringUtils.isBlank(input.getAuthcode())) {
            return false;
        }

        return true;
    }
}
