package com.saicmobility.evcard.md.act.entity.siac;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 渠道优惠券操作日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ChannelCouponOperationLog对象", description="渠道优惠券操作日志")
public class ChannelCouponOperationLog extends Model<ChannelCouponOperationLog> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "操作类型 1=渠道发放 2=渠道用户手动退 3=渠道过期自动退")
    private Integer operationType;

    @ApiModelProperty(value = "channel_coupon主键")
    private String channelCouponId;

    @ApiModelProperty(value = "渠道唯一标识")
    private String channelId;

    @ApiModelProperty(value = "渠道的用户唯一标识")
    private String channelUserId;

    @ApiModelProperty(value = "渠道的请求唯一标识")
    private String channelOrderId;

    @ApiModelProperty(value = "渠道的活动编号")
    private String channelActivityId;

    @ApiModelProperty(value = "优惠券模板id")
    private Long mmpThirdCouponId;

    @ApiModelProperty(value = "发放数量")
    private Integer offerNum;

    @ApiModelProperty(value = "接口成功标记（0：成功 1：失败）")
    private Integer successFlag;

    @ApiModelProperty(value = "原始请求报文")
    private String originRequest;

    @ApiModelProperty(value = "返回报文")
    private String originResponse;

    @ApiModelProperty(value = "状态（0=正常   1=已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人名称")
    private String createOperName;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateOperId;

    @ApiModelProperty(value = "更新人名称")
    private String updateOperName;

}
