package com.saicmobility.evcard.md.act.enums.channelcoupon;

/**
 * 建行优惠券兑换码状态
 */
public enum CCBCouponStatusChangeEnum {
    NOT_TO_USED("011019", "未使用->已使用（已产生成本，记作结算依据）"),
    USED_TO_NOT("011020", "已使用->未使用（因退单而返还优惠券）"),
    NOT_TO_RECEIVED("011036", "未兑换->已领取（已产生成本，记作结算依据）"),
    NOT_TO_REDEEM("011037", "未兑换->已兑换（未产生成本、不作结算依据，中间态）"),
    ;
    private String code;
    private String msg;

    CCBCouponStatusChangeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
