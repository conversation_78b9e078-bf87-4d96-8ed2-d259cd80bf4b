package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_operate_log")
@ApiModel(value="OperateLog对象", description="操作日志表")
public class OperateLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "外键id")
    private String foreignId;

    @ApiModelProperty(value = "操作类型 1:套餐日志 2:立减活动日志 3:渠道营销活动日志 4:自营活动报名操作日志 5:自营活动操作日志")
    private Integer operateType;

    @ApiModelProperty(value = "操作内容")
    private String operateContent;

    @ApiModelProperty(value = "备注")
    private String miscDesc;

    @ApiModelProperty(value = "创建人所属公司代码")
    private String createOperOrgName;

    @ApiModelProperty(value = "修改人所属公司名称")
    private String updateOperOrgName;
}
