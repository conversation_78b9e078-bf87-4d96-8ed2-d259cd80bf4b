package com.saicmobility.evcard.md.act.annotation;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface ConfigurationAttribute {


    String fieldName() default "";

    boolean isComplex() default false;

    // 当为true时，这个用于填充查询属性时的 configuration_id, 这个属性不会入库
    boolean isConfigurationId() default false;
}
