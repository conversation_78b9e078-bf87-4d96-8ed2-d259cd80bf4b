package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.saicmobility.evcard.md.act.util.DateUtils;
import com.saicmobility.evcard.md.mdactservice.api.BrandModelLogInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 品牌车型操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_brand_model_log")
@ApiModel(value="BrandModelLog对象", description="品牌车型操作日志表")
public class BrandModelLog extends Model<BrandModelLog> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "删除标记（0：正常 1：删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人")
    private String createOperName;
    @ApiModelProperty(value = "创建人Email")
    private String createOperEmail;

    @ApiModelProperty(value = "创建人所属公司名称")
    private String createOperOrgName;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人id")
    private Long updateOperId;

    @ApiModelProperty(value = "修改人")
    private String updateOperName;
    @ApiModelProperty(value = "修改人邮箱")
    private String updateOperEmail;

    @ApiModelProperty(value = "修改人所属公司名称")
    private String updateOperOrgName;

    @ApiModelProperty(value = "品牌车型表主键id")
    private Long brandModelId;

    @ApiModelProperty(value = "操作类型 1-新增,2-上线,3-修改,4-暂停,5-下线")
    private Integer operateType;

    @ApiModelProperty(value = "操作内容")
    private String operateContent;

    @ApiModelProperty(value = "操作备注")
    private String operateRemark;


    public static BrandModelLogInfo toRes(BrandModelLog brandModelLog){
        return BrandModelLogInfo.newBuilder()
                .setId(brandModelLog.getId())
                .setCreateTime(DateUtils.dateToString(brandModelLog.getCreateTime(),DateUtils.DATE_TYPE1))
                .setCreateOperId(brandModelLog.getCreateOperId())
                .setCreateOperName(brandModelLog.getCreateOperName())
                .setCreateOperOrgName(brandModelLog.getCreateOperOrgName())
                .setUpdateTime(DateUtils.dateToString(brandModelLog.getUpdateTime(),DateUtils.DATE_TYPE1))
                .setUpdateOperId(brandModelLog.getUpdateOperId())
                .setUpdateOperName(brandModelLog.getUpdateOperName())
                .setUpdateOperOrgName(brandModelLog.getUpdateOperOrgName())
                .setBrandModelId(brandModelLog.getBrandModelId())
                .setOperateType(brandModelLog.getOperateType())
                .setOperateContent(brandModelLog.getOperateContent())
                .setOperateRemark(brandModelLog.getOperateRemark())
                .setCreateOperEmail(brandModelLog.getCreateOperEmail())
                .setCreateOperEmail(brandModelLog.getUpdateOperEmail())
                .build();
    }
}
