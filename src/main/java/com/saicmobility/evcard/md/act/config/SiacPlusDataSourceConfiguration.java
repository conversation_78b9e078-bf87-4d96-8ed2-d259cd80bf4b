package com.saicmobility.evcard.md.act.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * mybatis-plus 配置
 */
@Configuration
public class SiacPlusDataSourceConfiguration {

    public static final String MAPPER_LOCATION = "classpath:mapper/siacPlus/*.xml";


    @Autowired
    MybatisPlusInterceptor mybatisPlusInterceptor;


    @Bean("siacPlusSqlSessionFactory")
    public SqlSessionFactory siacPlusSqlSessionFactory(@Qualifier("siacDataSource") DataSource dataSource) throws Exception {
        // 使用到了mybatis plus , 所以这里使用的是mybatis plus组件
        MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION));
        sqlSessionFactoryBean.setPlugins(mybatisPlusInterceptor);
        return sqlSessionFactoryBean.getObject();
    }

    @Bean("siacPlusSqlSessionTemplate")
    public SqlSessionTemplate siacSqlSessionTemplate(@Qualifier("siacDataSource") DataSource dataSource) throws Exception {
        return new SqlSessionTemplate(siacPlusSqlSessionFactory(dataSource));
    }
}
