package com.saicmobility.evcard.md.act.util;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

public class ConfigSupportUtil {

    /**
     * 获取下一个 12点或者24点
     * @return
     */
    public static Date getNextNoon() {
        LocalDateTime today = LocalDate.now().atStartOfDay();
        LocalDateTime now = LocalDateTime.now();
        if (now.compareTo(today.plusHours(12)) < 0) {
            // 小于12点，
            return DateUtil.localTimeToDate(today.plusHours(12));
        } else {
            return DateUtil.localTimeToDate(today.plusDays(1));
        }
    }

    /**
     * 获取下一个 12点或者24点
     * @return
     */
    public static LocalDateTime getNextNoonLocalDate() {
        LocalDateTime today = LocalDate.now().atStartOfDay();
        LocalDateTime now = LocalDateTime.now();
        if (now.compareTo(today.plusHours(12)) < 0) {
            // 小于12点，
            return today.plusHours(12);
        } else {
            return today.plusDays(1);
        }
    }
}
