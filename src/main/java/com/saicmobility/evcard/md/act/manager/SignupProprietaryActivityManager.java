package com.saicmobility.evcard.md.act.manager;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.bo.proprietary.GetProprietaryActivityInfoBo;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.domain.activity.SignupProprietaryActivityDto;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivity;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivitySignup;
import com.saicmobility.evcard.md.act.enums.ConfigAttrType;
import com.saicmobility.evcard.md.act.enums.IsDeletedEnum;
import com.saicmobility.evcard.md.act.enums.proprietary.ActivitySignupStatusEnum;
import com.saicmobility.evcard.md.act.service.IProprietaryActivityService;
import com.saicmobility.evcard.md.act.service.IProprietaryActivitySignupService;
import com.saicmobility.evcard.md.act.service.OperateLogService;
import com.saicmobility.evcard.md.mdactservice.api.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SignupProprietaryActivityManager {
    @Resource
    private IProprietaryActivitySignupService proprietaryActivitySignupService;

    @Resource
    private IProprietaryActivityService proprietaryActivityService;

    @Resource
    private OperateLogService operateLogService;

    /**
     * 报名
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertSignupActivity(List<ProprietaryActivitySignup> actSignupList, SignupProprietaryActivityDto dto, GetProprietaryActivityInfoBo activityInfo, CurrentUser currentUser, String logContent) throws BusinessException {

        // TODO: 需要加锁！！！！

        UserDTO userDTO = new UserDTO();
        if (currentUser != null) {
            userDTO.setId(currentUser.getUserId());
            userDTO.setUsername(currentUser.getUserName());
        }
        Date currDate = new Date();
        //批量向t_proprietary_activity_signup插入数据
        boolean ret = false;
        ret = proprietaryActivitySignupService.saveBatch(actSignupList, userDTO, currDate);
        if (!ret) {
            log.error("向t_proprietary_activity_signup插入数据失败,ProprietaryActivitySignup={}", JSON.toJSONString(dto));
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "向t_proprietary_activity_signup插入数据失败");
        }

        // 更新 t_proprietary_activity 表
//        List<String> orgCodeList = orgCodesSplit(activityInfo.getSignUpOrgCodes());
//        List<String> tmpList = new ArrayList<>(orgCodeList);
//        for (String orgCode : dto.getOrgCodes()) {
//            if (!orgCodeList.contains(orgCode)) {
//                tmpList.add(orgCode); // 添加新机构编号
//            }
//        }
//        ret = proprietaryActivityService.lambdaUpdate()
//                .set(ProprietaryActivity::getSignUpOrgCodes, signupOrgCodeStr(tmpList))
//                .set(ProprietaryActivity::getUpdateOperId, userDTO.getId())
//                .set(ProprietaryActivity::getUpdateOperName, userDTO.getUsername())
//                .set(ProprietaryActivity::getUpdateTime, currDate)
//                .eq(ProprietaryActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
//                .eq(ProprietaryActivity::getId, dto.getActivityId())
//                .update();
//        if (!ret) {
//            log.error("t_proprietary_activity更细数据失败,ProprietaryActivity={}", JSON.toJSONString(orgCodeList));
//            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "t_proprietary_activity更细数据失败");
//        }

        //记录日志
        ret = operateLogService.saveOperateLog(logContent, String.valueOf(dto.getActivityId()), ConfigAttrType.SIGNUP_ACTIVITY_CONFIG.getType(), currentUser);
        if (!ret) {
            log.error("记录日志失败");
            throw new BusinessException(ErrorEnum.ADD_LOG_FAILED.getCode(), ErrorEnum.ADD_LOG_FAILED.getMsg());
        }
    }

    /**
     * 退出报名
     */
    @Transactional(rollbackFor = Exception.class)
    public void quitSignupActivity(ProprietaryActivitySignup sinnupInfo, GetProprietaryActivityInfoBo act, CurrentUser currentUser, String logContent) throws BusinessException {
        UserDTO userDTO = new UserDTO();
        if (currentUser != null) {
            userDTO.setId(currentUser.getUserId());
            userDTO.setUsername(currentUser.getUserName());
        }
        Date currDate = new Date();

        boolean ret = false;
        // 老记录逻辑删除
        ret = proprietaryActivitySignupService.lambdaUpdate()
                .set(ProprietaryActivitySignup::getIsDeleted, IsDeletedEnum.IS_DELETE.getType())
                .set(ProprietaryActivitySignup::getUpdateOperId, userDTO.getId())
                .set(ProprietaryActivitySignup::getUpdateOperName, userDTO.getUsername())
                .set(ProprietaryActivitySignup::getUpdateTime, currDate)
                .eq(ProprietaryActivitySignup::getId, sinnupInfo.getId())
                .update();
        if (!ret) {
            log.error("退出报名自营活动失败, id ={}", sinnupInfo.getId());
            throw new BusinessException(ErrorEnum.CANCEL_FAIL.getCode(), ErrorEnum.CANCEL_FAIL.getMsg());
        }

//        // 更新 t_proprietary_activity 表
//        List<String> orgCodeList = orgCodesSplit(act.getSignUpOrgCodes());
//        if (orgCodeList.contains(sinnupInfo.getOrgCode())) {
//            orgCodeList.remove(sinnupInfo.getOrgCode()); // 移除报名机构
//        }
//        ret = proprietaryActivityService.lambdaUpdate()
//                .set(ProprietaryActivity::getSignUpOrgCodes, signupOrgCodeStr(orgCodeList))
//                .set(ProprietaryActivity::getUpdateOperId, userDTO.getId())
//                .set(ProprietaryActivity::getUpdateOperName, userDTO.getUsername())
//                .set(ProprietaryActivity::getUpdateTime, currDate)
//                .eq(ProprietaryActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
//                .eq(ProprietaryActivity::getId, sinnupInfo.getActivityId())
//                .update();
//        if (!ret) {
//            log.error("t_proprietary_activity更细数据失败,ProprietaryActivity={}", JSON.toJSONString(orgCodeList));
//            throw new BusinessException(ErrorEnum.ADD_ACTIVITY_FAILED.getCode(), ErrorEnum.ADD_ACTIVITY_FAILED.getMsg());
//        }

        //记录日志
        ret = operateLogService.saveOperateLog(logContent, String.valueOf(sinnupInfo.getActivityId()), ConfigAttrType.SIGNUP_ACTIVITY_CONFIG.getType(), currentUser);
        if (!ret) {
            log.error("记录日志失败");
            throw new BusinessException(ErrorEnum.ADD_LOG_FAILED.getCode(), ErrorEnum.ADD_LOG_FAILED.getMsg());
        }
    }

    /**
     * 编辑报名信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSignupActivity(ProprietaryActivitySignup updateInfo, long oldSignupId, CurrentUser currentUser, String logContent) throws BusinessException {
        UserDTO userDTO = new UserDTO();
        if (currentUser != null) {
            userDTO.setId(currentUser.getUserId());
            userDTO.setUsername(currentUser.getUserName());
        }
        Date currDate = new Date();

        boolean ret = false;
        // 先将老记录逻辑删除
        ret = proprietaryActivitySignupService.lambdaUpdate()
                .set(ProprietaryActivitySignup::getIsDeleted, IsDeletedEnum.IS_DELETE.getType())
                .set(ProprietaryActivitySignup::getUpdateOperName, userDTO.getUsername())
                .set(ProprietaryActivitySignup::getUpdateTime, currDate)
                .eq(ProprietaryActivitySignup::getId, oldSignupId)
                .update();
        if (!ret) {
            log.error("更新报名记录失败");
            throw new BusinessException(ErrorEnum.UPDATE_ERROR8.getCode(), ErrorEnum.UPDATE_ERROR8.getMsg());
        }

        // 再落库一条新数据
        ret = proprietaryActivitySignupService.save(updateInfo, userDTO, currDate);
        if (!ret) {
            log.error("向t_proprietary_activity_signup插入数据失败,ProprietaryActivitySignup={}", JSON.toJSONString(updateInfo));
            throw new BusinessException(ErrorEnum.UPDATE_ERROR8.getCode(), ErrorEnum.UPDATE_ERROR8.getMsg());
        }

        // 记录日志
        ret = operateLogService.saveOperateLog(logContent, String.valueOf(updateInfo.getActivityId()), ConfigAttrType.SIGNUP_ACTIVITY_CONFIG.getType(), currentUser);
        if (!ret) {
            log.error("记录日志失败");
            throw new BusinessException(ErrorEnum.ADD_LOG_FAILED.getCode(), ErrorEnum.ADD_LOG_FAILED.getMsg());
        }
    }

//    private String signupOrgCodeStr(List<String> orgCodeList) {
//        if (CollectionUtils.isEmpty(orgCodeList)) {
//            return "";
//        }
//        StringBuilder sb = new StringBuilder("");
//        for (String orgCode : orgCodeList) {
//            sb.append("(" + orgCode + ")");
//        }
//        return sb.toString();
//    }
//
//    private List<String> orgCodesSplit(String orgCodes) {
//        if (StringUtils.isEmpty(orgCodes)) {
//            return new ArrayList<>();
//        }
//        orgCodes = orgCodes.substring(1, orgCodes.length());  // 去除首字字符
//        orgCodes = orgCodes.substring(0, orgCodes.length() - 1); // 去除尾字符
//        String[] arr = orgCodes.split("\\)\\(");  // 根据字符串分割转数组
//        List<String> list = Arrays.stream(arr).collect(Collectors.toList()); // 数组转List
//        return list;
//    }

}

