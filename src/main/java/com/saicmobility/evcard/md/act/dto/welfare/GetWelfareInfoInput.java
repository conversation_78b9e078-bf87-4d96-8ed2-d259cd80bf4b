package com.saicmobility.evcard.md.act.dto.welfare;

import lombok.Data;

import java.io.Serializable;

@Data
public class GetWelfareInfoInput implements Serializable {

    /**
     *
     * key 长度
     *  随想卡兑换码 16位
     *  优惠券兑换码 15位
     *  一码多券 17位
     *
     *
     */
    private String key; // 唯一标识，id 或者 兑换码
    private int welfareType; // 类型 随享卡 1，优惠券 2
    private int source; // 来源（扫码发券 1、转赠 2）
}
