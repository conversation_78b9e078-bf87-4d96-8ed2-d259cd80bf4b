package com.saicmobility.evcard.md.act.service;

import com.saicmobility.evcard.md.act.entity.BrandModelLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.saicmobility.evcard.md.act.enums.BrandModelOperStateEnum;
import com.saicmobility.evcard.md.mdactservice.api.CurrentUser;
import com.saicmobility.evcard.md.mdactservice.api.QueryBrandModelLogsByActIdReq;
import com.saicmobility.evcard.md.mdactservice.api.QueryBrandModelLogsByActIdRes;

import java.util.List;

/**
 * <p>
 * 品牌车型操作日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
public interface IBrandModelLogService extends IService<BrandModelLog> {

    Boolean saveOperateLog(Long brandModelId, BrandModelOperStateEnum brandModelEnum, String remark, CurrentUser currentUser);

    QueryBrandModelLogsByActIdRes queryBrandModelLogsByActId(QueryBrandModelLogsByActIdReq queryBrandModelLogsByActIdReq);
}
