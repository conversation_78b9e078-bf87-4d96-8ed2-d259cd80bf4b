package com.saicmobility.evcard.md.act.job;

import com.saicmobility.evcard.md.act.entity.ChannelActivity;
import com.saicmobility.evcard.md.act.service.ExternalSystemFacade;
import com.saicmobility.evcard.md.act.service.IChannelActivityService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 营销活动 全量更新擎路
 */

@Slf4j
@Component
@JobHandler("AllActivityToQlJob")
public class AllActivityToQlJob extends IJobHandler {

    @Resource
    private ExternalSystemFacade externalSystemFacade;

    @Resource
    private IChannelActivityService channelActivityService;

    @Value("${all.activity.to.ql.enable:0}")
    private String enable;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("============> 开始执行 营销活动全量更新擎路 定时任务 <============");

        Long startId = 0L;
        List<ChannelActivity> list = new ArrayList<>();
        do {
            if ("1".equals(enable)) {
                break;
            }

            try {
                log.info("营销活动全量更新擎路 ，start={}", startId);
                list = channelActivityService.lambdaQuery()
                        .gt(ChannelActivity::getId, startId)
                        .orderByAsc(ChannelActivity::getId)
                        .last("limit 50")
                        .list();
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }

                ChannelActivity lastChannelActivity = list.get(list.size() - 1);
                startId = lastChannelActivity.getId();
                // 批量调用 擎路同步接口
                externalSystemFacade.batchNotifyChannelActivitySync(list);
            } catch (Exception e) {
                log.error("营销活动全量更新擎路,异常", e);
                break;
            }
        } while (CollectionUtils.isNotEmpty(list));

        log.info("============> 结束执行 营销活动全量更新擎路 定时任务 <============");
        return ReturnT.SUCCESS;
    }
}
