package com.saicmobility.evcard.md.act.manager;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.constant.BusinessConst;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.domain.QlSecondChannelBo;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.entity.ChannelActivity;
import com.saicmobility.evcard.md.act.entity.OperateLog;
import com.saicmobility.evcard.md.act.enums.ConfigAttrType;
import com.saicmobility.evcard.md.act.enums.IsDeletedEnum;
import com.saicmobility.evcard.md.act.service.IChannelActivityService;
import com.saicmobility.evcard.md.act.service.OperateLogService;
import com.saicmobility.evcard.md.act.service.extern.ConfigLoader;
import com.saicmobility.evcard.md.mdactservice.api.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class MarketingModuleManager {

    @Resource
    private IChannelActivityService channelActivityService;

    @Resource
    private OperateLogService operateLogService;

    @Resource
    private ConfigLoader configLoader;

    @Transactional(rollbackFor = Exception.class)
    public void insertChannelActivityInfo(ChannelActivity act, CurrentUser currentUser, String logContent) throws BusinessException {
        UserDTO userDTO = new UserDTO();
        if (currentUser != null) {
            userDTO.setId(currentUser.getUserId());
            userDTO.setUsername(currentUser.getUserName());
            userDTO.setName(currentUser.getName());
        }
        Date currDate = new Date();

        //向t_activity_channel插入数据
        boolean ret = false;
        ret = channelActivityService.save(act, userDTO, currDate);
        if (!ret) {
            log.error("向t_activity_channel插入数据失败,ChannelActivity={}", JSON.toJSONString(act));
            throw new BusinessException(ErrorEnum.ADD_ACT_FAILED.getCode(), ErrorEnum.ADD_ACT_FAILED.getMsg());
        }
        //记录日志
        //日志细节
        QlSecondChannelBo qlSecondChannelBo = configLoader.getQlSecondChannelBo(act.getSecondAppKey());
        if (qlSecondChannelBo != null) {
            logContent += qlSecondChannelBo.getSecondChannelName();
        }

        logContent += "渠道活动";
        ret = operateLogService.saveOperateLog(logContent, act.getId().toString(), ConfigAttrType.CHANNEL_ACTIVITY_CONFIG.getType(), currentUser);
        if (!ret) {
            log.error("记录日志失败");
            throw new BusinessException(ErrorEnum.ADD_LOG_FAILED.getCode(), ErrorEnum.ADD_LOG_FAILED.getMsg());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateChannelActivityInfo(ChannelActivity act, CurrentUser currentUser, String logContent, Long oldId) throws BusinessException {
        UserDTO userDTO = new UserDTO();
        if (currentUser != null) {
            userDTO.setId(currentUser.getUserId());
            userDTO.setUsername(currentUser.getUserName());
            userDTO.setName(currentUser.getName());
        }
        Date currDate = new Date();

        //逻辑删除
        boolean ret = channelActivityService.lambdaUpdate()
                .set(ChannelActivity::getIsDeleted, IsDeletedEnum.IS_DELETE.getType())
                .set(ChannelActivity::getUpdateOperId, userDTO.getId())
                .set(ChannelActivity::getUpdateOperName, userDTO.getUsername())
                .set(ChannelActivity::getUpdateTime, currDate)
                .eq(ChannelActivity::getId, oldId)
                .update();
        if (!ret) {
            log.error("逻辑删除原记录失败");
            throw new BusinessException(ErrorEnum.UPDATE_ERROR7.getCode(), ErrorEnum.UPDATE_ERROR7.getMsg());
        }

        //更新
        ret = channelActivityService.save(act, userDTO, currDate);
        if (!ret) {
            log.error("更新数据失败");
            throw new BusinessException(ErrorEnum.UPDATE_ERROR8.getCode(), ErrorEnum.UPDATE_ERROR8.getMsg());
        }

        //继承操作日志
        ret = operateLogService.lambdaUpdate()
                .set(OperateLog::getForeignId, act.getId())
                .eq(OperateLog::getOperateType, ConfigAttrType.CHANNEL_ACTIVITY_CONFIG.getType())
                .eq(OperateLog::getForeignId, oldId)
                .update();
        if (!ret) {
            log.error("将被逻辑删除的数据日志增加到新数据的日志中失败");
            throw new BusinessException(ErrorEnum.OPERATORLOG_ERROR1.getCode(), ErrorEnum.OPERATORLOG_ERROR1.getMsg());
        }

        //更新日志
        ret = operateLogService.saveOperateLog(logContent, act.getId().toString(), ConfigAttrType.CHANNEL_ACTIVITY_CONFIG.getType(),currentUser);
        if(!ret){
            log.error("更新操作日志失败");
            throw new BusinessException(ErrorEnum.OPERATORLOG_ERROR2.getCode(), ErrorEnum.OPERATORLOG_ERROR2.getMsg());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteChannelActivityInfo(long id, CurrentUser currentUser, String logContent) throws BusinessException {
        UserDTO userDTO = new UserDTO();
        if (currentUser != null) {
            userDTO.setId(currentUser.getUserId());
            userDTO.setUsername(currentUser.getUserName());
            userDTO.setName(currentUser.getName());
        }
        Date currDate = new Date();

        boolean ret = channelActivityService.lambdaUpdate()
                .set(ChannelActivity::getIsDeleted, IsDeletedEnum.IS_DELETE.getType())
                .set(ChannelActivity::getUpdateOperId, userDTO.getId())
                .set(ChannelActivity::getUpdateOperName, userDTO.getUsername())
                .set(ChannelActivity::getUpdateTime, currDate)
                .eq(ChannelActivity::getId, id)
                .update();
        if (!ret) {
            log.error("逻辑删除数据失败, id ={}", id);
            throw new BusinessException(ErrorEnum.DELETE_ERROR2.getCode(), ErrorEnum.DELETE_ERROR2.getMsg());
        }

        //记录日志
        ret = operateLogService.saveOperateLog(logContent, String.valueOf(id), ConfigAttrType.CHANNEL_ACTIVITY_CONFIG.getType(), currentUser);
        if(!ret){
            log.error("更新操作日志失败");
            throw new BusinessException(ErrorEnum.OPERATORLOG_ERROR2.getCode(), ErrorEnum.OPERATORLOG_ERROR2.getMsg());
        }
    }
}
