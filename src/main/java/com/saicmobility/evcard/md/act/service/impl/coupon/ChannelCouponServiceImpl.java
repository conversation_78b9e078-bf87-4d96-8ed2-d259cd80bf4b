package com.saicmobility.evcard.md.act.service.impl.coupon;

import com.saicmobility.evcard.md.act.entity.siac.ChannelCoupon;
import com.saicmobility.evcard.md.act.mapper.siacPlus.ChannelCouponMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saicmobility.evcard.md.act.service.coupun.IChannelCouponService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 第三方发放优惠券记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Service
public class ChannelCouponServiceImpl extends ServiceImpl<ChannelCouponMapper, ChannelCoupon> implements IChannelCouponService {

}
