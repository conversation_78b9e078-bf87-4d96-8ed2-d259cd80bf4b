package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店参与的立减活动操作日志表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_reduce_activity_participate_log")
public class ReduceActivityParticipateLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @ApiModelProperty(value = "立减活动id")
    private Long activityId;

    @ApiModelProperty(value = "商品车型")
    private Long goodsModelId;

    @ApiModelProperty(value = "操作内容")
    private String operateContent;

    @ApiModelProperty(value = "创建人所属公司代码")
    private String createOperOrgName;

    @ApiModelProperty(value = "修改人所属公司名称")
    private String updateOperOrgName;

}
