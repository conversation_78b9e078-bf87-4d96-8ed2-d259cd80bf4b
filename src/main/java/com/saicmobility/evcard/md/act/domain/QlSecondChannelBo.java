package com.saicmobility.evcard.md.act.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class QlSecondChannelBo {

    // 第三方id 写在配置中心
    private Long thirdId;

    // 优惠码前缀，优惠码 前缀 1：XX 2：XC 3:FZ 4:HL 5:ZZC 6:WK 10 FX 写在配置中心
    private String disCountCode;

    // 二级渠道名称
    private String secondChannelName;
    // 二级渠道key
    private String secondAppKey;



}
