package com.saicmobility.evcard.md.act.dto.suixiangcard;

import com.saicmobility.evcard.md.mdactservice.api.GetSuiXiangCardThirdSaleInfoReq;
import lombok.Data;

import java.io.Serializable;

@Data
public class SuiXiangCardThirdSaleInfoInput implements Serializable {
    private Long purchaseId;
    private Long useCardId;
    /**
     * 类型 1：用户未支付 purchaseId必填 2：用户已支付 useCardId必填
     */
    private Integer type;

    public SuiXiangCardThirdSaleInfoInput(GetSuiXiangCardThirdSaleInfoReq req) {
        this.purchaseId = req.getPurchaseId();
        this.useCardId = req.getUseCardId();
        this.type = req.getType();
    }
}
