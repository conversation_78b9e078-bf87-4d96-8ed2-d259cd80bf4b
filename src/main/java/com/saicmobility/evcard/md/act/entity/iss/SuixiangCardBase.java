package com.saicmobility.evcard.md.act.entity.iss;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 随享卡基础表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("suixiang_card_base")
@ApiModel(value="SuixiangCardBase对象", description="随享卡基础表")
public class SuixiangCardBase extends Model<SuixiangCardBase> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "随享卡主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "随享卡-卡片名称")
    private String cardName;

    @ApiModelProperty(value = "卡片所属机构(运营公司)")
    private String orgId;

    @ApiModelProperty(value = "可用区域，多个id以逗号分隔")
    private String cityId;

    @ApiModelProperty(value = "预告时间，格式 yyyy-MM-dd HH:mm")
    private LocalDateTime advanceNoticeTime;

    @ApiModelProperty(value = "售卖开始时间，格式 yyyy-MM-dd HH:mm")
    private LocalDateTime saleStartTime;

    @ApiModelProperty(value = "售卖结束时间，格式 yyyy-MM-dd HH:mm")
    private LocalDateTime saleEndTime;

    @ApiModelProperty(value = "购买后n天生效，默认立即生效")
    private Integer effectiveDays;

    @ApiModelProperty(value = "卡片有效期天数类型 有效期：1-一个月、2-两个月、3-三个月、6-六个月、12-一年")
    private Integer validDaysType;

    @ApiModelProperty(value = "初始库存")
    private Integer initStock;

    @ApiModelProperty(value = "当前库存")
    private Integer stock;

    @ApiModelProperty(value = "销量")
    private Integer sales;

    @ApiModelProperty(value = " 是否对外展示：1-是、2-否")
    private Integer displayFlag;

    @ApiModelProperty(value = "单订单时长（当前城市平均订单时长，单位是天）")
    private BigDecimal singleOrderDuration;

    @ApiModelProperty(value = "卡面样式 0：自定义  1：样式1 2：样式2 3：样式3")
    private Integer styleType;

    @ApiModelProperty(value = "卡面背景图片")
    private String backUrl;

    @ApiModelProperty(value = "活动规则说明")
    private String rules;

    @ApiModelProperty(value = "节假日是否可用 ;1:可用  2：不可用")
    private Integer holidayAvailable;

    @ApiModelProperty(value = "不可用日期")
    private String unavailableDate;

    @ApiModelProperty(value = "卡片状态：1-待提交、2-待上架、3-已上架(开放购买)、4-已下架")
    private Integer cardStatus;

    @ApiModelProperty(value = "购买上限数，0代表没有购买张数限制")
    private Integer purchaseLimitNum;

    @ApiModelProperty(value = "兑换卡是否可以合并，1：可以 2：不可以 默认为2")
    private Integer mergeFlag;

    @ApiModelProperty(value = "合作车企品牌id集合，多个用逗号分割")
    private String vehicleBrandIds;

    @ApiModelProperty(value = "是否有落地页，1：有 2：没有，默认为2")
    private Integer landingPageFlag;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人")
    private String createOperName;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人id")
    private Long updateOperId;

    @ApiModelProperty(value = "修改人")
    private String updateOperName;

    @ApiModelProperty(value = "删除标记（0：正常 1：删除）")
    private Integer isDeleted;


}
