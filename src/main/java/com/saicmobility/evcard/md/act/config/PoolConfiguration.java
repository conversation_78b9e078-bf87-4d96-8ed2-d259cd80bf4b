package com.saicmobility.evcard.md.act.config;

import krpc.rpc.impl.TracablePool;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PoolConfiguration {

    @Value("${mdactservice.channelActivitySyncPool.threads:1}")
    int channelActivitySyncPoolThreads = 1;
    @Value("${mdactservice.cancelRuleSyncPool.queueSize:1000}")
    int channelActivitySyncPoolQueueSize = 1000;

    @Bean(initMethod = "init", destroyMethod = "close")
    TracablePool channelActivitySyncPool() {
        TracablePool pool = new TracablePool();
        pool.setThreads(channelActivitySyncPoolThreads);
        pool.setMaxThreads(channelActivitySyncPoolThreads);
        pool.setQueueSize(channelActivitySyncPoolQueueSize);
        pool.setDefaultTimeout(30000);
        pool.setName("channelActivitySyncPool");
        return pool;
    }

    @Value("${mdactservice.ccbCouponStatusCallBackPool.threads:1}")
    int ccbCouponStatusCallBackPoolThreads = 1;
    @Value("${mdactservice.ccbCouponStatusCallBackPool.queueSize:1000}")
    int ccbCouponStatusCallBackPoolQueueSize = 1000;

    @Bean(initMethod = "init", destroyMethod = "close")
    TracablePool ccbCouponStatusCallBackPool() {
        TracablePool pool = new TracablePool();
        pool.setThreads(ccbCouponStatusCallBackPoolThreads);
        pool.setMaxThreads(ccbCouponStatusCallBackPoolThreads);
        pool.setQueueSize(ccbCouponStatusCallBackPoolQueueSize);
        pool.setDefaultTimeout(30000);
        pool.setName("ccbCouponStatusCallBackPool");
        return pool;
    }

    @Value("${mdactservice.ccbPushReconciliationFilePool.threads:1}")
    int ccbPushReconciliationFilePoolThreads = 1;
    @Value("${mdactservice.ccbPushReconciliationFilePool.queueSize:1000}")
    int ccbPushReconciliationFilePoolQueueSize = 1000;

    @Bean(initMethod = "init", destroyMethod = "close")
    TracablePool ccbPushReconciliationFilePool() {
        TracablePool pool = new TracablePool();
        pool.setThreads(ccbPushReconciliationFilePoolThreads);
        pool.setMaxThreads(ccbPushReconciliationFilePoolThreads);
        pool.setQueueSize(ccbPushReconciliationFilePoolQueueSize);
        pool.setDefaultTimeout(30000);
        pool.setName("ccbPushReconciliationFilePool");
        return pool;
    }
}

