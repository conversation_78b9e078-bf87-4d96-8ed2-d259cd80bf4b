/*
package com.saicmobility.evcard.md.act.service.impl;

import com.saicmobility.evcard.md.act.constant.BusinessConst;
import com.saicmobility.evcard.md.act.domain.OperateLogBo;
import com.saicmobility.evcard.md.act.domain.common.PageResult;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.domain.packages.*;
import com.saicmobility.evcard.md.act.entity.PackageConfiguration;
import com.saicmobility.evcard.md.act.enums.ConfigAttrType;
import com.saicmobility.evcard.md.act.enums.PackageConfigStateEnum;
import com.saicmobility.evcard.md.act.exception.BusinessException;
import com.saicmobility.evcard.md.act.service.ConfigurationAttrService;
import com.saicmobility.evcard.md.act.service.OperateLogService;
import com.saicmobility.evcard.md.act.service.PackageConfigurationService;
import com.saicmobility.evcard.md.act.service.PackageManageService;
import com.saicmobility.evcard.md.act.service.user.IUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

*/
/**
 * @Author: fsh
 * @Date: 2022/4/15 16:43
 *//*

@Component
public class PackageManageServiceImpl implements PackageManageService {

    @Autowired
    PackageConfigurationService packageConfigurationService;

    @Autowired
    IUserService userService;

    @Autowired
    ConfigurationAttrService configurationAttrService;

    @Autowired
    OperateLogService operateLogService;

    */
/** 版本号*//*

    private static final String VERSION = "1";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addPackageConfig(AddPackageConfigDto configDto) throws BusinessException {

       */
/* *//*
*/
/**
         * 判断生效时间
         *//*
*/
/*
        if(configDto.getStartTime().isAfter(configDto.getUseStartDate())){
            throw new BusinessException(-1,"生效时间不可晚于用车日期区间的开始日期");
        }
        if(configDto.getStartTime().isBefore(DateUtil.getSystemDate())){
            throw new BusinessException(-1,"生效时间不可早于当前日期");
        }

        *//*
*/
/**
         * 判断是否存在有效的套餐配置
         *//*
*/
/*
        List<PackageConfiguration> list = packageConfigurationService.lambdaQuery()
                .eq(PackageConfiguration::getOrgCode, configDto.getOrgCode())
                .eq(PackageConfiguration::getStoreId, configDto.getStoreId())
                .eq(PackageConfiguration::getGoodsModelId, configDto.getGoodsModelId())
                .eq(PackageConfiguration::getDaysNumber, configDto.getDaysNumber())
                .in(PackageConfiguration::getConfigState, Arrays.asList(1,2))
                .list();
        if(CollectionUtils.isNotEmpty(list)){
            for (PackageConfiguration configuration : list) {
                *//*
*/
/**
                 * 同一公司、同一门店、同一车型，同一天数，
                 * （1）用车日期不重叠。可以。
                 * （2）用车日期重叠，生效日期不重叠。可以。
                 * （3）用车日期重叠，生效日期也重叠。不可以，需要报错。
                 *//*
*/
/*
                if(!(configuration.getUseStartDate().isAfter(configDto.getUseEndDate()) || configuration.getUseEndDate().isBefore(configDto.getUseStartDate()))){
                    //用车日期重叠
                    if(!(configuration.getStartTime().isAfter(configDto.getUseEndDate()) || configuration.getEndTime().isBefore(configDto.getStartTime()))){
                        //生效时间重叠
                        throw new BusinessException(-1, "已有重叠时间段的配置");
                    }
                }
            }
        }
        return savePackageConfig(configDto);*//*

        return null;
    }

    @Override
    public PageResult<PackageConfigBo> getPackageConfigList(QueryPackageConfigInput queryPackageConfigInput) {
        PageResult<PackageConfiguration> packageConfigPage = packageConfigurationService.pagePackageConfigList(queryPackageConfigInput);

        List<PackageConfiguration> dataList = packageConfigPage.getDatas();

        List<PackageConfigBo> packageConfigList = dataList.stream().map(config -> {
            PackageConfigBo packageConfigBo = new PackageConfigBo();
            BeanUtils.copyProperties(config, packageConfigBo);
            return packageConfigBo;
        }).collect(Collectors.toList());

        PageResult<PackageConfigBo> result = new PageResult<>();
        result.setTotal(packageConfigPage.getTotal());
        result.setDatas(packageConfigList);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean offlinePackageConfig(Long id,String userName) throws BusinessException {
        PackageConfiguration configuration = packageConfigurationService.getById(id);
        if(configuration == null){
            throw new BusinessException(-1,"记录不存在，刷新后重试");
        }

        if(!PackageConfigStateEnum.IN_EFFECT.getState().equals(configuration.getConfigState())){
            throw new BusinessException(-1,"只有生效中的配置才可下线");
        }

        UserDTO userDTO = userService.selectUserByUserName(userName);
        packageConfigurationService.lambdaUpdate()
                .set(PackageConfiguration::getConfigState,PackageConfigStateEnum.ABOLISHED.getState())
                .set(PackageConfiguration::getUpdateOperId,userDTO.getId())
                .set(PackageConfiguration::getUpdateOperName,userDTO.getUsername())
                .set(PackageConfiguration::getUpdateTime,new Date())
                .eq(PackageConfiguration::getId,id)
                .eq(PackageConfiguration::getConfigState,PackageConfigStateEnum.IN_EFFECT.getState())
                .update();
        //操作日志
        savePackageConfigLog(id, VERSION, BusinessConst.LOG_OPT_CHANGE,"下线", userDTO);
        return true;
    }

    private Boolean savePackageConfig(AddPackageConfigDto configDto){
        PackageConfiguration configuration = new PackageConfiguration();
        BeanUtils.copyProperties(configDto,configDto);
        configuration.setConfigState(PackageConfigStateEnum.PENDING.getState());
        //用车日期范围的结束日期
        //configuration.setEndTime(configDto.getUseEndDate());
        packageConfigurationService.save(configuration, userService.selectUserByUserName(configDto.getUserName()), new Date());

        UserDTO userDTO = userService.selectUserByUserName(configDto.getUserName());
        PackageConfigAttrDto packageConfigAttrDto = new PackageConfigAttrDto();
        BeanUtils.copyProperties(configDto, packageConfigAttrDto);
        configurationAttrService.saveAttrs(ConfigAttrType.PACKAGE_CONFIG.getType(),
                configuration.getId(), packageConfigAttrDto, userDTO);
        //操作日志
        savePackageConfigLog(configuration.getId(),VERSION,BusinessConst.LOG_OPT_NORMAL,"新增套餐配置",userDTO);
        return true;
    }


    @Override
    public PageResult<OperateLogBo> queryOperateLog(QueryPackageConfigLogInput input) {
        */
/**
         * TODO 日志查询-变更为新的操作日志表
         * BusinessConst.LOG_TYPE_PACKAGE
         *//*

        return null;
//        PageResult<PackageConfigurationLog> packageConfigurationLogPageResult = packageConfigurationLogService.queryPackageConfigLog(input);
//        List<OperateLogBo> logList = packageConfigurationLogPageResult.getList().stream().map(configLog -> {
//            OperateLogBo operateLogBo = new OperateLogBo();
//            operateLogBo.setOperateTime(configLog.getCreateTime());
//            operateLogBo.setOperatorName(configLog.getCreateOperName());
//            operateLogBo.setOperateContent(configLog.getLogValue());
//            return operateLogBo;
//        }).collect(Collectors.toList());
//
//        PageResult<OperateLogBo> pageResult = new PageResult<>();
//        pageResult.setTotal(packageConfigurationLogPageResult.getTotal());
//        pageResult.setList(logList);
//        return pageResult;
    }

    */
/**
     * 保存操作日志
     * @param packageId
     * @param version
     * @param type
     * @param logValue
     * @param user
     *//*

    private void savePackageConfigLog(Long packageId, String version, Integer type, String logValue, UserDTO user) {
        */
/**
         * TODO 日志保存-变更为新的操作日志表
         * BusinessConst.LOG_TYPE_PACKAGE
         *//*

//        SavePackageConfigLogDto logDto = new SavePackageConfigLogDto();
//        logDto.setPackageId(packageId);
//        logDto.setVersion(version);
//        logDto.setType(type);
//        logDto.setLogValue(logValue);
//        packageConfigurationLogService.savePackageConfigLog(logDto, user);
    }
}
*/
