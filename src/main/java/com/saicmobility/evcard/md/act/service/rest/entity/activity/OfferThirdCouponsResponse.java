package com.saicmobility.evcard.md.act.service.rest.entity.activity;

import cn.hutool.core.util.ObjectUtil;
import com.extracme.evcard.rpc.dto.CommonAddRespDto;
import com.saicmobility.evcard.md.mdactservice.api.OfferThirdCouponsRes;

public class OfferThirdCouponsResponse extends CommonAddRespDto {

    public OfferThirdCouponsRes toRes() {
        OfferThirdCouponsRes.Builder builder = OfferThirdCouponsRes.newBuilder();
        if (ObjectUtil.isNotNull(this.getId())){
            builder.setUserCouponSeq(this.getId());
        }
        builder.setRetCode(this.getCode());
        builder.setRetMsg(this.getMessage());

        return builder.build();
    }
}
