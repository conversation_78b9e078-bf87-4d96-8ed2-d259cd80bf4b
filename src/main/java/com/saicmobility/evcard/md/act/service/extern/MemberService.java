package com.saicmobility.evcard.md.act.service.extern;

import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.saicmobility.evcard.md.mduserservice.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MemberService {

    @Autowired
    private MdUserService mdUserService;

    public MemberBasicInfo getMemberByMId(String mid) {
        GetMemberByMIdRes memberRes = mdUserService.getMemberByMId(GetMemberByMIdReq.newBuilder().setMid(mid).build());
        if(memberRes == null || memberRes.getMember() == null || StringUtils.isBlank(memberRes.getMember().getMid())) {
            return null;
        }
        return memberRes.getMember();
    }

    public String getMemberAuthId(String mid) {
        ConvertIdRes res = mdUserService.convertId(ConvertIdReq.newBuilder()
                .setType(1).setValue(mid).build());
        if (res.getRetCode() != 0 || StringUtils.isBlank(res.getAuthId())) {
            return StringUtils.EMPTY;
        }
        return res.getAuthId();
    }
}
