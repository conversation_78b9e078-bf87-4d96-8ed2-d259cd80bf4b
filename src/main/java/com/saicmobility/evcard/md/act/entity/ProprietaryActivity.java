package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 自营活动表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_proprietary_activity")
@ApiModel(value="ProprietaryActivity对象", description="自营活动表")
public class ProprietaryActivity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "活动标签")
    private String activityTag;

    //格式如：(000T)
    @ApiModelProperty(value = "参与机构集合,-1代表全部机构")
    private String orgCodes;

    @ApiModelProperty(value = "参与门店id集合，-1代表全部")
    private String storeIds;

    @ApiModelProperty(value = "车型id集合,-1代表全部车型")
    private String vehicleModelIds;

    @ApiModelProperty(value = "活动类型：1-满减、2-打折")
    private Integer activityType;

    @ApiModelProperty(value = "活动状态：1-未开始、2-生效中、3-已过期、4-已作废")
    private Integer activityStatus;

    @ApiModelProperty(value = "定价类型：1-灵活定价、2-规范定价")
    private Integer pricingType;

    @ApiModelProperty(value = "优惠纬度：1-车辆租金")
    private Integer discountLatitude;

    //格式如：[{"days":"3","discountAmount":"50"},{"days":"3","discountAmount":"50"}]
    @ApiModelProperty(value = "满减规范定价")
    private String fullMinusStandardPricing;

    //格式如：[{"days":"3","minDiscountAmount":"50","maxDiscountAmount":"100"},{"days":"3","minDiscountAmount":"50","maxDiscountAmount":"100"}]
    @ApiModelProperty(value = "满减灵活定价")
    private String fullMinusFlexiblePricing;

    //格式如：[{"days":"3","discount":"50"},{"days":"3","discount":"50"}]
    @ApiModelProperty(value = "打折规范定价")
    private String discountStandardPricing;

    //格式如：[{"days":"3","minDiscount":"50","maxDiscount":"100"},{"days":"3","minDiscount":"50","maxDiscount":"100"}]
    @ApiModelProperty(value = "打折灵活定价")
    private String discountFlexiblePricing;

    @ApiModelProperty(value = "最小租期")
    private Integer minRentDays;
    @ApiModelProperty(value = "最大租期")
    private Integer maxRentDays;

    @ApiModelProperty(value = "节假日是否可用：1-可用、2-不可用")
    private Integer availableOnHolidays;

    @ApiModelProperty(value = "报名开始时间")
    private LocalDate signUpStartDate;

    @ApiModelProperty(value = "报名结束时间")
    private LocalDate signUpEndDate;

    @ApiModelProperty(value = "取车时间")
    private LocalDate pickUpDate;

    @ApiModelProperty(value = "还车时间")
    private LocalDate returnDate;

    @ApiModelProperty(value = "活动开始时间")
    private LocalDate activityStartDate;

    @ApiModelProperty(value = "活动结束时间")
    private LocalDate activityEndDate;

    //格式如：[{"startDate":"20230101","endDate":"20230102"},{"startDate":"20230201","endDate":"20230202"}]
    @ApiModelProperty(value = "不可用时间范围")
    private String unavailableDateRanges;

    @ApiModelProperty(value = "报名机构集合")
    private String signUpOrgCodes;
    @ApiModelProperty(value = "仅限下单当日取车  0-非当日使用 1-当日使用")
    private Integer sameDayUseFlag;

    @ApiModelProperty(value = "是否指定下单日期 0-不限制 1-限制")
    private Integer specifyDateFlag;
    @ApiModelProperty(value = "指定日期，格式以数字，英文分号分割。空代表未选择： 1;3-周一，周三")
    private String specifyDate;
    @ApiModelProperty(value = "屏蔽节假日 0-不屏蔽 1-屏蔽")
    private Integer blockHolidayFlag;

    @ApiModelProperty(value = "取还车时间交集开关，1=取还车都必须在时间范围内 2=取还车任一时间在时间范围内")
    private Integer intersectionFlag;
}
