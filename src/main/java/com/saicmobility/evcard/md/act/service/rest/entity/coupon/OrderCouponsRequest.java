package com.saicmobility.evcard.md.act.service.rest.entity.coupon;

import cn.hutool.core.util.StrUtil;
import com.extracme.evcard.rpc.coupon.dto.OrderCouponDto;
import com.saicmobility.evcard.md.act.constant.BusinessConst;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.act.util.NumberUtils;
import com.saicmobility.evcard.md.mdactservice.api.OrderCouponCondition;
import com.saicmobility.evcard.md.mdactservice.api.OrderCouponReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrderCouponsRequest extends OrderCouponDto {

    public static OrderCouponsRequest fromRes(OrderCouponReq req) {
        OrderCouponsRequest request = new OrderCouponsRequest();
        OrderCouponCondition orderCondition = req.getOrderCouponCondition();
        request.setPickshopCity(orderCondition.getPickUpCity());
        request.setReturnshopCity(orderCondition.getReturnCity());
        request.setAmount(NumberUtils.getBigDecimal(orderCondition.getAmount()));
        request.setActivityType(orderCondition.getActivityType());
        request.setCostTime(orderCondition.getCostTime());
        request.setVehicleNo(orderCondition.getVehicleNo());
        request.setOrderSeq(orderCondition.getOrderNo());
        request.setRentMethod(orderCondition.getRentMethod());
        request.setPickupTime(DateUtil.getDateFromTimeStr(orderCondition.getPickupTime(), DateUtil.DATE_TYPE1));
        if(StringUtils.isNotBlank(orderCondition.getReturnTime())) {
            request.setReturnTime(DateUtil.getDateFromTimeStr(orderCondition.getReturnTime(), DateUtil.DATE_TYPE1));
        }
        request.setPickupStoreId(orderCondition.getPickUpStoreId());
        if (StrUtil.isBlank(orderCondition.getReturnStoreId())) {
            request.setReturnStoreId(orderCondition.getPickUpStoreId());
        } else {
            request.setReturnStoreId(orderCondition.getReturnStoreId());
        }
        request.setGoodsModelId(orderCondition.getGoodsModelId());
        request.setServiceType(orderCondition.getServiceType());
        if (orderCondition.getCostTime() != 0) {
            request.setCostTime(orderCondition.getCostTime());
        }
        request.setServiceTags(Optional.ofNullable(orderCondition.getServiceTags()).orElse(StringUtils.EMPTY));
        request.setCallService(BusinessConst.CALL_SERVICE_TYPE);
        request.setSort(req.getSort());
        //app5.7 修改订单
        request.setOldOrderSeq(orderCondition.getOldOrderNo());
        request.setOldCouponSeq(String.valueOf(orderCondition.getOldUserCouponSeq()));

        // 优惠券模板升级
        request.setUseSuiXiangCardId(orderCondition.getUseSuiXiangCardId());
        request.setUesSelfActivityId(orderCondition.getUseSelfActId());
        request.setStoreVehicleModelId(orderCondition.getStoreVehicleModelId());
        request.setUseMemberCardId(orderCondition.getUseMemberCardId());

        return request;
    }
}
