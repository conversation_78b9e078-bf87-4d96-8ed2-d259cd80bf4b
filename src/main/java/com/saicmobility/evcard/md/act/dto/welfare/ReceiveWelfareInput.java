package com.saicmobility.evcard.md.act.dto.welfare;

import lombok.Data;

import java.io.Serializable;

@Data
public class ReceiveWelfareInput implements Serializable {

    private GetWelfareInfoInput getWelfareInfoInput;

    private String mobile; // 用户未注册时，预绑定的手机号 ；mobile和mid 都有时，mid优先
    private String mid; //  用户已经注册过的，已注册的会员mid（前端只要token） ；mobile和mid 都有时，mid优先
    private int type; //  领取场景类型 1 已注册的用户领取  2 预绑定的领取

    // source=3 时的新增参数
    private String authcode; // 短信验证码，当 source=3 时为必传参数
    private String membershipPolicyVersion; // 会员条款版本号，当 source=3 时为可选参数
    private String privacyPolicyVersion; // 隐私政策版本号，当 source=3 时为可选参数
}
