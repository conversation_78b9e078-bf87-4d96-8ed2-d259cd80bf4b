package com.saicmobility.evcard.md.act.enums;

/**
 * 配置类型枚举
 * <AUTHOR>
 * @since 2022/4/13 16:58
 */
public enum ConfigTypeEnum {

    /**
     * 1：机构默认配置
     * 2：门店配置
     */
    ORG_CONFIG(1),
    STORE_CONFIG(2);

    private int configType;

    ConfigTypeEnum(int configType) {
        this.configType = configType;
    }

    public int getConfigType() {
        return configType;
    }

    public static boolean isStoreConfig(int configType) {
        if (ConfigTypeEnum.STORE_CONFIG.getConfigType() == configType) {
            return true;
        }
        return false;
    }
}
