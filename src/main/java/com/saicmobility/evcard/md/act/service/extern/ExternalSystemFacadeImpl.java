package com.saicmobility.evcard.md.act.service.extern;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.adapter.QingluAdapter;
import com.saicmobility.evcard.md.act.adapter.dto.*;
import com.saicmobility.evcard.md.act.constant.BusinessConst;
import com.saicmobility.evcard.md.act.constant.Constants;
import com.saicmobility.evcard.md.act.constant.RemoteMethodConstant;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.dto.coupon.channel.ccb.CCBCouponStatusDto;
import com.saicmobility.evcard.md.act.dto.coupon.channel.ccb.CCBFilePushDto;
import com.saicmobility.evcard.md.act.dto.coupon.channel.ccb.CCBHttpResDto;
import com.saicmobility.evcard.md.act.entity.ChannelActivity;
import com.saicmobility.evcard.md.act.entity.Task;
import com.saicmobility.evcard.md.act.enums.IsDeletedEnum;
import com.saicmobility.evcard.md.act.enums.channelcoupon.CCBCodeEnum;
import com.saicmobility.evcard.md.act.iservice.ITaskService;
import com.saicmobility.evcard.md.act.service.ExternalSystemFacade;
import com.saicmobility.evcard.md.act.service.MarketingModuleService;
import com.saicmobility.evcard.md.act.util.CCBHttpUtils;
import com.saicmobility.evcard.md.act.util.OssUtil;
import krpc.rpc.impl.TracablePool;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.saicmobility.evcard.md.act.enums.channelcoupon.CCBUrlEnum;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ExternalSystemFacadeImpl implements ExternalSystemFacade {
    @Autowired
    private QingluAdapter qingluAdapter;

    @Autowired
    private ITaskService taskService;

    @Autowired
    private MarketingModuleService marketingModuleService;

    @Autowired
    @Qualifier("channelActivitySyncPool")
    private TracablePool channelActivitySyncPool;

    @Override
    public void notifyChannelActivitySync(ChannelActivity activity, boolean isRetry) throws BusinessException {
        //是否需要异步调用 channelActivitySyncPool 待定
        log.info("tid:{},同步渠道活动到擎路开始：渠道活动id:{},渠道活动code:{}", Trace.currentTraceId(),activity.getId(),activity.getDiscountCode());
        MarketingCampaignInfoRequest marketingCampaignInfoRequest = null;
        try {
             marketingCampaignInfoRequest = marketingModuleService.dataSync(activity);
        } catch (Exception e) {
            log.error("tid:{},同步给擎路前，数据封装处理异常，发起重试，activity={}", Trace.currentTraceId(), JSON.toJSONString(activity));
            if (!processChannelActivitySync(null, isRetry, activity)) {
                log.info("tid:{} ,notifyChannelActivitySync 同步给擎路前，数据封装处理异常 擎路营销活动同步失败，发起重试！callList={}"
                        , Trace.currentTraceId(),JSON.toJSONString(marketingCampaignInfoRequest));
            }
            return;
        }
        if(marketingCampaignInfoRequest == null){
            log.info("tid:{},notifyChannelActivitySync 同步给擎路前，获取封装数据为空 marketingCampaignInfoRequest={}"
                    , Trace.currentTraceId(), JSON.toJSONString(marketingCampaignInfoRequest));
            return;
        }
        //所有门店
        List<MarketStoreRequest> marketStoreList = marketingCampaignInfoRequest.getMarketStoreList();
        //所有车型
        List<VehicleModelRequest> vehicleModelList = marketingCampaignInfoRequest.getVehicleModelList();
        //调用擎路活动绑定门店车型关系
        Map<String, List<VehicleModelRequest>> storeVehicleMap = vehicleModelList.stream().collect(Collectors.groupingBy(VehicleModelRequest::getMarketStoreId));
        //同步活动最新信息
        OpenMarketingCampaignRequest request = new OpenMarketingCampaignRequest();
        MarketingCampaignInfoRequest addMCIReq = new MarketingCampaignInfoRequest();
        BeanUtils.copyProperties(marketingCampaignInfoRequest,addMCIReq);
        addMCIReq.setMarketStoreList(null);
        addMCIReq.setVehicleModelList(null);
        List<MarketingCampaignInfoRequest> list = new ArrayList<>();
        list.add(addMCIReq);
        request.setMarketingCampaigns(list);
        String synActResult = qingluAdapter.callQinglu(RemoteMethodConstant.SYN_CHANEL_ACTIVITY, JSON.toJSONString(request));
        if (!processChannelActivitySync(synActResult, isRetry, activity)) {
            log.info("tid:{},notifyChannelActivitySync 擎路营销活动同步失败，发起重试！request={}", Trace.currentTraceId(), JSON.toJSONString(request));
            return;
        }
        if(Objects.equals(activity.getIsDeleted(), IsDeletedEnum.IS_DELETE.getType())){
            log.info("tid:{},notifyChannelActivitySync 同步渠道活动信息，删除成功,不在绑定活动门店车型关系!", Trace.currentTraceId());
            return;
        }

        log.info("tid:{},同步渠道活动信息成功,绑定活动门店车型关系开始:", Trace.currentTraceId());
        //绑定活动门店车型关系
        int size = marketStoreList.size();
        int index=0;
        while (index < size) {
            OpenMarketingBindingRequest  ombReq = new OpenMarketingBindingRequest();
            ombReq.setMarketId(marketingCampaignInfoRequest.getMarketId());
            //分批次处理 擎路每次处理最大门店为10
            List<MarketStoreRequest> syncList = marketStoreList.subList(index, Math.min(index + 10, size));
            List<MarketStoreRequest> needSyncList = new ArrayList<>(syncList);
            ombReq.setMarketStoreList(needSyncList);
            if(ombReq.getVehicleModelList() == null){
                ombReq.setVehicleModelList(new ArrayList<>());
            }
            for (MarketStoreRequest marketStoreRequest : needSyncList) {
                List<VehicleModelRequest> vehicleModelRequest = storeVehicleMap.get(marketStoreRequest.getStoreId());
                if (vehicleModelRequest != null) {
                    ombReq.getVehicleModelList().addAll(vehicleModelRequest);
                }
            }
            String jsonString = JSON.toJSONString(ombReq);
            String result = qingluAdapter.callQinglu(RemoteMethodConstant.SYN_CHANEL_ACTIVITY_STORE_VEHICLE_REF,jsonString);
            if (!processChannelActivitySync(result, isRetry, activity)) {
                log.info("tid:{},notifyChannelActivitySyncInPool 擎路营销活动同步成功,绑定活动车型门店关系失败，发起重试！jsonString:{}", Trace.currentTraceId(),jsonString);
                return;
            }
            index +=syncList.size();
        }
        log.info("tid:{},同步渠道活动信息成功,绑定活动门店车型关系结束:", Trace.currentTraceId());
    }

    @Override
    public void batchNotifyChannelActivitySync(List<ChannelActivity> list) {
        OpenMarketingCampaignRequest request = new OpenMarketingCampaignRequest();
        try {
            List<MarketingCampaignInfoRequest> requestList = new ArrayList<>();
            list.stream().forEach(channelActivity -> {
                try {
                    if (!BusinessConst.NOT_SYNC_SECOND_APP_KEY_LIST.contains(channelActivity.getSecondAppKey())) {
                        MarketingCampaignInfoRequest r = marketingModuleService.dataSync(channelActivity);
                        requestList.add(r);
                    }
                } catch (Exception e) {
                    log.error("批量调用同步擎路接口时，对象封装异常，channelActivity={}", JSON.toJSONString(channelActivity), e);
                }
            });
            request.setMarketingCampaigns(requestList);
            // 批量接口 不去重试
            qingluAdapter.callQinglu("qinglu.open.marketing.sync", JSON.toJSONString(request));
        } catch (Exception e) {
            log.error("批量调用同步擎路接口时，异常，request={}", JSON.toJSONString(request));
        }
    }

    /**
     * 推送优惠券状态变动
     * @param jsonStr
     */
    @Override
    public void ccbCouponStatusCallBackTask(String jsonStr) throws BusinessException {
        //组装推送报文
        CCBCouponStatusDto ccbCouponStatusDto = JSON.parseObject(jsonStr, CCBCouponStatusDto.class);
        //推送建行
        try {
            CCBHttpResDto ccbHttpResDto = CCBHttpUtils.sendCcbMix(CCBUrlEnum.CALL_BACK_COUPON_STATUS.getUrl(), CCBUrlEnum.CALL_BACK_COUPON_STATUS.getTransCode(), null, jsonStr, null,null,ccbCouponStatusDto.getProductId().concat("|||").concat(ccbCouponStatusDto.getCouponCode()));
            log.info("ccbHttpResDto code->{},Msg->{}",ccbHttpResDto.getCode(),ccbHttpResDto.getMsg());
            if (!CCBCodeEnum.SUCCESS.getCcbCode().equals(ccbHttpResDto.getCode())){
                throw new BusinessException("ccbCouponStatusCallBackTask ccbHttpResDto Error -> " + ccbHttpResDto.getMsg() + " .traceId - >" + ccbHttpResDto.getTraceId());
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new BusinessException("ccbCouponStatusCallBackTask ccbHttpResDto Error -> " + e.getMessage());
        }


    }

    /**
     * 推送对账文件
     * @param jsonStr
     */
    @Override
    public void ccbPushReconciliationFileTask(String jsonStr) throws BusinessException {
        //组装推送报文
        CCBFilePushDto ccbFilePushDto = JSON.parseObject(jsonStr, CCBFilePushDto.class);
        //推送建行
        BufferedInputStream input = null;
        try {
            BufferedInputStream bufferedInputStream = OssUtil.downloadStream(ccbFilePushDto.getFullFilePath());
            CCBHttpResDto ccbHttpResDto = CCBHttpUtils.sendCcbMix(CCBUrlEnum.RECONCILIATION_FILE.getUrl().concat(ccbFilePushDto.getFileName()), CCBUrlEnum.RECONCILIATION_FILE.getTransCode(), null, null, null, bufferedInputStream,ccbFilePushDto.getFileName());
            if (!CCBCodeEnum.SUCCESS.getCcbCode().equals(ccbHttpResDto.getCode())){
                throw new BusinessException("ccbPushReconciliationFileTask ccbHttpResDto Error -> " + ccbHttpResDto.getMsg()+ " .traceId - >" + ccbHttpResDto.getTraceId());
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new BusinessException("ccbPushReconciliationFileTask ccbHttpResDto Error -> " + e.getMessage());
        }finally {
            if (input != null){
                IOUtils.closeQuietly(input);
            }
        }

    }

    public boolean processChannelActivitySync(String result, boolean isRetry,
                                              ChannelActivity activity) throws BusinessException {
        UserDTO userDTO = new UserDTO();
        userDTO.setId(-1L);
        userDTO.setName("system");

        Task task = new Task();
        task.setTaskType(Constants.TASK_TYPE_CHANNEL_ACT_SYNC);
        task.setTaskStatus(Constants.TASK_STATUS_PENDING);
        task.setNextRunTime(new Date(new Date().getTime() + 10 * 1000L)); // 10秒后再尝试
        task.setTaskParam(activity.getId().toString());

        if (result == null) {
            if (isRetry) {
                throw new BusinessException("擎路营销活动同步，应答为空");
            } else {
                taskService.save(task, userDTO, new Date());
            }
            return false;
        } else {
            QingluResponse qingluResponse = JSON.parseObject(result, QingluResponse.class);
            if (!Constants.QINGLU_SUCCESS_CODE.equals(qingluResponse.getCode())) {
                if (isRetry) {
                    throw new BusinessException("擎路营销活动同步，应答失败！result=" + result);
                } else {
                    try {
                        taskService.save(task, userDTO, new Date());
                    } catch (Exception e) {
                        log.error("task表入库失败", e);
                    }
                }
                return false;
            }
        }

        return true;
    }

}
