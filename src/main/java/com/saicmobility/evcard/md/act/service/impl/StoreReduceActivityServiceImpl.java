package com.saicmobility.evcard.md.act.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.saicmobility.evcard.md.act.domain.GetStoreAvailableReduceActivityBo;
import com.saicmobility.evcard.md.act.domain.MmpUserTagDto;
import com.saicmobility.evcard.md.act.domain.StoreInfoCombobox;
import com.saicmobility.evcard.md.act.domain.StoreReduceActivityBo;
import com.saicmobility.evcard.md.act.entity.*;
import com.saicmobility.evcard.md.act.mapper.act.*;
import com.saicmobility.evcard.md.act.mapper.siac.MmpUserTagMapper;
import com.saicmobility.evcard.md.act.service.StoreReduceActivityService;
import com.saicmobility.evcard.md.act.service.extern.ConfigLoader;
import com.saicmobility.evcard.md.act.service.extern.GoodsModelService;
import com.saicmobility.evcard.md.act.service.extern.OrgService;
import com.saicmobility.evcard.md.act.service.extern.StoreService;
import com.saicmobility.evcard.md.mdactservice.api.*;
import com.saicmobility.evcard.md.mduserservice.api.GetMemberByMIdReq;
import com.saicmobility.evcard.md.mduserservice.api.GetMemberByMIdRes;
import com.saicmobility.evcard.md.mduserservice.api.MdUserService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class StoreReduceActivityServiceImpl implements StoreReduceActivityService {

    @Resource
    private StoreReduceActivityMapper storeReduceActivityMapper;

    @Resource
    private StoreReduceActivityGoodsModelMapper storeReduceActivityGoodsModelMapper;

    @Resource
    private ReduceActivityParticipateLogMapper reduceActivityParticipateLogMapper;

    @Resource
    private ReduceActivityMapper reduceActivityMapper;

    @Resource
    private MdUserService mdUserService;

    @Resource
    private OrgService orgService;

    @Resource
    private GoodsModelService goodsModelService;

    @Resource
    private StoreService storeService;

    @Resource
    private UserOrderReduceActivityRecordMapper userOrderReduceActivityRecordMapper;

    @Resource
    private ConfigLoader configLoader;

    @Resource
    private MmpUserTagMapper mmpUserTagMapper;

    @Value("${oss.path.prefix}")
    private String ossPathPrefix;

    //环球总公司orgCode
    private static final String EVCARD_ORG_CODE = "00";

    //仅产品线内首单可用
    private static final Integer FIRST_ORDER_USE = 1;

    @Override
    public GetStoreAvailableReduceActivityRes getStoreAvailableReduceActivity(GetStoreAvailableReduceActivityReq req) {
        GetStoreAvailableReduceActivityRes.Builder builder = GetStoreAvailableReduceActivityRes.newBuilder();
        long storeId = req.getStoreId();
        long goodsModelId = req.getGoodsModelId();
        String mid = req.getMid();
        if (0L == storeId || 0L == goodsModelId) {
            return builder.setRetCode(-25011).setRetMsg("门店信息为空").build();
        }
        Boolean firstOrderFlag = Boolean.FALSE;
        String registerTime = "";
        if (StrUtil.isNotBlank(mid)) {
            GetMemberByMIdReq midReq = GetMemberByMIdReq.newBuilder().setMid(mid).build();
            GetMemberByMIdRes memberByMidRes = mdUserService.getMemberByMId(midReq);
            if (memberByMidRes.getRetCode() != 0 || ObjectUtil.isNull(memberByMidRes.getMember())) {
                return builder.setRetCode(-1000).setRetMsg("未查询到用户信息").build();
            }
            registerTime = memberByMidRes.getMember().getRegTime();
            if (StrUtil.isBlank(registerTime)) {
                return builder.setRetCode(-1000).setRetMsg("用户注册时间为空").build();
            }
            try {
                Date orderDateStart = new SimpleDateFormat("yyyyMMddHHmmss").parse(registerTime);
                registerTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(orderDateStart);
            } catch (ParseException e) {
                return builder.setRetCode(-1000).setRetMsg("用户信息解析失败").build();
            }
            firstOrderFlag = getFirstOrderFlag(memberByMidRes.getMember().getAuthId());

        }

        //立减活动的用户注册时间可能为空(非必要条件),不能作为条件判断
        List<GetStoreAvailableReduceActivityBo> reduceActivityList = storeReduceActivityMapper.getStoreAvailableReduceActivity(storeId, goodsModelId);
        if (CollectionUtil.isEmpty(reduceActivityList)) {
            return builder.build();
        }

        //用户可参与次数问题
        GetStoreAvailableReduceActivityBo availableReduceActivity = null;
        if (StrUtil.isNotBlank(mid)) {
            for (GetStoreAvailableReduceActivityBo reduceActivityBo : reduceActivityList) {
                //校验下注册时间
                if (StrUtil.isAllNotBlank(reduceActivityBo.getRegisterStartTime(), reduceActivityBo.getRegisterEndTime())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime regStartTime = LocalDateTime.parse(reduceActivityBo.getRegisterStartTime(), formatter);
                    LocalDateTime regEndTime = LocalDateTime.parse(reduceActivityBo.getRegisterEndTime(), formatter);
                    LocalDateTime regTime = LocalDateTime.parse(registerTime, formatter);
                    if (regTime.isBefore(regStartTime) || regTime.isAfter(regEndTime)) {
                        continue;
                    }
                }

                //首单校验
                if (FIRST_ORDER_USE.equals(reduceActivityBo.getFirstOrderAvailable())) {
                    if (!firstOrderFlag) {
                        continue;
                    }
                }

                //活动用户参与次数限制
                Integer userParticipateNumber = reduceActivityBo.getUserParticipateNumber();
                LambdaQueryWrapper<UserOrderReduceActivityRecord> participateWrapper = new LambdaQueryWrapper<UserOrderReduceActivityRecord>()
                        .eq(UserOrderReduceActivityRecord::getMid, mid)
                        .eq(UserOrderReduceActivityRecord::getActivityId, reduceActivityBo.getId())
                        .eq(UserOrderReduceActivityRecord::getIsDeleted, 0);
                Integer participateCount = userOrderReduceActivityRecordMapper.selectCount(participateWrapper);
                //已参与次数小于可参与次数,此活动为可参与活动
                if (participateCount < userParticipateNumber) {
                    availableReduceActivity = reduceActivityBo;
                    break;
                }

            }
        } else {
            //用户未登录
            availableReduceActivity = reduceActivityList.get(0);
        }
        if (availableReduceActivity != null) {
            builder.setId(availableReduceActivity.getId() == null ? 0L : availableReduceActivity.getId())
                    .setActivityName(availableReduceActivity.getActivityName())
                    .setActivityStartTime(availableReduceActivity.getActivityStartTime())
                    .setActivityEndTime(availableReduceActivity.getActivityEndTime())
                    .setActivityDiscount(availableReduceActivity.getActivityDiscount().toString())
                    .setActivityRuleDescription(availableReduceActivity.getActivityRuleDescription())
                    .setActivityPicUrl(ossPathPrefix + availableReduceActivity.getActivityPicUrl());
        }

        return builder.build();
    }

    public Boolean getFirstOrderFlag(String authId) {
        if (StrUtil.isBlank(authId)) {
            return Boolean.FALSE;
        }
        //查询首单记录
        Integer countFirstOrder = mmpUserTagMapper.countFirstOrderByAuthId(authId);

        if (countFirstOrder > 0) {
            return Boolean.FALSE;
        }else {
            //没有首单记录,说明是首单
            return Boolean.TRUE;
        }
    }

    @Override
    public SearchStoreUnParticipateActivityRes searchStoreUnParticipateActivity(SearchStoreUnParticipateActivityReq req) {

        SearchStoreUnParticipateActivityRes.Builder builder = SearchStoreUnParticipateActivityRes.newBuilder();
        //查询条件里的机构编号
        String orgCode = req.getOrgCode();
        if (StrUtil.isBlank(orgCode)){
            //如果未传机构编号,根据登录人所属的机构限制权限
            orgCode = req.getCurrentUser().getOrgCode();
        }

        Page<StoreReduceActivityBo> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<StoreReduceActivityBo> result = storeReduceActivityMapper.SearchStoreUnParticipateActivity(orgCode,
                req.getActivityStatus(), req.getStoreId(), req.getActivityName(), req.getActivityId(), page);
        List<StoreReduceActivityBo> storeReduceActivityBos = result.getRecords();
        for (StoreReduceActivityBo storeReduceActivityBo : storeReduceActivityBos) {
            //查询的orgCode不为空且不是总公司(总公司查询全部)
            if (StrUtil.isNotBlank(req.getOrgCode()) && EVCARD_ORG_CODE.equals(req.getOrgCode()) && req.getStoreId() > 0) {
                //查询门店是否曾参与活动,后又全部退出
                Long activityId = storeReduceActivityBo.getId();
                LambdaQueryWrapper<StoreReduceActivity> cancelWrapper = new LambdaQueryWrapper<StoreReduceActivity>()
                        .eq(req.getStoreId() > 0, StoreReduceActivity::getStoreId, req.getStoreId())
                        .eq(StoreReduceActivity::getActivityId, activityId)
                        .eq(StoreReduceActivity::getIsDeleted, 1)
                        .last("limit 1");

                StoreReduceActivity storeReduceActivity = storeReduceActivityMapper.selectOne(cancelWrapper);
                if (ObjectUtil.isNotNull(storeReduceActivity)) {
                    storeReduceActivityBo.setStoreId(storeReduceActivity.getStoreId());
                    storeReduceActivityBo.setOperOrgCode(storeReduceActivity.getOrgCode());
                }
            }
        }

        List<ReduceActivityInfoForStore> list = storeReduceActivityBos.stream().map(this::convertReduceActivityInfoForStore).collect(Collectors.toList());
        return builder.addAllInfo(list).setTotal((int) result.getTotal()).build();
    }

    private ReduceActivityInfoForStore convertReduceActivityInfoForStore(StoreReduceActivityBo storeReduceActivityBo) {
        ReduceActivityInfoForStore.Builder builder = ReduceActivityInfoForStore.newBuilder();
        builder.setId(storeReduceActivityBo.getId())
                .setOrgCode(storeReduceActivityBo.getOrgCode())
                .setOrgName(orgService.getOrgNameByOrgCode(storeReduceActivityBo.getOrgCode()))
                .setActivityName(storeReduceActivityBo.getActivityName())
                .setActivityStatus(storeReduceActivityBo.getActivityStatus())
                .setSignUpDeadline(storeReduceActivityBo.getSignUpDeadline())
                .setActivityStartTime(storeReduceActivityBo.getActivityStartTime())
                .setActivityEndTime(storeReduceActivityBo.getActivityEndTime())
                .setUpdateOperId(storeReduceActivityBo.getUpdateOperId().toString())
                .setUpdateOperName(storeReduceActivityBo.getUpdateOperName());

        if (StrUtil.isNotBlank(storeReduceActivityBo.getOperOrgCode())) {
            builder.setOperOrgCode(storeReduceActivityBo.getOperOrgCode());
        }
        if (ObjectUtil.isNotNull(storeReduceActivityBo.getGoodsModelId())) {
            builder.setGoodsModelId(storeReduceActivityBo.getGoodsModelId());
            builder.setGoodsModelName(goodsModelService.getGoodsModelNameById(storeReduceActivityBo.getGoodsModelId()));
        }
        if (ObjectUtil.isNotNull(storeReduceActivityBo.getStoreId())) {
            builder.setStoreId(storeReduceActivityBo.getStoreId())
                    .setStoreName(storeService.getStoreNameByStoreId(storeReduceActivityBo.getStoreId()));
        }
        return builder.build();
    }

    @Override
    public SearchStoreParticipateActivityRes searchStoreParticipateActivity(SearchStoreParticipateActivityReq req) {
        SearchStoreParticipateActivityRes.Builder builder = SearchStoreParticipateActivityRes.newBuilder();
        Page<StoreReduceActivityBo> page = new Page<>(req.getPageNum(), req.getPageSize());
        //查询条件里的机构编号
        String orgCode = req.getOrgCode();
        if (StrUtil.isBlank(orgCode)){
            //如果未传机构编号,根据登录人所属的机构限制权限
            orgCode = req.getCurrentUser().getOrgCode();
        }

        Page<StoreReduceActivityBo> result = storeReduceActivityMapper.SearchStoreParticipateActivity(orgCode,
                req.getActivityStatus(), req.getStoreId(), req.getActivityName(), req.getActivityId(), req.getGoodsModelId(), page);
        List<StoreReduceActivityBo> storeReduceActivityBos = result.getRecords();
        List<ReduceActivityInfoForStore> list = storeReduceActivityBos.stream().map(this::convertReduceActivityInfoForStore).collect(Collectors.toList());
        return builder.addAllInfo(list).setTotal((int) result.getTotal()).build();
    }

    @Override
    @Transactional
    public ParticipateStoreReduceActivityRes participateStoreReduceActivity(ParticipateStoreReduceActivityReq req) {
        if(req.getStoreId() <= 0 && CollectionUtil.isEmpty(req.getStoreIdsList())) {
            return ParticipateStoreReduceActivityRes.failed(-10001, "参与活动的门店基本信息不能为空");
        }
        if (req.getActivityId() <= 0 || CollectionUtil.isEmpty(req.getGoodsModelIdList())) {
            return ParticipateStoreReduceActivityRes.failed(-10001, "参与活动的门店基本信息不能为空");
        }
        CurrentUser currentUser = req.getCurrentUser();
        ParticipateStoreReduceActivityRes.Builder builder = ParticipateStoreReduceActivityRes.newBuilder();
        long activityId = req.getActivityId();

        if (activityId <= 0) {
            ParticipateStoreReduceActivityRes.failed(-1000, "立减活动id不能为空");
        }

        if (CollectionUtil.isEmpty(req.getGoodsModelIdList())) {
            ParticipateStoreReduceActivityRes.failed(-1000, "参与活动的车型不能为空");
        }

        //校验与已有活动的生效起止日期是否有重叠
        ReduceActivity reduceActivity = reduceActivityMapper.selectById(req.getActivityId());
        //校验是否超过了报名截止时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime signUpDeadline = LocalDateTime.parse(reduceActivity.getSignUpDeadline(), formatter);
        if (LocalDateTime.now().isAfter(signUpDeadline)){
            return ParticipateStoreReduceActivityRes.failed(-1009, "已超过活动报名截止时间");
        }
        if (ObjectUtil.isNull(reduceActivity)) {
            return ParticipateStoreReduceActivityRes.failed(-1009, "未查询到相关活动");
        }

        Integer checkActivityEffectTime = storeReduceActivityMapper.checkActivityEffectTime(req.getStoreId(), reduceActivity.getActivityStartTime(), reduceActivity.getActivityEndTime());
        if (checkActivityEffectTime > 0) {
            return ParticipateStoreReduceActivityRes.failed(-1010, "当前活动与已参与活动日期有冲突");
        }
        //查询门店所属的operOrgCode
        if (req.getStoreId() > 0) {
            StoreInfoCombobox storeInfo = configLoader.getStore(req.getStoreId());
            String operOrgCode = storeInfo.getOperOrgCode();
            ParticipateStoreReduceActivityRes builder1 = getParticipateStoreReduceActivityRes(req, currentUser, builder, req.getStoreId(), activityId, operOrgCode);
            if (builder1 != null) return builder1;
        } else if(CollectionUtil.isNotEmpty(req.getStoreIdsList())) {
            List<Long> storeIdsList = req.getStoreIdsList();
            for (Long storeIds : storeIdsList) {
                StoreInfoCombobox storeInfo = configLoader.getStore(storeIds);
                String operOrgCode = storeInfo.getOperOrgCode();
                ParticipateStoreReduceActivityRes builder1 = getParticipateStoreReduceActivityRes(req, currentUser, builder, storeIds, activityId, operOrgCode);
                if (builder1 != null) {
                    return builder1;
                }
            }
        }


        /*StringBuilder goodsName = new StringBuilder();
        for (int i = 0; i < goodsModelIdList.size(); i++) {
            String goodsModelName = goodsModelService.getGoodsModelNameById(goodsModelIdList.get(i));
            if (i != 0) {
                goodsName.append("、");
            }
            if (StrUtil.isNotBlank(goodsModelName)) {
                goodsName.append(goodsModelName + "商品车型");
            }
        }

        ReduceActivityParticipateLog participateLog = new ReduceActivityParticipateLog();
        participateLog.setStoreId(req.getStoreId());
        participateLog.setActivityId(req.getActivityId());
        participateLog.setGoodsModelId();
        participateLog.setOperateContent("门店" + goodsName + "已参与活动");
        participateLog.setCreateOperId(currentUser.getUserId());
        participateLog.setCreateOperName(currentUser.getUserName());
        participateLog.setCreateOperOrgName(currentUser.getOrgName());
        reduceActivityParticipateLogMapper.insert(participateLog);*/

        return builder.build();
    }

    private ParticipateStoreReduceActivityRes getParticipateStoreReduceActivityRes(ParticipateStoreReduceActivityReq req, CurrentUser currentUser, ParticipateStoreReduceActivityRes.Builder builder, long storeId, long activityId, String operOrgCode) {
        LambdaQueryWrapper<StoreReduceActivity> queryWrapper = new LambdaQueryWrapper<StoreReduceActivity>()
                .eq(StoreReduceActivity::getStoreId, storeId)
                .eq(StoreReduceActivity::getActivityId, activityId)
                .eq(StoreReduceActivity::getOrgCode, operOrgCode)
                .eq(StoreReduceActivity::getIsDeleted, 0)
                .last("limit 1");

        StoreReduceActivity storeReduceActivity;

        storeReduceActivity = storeReduceActivityMapper.selectOne(queryWrapper);
        //首次参与活动,添加记录
        if (ObjectUtil.isNull(storeReduceActivity)) {
            storeReduceActivity = new StoreReduceActivity();
            storeReduceActivity.setStoreId(storeId);
            storeReduceActivity.setActivityId(activityId);
            storeReduceActivity.setOrgCode(operOrgCode);
            int insert = storeReduceActivityMapper.insert(storeReduceActivity);
            if (insert < 1) {
                return builder.setRetCode(-25001).setRetMsg("门店参与立减活动失败").build();
            }
        }

        List<Long> goodsModelIdList = req.getGoodsModelIdList();
        for (Long goodsModelId : goodsModelIdList) {
            LambdaQueryWrapper<StoreReduceActivityGoodsModel> wrapper = new LambdaQueryWrapper<StoreReduceActivityGoodsModel>()
                    .eq(StoreReduceActivityGoodsModel::getStoreReduceActivityId, storeReduceActivity.getId())
                    .eq(StoreReduceActivityGoodsModel::getGoodsModelId, goodsModelId)
                    .eq(StoreReduceActivityGoodsModel::getIsDeleted, 0)
                    .last("limit 1");
            Integer record = storeReduceActivityGoodsModelMapper.selectCount(wrapper);
            //此商品车型没有参与过活动,插入记录(ps:如果参与过,则忽略)
            if (record < 1) {
                StoreReduceActivityGoodsModel goodsModel = new StoreReduceActivityGoodsModel();
                goodsModel.setStoreReduceActivityId(storeReduceActivity.getId());
                goodsModel.setGoodsModelId(goodsModelId);
                storeReduceActivityGoodsModelMapper.insert(goodsModel);

                //日志
                String goodsModelName = goodsModelService.getGoodsModelNameById(goodsModelId);
                ReduceActivityParticipateLog participateLog = new ReduceActivityParticipateLog();
                participateLog.setStoreId(req.getStoreId());
                participateLog.setActivityId(req.getActivityId());
                participateLog.setGoodsModelId(goodsModelId);
                participateLog.setOperateContent("门店" + goodsModelName + "商品车型已参与活动");
                participateLog.setCreateOperId(currentUser.getUserId());
                participateLog.setCreateOperName(currentUser.getUserName());
                participateLog.setCreateOperOrgName(currentUser.getOrgName());
                reduceActivityParticipateLogMapper.insert(participateLog);
            }
        }
        return null;
    }

    @Override
    public CancelStoreReduceActivityRes cancelStoreReduceActivity(CancelStoreReduceActivityReq req) {

        if (req.getStoreId() <= 0 || req.getActivityId() <= 0 || req.getGoodsModelId() <= 0) {
            return CancelStoreReduceActivityRes.failed(-10001, "退出活动的门店基本信息不能为空");
        }

        //门店参与活动的记录,这条记录对应门店参与的活动的多个车型记录
        CurrentUser currentUser = req.getCurrentUser();
        CancelStoreReduceActivityRes.Builder builder = CancelStoreReduceActivityRes.newBuilder();
        LambdaQueryWrapper<StoreReduceActivity> queryWrapper = new LambdaQueryWrapper<StoreReduceActivity>()
                .eq(StoreReduceActivity::getStoreId, req.getStoreId())
                .eq(StoreReduceActivity::getActivityId, req.getActivityId())
                .eq(StoreReduceActivity::getIsDeleted, 0).last("limit 1");
        StoreReduceActivity storeReduceActivity = storeReduceActivityMapper.selectOne(queryWrapper);

        if (ObjectUtil.isNull(storeReduceActivity)) {
            return CancelStoreReduceActivityRes.failed(-10001, "门店退出活动失败,未查询到参与活动记录");
        }

        LambdaQueryWrapper<StoreReduceActivityGoodsModel> goodsWrapper = new LambdaQueryWrapper<StoreReduceActivityGoodsModel>()
                .eq(StoreReduceActivityGoodsModel::getGoodsModelId, req.getGoodsModelId())
                .eq(StoreReduceActivityGoodsModel::getStoreReduceActivityId, storeReduceActivity.getId())
                .eq(StoreReduceActivityGoodsModel::getIsDeleted, 0).last("limit 1");

        StoreReduceActivityGoodsModel storeReduceActivityGoodsModel = storeReduceActivityGoodsModelMapper.selectOne(goodsWrapper);
        if (ObjectUtil.isNull(storeReduceActivityGoodsModel)) {
            return CancelStoreReduceActivityRes.failed(-10001, "门店退出活动失败,未查询到该商品车型参与活动记录");
        }
        storeReduceActivityGoodsModel.setIsDeleted(1);
        storeReduceActivityGoodsModelMapper.updateById(storeReduceActivityGoodsModel);
        //storeReduceActivityGoodsModelMapper.deleteById(storeReduceActivityGoodsModel.getId());

        //如果所有车型都退出活动,删除门店参与活动表的记录(逻辑删除)
        LambdaQueryWrapper<StoreReduceActivityGoodsModel> goodsCountWrapper = new LambdaQueryWrapper<StoreReduceActivityGoodsModel>()
                .eq(StoreReduceActivityGoodsModel::getStoreReduceActivityId, storeReduceActivity.getId())
                .eq(StoreReduceActivityGoodsModel::getIsDeleted, 0).last("limit 1");
        Integer goodsCount = storeReduceActivityGoodsModelMapper.selectCount(goodsCountWrapper);

        if (goodsCount < 1) {
            storeReduceActivity.setIsDeleted(1);
            storeReduceActivityMapper.updateById(storeReduceActivity);
            //storeReduceActivityMapper.deleteById(storeReduceActivity.getId());
        }

        //查询车型名称
        String goodsModelName = goodsModelService.getGoodsModelNameById(req.getGoodsModelId());

        if (StrUtil.isNotBlank(goodsModelName)) {
            goodsModelName = (goodsModelName + "商品车型");
        }
        ReduceActivityParticipateLog participateLog = new ReduceActivityParticipateLog();
        participateLog.setStoreId(req.getStoreId());
        participateLog.setActivityId(req.getActivityId());
        participateLog.setOperateContent("门店" + goodsModelName + "已退出活动");
        participateLog.setGoodsModelId(req.getGoodsModelId());
        participateLog.setCreateOperId(currentUser.getUserId());
        participateLog.setCreateOperName(currentUser.getUserName());
        participateLog.setCreateOperOrgName(currentUser.getOrgName());
        reduceActivityParticipateLogMapper.insert(participateLog);

        return builder.build();
    }

}
