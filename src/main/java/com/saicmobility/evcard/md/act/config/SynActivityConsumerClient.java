package com.saicmobility.evcard.md.act.config;

import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.OrderConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import com.aliyun.openservices.ons.api.order.MessageOrderListener;
import com.saicmobility.evcard.md.act.mq.SynActivityListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Configuration
public class SynActivityConsumerClient {

    @Autowired
    private SynActivityListener activityService;

    @Autowired
    private OnsConfiguration mqConfig;


    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public OrderConsumerBean synActivityConsumer() {
        OrderConsumerBean consumerBean = new OrderConsumerBean();
        //配置文件
        Properties properties = mqConfig.getMqProperties();
        properties.setProperty(PropertyKeyConst.GROUP_ID, mqConfig.getSynActGroupId());
        //将消费者线程数固定为1个
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "1");
        consumerBean.setProperties(properties);
        //订阅关系
        Map<Subscription, MessageOrderListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic(mqConfig.getSyncTopic());
        subscription.setExpression("*");
        subscriptionTable.put(subscription, activityService);

        consumerBean.setSubscriptionTable(subscriptionTable);
        return consumerBean;
    }

}
