package com.saicmobility.evcard.md.act.service;

import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.entity.ConfigurationAttr;
import com.saicmobility.evcard.md.act.service.inner.IBaseService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 扩展属性配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
public interface ConfigurationAttrService extends IBaseService<ConfigurationAttr> {
    void getAttrs(Integer type, Long configurationId, Object configurationObj);

    <T> T getAttrs(Integer type, Long configurationId, Class<T> configurationClass);

    <T> Map<Long, T> batchGetAttrs(Integer type, List<Long> configIds, Class<T> configurationClass);

    boolean saveAttrs(Integer type, Long configurationId, Object configurationObj, UserDTO userDTO);

    boolean deleteAttrs(Integer type, Long configurationId, UserDTO userDTO);

    boolean updateAttrs(Integer type, Long configurationId, Object configurationObj, UserDTO userDTO);

}
