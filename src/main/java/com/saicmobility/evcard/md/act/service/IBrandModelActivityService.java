package com.saicmobility.evcard.md.act.service;

import com.saicmobility.evcard.md.act.entity.BrandModelActivity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.saicmobility.evcard.md.mdactservice.api.*;

/**
 * <p>
 * 品牌车型配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
public interface IBrandModelActivityService extends IService<BrandModelActivity> {

    SaveOrUpdateBrandModelActivityRes insertOrUpdate(SaveOrUpdateBrandModelActivityReq req);

    UpdateBrandModelActivityStatusRes updateBrandModelActivityStatus(UpdateBrandModelActivityStatusReq req);

    QueryBrandModelActivityListRes queryBrandModelActivityList(QueryBrandModelActivityListReq req);

    QueryBrandModelActivityDetailRes queryBrandModelActivityDetail(QueryBrandModelActivityDetailReq queryBrandModelActivityDetailReq);
}
