package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * Task表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_task")
@ApiModel(value="Task对象", description="任务表")
public class Task extends BaseEntity {

    @ApiModelProperty(value = "任务类型")
    private Integer taskType;

    @ApiModelProperty(value = "任务参数，不同的task_type对应不同的值")
    private String taskParam;

    @ApiModelProperty(value = "任务状态 1=未完成 2=已完成 3=已失败")
    private Integer taskStatus;

    @ApiModelProperty(value = "下次运行时间")
    private Date nextRunTime;

    @ApiModelProperty(value = "最后一次运行时间")
    private Date lastRunTime;

    @ApiModelProperty(value = "最后一次运行信息，成功或失败错误码等信息")
    private String lastRunMsg;

    @ApiModelProperty(value = "失败次数")
    private Integer failedTimes;
}
