package com.saicmobility.evcard.md.act.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saicmobility.evcard.md.act.domain.ReduceActivityBo;
import com.saicmobility.evcard.md.act.domain.ReduceActivityNameBo;
import com.saicmobility.evcard.md.act.entity.OperateLog;
import com.saicmobility.evcard.md.act.entity.ReduceActivity;
import com.saicmobility.evcard.md.act.entity.StoreReduceActivity;
import com.saicmobility.evcard.md.act.mapper.act.OperateLogMapper;
import com.saicmobility.evcard.md.act.mapper.act.ReduceActivityMapper;
import com.saicmobility.evcard.md.act.mapper.act.StoreReduceActivityMapper;
import com.saicmobility.evcard.md.act.service.ReduceActivityService;
import com.saicmobility.evcard.md.act.service.extern.GoodsModelService;
import com.saicmobility.evcard.md.act.service.extern.OrgService;
import com.saicmobility.evcard.md.act.service.extern.StoreService;
import com.saicmobility.evcard.md.act.util.ComUtils;
import com.saicmobility.evcard.md.mdactservice.api.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class ReduceActivityServiceImpl extends ServiceImpl<ReduceActivityMapper, ReduceActivity> implements ReduceActivityService {

    @Resource
    private ReduceActivityMapper reduceActivityMapper;

    @Resource
    private StoreReduceActivityMapper storeReduceActivityMapper;

    @Resource
    private OperateLogMapper operateLogMapper;

    @Resource
    private OrgService orgService;

    @Resource
    private StoreService storeService;

    @Resource
    private GoodsModelService goodsModelService;

    //下线状态 1 未下线
    private static final Integer ONLINE_STATUS = 1;

    //下线状态 2 已下线
    private static final Integer OFFLINE_STATUS = 2;

    @Value("${oss.path.prefix}")
    private String ossPathPrefix;


    @Override
    public SearchReduceActivityRes searchReduceActivity(SearchReduceActivityReq req) {

        SearchReduceActivityRes.Builder builder = SearchReduceActivityRes.newBuilder();
        Page<ReduceActivity> page = new Page<>(req.getPageNum(), req.getPageSize(), true);
        Page<ReduceActivityBo> result = reduceActivityMapper.queryReduceActivity(req.getOrgCode(), req.getActivityStatus(), req.getActivityName(), req.getActivityId(), page);
        List<ReduceActivityBo> activityList = result.getRecords();
        if (CollectionUtil.isNotEmpty(activityList)) {
            List<ReduceActivityInfo> list = activityList.stream().map(this::convertReduceActivityInfo).collect(Collectors.toList());
            builder.addAllInfo(list);
        }
        return builder.setTotal((int) result.getTotal()).build();
    }

    private ReduceActivityInfo convertReduceActivityInfo(ReduceActivityBo reduceActivity) {

        //处理门店名称和机构名称
        return ReduceActivityInfo.newBuilder()
                .setId(reduceActivity.getId())
                .setOrgCode(reduceActivity.getOrgCode())
                .setOrgName(orgService.getOrgNameByOrgCode(reduceActivity.getOrgCode()))
                .setActivityName(reduceActivity.getActivityName())
                .setSignUpDeadline(reduceActivity.getSignUpDeadline())
                .setActivityStartTime(reduceActivity.getActivityStartTime())
                .setActivityEndTime(reduceActivity.getActivityEndTime())
                .setActivityStatus(reduceActivity.getActivityStatus())
                .setUpdateOperId(reduceActivity.getUpdateOperId().toString())
                .setUpdateOperName(reduceActivity.getUpdateOperName())
                .build();
    }

    @Override
    public GetReduceActivityWithStoreRes getReduceActivityWithStore(GetReduceActivityWithStoreReq req) {
        GetReduceActivityWithStoreRes.Builder builder = GetReduceActivityWithStoreRes.newBuilder();
        long activityId = req.getActivityId();
        ReduceActivityBo activityBo = reduceActivityMapper.getByActivityId(activityId);
        if (null == activityBo) {
            return builder.setRetCode(-25001).setRetMsg("未查询到该门店信息").build();
        }
        //查询出所有参与该活动的门店
        LambdaQueryWrapper<StoreReduceActivity> queryWrapper = new LambdaQueryWrapper<StoreReduceActivity>()
                .eq(StoreReduceActivity::getActivityId, activityId)
                .eq(StoreReduceActivity::getIsDeleted, 0);
        List<StoreReduceActivity> storeReduceActivities = storeReduceActivityMapper.selectList(queryWrapper);

        //获取参与的门店名称
        ArrayList<String> storeCityAndNameList = new ArrayList<>();
        for (StoreReduceActivity storeReduceActivity : storeReduceActivities) {
            String storeCityAndName = storeService.getStoreCityAndNameByStoreId(storeReduceActivity.getStoreId());
            if (StrUtil.isNotBlank(storeCityAndName)) {
                storeCityAndNameList.add(storeCityAndName);
            }
        }

        return builder
                .setId(activityBo.getId())
                .setOrgCode(activityBo.getOrgCode())
                .setOrgName(orgService.getOrgNameByOrgCode(activityBo.getOrgCode()))
                .setActivityName(activityBo.getActivityName())
                .setActivityStatus(activityBo.getActivityStatus())
                .setSignUpDeadline(activityBo.getSignUpDeadline())
                .setActivityStartTime(activityBo.getActivityStartTime())
                .setActivityEndTime(activityBo.getActivityEndTime())
                .setRegisterStartTime(activityBo.getRegisterStartTime())
                .setRegisterEndTime(activityBo.getRegisterEndTime())
                .setFirstOrderAvailable(activityBo.getFirstOrderAvailable())
                .setUserParticipateNumber(activityBo.getUserParticipateNumber())
                .setActivityDiscount(activityBo.getActivityDiscount().toString())
                .setActivityRuleDescription(activityBo.getActivityRuleDescription())
                .setActivityPicUrl(ossPathPrefix + activityBo.getActivityPicUrl())
                .addAllStoreNameInfo(storeCityAndNameList)
                .build();
    }

    @Override
    public AddReduceActivityRes addReduceActivity(AddReduceActivityReq req) {
        AddReduceActivityRes.Builder builder = AddReduceActivityRes.newBuilder();

        if (StrUtil.isBlank(req.getActivityName())) {
            return AddReduceActivityRes.failed(-1001, "请填写活动名称");
        }

        if (StrUtil.isBlank(req.getOrgCode())) {
            return AddReduceActivityRes.failed(-1002, "请填写运营公司");
        }

        if (StrUtil.isBlank(req.getActivityStartTime()) || StrUtil.isBlank(req.getActivityEndTime())) {
            return AddReduceActivityRes.failed(-1003, "请选择生效起止日期");
        }

        if (req.getFirstOrderAvailable() < 1) {
            return AddReduceActivityRes.failed(-1005, "请填写是否仅产品线内首单可用");
        }

        if (req.getUserParticipateNumber() < 0) {
            return AddReduceActivityRes.failed(-1006, "请填写用户可参与次数");
        }

        if (StrUtil.isBlank(req.getActivityDiscount())) {
            return AddReduceActivityRes.failed(-1007, "请填写活动立减金额");
        }

        if (StrUtil.isBlank(req.getActivityRuleDescription())) {
            return AddReduceActivityRes.failed(-1008, "请填写活动规则说明");
        } else if (req.getActivityRuleDescription().length() > 200) {
            return AddReduceActivityRes.failed(-1011, "活动规则说明最大长度为200");

        }

        if (StrUtil.isBlank(req.getActivityPicUrl())) {
            return AddReduceActivityRes.failed(-1009, "请上传活动图片");
        }

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime signUpDeadline = LocalDateTime.parse(req.getSignUpDeadline(), df);
        LocalDateTime activityStartTime = LocalDateTime.parse(req.getActivityStartTime(), df);
        if (signUpDeadline.isAfter(activityStartTime)) {
            return AddReduceActivityRes.failed(-1010, "报名截止时间必须早于生效开始时间");
        }

        CurrentUser currentUser = req.getCurrentUser();
        ReduceActivity reduceActivity = new ReduceActivity();
        reduceActivity.setActivityName(ComUtils.splitStr(req.getActivityName(), 20));
        reduceActivity.setOrgCode(req.getOrgCode());
        reduceActivity.setSignUpDeadline(req.getSignUpDeadline());
        reduceActivity.setActivityStartTime(req.getActivityStartTime());
        reduceActivity.setActivityEndTime(req.getActivityEndTime());
        reduceActivity.setFirstOrderAvailable(req.getFirstOrderAvailable());
        reduceActivity.setUserParticipateNumber(req.getUserParticipateNumber());
        reduceActivity.setActivityDiscount(new BigDecimal(req.getActivityDiscount()));
        reduceActivity.setActivityRuleDescription(ComUtils.splitStr(req.getActivityRuleDescription(), 512));
        reduceActivity.setActivityPicUrl(req.getActivityPicUrl());
        reduceActivity.setCreateOperId(currentUser.getUserId());
        reduceActivity.setCreateOperName(ComUtils.splitStr(currentUser.getUserName(), 128));
        reduceActivity.setUpdateOperId(currentUser.getUserId());
        reduceActivity.setUpdateOperName(ComUtils.splitStr(currentUser.getUserName(), 128));
        if (StrUtil.isAllNotBlank(req.getRegisterStartTime(), req.getRegisterEndTime())) {
            reduceActivity.setRegisterStartTime(req.getRegisterStartTime());
            reduceActivity.setRegisterEndTime(req.getRegisterEndTime());
        }
        int insert = reduceActivityMapper.insert(reduceActivity);
        if (insert < 1) {
            return builder.setRetCode(-25001).setRetMsg("新增立减活动失败").build();
        }
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(String.valueOf(reduceActivity.getId()));
        operateLog.setOperateType(2);
        operateLog.setOperateContent("新增立减活动");
        operateLog.setCreateOperId(currentUser.getUserId());
        operateLog.setCreateOperName(ComUtils.splitStr(currentUser.getUserName(), 128));
        operateLog.setCreateOperOrgName(ComUtils.splitStr(currentUser.getOrgName(), 128));
        operateLogMapper.insert(operateLog);

        return builder.setId(reduceActivity.getId()).build();
    }

    @Override
    public UpdateReduceActivityRes updateReduceActivity(UpdateReduceActivityReq req) {
        if (StrUtil.isBlank(req.getActivityName())) {
            return UpdateReduceActivityRes.failed(-1001, "请填写活动名称");
        }

        if (StrUtil.isBlank(req.getOrgCode())) {
            return UpdateReduceActivityRes.failed(-1002, "请填写运营公司");
        }

        if (StrUtil.isBlank(req.getActivityStartTime()) || StrUtil.isBlank(req.getActivityEndTime())) {
            return UpdateReduceActivityRes.failed(-1003, "请选择生效起止日期");
        }

        if (req.getFirstOrderAvailable() < 1) {
            return UpdateReduceActivityRes.failed(-1005, "请填写是否仅产品线内首单可用");
        }

        if (req.getUserParticipateNumber() < 0) {
            return UpdateReduceActivityRes.failed(-1006, "请填写用户可参与次数");
        }

        if (StrUtil.isBlank(req.getActivityDiscount())) {
            return UpdateReduceActivityRes.failed(-1007, "请填写活动立减金额");
        }

        if (StrUtil.isBlank(req.getActivityRuleDescription())) {
            return UpdateReduceActivityRes.failed(-1008, "请填写活动规则说明");
        } else if (req.getActivityRuleDescription().length() > 200) {
            return UpdateReduceActivityRes.failed(-1011, "活动规则说明最大长度为200");
        }

        if (StrUtil.isBlank(req.getActivityPicUrl())) {
            return UpdateReduceActivityRes.failed(-1009, "请上传活动图片");
        }

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime signUpDeadline = LocalDateTime.parse(req.getSignUpDeadline(), df);
        LocalDateTime activityStartTime = LocalDateTime.parse(req.getActivityStartTime(), df);
        if (signUpDeadline.isAfter(activityStartTime)) {
            return UpdateReduceActivityRes.failed(-1010, "报名截止时间必须早于生效开始时间");
        }

        UpdateReduceActivityRes.Builder builder = UpdateReduceActivityRes.newBuilder();
        CurrentUser currentUser = req.getCurrentUser();
        ReduceActivity reduceActivity = new ReduceActivity();
        reduceActivity.setOfflineFlag(ONLINE_STATUS);
        reduceActivity.setId(req.getActivityId());
        reduceActivity.setActivityName(ComUtils.splitStr(req.getActivityName(), 20));
        reduceActivity.setOrgCode(req.getOrgCode());
        reduceActivity.setSignUpDeadline(req.getSignUpDeadline());
        reduceActivity.setActivityStartTime(req.getActivityStartTime());
        reduceActivity.setActivityEndTime(req.getActivityEndTime());
        reduceActivity.setFirstOrderAvailable(req.getFirstOrderAvailable());
        reduceActivity.setUserParticipateNumber(req.getUserParticipateNumber());
        reduceActivity.setActivityDiscount(new BigDecimal(req.getActivityDiscount()));
        reduceActivity.setActivityRuleDescription(ComUtils.splitStr(req.getActivityRuleDescription(), 512));
        reduceActivity.setActivityPicUrl(req.getActivityPicUrl());
        if (StrUtil.isAllNotBlank(req.getRegisterStartTime(), req.getRegisterEndTime())) {
            reduceActivity.setRegisterStartTime(req.getRegisterStartTime());
            reduceActivity.setRegisterEndTime(req.getRegisterEndTime());
        }
        int result = reduceActivityMapper.updateById(reduceActivity);
        if (result < 1) {
            return builder.setRetCode(-25001).setRetMsg("修改立减活动失败").build();
        }

        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(String.valueOf(req.getActivityId()));
        operateLog.setOperateType(2);
        operateLog.setOperateContent("修改立减活动");
        operateLog.setCreateOperId(currentUser.getUserId());
        operateLog.setCreateOperName(ComUtils.splitStr(currentUser.getUserName(), 128));
        operateLog.setCreateOperOrgName(ComUtils.splitStr(currentUser.getOrgName(), 128));
        operateLogMapper.insert(operateLog);
        return builder.build();
    }

    @Override
    public OfflineReduceActivityRes offlineReduceActivity(OfflineReduceActivityReq req) {
        OfflineReduceActivityRes.Builder builder = OfflineReduceActivityRes.newBuilder();
        CurrentUser currentUser = req.getCurrentUser();
        long activityId = req.getActivityId();
        ReduceActivity reduceActivity = reduceActivityMapper.selectById(activityId);
        if (null == reduceActivity) {
            return builder.setRetCode(-25001).setRetMsg("未查询到该活动信息").build();
        }
        if (reduceActivity.getOfflineFlag().equals(OFFLINE_STATUS)) {
            return builder.setRetCode(-25001).setRetMsg("该立减活动已下线,不能重复下线").build();
        }
        reduceActivity.setOfflineFlag(OFFLINE_STATUS);
        reduceActivity.setUpdateOperId(currentUser.getUserId());
        reduceActivity.setUpdateOperName(currentUser.getUserName());
        int result = reduceActivityMapper.updateById(reduceActivity);
        if (result < 1) {
            return builder.setRetCode(-25001).setRetMsg("下线立减活动失败").build();
        }
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(String.valueOf(req.getActivityId()));
        operateLog.setOperateType(2);
        operateLog.setOperateContent("下线立减活动");
        operateLog.setCreateOperId(currentUser.getUserId());
        operateLog.setCreateOperName(ComUtils.splitStr(currentUser.getUserName(), 128));
        operateLog.setCreateOperOrgName(ComUtils.splitStr(currentUser.getOrgName(), 128));
        operateLogMapper.insert(operateLog);
        return builder.build();
    }

    @Override
    public GetReduceActivityRes getReduceActivity(GetReduceActivityReq req) {
        GetReduceActivityRes.Builder builder = GetReduceActivityRes.newBuilder();
        ReduceActivityBo activityBo = reduceActivityMapper.getByActivityId(req.getActivityId());
        if (null == activityBo) {
            return builder.setRetCode(-25001).setRetMsg("未查询到该活动信息").build();
        }
        return builder
                .setId(activityBo.getId())
                .setOrgCode(activityBo.getOrgCode())
                .setOrgName(orgService.getOrgNameByOrgCode(activityBo.getOrgCode()))
                .setActivityName(activityBo.getActivityName())
                .setActivityStatus(activityBo.getActivityStatus())
                .setSignUpDeadline(activityBo.getSignUpDeadline())
                .setActivityStartTime(activityBo.getActivityStartTime())
                .setActivityEndTime(activityBo.getActivityEndTime())
                .setRegisterStartTime(activityBo.getRegisterStartTime())
                .setRegisterEndTime(activityBo.getRegisterEndTime())
                .setFirstOrderAvailable(activityBo.getFirstOrderAvailable())
                .setUserParticipateNumber(activityBo.getUserParticipateNumber())
                .setActivityDiscount(activityBo.getActivityDiscount().toString())
                .setActivityRuleDescription(activityBo.getActivityRuleDescription())
                .setActivityPicUrl(ossPathPrefix + activityBo.getActivityPicUrl())
                .setActivityRelativeUrl(activityBo.getActivityPicUrl())
                .build();
    }

    @Override
    public SearchReduceActivityNameRes searchReduceActivityName(SearchReduceActivityNameReq req) {
        List<ReduceActivityNameBo> reduceActivityNameBos = reduceActivityMapper.searchReduceActivityName();
        List<ReduceActivityNameInfo> list = reduceActivityNameBos.stream().map(act -> {
            ReduceActivityNameInfo.Builder builder = ReduceActivityNameInfo.newBuilder();
            builder.setId(act.getId()).setActivityName(act.getActivityName());
            return builder.build();
        }).collect(Collectors.toList());
        return SearchReduceActivityNameRes.newBuilder().addAllReduceActivityNameInfo(list).build();
    }

    @Override
    public GetReduceActivityWithGoodModelNameRes getReduceActivityWithGoodModelName(GetReduceActivityWithGoodModelNameReq req) {
        GetReduceActivityWithGoodModelNameRes.Builder builder = GetReduceActivityWithGoodModelNameRes.newBuilder();
        if (req.getStoreId() <= 0) {
            return GetReduceActivityWithGoodModelNameRes.failed(-1000, "门店id不能为空");
        }
        if (req.getActivityId() <= 0) {
            return GetReduceActivityWithGoodModelNameRes.failed(-1001, "立减活动id不能为空");
        }
        ReduceActivityBo activityBo = reduceActivityMapper.getByActivityId(req.getActivityId());
        if (null == activityBo) {
            return builder.setRetCode(-25001).setRetMsg("未查询到该活动信息").build();
        }
        List<Long> goodsModelIdList = storeReduceActivityMapper.SearchStoreParticipateGoodsModelId(req.getActivityId(), req.getStoreId());
        if (CollectionUtil.isNotEmpty(goodsModelIdList)) {
            ArrayList<String> nameList = new ArrayList<>();
            for (Long goodsModelId : goodsModelIdList) {
                String goodsModelName = goodsModelService.getGoodsModelNameById(goodsModelId);
                if (StrUtil.isNotBlank(goodsModelName)) {
                    nameList.add(goodsModelName);
                }
            }
            builder.addAllGoodsModelId(goodsModelIdList).addAllGoodsModelName(nameList);

        }
        return builder
                .setId(activityBo.getId())
                .setOrgCode(activityBo.getOrgCode())
                .setOrgName(orgService.getOrgNameByOrgCode(activityBo.getOrgCode()))
                .setActivityName(activityBo.getActivityName())
                .setActivityStatus(activityBo.getActivityStatus())
                .setSignUpDeadline(activityBo.getSignUpDeadline())
                .setActivityStartTime(activityBo.getActivityStartTime())
                .setActivityEndTime(activityBo.getActivityEndTime())
                .setRegisterStartTime(activityBo.getRegisterStartTime())
                .setRegisterEndTime(activityBo.getRegisterEndTime())
                .setFirstOrderAvailable(activityBo.getFirstOrderAvailable())
                .setUserParticipateNumber(activityBo.getUserParticipateNumber())
                .setActivityDiscount(activityBo.getActivityDiscount().toString())
                .setActivityRuleDescription(activityBo.getActivityRuleDescription())
                .setActivityPicUrl(ossPathPrefix + activityBo.getActivityPicUrl())
                .build();
    }

    @Override
    public SearchAllReduceActivityRes searchAllReduceActivity(SearchAllReduceActivityReq req) {
        SearchAllReduceActivityRes.Builder builder = SearchAllReduceActivityRes.newBuilder();
        builder.setCfgMd5(req.getCfgMd5());
        List<ReduceActivityBo> reduceActivityBoList = reduceActivityMapper.getAllReduceActivity();
        if (CollectionUtil.isNotEmpty(reduceActivityBoList)) {
            List<ReduceActivityForApp> list = reduceActivityBoList.stream().map(this::convertActivityInfoForApp).collect(Collectors.toList());
            builder.addAllInfo(list);
        }
        return builder.build();
    }

    private ReduceActivityForApp convertActivityInfoForApp(ReduceActivityBo activityBo) {

        //处理门店名称和机构名称
        return ReduceActivityForApp.newBuilder()
                .setId(activityBo.getId())
                .setOrgCode(activityBo.getOrgCode())
                .setOrgName(orgService.getOrgNameByOrgCode(activityBo.getOrgCode()))
                .setActivityName(activityBo.getActivityName())
                .setActivityStatus(activityBo.getActivityStatus())
                .setSignUpDeadline(activityBo.getSignUpDeadline())
                .setActivityStartTime(activityBo.getActivityStartTime())
                .setActivityEndTime(activityBo.getActivityEndTime())
                .setRegisterStartTime(activityBo.getRegisterStartTime())
                .setRegisterEndTime(activityBo.getRegisterEndTime())
                .setFirstOrderAvailable(activityBo.getFirstOrderAvailable())
                .setUserParticipateNumber(activityBo.getUserParticipateNumber())
                .setActivityDiscount(activityBo.getActivityDiscount().toString())
                .setActivityRuleDescription(activityBo.getActivityRuleDescription())
                .setActivityPicUrl(ossPathPrefix + activityBo.getActivityPicUrl())
                .build();
    }

}
