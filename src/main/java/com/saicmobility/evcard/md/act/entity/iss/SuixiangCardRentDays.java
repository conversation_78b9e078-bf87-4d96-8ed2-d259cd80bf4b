package com.saicmobility.evcard.md.act.entity.iss;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 随享卡租期表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SuixiangCardRentDays对象", description="随享卡租期表")
@TableName("suixiang_card_rent_days")
public class SuixiangCardRentDays extends Model<SuixiangCardRentDays> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "随享卡租期表主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "随享卡基础表表主键")
    private Long cardBaseId;

    @ApiModelProperty(value = "租期天数")
    private Integer rentDays;

    @ApiModelProperty(value = "服务费类型(feeType)：1-日租服务费、2-畅行服务费、3-车辆整备费、4-加油服务费、5-充电服务费、6-夜间服务费；划线价(crossedPrice)，单位为元，大于等于0的整数；")
    private String serviceFees;

    @ApiModelProperty(value = "服务费总节省金额，单位元")
    private BigDecimal totalServiceFeesAmout;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人")
    private String createOperName;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人id")
    private Long updateOperId;

    @ApiModelProperty(value = "修改人")
    private String updateOperName;

    @ApiModelProperty(value = "删除标记（0：正常 1：删除）")
    private Integer isDeleted;


}
