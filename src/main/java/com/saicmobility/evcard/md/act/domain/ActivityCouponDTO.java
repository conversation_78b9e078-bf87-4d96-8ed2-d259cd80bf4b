package com.saicmobility.evcard.md.act.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *活动优惠券模板属性，发放优惠券时用到
 */
public class ActivityCouponDTO implements Serializable {

    private static final long serialVersionUID = -5010046502185066718L;

    /**
     * 优惠券模板记录id，为表主键.<br>
     */
    private Long id;

    /**
     * 模板编号.<br>
     */
    private Long couponSeq;
    /**
     * 优惠券模板名称.<br>
     */
    private String couponName;
    /**
     * 所属运营公司，记录当时活动所属的运营公司作为发券单位.<br>
     */
    private String orgId;

    /**
     * 所属市场活动id
     */
    private Long activityId;

    private Integer activityType;

    /**
     *活动状态(0:待发布 1:待上线 2:进行中 3:已停止 4:暂停中)
     */
    private Long activityStatus;

    /**
     * 有效期类型（1起止时间 2时长）.<br>
     */
    private Integer validTimeType;
    /**
     * 有效时长 生效后几天过期(validTimeType=2时传).<br>
     */
    private Integer validDays;
    /**
     * 有效时长 到账几天有效(validTimeType=2时传).<br>
     */
    private Integer effectiveDays;

    /**
     * 有效期开始时间 YYYY-MM-DD.<br>
     */
    private String startDate;
    /**
     * 有效结束时间 YYYY-MM-DD.<br>
     */
    private String expiresDate;
    /**
     * 优惠券模板发放张数.<br>
     */
    private Integer offerQuantity;

    /**
     * 发券目标对象，0缺省 1仅邀请人 2仅被邀请人(仅邀请好友用)
     */
    private Integer couponTarget = 0;

    /**
     * 优惠券类型 1:直扣 2：折扣.<br>
     */
    private Integer couponType;

    /**
     * 优惠券交易类型 1:直扣 2：折扣.3.非收入券 4购买类全<br>
     */
    private Integer transactionType;
    
    /**
     * 优惠券面额
     */
    private Double couponValue;
    
    /**
     * 折扣
     */
    private Integer discountRate;

    /**
     * 业务类型（0不限/ 1分时/ 2短租/3长租).<br>
     */
    private Integer serviceType;
    
    /**
     * 可使用券的最低消费金额
     */
    private Double minAmount;

    private BigDecimal durationLimit;

    private String vehicleLimitDesc;

    private List<String> tags = new ArrayList<String>();


    public String getVehicleLimitDesc() {
        return vehicleLimitDesc;
    }

    public void setVehicleLimitDesc(String vehicleLimitDesc) {
        this.vehicleLimitDesc = vehicleLimitDesc;
    }

    public Double getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(Double minAmount) {
        this.minAmount = minAmount;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Integer getServiceType() {
        return serviceType;
    }

    public void setServiceType(Integer serviceType) {
        this.serviceType = serviceType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCouponSeq() {
        if(null == couponSeq) {
            return 0L;
        }
        return couponSeq;
    }

    public void setCouponSeq(Long couponSeq) {
        this.couponSeq = couponSeq;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Integer getValidTimeType() {
        if(null == validTimeType) {
            return 0;
        }
        return validTimeType;
    }

    public void setValidTimeType(Integer validTimeType) {
        this.validTimeType = validTimeType;
    }

    public Integer getValidDays() {
        return validDays;
    }

    public void setValidDays(Integer validDays) {
        this.validDays = validDays;
    }

    public Integer getEffectiveDays() {
        return effectiveDays;
    }

    public void setEffectiveDays(Integer effectiveDays) {
        this.effectiveDays = effectiveDays;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getExpiresDate() {
        return expiresDate;
    }

    public void setExpiresDate(String expiresDate) {
        this.expiresDate = expiresDate;
    }

    public Integer getOfferQuantity() {
        return offerQuantity;
    }

    public void setOfferQuantity(Integer offerQuantity) {
        this.offerQuantity = offerQuantity;
    }

    public Integer getCouponType() {
        if(couponType == null) {
            return 1;
        }
        return couponType;
    }

    public void setCouponType(Integer couponType) {
        this.couponType = couponType;
    }
    
    public Double getCouponValue() {
        return couponValue;
    }
    
    public void setCouponValue(Double couponValue) {
        this.couponValue = couponValue;
    }
    
    public Integer getDiscountRate() {
        return discountRate;
    }
    
    public void setDiscountRate(Integer discountRate) {
        this.discountRate = discountRate;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Integer getCouponTarget() {
        if(null == couponTarget) {
            return 0;
        }
        return couponTarget;
    }

    public void setCouponTarget(Integer couponTarget) {
        this.couponTarget = couponTarget;
    }

    public Integer getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(Integer transactionType) {
        this.transactionType = transactionType;
    }

    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }

    @Override
    public String toString() {
        return "ActivityCouponDTO{" +
                "id=" + id +
                ", couponSeq=" + couponSeq +
                ", couponName='" + couponName + '\'' +
                ", orgId='" + orgId + '\'' +
                ", validTimeType=" + validTimeType +
                ", validDays=" + validDays +
                ", effectiveDays=" + effectiveDays +
                ", startDate='" + startDate + '\'' +
                ", expiresDate='" + expiresDate + '\'' +
                ", offerQuantity=" + offerQuantity +
                ", couponType=" + couponType +
                ", couponValue=" + couponValue +
                ", discountRate=" + discountRate +
                ", serviceType=" + serviceType +
                ", minAmount=" + minAmount +
                ", vehicleLimitDesc='" + vehicleLimitDesc + '\'' +
                ", tags=" + tags +
                '}';
    }

    public Long getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(Long activityStatus) {
        this.activityStatus = activityStatus;
    }

    public BigDecimal getDurationLimit() {
        return durationLimit;
    }

    public void setDurationLimit(BigDecimal durationLimit) {
        this.durationLimit = durationLimit;
    }
}