package com.saicmobility.evcard.md.act.bo.proprietary;

import com.saicmobility.evcard.md.act.dto.market.TimeRange;
import com.saicmobility.evcard.md.act.dto.proprietary.DiscountFlexiblePricing;
import com.saicmobility.evcard.md.act.dto.proprietary.DiscountStandardPricing;
import com.saicmobility.evcard.md.act.dto.proprietary.FullMinusFlexiblePricing;
import com.saicmobility.evcard.md.act.dto.proprietary.FullMinusStandardPricing;
import com.saicmobility.evcard.md.mdactservice.api.GetProprietaryActivityInfoRes;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/5 8:55
 */
@Data
public class GetProprietaryActivityInfoBo {
    private long id; // 活动ID
    private String activityName; //活动名称
    private String activityTag; //活动标签
    private List<String> orgCodes;//机构列表
    private List<Long> vehicleModelIds;//车型id列表
    private List<Long> storeIdList;//门店id列表
    private int allStore; //全部门店 1-全部  2-不是全部
    private int allOrgCodes; //全部机构  1-全部  2-不是全部
    private int allModelIds;//全部车型  1-全部  2-不是全部
    private int activityType; //活动类型
    private int pricingType; //定价类型：1-灵活定价、2-规范定价
    private int activityStatus; //活动状态：1-未开始、2-生效中、3-已过期、4-已作废
    private int discountLatitude; //优惠纬度：1-车辆租金
    private List<FullMinusStandardPricing> fullMinusStandardPricing;//满减规范定价
    private List<FullMinusFlexiblePricing> fullMinusFlexiblePricing;//满减灵活定价
    private List<DiscountStandardPricing> discountStandardPricing;//打折规范定价
    private List<DiscountFlexiblePricing> discountFlexiblePricing;//打折灵活定价
    private int minRentDays; //最小租期
    private int maxRentDays; //最大租期
    private int availableOnHolidays; //节假日是否可用：1-可用、2-不可用
    private String signUpStartDate; //报名开始时间 yyyy-MM-dd
    private String signUpEndDate; //报名结束时间 yyyy-MM-dd
    private String pickUpDate; //取车时间 yyyy-MM-dd
    private String returnDate; //还车时间 yyyy-MM-dd
    private String activityStartDate; //活动开始时间 yyyy-MM-dd
    private String activityEndDate; //活动结束时间 yyyy-MM-dd
    private List<TimeRange> unavailableDateRanges; //不可用时间范围
    private String signUpOrgCodes;//报名机构集合
    private int sameDayUseFlag;//仅限下单当日取车  0-非当日使用 1-当日使用
    private int specifyDateFlag;//是否指定下单日期 0-不限制 1-限制
    private String specifyDate;//指定日期，格式以数字，英文分号分割。空代表未选择： 1;3-周一，周三
    private int blockHolidayFlag; //屏蔽节假日 0-不屏蔽 1-屏蔽
    private int intersectionFlag; //取还车时间交集开关，1=取还车都必须在时间范围内 2=取还车任一时间在时间范围内

    public GetProprietaryActivityInfoRes toRes(){
        return GetProprietaryActivityInfoRes.newBuilder()
                .setId(id)
                .setActivityName(activityName)
                .setActivityTag(activityTag)
                .addAllOrgCodes(orgCodes)
                .addAllStoreIdList(storeIdList)
                .setAllStore(allStore)
                .addAllVehicleModelIds(vehicleModelIds)
                .setAllOrgCodes(allOrgCodes)
                .setAllModelIds(allModelIds)
                .setActivityType(activityType)
                .setPricingType(pricingType)
                .setActivityStatus(activityStatus)
                .setDiscountLatitude(discountLatitude)
                .addAllFullMinusStandardPricing(toFullMinusStandardPricing())
                .addAllFullMinusFlexiblePricing(toFullMinusFlexiblePricing())
                .addAllDiscountStandardPricing(toDiscountStandardPricing())
                .addAllDiscountFlexiblePricing(toDiscountFlexiblePricing())
                .setMinRentDays(minRentDays)
                .setMaxRentDays(maxRentDays)
                .setAvailableOnHolidays(availableOnHolidays)
                .setSignUpStartDate(signUpStartDate)
                .setSignUpEndDate(signUpEndDate)
                .setPickUpDate(pickUpDate)
                .setReturnDate(returnDate)
                .setActivityStartDate(activityStartDate)
                .setActivityEndDate(activityEndDate)
                .addAllUnavailableDateRanges(toUnavailableDateRanges())
                .setSignUpOrgCodes(signUpOrgCodes)
                .setSameDayUseFlag(sameDayUseFlag)
                .setSpecifyDateFlag(specifyDateFlag)
                .setSpecifyDate(specifyDate)
                .setBlockHolidayFlag(blockHolidayFlag)
                .setIntersectionFlag(intersectionFlag)
                .build();
    }

    public List<com.saicmobility.evcard.md.mdactservice.api.FullMinusStandardPricing> toFullMinusStandardPricing() {
        List<com.saicmobility.evcard.md.mdactservice.api.FullMinusStandardPricing> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(fullMinusStandardPricing)){
            for (FullMinusStandardPricing item : fullMinusStandardPricing) {
                com.saicmobility.evcard.md.mdactservice.api.FullMinusStandardPricing fmsp = com.saicmobility.evcard.md.mdactservice.api.FullMinusStandardPricing.newBuilder()
                        .setDays(item.getDays())
                        .setDiscountAmount(item.getDiscountAmount())
                        .build();
                list.add(fmsp);
            }
        }
        return list;
    }

    public List<com.saicmobility.evcard.md.mdactservice.api.FullMinusFlexiblePricing> toFullMinusFlexiblePricing() {
        List<com.saicmobility.evcard.md.mdactservice.api.FullMinusFlexiblePricing> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(fullMinusFlexiblePricing)){
            for (FullMinusFlexiblePricing item : fullMinusFlexiblePricing) {
                com.saicmobility.evcard.md.mdactservice.api.FullMinusFlexiblePricing fmfp = com.saicmobility.evcard.md.mdactservice.api.FullMinusFlexiblePricing.newBuilder()
                        .setDays(item.getDays())
                        .setMinDiscountAmount(item.getMinDiscountAmount())
                        .setMaxDiscountAmount(item.getMaxDiscountAmount())
                        .build();
                list.add(fmfp);
            }
        }
        return list;
    }

    public List<com.saicmobility.evcard.md.mdactservice.api.DiscountStandardPricing> toDiscountStandardPricing() {
        List<com.saicmobility.evcard.md.mdactservice.api.DiscountStandardPricing> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(discountStandardPricing)){
            for (DiscountStandardPricing item : discountStandardPricing) {
                com.saicmobility.evcard.md.mdactservice.api.DiscountStandardPricing dsp = com.saicmobility.evcard.md.mdactservice.api.DiscountStandardPricing.newBuilder()
                        .setDays(item.getDays())
                        .setDiscount(item.getDiscount())
                        .build();
                list.add(dsp);
            }
        }
        return list;
    }

    public List<com.saicmobility.evcard.md.mdactservice.api.DiscountFlexiblePricing> toDiscountFlexiblePricing() {
        List<com.saicmobility.evcard.md.mdactservice.api.DiscountFlexiblePricing> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(discountFlexiblePricing)){
            for (DiscountFlexiblePricing item : discountFlexiblePricing) {
                com.saicmobility.evcard.md.mdactservice.api.DiscountFlexiblePricing dfp = com.saicmobility.evcard.md.mdactservice.api.DiscountFlexiblePricing.newBuilder()
                        .setDays(item.getDays())
                        .setMinDiscount(item.getMinDiscount())
                        .setMaxDiscount(item.getMaxDiscount())
                        .build();
                list.add(dfp);
            }
        }
        return list;
    }

    public List<com.saicmobility.evcard.md.mdactservice.api.TimeRange> toUnavailableDateRanges() {
        List<com.saicmobility.evcard.md.mdactservice.api.TimeRange> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(unavailableDateRanges)){
            for (TimeRange item : unavailableDateRanges) {
                com.saicmobility.evcard.md.mdactservice.api.TimeRange tr = com.saicmobility.evcard.md.mdactservice.api.TimeRange.newBuilder()
                        .setStartDate(item.getStartDate())
                        .setEndDate(item.getEndDate())
                        .build();
                list.add(tr);
            }
        }
        return list;
    }

}
