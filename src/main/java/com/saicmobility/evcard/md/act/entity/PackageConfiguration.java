package com.saicmobility.evcard.md.act.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 套餐配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_package_configuration")
@ApiModel(value="PackageConfiguration对象", description="套餐配置")
public class PackageConfiguration extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "运营公司id")
    private String orgCode;

    @ApiModelProperty(value = "商品车型")
    private Long goodsModelId;

    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @ApiModelProperty(value = "套餐名称")
    private String packageName;

    @ApiModelProperty(value = "天数")
    private Integer daysNumber;

    @ApiModelProperty(value = "天数总价")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "用车开始时间")
    private String useStartDate;

    @ApiModelProperty(value = "用车结束时间")
    private String useEndDate;

    @ApiModelProperty(value = "生效时间")
    private String startTime;

    @ApiModelProperty(value = "失效时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String endTime;

    @ApiModelProperty(value = "续租是否可用 1是 2否 ")
    private Integer renewUseFlag;

    @ApiModelProperty(value = "状态 1生效中 2待生效 3已失效 4已作废")
    private Integer configState;

    @ApiModelProperty(value = "早鸟开始时间")
    private String earlyStartDate;

    @ApiModelProperty(value = "早鸟结束时间")
    private String earlyEndDate;

    @ApiModelProperty(value = "早鸟总价")
    private BigDecimal earlyPrice;

    @ApiModelProperty(value = "是否可用早鸟套餐 1是 2否 ")
    private Integer earlyFlag;
}
