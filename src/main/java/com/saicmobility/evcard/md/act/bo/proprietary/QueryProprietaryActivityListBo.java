package com.saicmobility.evcard.md.act.bo.proprietary;

import com.saicmobility.evcard.md.mdactservice.api.ProprietaryActivityListInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/4 15:53
 */
@Data
public class QueryProprietaryActivityListBo {
    private List<QueryProprietaryActivityListInfo> list;
    private int total;

    public List<ProprietaryActivityListInfo> toList(List<QueryProprietaryActivityListInfo> list) {
        List<ProprietaryActivityListInfo> acts = new ArrayList<>();
        for (QueryProprietaryActivityListInfo info : list) {
            ProprietaryActivityListInfo act = ProprietaryActivityListInfo.newBuilder()
                    .setId(info.getId())
                    .setActivityName(info.getActivityName())
                    .setActivityTag(info.getActivityTag())
                    .setActivityType(info.getActivityType())
                    .setSignUpStartDate(info.getSignUpStartDate())
                    .setSignUpEndDate(info.getSignUpEndDate())
                    .setPricingType(info.getPricingType())
                    .setActivityStatus(info.getActivityStatus())
                    .build();
            acts.add(act);
        }
        return acts;
    }
}
