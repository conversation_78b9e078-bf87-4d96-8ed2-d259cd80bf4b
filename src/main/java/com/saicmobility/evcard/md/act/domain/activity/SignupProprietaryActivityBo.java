package com.saicmobility.evcard.md.act.domain.activity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

@Data
public class SignupProprietaryActivityBo implements Serializable {
    private long id; // 报名id
    private long activityId; // 活动id
    private String activityName; // 活动名称
    private String activityTag; // 活动标签
    private int activityType; // 活动类型：1-满减、2-打折
    private LocalDate signUpStartDate; // 报名开始时间 yyyy-MM-dd
    private LocalDate signUpEndDate; // 报名结束时间 yyyy-MM-dd
    private int pricingType; // 定价类型：1-灵活定价、2-规范定价
    private int activityStatus; // 活动状态：1-未开始、2-生效中、3-已过期、4-已作废
    private int signupStatus; // 报名状态：1-未参加、2-已参加
    private Long storeId;  //门店id
    private String storeIds;
    private Date createTime;

}
