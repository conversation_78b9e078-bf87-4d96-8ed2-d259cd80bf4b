package com.saicmobility.evcard.md.act.enums.market;

public enum CostBearingPartyEnum {
    PLATFORM(1, "平台全部承担"),
    MERCHANT(2, "商家全部承担"),
    TOGETHER(3, "共同承担");

    private Integer type;
    private String msg;

    CostBearingPartyEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }
}
