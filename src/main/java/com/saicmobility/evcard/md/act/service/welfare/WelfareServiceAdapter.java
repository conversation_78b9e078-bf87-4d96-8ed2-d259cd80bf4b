package com.saicmobility.evcard.md.act.service.welfare;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.dto.welfare.*;
import com.saicmobility.evcard.md.mdactservice.api.GetWelfareDetailInfoRes;
import com.saicmobility.evcard.md.mdactservice.api.ReceiveWelfareRes;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class WelfareServiceAdapter {

    @Autowired
    private ScanCodeWelfareServiceImpl scanCodeWelfareServiceImpl;
    @Autowired
    private OneCodeMulCouponScanCodeWelfareServiceImpl oneCodeMulCouponScanCodeWelfareService;
    @Autowired
    private H5ReceiveWelfareServiceImpl h5ReceiveWelfareService;


    /**
     * 获取服务类
     *
     * @param input
     * @return
     */
    public IWelfareService getWelfareService(GetWelfareInfoInput input) {
        int source = input.getSource();
        if (source == 1) {
            String key = input.getKey();
            // 一码多券 key长度为17位
            if (StringUtils.isNotBlank(key) && key.length() == 17) {
                return oneCodeMulCouponScanCodeWelfareService;
            }else{
                return scanCodeWelfareServiceImpl;
            }
        } else if (source == 3) {
            // H5领取场景
            return h5ReceiveWelfareService;
        }
        return null;
    }

    /**
     * 获取福利详情
     *
     * @param input
     * @return
     */
    public GetWelfareDetailInfoRes getWelfareDetailInfo(GetWelfareInfoInput input) {
        IWelfareService welfareService = getWelfareService(input);
        int welfareType = input.getWelfareType();
        if (welfareType == 1) {
            List<SuiXiangCardWelfareInfo> suiXiangCardWelfareDetailInfoList = welfareService.getSuiXiangCardWelfareDetailInfo(input);
            return SuiXiangCardWelfareInfo.listToRes(suiXiangCardWelfareDetailInfoList);
        } else if (welfareType == 2) {
            List<CouponWelfareInfo> couponWelfareDetailInfoList = welfareService.getCouponWelfareDetailInfo(input);
            return CouponWelfareInfo.listToRes(couponWelfareDetailInfoList);
        }
        log.error("getWelfareDetailInfo 查询失败,input={},tid={}", JSON.toJSONString(input), Trace.currentTraceId());
        return GetWelfareDetailInfoRes.failed(ErrorEnum.NOT_FOUND_WELFARE_INFO.getCode(), ErrorEnum.NOT_FOUND_WELFARE_INFO.getMsg());
    }

    /**
     * 领取福利
     *
     * @param input
     * @return
     */

    public ReceiveWelfareRes receiveWelfare(ReceiveWelfareInput input) {
        log.info("receiveWelfare start,input={},tid={}", JSON.toJSONString(input), Trace.currentTraceId());
        GetWelfareInfoInput getWelfareInfoInput = input.getGetWelfareInfoInput();
        IWelfareService welfareService = getWelfareService(getWelfareInfoInput);
        int welfareType = getWelfareInfoInput.getWelfareType();
        try {
            if (welfareType == 1) {
                ReceiveWelfareDto receiveWelfareDto = welfareService.receiveSuiXiangCardWelfare(input);
                return receiveWelfareDto.toRes();
            } else if (welfareType == 2) {
                ReceiveWelfareDto receiveWelfareDto = welfareService.receiveCouponWelfare(input);
                return receiveWelfareDto.toRes();
            }
        } catch (BusinessException e) {
            log.error("receiveWelfare 领取业务异常,input={},tid={}", JSON.toJSONString(input), Trace.currentTraceId());
            return ReceiveWelfareRes.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("receiveWelfare 领取异常,input={},tid={}", JSON.toJSONString(input), Trace.currentTraceId());
            return ReceiveWelfareRes.failed(ErrorEnum.GET_WELFARE_FAIL.getCode(), ErrorEnum.GET_WELFARE_FAIL.getMsg());
        }
        return ReceiveWelfareRes.failed(ErrorEnum.GET_WELFARE_FAIL.getCode(), ErrorEnum.GET_WELFARE_FAIL.getMsg());
    }


}
