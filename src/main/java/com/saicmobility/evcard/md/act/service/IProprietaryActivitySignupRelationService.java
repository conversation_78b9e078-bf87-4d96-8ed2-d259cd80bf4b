package com.saicmobility.evcard.md.act.service;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivity;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivitySignupRelation;
import com.saicmobility.evcard.md.act.service.inner.IBaseService;

import java.util.Set;

public interface IProprietaryActivitySignupRelationService extends IBaseService<ProprietaryActivitySignupRelation> {


    /**
     * 新增或修改
     * @param orgCodeList  机构集合
     * @param activityId  活动id
     * @param isAllStore  是否全部门店    0否，1是
     * @param isAllVehicle  是否全部车型  0否，1是
     * @param updateOperId  更新人
     * @param updateOperName  更新名称
     * @param vehicleModelIds  报名车型
     * @param flexiblePricing  灵活定价
     */
    void addOrUpdateSignupRelation(Set<String> orgCodeList,Long activityId, Integer isAllStore, Integer isAllVehicle
            , Long updateOperId, String updateOperName,String vehicleModelIds,String flexiblePricing) throws BusinessException;


}
