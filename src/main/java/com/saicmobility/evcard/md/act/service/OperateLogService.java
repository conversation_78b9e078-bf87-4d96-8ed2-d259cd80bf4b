package com.saicmobility.evcard.md.act.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.entity.OperateLog;
import com.saicmobility.evcard.md.mdactservice.api.*;

/**
 * 操作日志表 服务类
 */
public interface OperateLogService extends IService<OperateLog> {

    SearchOperateLogRes searchOperateLog(SearchOperateLogReq req);

    SearchActivityParticipateLogRes searchActivityParticipateLog(SearchActivityParticipateLogReq req);

    boolean saveOperateLog(String log, String foreignId, Integer type, CurrentUser currentUser);
}
