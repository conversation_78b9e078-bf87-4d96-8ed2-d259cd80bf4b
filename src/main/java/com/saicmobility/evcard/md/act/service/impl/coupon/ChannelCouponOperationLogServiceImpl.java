package com.saicmobility.evcard.md.act.service.impl.coupon;

import com.saicmobility.evcard.md.act.entity.siac.ChannelCouponOperationLog;
import com.saicmobility.evcard.md.act.mapper.siacPlus.ChannelCouponOperationLogMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saicmobility.evcard.md.act.service.coupun.IChannelCouponOperationLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 渠道优惠券操作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Service
public class ChannelCouponOperationLogServiceImpl extends ServiceImpl<ChannelCouponOperationLogMapper, ChannelCouponOperationLog> implements IChannelCouponOperationLogService {

}
