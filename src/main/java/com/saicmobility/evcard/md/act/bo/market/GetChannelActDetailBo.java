package com.saicmobility.evcard.md.act.bo.market;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.dto.market.TimeRange;
import com.saicmobility.evcard.md.act.dto.market.UnavailableDateRanges;
import com.saicmobility.evcard.md.act.entity.ChannelActivity;
import com.saicmobility.evcard.md.mdactservice.api.ChannelActDetail;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Slf4j
public class GetChannelActDetailBo {
    private long id;
    private String channelText; //渠道类型：1-携程、2-哈罗、3-飞猪、4-悟空、5-高德、6-滴滴
    private String discountCode; //优惠码
    private List<String> orgCodes; //机构
    private String actName; //活动名称
    private int actStatus; //活动状态：1-生效、2-已过期
    private List<Long> storeIds;
    private List<Long> catIds;
    private int actType; //活动类型：1-满减、2-打折、3-减至
    private int discountLatitude; //优惠纬度：1-车辆租金、2-订单整单
    private int discountMethod; //优惠方式：1-针对金额、2-针对租期、3-满天减天、4-满天减金额
    private String discountConditional1; //优惠条件1 满...
    private String discountConditional2; //优惠条件2 减至...元//减至...天//打...折
    private int restrictDiscounts; //是否限制优惠：1-有限制、2-无限制
    private String maxDiscountAmount; //最高优惠金额，当restrictDiscounts=1才需要设置此字段
    private int maxRentDays; //最大租期，当restrictDiscounts=1才需要设置此字段
    private int minRentDays; //最小租期，当restrictDiscounts=1才需要设置此字段
    private String actStartDate; //活动开始时间
    private String actEndDate; //活动结束时间
    private String pickupStartDate; //取车开始时间
    private String pickupEndDate; //取车结束时间
    private String returnStartDate; //还车开始时间
    private String returnEndDate; //还车结束时间
    private List<TimeRange> unavailableDateRanges; //不可用时间范围
    private int costBearingParty; //成本承担方：1-平台全部承担、2-商家全部承担、3-共同承担
    private int costAllocationMethod; //成本分摊方式：1-百分比设置、2-按固定金额设置
    private String merchantBear; //商户承担(百分比或金额)
    private String platformBear; //平台承担(百分比或金额)
    private String appKey; //二级渠道Key
    private int isDeleted; //伪删除
    private Integer isAllStore;
    private Integer isAllVehicle;
    private Integer intersectionFlag; //取还车时间交集开关，1=取还车都必须在时间范围内 2=取还车任一时间在时间范围内

    public List<com.saicmobility.evcard.md.mdactservice.api.TimeRange> toUnavailableList(List<TimeRange> unavailableDateRanges) {
        List<com.saicmobility.evcard.md.mdactservice.api.TimeRange> list = new ArrayList<>();
        for (TimeRange timeRange : unavailableDateRanges) {
            com.saicmobility.evcard.md.mdactservice.api.TimeRange tr = com.saicmobility.evcard.md.mdactservice.api.TimeRange.newBuilder()
                    .setStartDate(timeRange.getStartDate())
                    .setEndDate(timeRange.getEndDate())
                    .build();
            list.add(tr);
        }
        return list;
    }

    public static GetChannelActDetailBo getInstanceFromDb(ChannelActivity act) throws BusinessException {
        try {
            GetChannelActDetailBo bo = new GetChannelActDetailBo();
            bo.setId(act.getId());
            //bo.setChannelText(getChannelName(act.getSecondAppKey()));
            bo.setAppKey(act.getSecondAppKey());
            bo.setDiscountCode(act.getDiscountCode());
            bo.setOrgCodes(Arrays.stream(act.getOrgCodes().split(",")).collect(Collectors.toList()));
            bo.setActName(act.getActivityName());
            bo.setActStatus(act.getActivityStatus());
            bo.setStoreIds(Arrays.stream(act.getStoreIds().split(",")).map(Long::parseLong).collect(Collectors.toList()));
            bo.setCatIds(Arrays.stream(act.getCarModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList()));
            bo.setActType(act.getActivityType());
            bo.setDiscountLatitude(act.getDiscountLatitude());
            bo.setDiscountMethod(act.getDiscountMethod());
            bo.setDiscountConditional1(act.getDiscountCondition1());
            bo.setDiscountConditional2(act.getDiscountCondition2());
            bo.setRestrictDiscounts(act.getRestrictDiscounts());
            bo.setMaxDiscountAmount(act.getMaxDiscountAmount().toString());
            bo.setMaxRentDays(act.getMaxRentDays());
            bo.setMinRentDays(act.getMinRentDays());
            bo.setActStartDate(act.getActivityStartDate().toString());
            bo.setActEndDate(act.getActivityEndDate().toString());
            bo.setPickupStartDate(act.getPickUpStartDate().toString());
            bo.setPickupEndDate(act.getPickUpEndDate().toString());
            bo.setReturnStartDate(act.getReturnStartDate().toString());
            bo.setReturnEndDate(act.getReturnEndDate().toString());
            bo.setUnavailableDateRanges(JSON.parseObject(act.getUnavailableDateRanges(), UnavailableDateRanges.class).getUnavailableDateRanges());
            bo.setCostBearingParty(act.getCostBearingParty());
            bo.setCostAllocationMethod(act.getCostAllocationMethod());
            bo.setMerchantBear(act.getMerchantBear().toString());
            bo.setPlatformBear(act.getPlatformBear().toString());
            bo.setIsDeleted(act.getIsDeleted());
            bo.setIsAllStore(act.getIsAllStore());
            bo.setIsAllVehicle(act.getIsAllVehicle());
            bo.setIntersectionFlag(act.getIntersectionFlag());
            return bo;
        } catch (Exception e) {
            log.error("ChannelActivity 转 GetChannelActDetailBo 异常, act = {}", JSON.toJSONString(act), e);
            throw new BusinessException(ErrorEnum.DATA_FORMAT_ERROR.getCode(), ErrorEnum.DATA_FORMAT_ERROR.getMsg());
        }
    }

    public static ChannelActDetail boToPb(GetChannelActDetailBo bo) {
        String amount = new BigDecimal(bo.getMaxDiscountAmount()).compareTo(new BigDecimal("-1")) != 0 ? bo.getMaxDiscountAmount() : "0";
        return ChannelActDetail.newBuilder()
                .setId(bo.getId())
                .setChannelText(bo.getChannelText())
                .setChannel(bo.getAppKey())
                .setDiscountCode(bo.getDiscountCode())
                .addAllOrgCodes(bo.getOrgCodes())
                .setActName(bo.getActName())
                .setActStatus(bo.getActStatus())
                .addAllStoreIdList(bo.getStoreIds())
                .addAllCarIdList(bo.getCatIds())
                .setActType(bo.getActType())
                .setDiscountLatitude(bo.getDiscountLatitude())
                .setDiscountMethod(bo.getDiscountMethod())
                .setDiscountConditional1(bo.getDiscountConditional1())
                .setDiscountConditional2(bo.getDiscountConditional2())
                .setRestrictDiscounts(bo.getRestrictDiscounts())
                //有无限制
                .setMaxDiscountAmount(amount)
                .setMaxRentDays(bo.getMaxRentDays())
                .setMinRentDays(bo.getMinRentDays())
                .setActStartDate(bo.getActStartDate())
                .setActEndDate(bo.getActEndDate())
                .setPickupStartDate(bo.getPickupStartDate())
                .setPickupEndDate(bo.getPickupEndDate())
                .setReturnStartDate(bo.getReturnStartDate())
                .setReturnEndDate(bo.getReturnEndDate())
                .addAllUnavailableDateRanges(bo.toUnavailableList(bo.getUnavailableDateRanges()))
                .setCostBearingParty(bo.getCostBearingParty())
                .setCostAllocationMethod(bo.getCostAllocationMethod())
                .setMerchantBear(bo.getMerchantBear())
                .setPlatformBear(bo.getPlatformBear())
                .setIsDeleted(bo.getIsDeleted())
                .setIntersectionFlag(bo.getIntersectionFlag())
                .build();
    }
}
