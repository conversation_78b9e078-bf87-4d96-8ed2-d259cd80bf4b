package com.saicmobility.evcard.md.act.entity.iss;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 随享卡购买记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SuixiangCardPurchaseRecord对象", description="随享卡购买记录表")
@TableName("suixiang_card_purchase_record")
public class SuixiangCardPurchaseRecord extends Model<SuixiangCardPurchaseRecord> {

    private static final long serialVersionUID = 734811126026867064L;

    @ApiModelProperty(value = "随享卡购买记录id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "卡片所属机构(运营公司)")
    private String orgId;

    @ApiModelProperty(value = "会员pk_id")
    private Long userId;

    @ApiModelProperty(value = "用户手机号")
    private String userMobile;

    @ApiModelProperty(value = "随享卡基础表id")
    private Long cardBaseId;

    @ApiModelProperty(value = "随享卡-卡片名称")
    private String cardName;

    @ApiModelProperty(value = "随享卡租期表id")
    private Long cardRentId;

    @ApiModelProperty(value = "随享卡价格表id")
    private Long cardPriceId;

    @ApiModelProperty(value = "随享卡兑换表id，兑换后才有值")
    private Long cdkId;

    @ApiModelProperty(value = "购卡方式 1：购买  2：赠送  3：兑换码")
    private Integer issueType;

    @ApiModelProperty(value = "具体获取方式 购卡方式为3时，0:兑换码兑换1：扫码兑换")
    private Integer obtainType;

    @ApiModelProperty(value = "购买状态 1：待支付 2：已支付 3：已取消 ")
    private Integer paymentStatus;

    @ApiModelProperty(value = "购买张数")
    private Integer quantity;

    @ApiModelProperty(value = "支付时间,实际调用支付接口时间")
    private LocalDateTime payTime;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal realAmount;

    @ApiModelProperty(value = "交易号")
    private String outTradeSeq;

    @ApiModelProperty(value = "随享卡用户使用表id（在支付成功后，此字段才有值）")
    private Long cardUseId;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "购买订单号，用于展示")
    private String orderSeq;

    @ApiModelProperty(value = "支付订单号")
    private String payOrderNo;

    @ApiModelProperty(value = "是否已读 0：未读 1：已读")
    private Integer remindStatus;

    @ApiModelProperty(value = "取消时间")
    private LocalDateTime cancelTime;

    @ApiModelProperty(value = "合并买卡来源 0单独&后付订单 1预付订单")
    private Integer mergePayOrigin;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人")
    private String createOperName;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人id")
    private Long updateOperId;

    @ApiModelProperty(value = "修改人")
    private String updateOperName;

    @ApiModelProperty(value = "删除标记（0：正常 1：删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "随享卡用户使用表ids（在支付成功后，此字段才有值）")
    private String cardUseIds;


}
