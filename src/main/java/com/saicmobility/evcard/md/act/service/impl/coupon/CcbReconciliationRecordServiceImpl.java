package com.saicmobility.evcard.md.act.service.impl.coupon;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saicmobility.evcard.md.act.entity.siac.CcbReconciliationRecord;
import com.saicmobility.evcard.md.act.mapper.siacPlus.CcbReconciliationRecordMapper;
import com.saicmobility.evcard.md.act.service.coupon.ICcbReconciliationRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 建行对账文件处理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Service
public class CcbReconciliationRecordServiceImpl extends ServiceImpl<CcbReconciliationRecordMapper, CcbReconciliationRecord> implements ICcbReconciliationRecordService {

}
