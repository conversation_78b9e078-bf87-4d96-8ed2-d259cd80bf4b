package com.saicmobility.evcard.md.act.manager;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivity;
import com.saicmobility.evcard.md.act.enums.ConfigAttrType;
import com.saicmobility.evcard.md.act.enums.proprietary.ActivityStatusEnum;
import com.saicmobility.evcard.md.act.service.IProprietaryActivityService;
import com.saicmobility.evcard.md.act.service.OperateLogService;
import com.saicmobility.evcard.md.act.service.extern.ConfigLoader;
import com.saicmobility.evcard.md.mdactservice.api.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/1/4 9:59
 */
@Slf4j
@Service
public class ProprietaryActivityManager {
    @Resource
    private IProprietaryActivityService proprietaryActivityService;

    @Resource
    private OperateLogService operateLogService;

    @Resource
    private ConfigLoader configLoader;

    @Transactional(rollbackFor = Exception.class)
    public Long insertProprietaryActivity(ProprietaryActivity act, CurrentUser currentUser, String logContent) throws BusinessException {
        UserDTO userDTO = new UserDTO();
        if (currentUser != null) {
            userDTO.setId(currentUser.getUserId());
            userDTO.setUsername(currentUser.getUserName());
        }
        Date currDate = new Date();
        //向t_proprietary_activity插入数据
        boolean ret = false;
        ret = proprietaryActivityService.save(act, userDTO, currDate);
        if (!ret) {
            log.error("向t_proprietary_activity插入数据失败,ProprietaryActivity={}", JSON.toJSONString(act));
            throw new BusinessException(ErrorEnum.ADD_ACTIVITY_FAILED.getCode(), ErrorEnum.ADD_ACTIVITY_FAILED.getMsg());
        }
        //记录日志
        //日志细节
        logContent += "自营活动";
        ret = operateLogService.saveOperateLog(logContent, act.getId().toString(), ConfigAttrType.PROPRIETARY_ACTIVITY_CONFIG.getType(), currentUser);
        if (!ret) {
            log.error("记录日志失败");
            throw new BusinessException(ErrorEnum.ADD_LOG_FAILED.getCode(), ErrorEnum.ADD_LOG_FAILED.getMsg());
        }
        return act.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelProprietaryActivity(long id, CurrentUser currentUser, String logContent) throws BusinessException{
        UserDTO userDTO = new UserDTO();
        if (currentUser != null) {
            userDTO.setId(currentUser.getUserId());
            userDTO.setUsername(currentUser.getUserName());
        }
        Date currDate = new Date();

        boolean ret = proprietaryActivityService.lambdaUpdate()
                .set(ProprietaryActivity::getUpdateOperId, userDTO.getId())
                .set(ProprietaryActivity::getUpdateOperName, userDTO.getUsername())
                .set(ProprietaryActivity::getUpdateTime, currDate)
                .set(ProprietaryActivity::getActivityStatus,ActivityStatusEnum.CANCEL.getType())
                .eq(ProprietaryActivity::getId, id)
                .update();

        if(!ret){
            log.error("作废自营活动失败, id ={}",id);
            throw new BusinessException(ErrorEnum.CANCEL_FAIL.getCode(),ErrorEnum.CANCEL_FAIL.getMsg());
        }

        //记录日志
        ret = operateLogService.saveOperateLog(logContent, String.valueOf(id), ConfigAttrType.PROPRIETARY_ACTIVITY_CONFIG.getType(), currentUser);
        if(!ret){
            log.error("更新操作日志失败");
            throw new BusinessException(ErrorEnum.OPERATORLOG_ERROR2.getCode(), ErrorEnum.OPERATORLOG_ERROR2.getMsg());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateProprietaryActivityInfo(ProprietaryActivity act, CurrentUser currentUser, String logContent, Long oldId) throws BusinessException{
        UserDTO userDTO = new UserDTO();
        if (currentUser != null) {
            userDTO.setId(currentUser.getUserId());
            userDTO.setUsername(currentUser.getUserName());
        }
        Date currDate = new Date();

        //更新
        boolean ret = proprietaryActivityService.updateById(act, userDTO, currDate);
        if(!ret){
            log.error("更新数据失败");
            throw new BusinessException(ErrorEnum.UPDATE_ERROR8.getCode(),ErrorEnum.UPDATE_ERROR8.getMsg());
        }

        //更新日志
        ret = operateLogService.saveOperateLog(logContent, act.getId().toString(), ConfigAttrType.PROPRIETARY_ACTIVITY_CONFIG.getType(),currentUser);
        if(!ret){
            log.error("更新操作日志失败");
            throw new BusinessException(ErrorEnum.OPERATORLOG_ERROR2.getCode(), ErrorEnum.OPERATORLOG_ERROR2.getMsg());
        }
    }

}
