package com.saicmobility.evcard.md.act.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.saicmobility.evcard.md.mdgoodsservice.api.GoodsModelDetail;
import com.saicmobility.evcard.md.mdgoodsservice.api.ListGoodsModelDetailReq;
import com.saicmobility.evcard.md.mdgoodsservice.api.ListGoodsModelDetailRes;
import com.saicmobility.evcard.md.mdgoodsservice.api.MdGoodsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OutService {

    @Resource
    private MdGoodsService mdGoodsService;

    public Map<Long, String> getGoodsNameMap(List<Long> goodsModelIdList) {
        Map<Long, String> goodsNameMap = new HashMap<>(16);
        ListGoodsModelDetailReq build = ListGoodsModelDetailReq.newBuilder().addAllId(goodsModelIdList).build();
        ListGoodsModelDetailRes listGoodsModelDetailRes = mdGoodsService.listGoodsModelDetail(build);
        if (listGoodsModelDetailRes.getRetCode() != 0 || CollectionUtil.isEmpty(listGoodsModelDetailRes.getGoodsModelDetailList())) {
            return goodsNameMap;
        }
        List<GoodsModelDetail> goodsModelDetailList = listGoodsModelDetailRes.getGoodsModelDetailList();
        goodsNameMap = goodsModelDetailList.stream().collect(Collectors.toMap(GoodsModelDetail::getId, GoodsModelDetail::getGoodsModelName, (o, n) -> n));
        return goodsNameMap;

    }
}
