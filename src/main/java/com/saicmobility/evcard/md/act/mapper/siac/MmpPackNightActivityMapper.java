package com.saicmobility.evcard.md.act.mapper.siac;

import com.saicmobility.evcard.md.act.domain.ActivityCouponDTO;
import com.saicmobility.evcard.md.act.entity.siac.MmpPackNightActivity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface MmpPackNightActivityMapper {

    MmpPackNightActivity selectById(@Param("id") Long id);

    /**
     * 根据活动id，批量获取活动配置信息
     */
    List<MmpPackNightActivity> selectActivitiesByIds(@Param("activityIds") List<Long> activityIds);

    /**
     * 根据指定第三方券活动模板ID批量查询的优惠券配置信息.
     */
    List<ActivityCouponDTO> selectCouponByMmpIds(@Param("mmpIds") Set<Long> mmpIds);

    /**
     * 根据活动id查询活动信息
     * @param thirdActivityId
     * @return
     */
    MmpPackNightActivity selectActivityByThirdActivityId(@Param("thirdActivityId") Long thirdActivityId);
    List<MmpPackNightActivity> selectActivityByThirdActivityIds(@Param("thirdActivityIds") List<Long> thirdActivityIds);
}
