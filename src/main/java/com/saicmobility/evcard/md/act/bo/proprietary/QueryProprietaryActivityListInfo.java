package com.saicmobility.evcard.md.act.bo.proprietary;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/4 15:54
 */
@Data
public class QueryProprietaryActivityListInfo {
    private long id; // 活动ID
    private String activityName; //活动名称
    private String activityTag; //活动标签
    private int activityType; //活动类型
    private int pricingType; //定价类型：1-灵活定价、2-规范定价
    private int activityStatus; //活动状态：1-未开始、2-生效中、3-已过期、4-已作废
    private String signUpStartDate; //报名开始时间 yyyy-MM-dd
    private String signUpEndDate; //报名结束时间 yyyy-MM-dd
}
