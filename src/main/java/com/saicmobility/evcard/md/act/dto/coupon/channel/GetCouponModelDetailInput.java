package com.saicmobility.evcard.md.act.dto.coupon.channel;

import lombok.Data;

@Data
public class GetCouponModelDetailInput {

    /**
     * 分页查询的每页笔数
     */
    private int pageSize;

    /**
     * 分页查询的指定页码，页码从1开始
     */
    private int pageNo;

    /**
     * 产品供应商，默认和对接渠道标识一致
     */
    private String channelId;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 机构号，查询与指定机构签订合约的产品。当该字段有值时表示仅查询该机构的产品信息。insId与productId两个字段必须至少其中一个字段有值
     */
    private String institutionId;

    /**
     * 产品编码，权益产品的唯一标识。当该字段有值时表示仅查询该产品的信息。insId与productId两个字段必须至少其中一个字段有值
     */
    private String productId;
}
