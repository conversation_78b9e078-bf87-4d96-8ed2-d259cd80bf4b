package com.saicmobility.evcard.md.act.job;

import com.saicmobility.evcard.md.act.domain.coupon.CouponConditionDto;
import com.saicmobility.evcard.md.act.entity.MmpThirdCoupon;
import com.saicmobility.evcard.md.act.entity.siac.MmpPackNightActivity;
import com.saicmobility.evcard.md.act.mapper.siac.MmpPackNightActivityMapper;
import com.saicmobility.evcard.md.act.mapper.siac.UserCouponListMapper;
import com.saicmobility.evcard.md.act.service.coupun.IMmpThirdCouponService;
import com.saicmobility.evcard.md.act.service.impl.coupon.channel.ccb.CCBCouponManagerServiceImpl;
import com.saicmobility.evcard.md.act.service.impl.coupon.channel.ccb.ICCBCouponManagerService;
import com.saicmobility.evcard.md.act.util.RedisUtil;
import com.saicmobility.evcard.md.mdactservice.api.ReconciliationFileToReq;
import com.saicmobility.evcard.md.mdactservice.api.ReconciliationFileToRes;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@JobHandler("CCBReconciliationFileJob")
public class CCBReconciliationFileJob extends IJobHandler {

    @Autowired
    private ICCBCouponManagerService iCCBCouponManagerService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("start CCBReconciliationFileJob");
        ReconciliationFileToReq build = ReconciliationFileToReq.newBuilder().build();
        ReconciliationFileToRes reconciliationFileToRes = iCCBCouponManagerService.reconciliationFileToCCB(build);
        int retCode = reconciliationFileToRes.getRetCode();
        String retMsg = reconciliationFileToRes.getRetMsg();
        log.info("start CCBReconciliationFileJob -> retCode : {},retMsg : {}",retCode,retMsg);

        return ReturnT.SUCCESS;
    }
}
