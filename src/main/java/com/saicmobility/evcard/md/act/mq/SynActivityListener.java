package com.saicmobility.evcard.md.act.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.order.ConsumeOrderContext;
import com.aliyun.openservices.ons.api.order.MessageOrderListener;
import com.aliyun.openservices.ons.api.order.OrderAction;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.exception.BusinessException;
import com.saicmobility.evcard.md.act.service.ISynActivityService;
import com.saicmobility.evcard.md.mdactservice.api.GetChannelActDetailRes;
import com.saicmobility.evcard.md.mdstoreservice.api.GetStoreBaseInfoReq;
import com.saicmobility.evcard.md.mdstoreservice.api.GetStoreBaseInfoRes;
import com.saicmobility.evcard.md.mdstoreservice.api.MdStoreService;
import com.saicmobility.evcard.md.mduserservice.api.MdUserService;
import com.saicmobility.evcard.md.mduserservice.api.QuerySecondChannelListReq;
import com.saicmobility.evcard.md.mduserservice.api.QuerySecondChannelListRes;
import com.saicmobility.evcard.md.mduserservice.api.SecondChannelInfo;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SynActivityListener implements MessageOrderListener {

    @Autowired
    private ISynActivityService synActivityService;

    @Autowired
    private MdStoreService mdStoreService;

    @Autowired
    private MdUserService mdUserService;

    public final static String EV_CARD="evcard";

    @Override
    public OrderAction consume(Message message, ConsumeOrderContext context) {

        log.info("tid:{},消费[同步活动门店/车型信息],--接收消息message={},context={}"
                ,  Trace.currentTraceId(),JSON.toJSONString(message), JSON.toJSONString(context));
        try {
            GoodsEventBody goodsEventBody = JSON.parseObject(message.getBody(), GoodsEventBody.class);
            if (goodsEventBody == null) {
                log.error("消息体为空");
                return OrderAction.Success;
            }
            long storeId = goodsEventBody.getStoreId();
            String orgCode = goodsEventBody.getOperOrgCode();
            List<JSONObject> jsonArray = goodsEventBody.getChannelList();
            List<String> channelList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(jsonArray)){
                jsonArray.forEach(jsonObject->{
                    if(jsonObject != null){
                        String channelId = jsonObject.getString("channelId");
                        if(channelId != null){
                            channelList.add(channelId);
                        }
                    }
                });
            }
            QuerySecondChannelListReq buildReq = QuerySecondChannelListReq.newBuilder()
                    .setSecondStatus(2).addAllFirstAppKeys(channelList).build();
            QuerySecondChannelListRes querySecondChannelListRes = mdUserService.querySecondChannelList(buildReq);
            if(querySecondChannelListRes.getRetCode() != 0){
                log.error("tid:{},消费[同步活动门店/车型信息],querySecondChannelList(channelList:{}) 失败",Trace.currentTraceId(),JSONObject.toJSONString(channelList));
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(),ErrorEnum.REQUEST_ERROR.getMsg());
            }
            //二级渠道
            List<SecondChannelInfo> secondChannelList = querySecondChannelListRes.getSecondChannelList();
            if(secondChannelList == null){
                log.error("tid:{},消费[同步活动门店/车型信息],获取二级渠道key列表为空,channelId:{}",Trace.currentTraceId(),JSONObject.toJSONString(channelList));
                secondChannelList = new ArrayList<>();
                // throw new BusinessException(ErrorEnum.PARAM_LACK.getCode(),ErrorEnum.PARAM_LACK.getMsg());
                //return OrderAction.Success;
            }
            //判断是否包含了自营渠道
            boolean selfOperatedChannels =  channelList.contains(EV_CARD);

            //循环querySecondChannelListRes.getSecondChannelList() 从里面获取 secondChannelKey
            List<String> otherChannelKeyList = secondChannelList.stream()
                    .map(SecondChannelInfo::getSecondChannelKey).collect(Collectors.toList());
            // 3-发布车型
            if (goodsEventBody.getAction() == 3) {
                List<Long> mdModelIdList = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(goodsEventBody.getVehicleModelInfoList())){
                    for(JSONObject jsonObject:goodsEventBody.getVehicleModelInfoList()){
                        Long storeVehicleModelId = jsonObject.getLong("storeVehicleModelId");
                        mdModelIdList.add(storeVehicleModelId);
                    }
                }
                synActivityService.doSyncActivityVehicle(storeId, mdModelIdList,orgCode,selfOperatedChannels,otherChannelKeyList);
            //同步门店
            }else if(goodsEventBody.getAction() == 1){
                if (checkStoreStatus(storeId)) {
                    synActivityService.doSyncActivityStore(storeId,selfOperatedChannels,otherChannelKeyList,orgCode);
                }
            }
        }catch (BusinessException e1 ){
            log.error("tid:{},消费[同步活动门店/车型信息],BusinessException消息处理异常！message={},context={}", Trace.currentTraceId()
                    , JSON.toJSONString(message), JSON.toJSONString(context), e1);
            //消费失败
            return OrderAction.Suspend;
        }catch (Exception e) {
            log.error("tid:{},消费[同步活动门店/车型信息],Exception消息处理异常！message={},context={}", Trace.currentTraceId()
                    , JSON.toJSONString(message), JSON.toJSONString(context), e);
            //消费失败
            return OrderAction.Suspend;
        }
        return OrderAction.Success;

    }


    private boolean checkStoreStatus(Long storeId) throws BusinessException {
        if(storeId == null){
            log.info("tid:{},消费[同步活动门店/车型信息],storeId is null",Trace.currentTraceId());
            throw new BusinessException(ErrorEnum.PARAM_LACK.getCode(),ErrorEnum.PARAM_LACK.getMsg());
        }
        //StoreStatus门店状态(1上线，2下线)    StoreNewStatus  门店新状态(1正常、2已关闭)
        GetStoreBaseInfoRes storeBaseInfoV2 = mdStoreService.getStoreBaseInfoV2(GetStoreBaseInfoReq.newBuilder().setId(storeId).build());
        if(storeBaseInfoV2.getRetCode() != 0){
            log.error("tid:{},消费[同步活动门店/车型信息],调用getStoreBaseInfoV2(storeId:{}) 失败",Trace.currentTraceId(),storeId);
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(),ErrorEnum.REQUEST_ERROR.getMsg());
        }
        if(storeBaseInfoV2.getStoreStatus() != 1 ){
            log.info("tid:{},消费[同步活动门店/车型信息],门店已下下线，不进行处理,storeId:{},storeStatus:{}",Trace.currentTraceId(),storeId,storeBaseInfoV2.getStoreStatus());
            return false;
        }
        return true;
    }
}
