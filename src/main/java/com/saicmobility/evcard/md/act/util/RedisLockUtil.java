package com.saicmobility.evcard.md.act.util;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import krpc.rpc.core.ServerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * 分布式锁工具类
 */
@Slf4j
@Component
public class RedisLockUtil {

    private static final String redisPreKey = "2534_mdactservice:lock:";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 加锁、失败时抛异常
     *
     * @param redisKey
     * @return
     * @throws BusinessException
     */
    public RedisLock acquireLock(String redisKey) throws BusinessException {
        String lockKey = redisPreKey + redisKey;
        RedisLock lock = new RedisLock(lockKey, 10 * 1000, ServerContext.get().getTraceContext().getTrace().getTraceId());
        if (!lock.acquire(stringRedisTemplate)) {
            throw new BusinessException(-25259999, "获取锁失败");
        }
        return lock;
    }

    /**
     * 获取redis锁
     * 获取锁失败抛异常
     *
     * @param redisKey 唯一标识
     * @return
     * @throws BusinessException
     */
    public RedisLock acquireLock2(String redisKey) throws BusinessException {
        String lockKey = redisPreKey + redisKey;
        RedisLock lock = new RedisLock(lockKey, 10 * 1000, UUID.randomUUID().toString().replaceAll("-", ""));
        if (!lock.acquire(stringRedisTemplate)) {
            throw new BusinessException(-25259999, "获取锁失败");
        }
        return lock;
    }

    /**
     * 获取redis锁
     * 获取锁失败抛异常
     *
     * @param redisKey 唯一标识
     * @return
     * @throws BusinessException
     */
    public RedisLock acquireLock2(String redisKey, int expiryTimeMillis) throws BusinessException {
        String lockKey = redisPreKey + redisKey;
        RedisLock lock = new RedisLock(lockKey, expiryTimeMillis, UUID.randomUUID().toString().replaceAll("-", ""));
        if (!lock.acquire(stringRedisTemplate)) {
            throw new BusinessException(-25259999, "获取锁失败");
        }
        return lock;
    }


    /**
     * 判断是否已经加锁
     */
    public boolean isLocked(RedisLock lock) {
        return lock.isLocked();
    }


    /**
     * 释放锁 使用的地方要加finally
     *
     * @param lock
     */
    public void releaseLock(RedisLock lock) {
        if (lock != null) {
            lock.releaseLua(stringRedisTemplate);
        }
    }

    /**
     * redis获取连环锁，任何一个没拿到都抛异常
     *
     * @param keys
     * @return
     * @throws BusinessException
     */
    public List<RedisLock> getLockList(Set<String> keys) throws BusinessException {
        List<RedisLock> result = new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(keys)) {
                for (String key : keys) {
                    result.add(acquireLock2(key));
                }
            }
        } catch (BusinessException e) {
            log.error("redis 获取连环锁异常，keys=[{}]", JSON.toJSONString(keys), e);
            throw e;
        } finally {
            if (keys.size() != result.size()) {
                releaseLocks(result);
            }
        }
        return result;
    }

    /**
     * 释放连环锁
     *
     * @param list
     */
    public void releaseLocks(List<RedisLock> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            list.stream().forEach(lock -> {
                try {
                    lock.releaseLua(stringRedisTemplate);
                } catch (Exception e) {
                    log.error("redis 释放连环锁异常", e);
                }
            });
        }
    }

}
