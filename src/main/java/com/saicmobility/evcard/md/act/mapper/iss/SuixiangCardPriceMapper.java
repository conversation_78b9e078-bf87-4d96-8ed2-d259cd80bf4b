package com.saicmobility.evcard.md.act.mapper.iss;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saicmobility.evcard.md.act.entity.iss.SuixiangCardPrice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 随享卡价格表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
public interface SuixiangCardPriceMapper extends BaseMapper<SuixiangCardPrice> {

    List<SuixiangCardPrice> selectInfoByBaseId(@Param("cardBaseId")Long cardBaseId);
}
