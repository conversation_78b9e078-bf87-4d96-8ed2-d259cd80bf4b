package com.saicmobility.evcard.md.act.domain.activity;

import com.saicmobility.evcard.md.mdactservice.api.GetSignUpProprietaryActivityByVehicleModelIdsReq;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Data
public class GetSignUpProprietaryActivityByVehicleModelIdsBo {
    private String orgCode; // 机构编号
    private String planPickupDateTime; // 计划取车时间 yyyyMMddHHmmss
    private String planReturnDateTime; // 计划还车时间 yyyyMMddHHmmss
    private List<Long> storeVehicleModelId; // 门店车型id列表
    private int rentDay; // 租期
    private int isHoliday; // 是否假期 1：假期 2：非假期
    private Long storeId;//门店id

    private String firstOrderCreateTime; // 首单下单时间（非首单计划取车时间）,如果还未下单，则传当前时间 yyyyMMddHHmmss
    private int firstOrderCreateTimeIsHoliday; // 首单下单时间是否假期 1：假期 2：非假期

    public static GetSignUpProprietaryActivityByVehicleModelIdsBo parse(GetSignUpProprietaryActivityByVehicleModelIdsReq req) {
        GetSignUpProprietaryActivityByVehicleModelIdsBo dto = new GetSignUpProprietaryActivityByVehicleModelIdsBo();
        if (!CollectionUtils.isEmpty(req.getStoreVehicleModelIdList())) {
            dto.setStoreVehicleModelId(req.getStoreVehicleModelIdList());
        }
        dto.setOrgCode(req.getOrgCode());
        dto.setPlanPickupDateTime(req.getPlanPickupDateTime());
        dto.setPlanReturnDateTime(req.getPlanReturnDateTime());
        dto.setRentDay(req.getRentDay());
        dto.setIsHoliday(req.getIsHoliday());
        dto.setStoreId(req.getStoreId());
        dto.setFirstOrderCreateTime(req.getFirstOrderCreateTime());
        dto.setFirstOrderCreateTimeIsHoliday(req.getFirstOrderCreateTimeIsHoliday());
        return dto;
    }
}
