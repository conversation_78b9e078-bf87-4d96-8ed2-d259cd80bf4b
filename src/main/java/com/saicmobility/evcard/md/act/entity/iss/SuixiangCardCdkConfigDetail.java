package com.saicmobility.evcard.md.act.entity.iss;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 随享卡兑换码配置详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SuixiangCardCdkConfigDetail对象", description="随享卡兑换码配置详情表")
@TableName("suixiang_card_cdk_config_detail")
public class SuixiangCardCdkConfigDetail extends Model<SuixiangCardCdkConfigDetail> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "随享卡兑换配置表id")
    private Long cardCdkConfigId;

    @ApiModelProperty(value = "随享卡基础表id")
    private Long cardBaseId;

    @ApiModelProperty(value = "随享卡租期表id")
    private Long cardRentId;

    @ApiModelProperty(value = "随享卡价格表id")
    private Long cardPriceId;

    @ApiModelProperty(value = "初始张数")
    private Integer quantity;

    @ApiModelProperty(value = "租期+售价")
    private String actDesc;

    @ApiModelProperty(value = "状态（0=正常 1=已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人名称")
    private String createOperName;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateOperId;

    @ApiModelProperty(value = "更新人名称")
    private String updateOperName;


}
