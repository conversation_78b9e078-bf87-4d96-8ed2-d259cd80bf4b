package com.saicmobility.evcard.md.act.service.welfare;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.dto.welfare.GetWelfareInfoInput;
import com.saicmobility.evcard.md.act.dto.welfare.ReceiveWelfareDto;
import com.saicmobility.evcard.md.act.dto.welfare.ReceiveWelfareInput;
import com.saicmobility.evcard.md.act.dto.welfare.SuiXiangCardWelfareInfo;

import java.util.List;

public interface ISuiXiangCardWelfareService {

    List<SuiXiangCardWelfareInfo> getSuiXiangCardWelfareDetailInfo(GetWelfareInfoInput input);

    ReceiveWelfareDto receiveSuiXiangCardWelfare(ReceiveWelfareInput input) throws BusinessException;
}
