package com.saicmobility.evcard.md.act.mapper.act;

import com.saicmobility.evcard.md.act.domain.packages.PackageNameDto;
import com.saicmobility.evcard.md.act.domain.packages.QueryAvailPackageDto;
import com.saicmobility.evcard.md.act.entity.PackageConfiguration;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 套餐配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
public interface PackageConfigurationMapper extends BaseMapper<PackageConfiguration> {

   List<PackageNameDto> searchPackageName(@Param("name") String name);

   List<PackageConfiguration> getAvailablePackage(@Param("dto") QueryAvailPackageDto dto);

   List<PackageConfiguration> getUpgradePackage(@Param("dto") QueryAvailPackageDto dto);

}
