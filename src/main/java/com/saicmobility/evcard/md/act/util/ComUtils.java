package com.saicmobility.evcard.md.act.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ComUtils {

    /**
     * 如果str的长度超过len，那么截取len位，否则返回原字符串
     *
     * @param str 原字符串
     * @param len 长度限制
     * @return
     */
    public static String splitStr(String str, int len) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        return str.length() > len ? str.substring(0, len) : str;
    }

}
