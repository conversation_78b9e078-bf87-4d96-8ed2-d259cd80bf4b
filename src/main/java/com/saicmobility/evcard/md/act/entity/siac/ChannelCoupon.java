package com.saicmobility.evcard.md.act.entity.siac;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 第三方发放优惠券记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ChannelCoupon对象", description="第三方发放优惠券记录表")
public class ChannelCoupon extends Model<ChannelCoupon> {

    private static final long serialVersionUID = -1415204289379652329L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "渠道唯一标识")
    private String channelId;

    @ApiModelProperty(value = "渠道的用户唯一标识")
    private String channelUserId;

    @ApiModelProperty(value = "渠道的订单唯一标识")
    private String channelOrderId;

    @ApiModelProperty(value = "渠道的活动编号")
    private String channelActivityId;

    @ApiModelProperty(value = "优惠券模板id")
    private Long mmpThirdCouponId;

    @ApiModelProperty(value = "用户优惠券SEQ")
    private Long userCouponSeq;

    @ApiModelProperty(value = "优惠券兑换码")
    private String couponCode;

    @ApiModelProperty(value = "状态（0=正常   1=已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人名称")
    private String createOperName;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateOperId;

    @ApiModelProperty(value = "更新人名称")
    private String updateOperName;

}
