package com.saicmobility.evcard.md.act.service.impl.coupon;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.annotation.ErrorNotify;
import com.saicmobility.evcard.md.act.domain.coupon.CouponConditionDto;
import com.saicmobility.evcard.md.act.dto.coupon.channel.*;
import com.saicmobility.evcard.md.act.dto.coupon.channel.ccb.GetChannelCouponStatusDto;
import com.saicmobility.evcard.md.act.dto.coupon.channel.ccb.GetChannelCouponStatusInput;
import com.saicmobility.evcard.md.act.dto.coupon.channel.ccb.InvalidChannelCouponInput;
import com.saicmobility.evcard.md.act.dto.coupon.channel.ccb.StatementOfAccountCallBackInput;
import com.saicmobility.evcard.md.act.entity.MmpThirdCoupon;
import com.saicmobility.evcard.md.act.entity.iss.MmpThirdActivity;
import com.saicmobility.evcard.md.act.entity.siac.*;
import com.saicmobility.evcard.md.act.enums.channelcoupon.*;
import com.saicmobility.evcard.md.act.enums.coupon.CouponCodeStatusEnum;
import com.saicmobility.evcard.md.act.enums.coupon.CouponStatusEnum;
import com.saicmobility.evcard.md.act.manager.ChannelCouponManager;
import com.saicmobility.evcard.md.act.mapper.siac.MmpPackNightActivityMapper;
import com.saicmobility.evcard.md.act.mapper.siac.UserCouponListMapper;
import com.saicmobility.evcard.md.act.mapper.siac.UserCouponTransactionRecordMapper;
import com.saicmobility.evcard.md.act.mapper.siacPlus.ChannelCouponMapper;
import com.saicmobility.evcard.md.act.service.coupun.IChannelCouponManagerService;
import com.saicmobility.evcard.md.act.service.coupun.IChannelCouponService;
import com.saicmobility.evcard.md.act.service.coupun.ICouponDefService;
import com.saicmobility.evcard.md.act.service.coupun.IMmpThirdCouponService;
import com.saicmobility.evcard.md.act.service.inner.IMmpThirdActivityService;
import com.saicmobility.evcard.md.act.util.*;
import com.saicmobility.evcard.md.mdorderservice.api.CouponUseQueryInfo;
import com.saicmobility.evcard.md.mdorderservice.api.CouponUseQueryReq;
import com.saicmobility.evcard.md.mdorderservice.api.CouponUseQueryRes;
import com.saicmobility.evcard.md.mdorderservice.api.MdOrderService;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.saicmobility.evcard.md.act.constant.Constants.TRANSACTION_RECORD_TYPE_EXCHANGE;
import static com.saicmobility.evcard.md.act.constant.Constants.TRANSACTION_RECORD_TYPE_USE;

@Slf4j
public abstract class AbstractChannelCouponManagerServiceImpl implements IChannelCouponManagerService {

    @Autowired
    private IMmpThirdCouponService mmpThirdCouponService;

    @Autowired
    private MmpPackNightActivityMapper mmpPackNightActivityMapper;

    @Autowired
    private ICouponDefService couponDefService;

    @Autowired
    private IMmpThirdActivityService mmpThirdActivityService;

    @Autowired
    private UserCouponListMapper userCouponListMapper;

    @Autowired
    private IChannelCouponService channelCouponService;
    @Autowired
    private ChannelCouponMapper channelCouponMapper;
    @Autowired
    private MdOrderService mdOrderService;
    @Autowired
    private ChannelCouponManager channelCouponManager;
    @Autowired
    private UserCouponTransactionRecordMapper userCouponTransactionRecordMapper;

    @Autowired
    private RedisLockUtil redisLockUtil;

    @Autowired
    private RedisUtil redisUtil;

    public abstract List<Long> getMmpThirdCouponIds();

    public abstract String getChannelId();

    public abstract int getTotalLimit();

    public String getOrderIdRedisKey(String orderId) {
        return "2534_COUPON_CHANNEL_ORDER_ID:" + getChannelId() + ":" + orderId;
    }

    public String getCouponStockRedisKey(long mmpThirdCouponId) {
        return "2534_COUPON_CODE_STOCK:" + getChannelId() + ":" + mmpThirdCouponId;
    }

    private List<Long> getThirdActivityIdsByThirdCouponIds(Long actionId) {
        List<Long> mmpThirdCouponIds = actionId == null ? getMmpThirdCouponIds() : Arrays.asList(Long.valueOf(actionId));
        List<Long> thirdActivityIds = mmpThirdCouponService.list(new LambdaQueryWrapper<MmpThirdCoupon>().in(MmpThirdCoupon::getId, mmpThirdCouponIds)).stream().map(MmpThirdCoupon::getThirdActivityId)
                .collect(Collectors.toList());
        return mmpPackNightActivityMapper.selectActivityByThirdActivityIds(thirdActivityIds).stream().map(MmpPackNightActivity::getId).collect(Collectors.toList());
    }

    /**
     * 处理渠道对接的产品ID
     * @param productId
     * @return
     * @throws BusinessException
     */
    public Long getMmpThirdCouponId(String productId) throws BusinessException {
        Long mmpThirdCouponId;
        if (StringUtils.isBlank(productId)) {
            throw new BusinessException(CCBCodeEnum.BODY_FIELD_MISSING.getInnerCode(), "产品ID不能为空");
        }

        try {
            mmpThirdCouponId = Long.parseLong(productId);
        } catch (Exception e) {
            throw new BusinessException(CCBCodeEnum.PRODUCT_NO_EXIST.getInnerCode(), "产品ID转换错误");
        }

        if (getMmpThirdCouponIds().contains(mmpThirdCouponId)) {
            return mmpThirdCouponId;
        } else {
            throw new BusinessException(CCBCodeEnum.PRODUCT_NO_EXIST.getInnerCode(), "未配置的优惠券模版");
        }
    }

    @Override
    public GetCouponModelDetailOutput getCouponModelDetail(GetCouponModelDetailInput input) throws BusinessException {
        log.info("tid:{},查询优惠券配置详情列表，input={}", Trace.currentTraceId(), JSON.toJSONString(input));
        if (StringUtils.isBlank(input.getProductId()) && StringUtils.isBlank(input.getInstitutionId())) {
            throw new BusinessException(CCBCodeEnum.BODY_FIELD_MISSING.getInnerCode(), "productId和institutionId不能同时为空");
        }

        List<Long> mmpThirdCouponIds = getMmpThirdCouponIds();

        GetCouponModelDetailOutput getCouponModelDetailOutput = new GetCouponModelDetailOutput();
        if (StringUtils.isNotBlank(input.getProductId())) {
            getCouponModelDetailOutput.setTotal(1);
            getCouponModelDetailOutput.setTotalPage(1);
            getCouponModelDetailOutput.setPageSize(1);
            getCouponModelDetailOutput.setPageNo(1);
            Long mmpThirdCouponId = getMmpThirdCouponId(input.getProductId());
            getCouponModelDetailOutput.setCouponModelDtos(Collections.singletonList(getCouponModelDetail(mmpThirdCouponId)));
        } else {
            getCouponModelDetailOutput.setTotal(mmpThirdCouponIds.size());
            getCouponModelDetailOutput.setTotalPage(mmpThirdCouponIds.size() / input.getPageSize() + 1);
            getCouponModelDetailOutput.setPageSize(input.getPageSize());
            getCouponModelDetailOutput.setPageNo(input.getPageNo());
            List<Long> queryMmpThirdCouponIds = mmpThirdCouponIds.subList((input.getPageNo() - 1) * input.getPageSize(), Math.min(input.getPageNo() * input.getPageSize(), mmpThirdCouponIds.size()));
            List<CouponModelDto> couponModelDto = new ArrayList<>();
            for (Long mmpThirdCouponId : queryMmpThirdCouponIds) {
                couponModelDto.add(getCouponModelDetail(mmpThirdCouponId));
            }
            getCouponModelDetailOutput.setCouponModelDtos(couponModelDto);
        }

        return getCouponModelDetailOutput;
    }

    private CouponModelDto getCouponModelDetail(long mmpThirdCouponId) throws BusinessException {
        CouponModelDto couponModelDto = new CouponModelDto();

        MmpThirdCoupon mmpThirdCoupon = mmpThirdCouponService.getById(mmpThirdCouponId);
        if (mmpThirdCoupon == null) {
            throw new BusinessException(CCBCodeEnum.PRODUCT_NO_EXIST.getInnerCode(), "未查询到优惠券配置");
        }
        BeanUtils.copyProperties(mmpThirdCoupon, couponModelDto);

        MmpPackNightActivity mmpPackNightActivity = mmpPackNightActivityMapper.selectActivityByThirdActivityId(mmpThirdCoupon.getThirdActivityId());
        if (mmpPackNightActivity == null || mmpPackNightActivity.getType() != 6) {
            throw new BusinessException(CCBCodeEnum.PRODUCT_NO_EXIST.getInnerCode(), "未查询到优惠券对应的活动配置");
        }
        BeanUtils.copyProperties(mmpPackNightActivity, couponModelDto);
        couponModelDto.setId(mmpThirdCoupon.getId());
        CouponDef couponDef = couponDefService.getById(mmpThirdCoupon.getCouponSeq());
        if (couponDef == null) {
            throw new BusinessException(CCBCodeEnum.PRODUCT_NO_EXIST.getInnerCode(), "未查询到优惠券定义");
        }
        couponModelDto.setCouponValue(couponDef.getCouponValue());
        MmpThirdActivity mmpThirdActivity = mmpThirdActivityService.getById(mmpThirdCoupon.getThirdActivityId());
        couponModelDto.setCdkExpiresTime(mmpThirdActivity.getCdkExpiresTime());
        return couponModelDto;
    }


    @Override
    public CouponStockDto getCouponStock(String productId, String channelActivityId) throws BusinessException {
        log.info("tid:{},查询优惠券库存，productId={},channelActivityId={}", Trace.currentTraceId(), productId, channelActivityId);
        if (StringUtils.isBlank(productId)) {
            throw new BusinessException(CCBCodeEnum.BODY_FIELD_MISSING.getInnerCode(), "产品ID不能为空");
        }

        Long mmpThirdCouponId = getMmpThirdCouponId(productId);

        CouponStockDto couponStockDto = new CouponStockDto();

        String now = DateUtil.getSystemDate(DateUtil.DATE_TYPE4);

        MmpThirdCoupon mmpThirdCoupon = mmpThirdCouponService.getById(mmpThirdCouponId);
        if (mmpThirdCoupon == null) {
            throw new BusinessException(CCBCodeEnum.PRODUCT_NO_EXIST.getInnerCode(), "未查询到优惠券配置");
        }
        long totalNum = mmpThirdCoupon.getOfferQuantity();
//        long issuedNum = channelCouponService.count(new LambdaQueryWrapper<ChannelCoupon>()
//                .eq(ChannelCoupon::getMmpThirdCouponId, mmpThirdCouponId)
//                .eq(ChannelCoupon::getChannelId, getChannelId())
//        );
        long stockNum = getCouponStockNum(mmpThirdCouponId);;

        couponStockDto.setMmpThirdCouponId(mmpThirdCouponId);
        couponStockDto.setRefreshTime(now);
        couponStockDto.setTotalNum(totalNum);
        couponStockDto.setAvailableNum(stockNum);
//        couponStockDto.setIssuedNum(issuedNum);
        return couponStockDto;
    }

    /**
     * 获取优惠券库存
     *
     * @param mmpThirdCouponId
     * @return
     */
    private long getCouponStockNum(long mmpThirdCouponId) throws BusinessException {
        String couponCodeStockKey = getCouponStockRedisKey(mmpThirdCouponId);
        // 只有未预热库存的时候，才加载库存到redis列表中
        RedisLock couponCodeStockLock = null;
        String isLoadKey = couponCodeStockKey + ":" + "isLoad";
        Integer isLoad = redisUtil.getInt(isLoadKey);
        if (!redisUtil.hasKey(couponCodeStockKey) && (isLoad == null || isLoad == 0)) {
            try {
                log.info("tid:{}, 加载库存尝试获取锁.", Trace.currentTraceId());
                couponCodeStockLock = redisLockUtil.acquireLock2(couponCodeStockKey);
                log.info("tid:{}, 加载库存尝试获取锁成功.", Trace.currentTraceId());
                isLoad = redisUtil.getInt(isLoadKey);
                if (!redisUtil.hasKey(couponCodeStockKey) && (isLoad == null || isLoad == 0)) {
                    MmpThirdCoupon mmpThirdCoupon = mmpThirdCouponService.getById(mmpThirdCouponId);
                    MmpPackNightActivity mmpPackNightActivity = mmpPackNightActivityMapper.selectActivityByThirdActivityId(mmpThirdCoupon.getThirdActivityId());
                    List<CouponConditionDto> couponConditionDtos = userCouponListMapper.queryAllUnissuedCoupon(mmpThirdCoupon.getCouponSeq(), String.valueOf(mmpPackNightActivity.getId()), mmpThirdCoupon.getCouponName());
                    List<Long> userCouponSeqs = couponConditionDtos.stream().map(CouponConditionDto::getUserCouponSeq).collect(Collectors.toList());
                    redisUtil.del(couponCodeStockKey);
                    if (CollectionUtils.isNotEmpty(userCouponSeqs)) {
                        redisUtil.lSet(couponCodeStockKey, new ArrayList<>(userCouponSeqs));
                    }
                    redisUtil.set(isLoadKey, 1);
                }
            } catch (Exception e) {
                log.info("tid:{}, 加载库存尝试获取锁失败.", Trace.currentTraceId());
                return redisUtil.lGetListSize(couponCodeStockKey);
            } finally {
                if (couponCodeStockLock != null) {
                    redisLockUtil.releaseLock(couponCodeStockLock);
                }
            }
        }


        return redisUtil.lGetListSize(couponCodeStockKey);
    }

    @Override
    @ErrorNotify(emails = {"<EMAIL>"})
    public OfferCouponOutput offerCoupon(OfferCouponInput input) throws BusinessException {
        log.info("tid:{}, 发放兑换码入参:{}", Trace.currentTraceId(), JSON.toJSONString(input));
        if (StringUtils.isBlank(input.getProductId())) {
            throw new BusinessException(CCBCodeEnum.BODY_FIELD_MISSING.getInnerCode(), "产品ID不能为空");
        }
        if (StringUtils.isBlank(input.getOrderId())) {
            throw new BusinessException(CCBCodeEnum.BODY_FIELD_MISSING.getInnerCode(), "订单号不能为空");
        }
        if (input.getNum() == 0) {
            throw new BusinessException(CCBCodeEnum.BODY_FIELD_MISSING.getInnerCode(), "发放数量不能为0");
        }


        boolean isRollbackStock = false;
        LocalDateTime now = DateUtil.getSystemDate();

        Long mmpThirdCouponId = getMmpThirdCouponId(input.getProductId());

        ChannelCouponOperationLog channelCouponOperationLog = new ChannelCouponOperationLog();
        channelCouponOperationLog.setOperationType(CCBOperationEnum.CHANNEL_GIVE.getType());
        channelCouponOperationLog.setChannelId(getChannelId());
        channelCouponOperationLog.setChannelUserId(input.getUserId());
        channelCouponOperationLog.setChannelOrderId(input.getOrderId());
        channelCouponOperationLog.setChannelActivityId(input.getDccpAvyId());
        channelCouponOperationLog.setMmpThirdCouponId(mmpThirdCouponId);
        channelCouponOperationLog.setOfferNum(input.getNum());
        channelCouponOperationLog.setOriginRequest(JSON.toJSONString(input));
        channelCouponOperationLog.setCreateOperName(getChannelId());
        channelCouponOperationLog.setUpdateOperName(getChannelId());

        // 校验活动
        MmpThirdCoupon mmpThirdCoupon = mmpThirdCouponService.getById(mmpThirdCouponId);
        if (mmpThirdCoupon == null) {
            throw new BusinessException(CCBCodeEnum.PRODUCT_NO_EXIST.getInnerCode(), "未查询到优惠券配置");
        }

        MmpThirdActivity mmpThirdActivity = mmpThirdActivityService.getById(mmpThirdCoupon.getThirdActivityId());
        if (mmpThirdActivity.getCdkExpiresTime() != null && now.isAfter(mmpThirdActivity.getCdkExpiresTime())) {
            throw new BusinessException(CCBCodeEnum.COUPON_NOT_RECEIVE_DATE.getInnerCode(), "活动有效期已过期");
        }

        // 订单号锁key
        String redisKey = getOrderIdRedisKey(input.getOrderId()) ;
        // 库存列表key
        String couponCodeStockKey = getCouponStockRedisKey(mmpThirdCouponId);


        // 待发放的优惠券
        List<Long> userCouponSeqs = new ArrayList<>();
        RedisLock redisLock = null;
        try {
            log.info("tid:{}, 发放优惠券尝试获取锁：{}", Trace.currentTraceId(), redisKey);
            redisLock = redisLockUtil.acquireLock2(redisKey);
            log.info("tid:{}, 发放优惠券尝试获取锁：{}成功", Trace.currentTraceId(), redisKey);
            OfferCouponOutput offerCouponOutput = new OfferCouponOutput();
            offerCouponOutput.setOrderId(input.getOrderId());
            offerCouponOutput.setAsynFlag(1);
            offerCouponOutput.setUseType(1);
            List<ChannelCoupon> channelCoupons = channelCouponService.list(new LambdaQueryWrapper<ChannelCoupon>()
                    .eq(ChannelCoupon::getChannelOrderId, input.getOrderId())
                    .eq(ChannelCoupon::getChannelId, getChannelId())
            );
            if (!CollectionUtils.isEmpty(channelCoupons)) {
                log.info("tid:{}, 该订单号orderId:{}已存在领取信息直接返回", Trace.currentTraceId(), input.getOrderId());
                offerCouponOutput.setStatus(3);
                offerCouponOutput.setRespFlag(2);
                offerCouponOutput.setCompleteTm(DateUtil.dateToString(channelCoupons.get(0).getCreateTime(), DateUtil.DATE_TYPE4));
                List<ChannelCouponDto> channelCouponDtos = new ArrayList<>();
                for (ChannelCoupon channelCoupon : channelCoupons) {
                    ChannelCouponDto channelCouponDto = new ChannelCouponDto();
                    channelCouponDto.setCouponCode(channelCoupon.getCouponCode());
                    channelCouponDtos.add(channelCouponDto);
                }
                offerCouponOutput.setCoupons(channelCouponDtos);
                return offerCouponOutput;
            }


            log.info("tid:{}, 该订单号orderId:{}不存在领取信息开始发放优惠券", Trace.currentTraceId(), input.getOrderId());
            // 校验领取数量
            if (StringUtils.isNotBlank(input.getUserId())) {
                int count = channelCouponService.count(new LambdaQueryWrapper<ChannelCoupon>()
                        .eq(ChannelCoupon::getChannelUserId, input.getUserId())
                        .eq(ChannelCoupon::getMmpThirdCouponId, mmpThirdCouponId)
                );
                if (count + input.getNum() > getTotalLimit()) {
                    log.warn("tid:{}, 已领取数量：{} + 待领取数量：{}超出了上限：{}", Trace.currentTraceId(), count, input.getNum(), getTotalLimit());
                    throw new BusinessException(CCBCodeEnum.USER_RECEIVE_LIMIT.getInnerCode(), CCBCodeEnum.USER_RECEIVE_LIMIT.getMsg());
                }
            }

            // 校验库存
            long stock = getCouponStockNum(mmpThirdCouponId);
            if (stock <= 0 || stock < input.getNum()) {
                log.info("tid:{}, 库存不足，剩余库存数量为:{}", Trace.currentTraceId(), stock);
                throw new BusinessException(CCBCodeEnum.COUPON_STOCK_ZERO.getInnerCode(), "优惠券库存不足");
            }

            // 扣减库存
            for (int i = 0; i < input.getNum(); i++) {
                Object o = redisUtil.rPop(couponCodeStockKey);
                if (o == null) {
                    log.info("tid:{}, 库存不足，扣减库存失败.", Trace.currentTraceId());
                    throw new BusinessException(CCBCodeEnum.COUPON_STOCK_ZERO.getInnerCode(), "优惠券库存不足");
                }
                userCouponSeqs.add(Long.valueOf(o.toString()));
            }
            log.info("tid:{}, 扣减Redis中优惠券库存准备发放, 待发放的优惠券userCouponSeqs:{}", Trace.currentTraceId(), JSON.toJSONString(userCouponSeqs));

            // 查询优惠券信息详情
            List<CouponConditionDto> couponConditionDtos = userCouponListMapper.selectListByIds(userCouponSeqs);
            channelCoupons = new ArrayList<>();
            List<ChannelCouponDto> channelCouponDtos = new ArrayList<>();
            for (CouponConditionDto couponConditionDto : couponConditionDtos) {
                // 校验是否已经被发放过了
                if (couponConditionDto.getCouponCodeOfferFlag() == 1 || couponConditionDto.getStatus() != 0) {
                    log.info("tid:{}, 该优惠券已发放，无法重复发放，userCouponSeq:{}", Trace.currentTraceId(), couponConditionDto.getUserCouponSeq());
                    throw new BusinessException(CCBCodeEnum.COUPON_OFFERED.getInnerCode(), "该优惠券已被发放过了");
                }
                ChannelCoupon channelCoupon = new ChannelCoupon();
                channelCoupon.setChannelId(getChannelId());
                channelCoupon.setChannelUserId(input.getUserId());
                channelCoupon.setChannelOrderId(input.getOrderId());
                channelCoupon.setChannelActivityId(input.getDccpAvyId());
                channelCoupon.setMmpThirdCouponId(mmpThirdCouponId);
                channelCoupon.setUserCouponSeq(couponConditionDto.getUserCouponSeq());
                channelCoupon.setCouponCode(couponConditionDto.getCouponCode());
                channelCoupon.setCreateOperName(getChannelId());
                channelCoupon.setUpdateOperName(getChannelId());
                channelCoupons.add(channelCoupon);

                ChannelCouponDto channelCouponDto = new ChannelCouponDto();
                channelCouponDto.setCouponCode(couponConditionDto.getCouponCode());
                channelCouponDtos.add(channelCouponDto);
            }
            offerCouponOutput.setRespFlag(1);
            offerCouponOutput.setCoupons(channelCouponDtos);
            offerCouponOutput.setCompleteTm(DateUtil.getSystemDate(DateUtil.DATE_TYPE4));
            offerCouponOutput.setStatus(CCBCouponOfferHandlerStatusEnum.ORDER_PROCESSING_SUCCESS.getStatus());
            channelCouponOperationLog.setSuccessFlag(0);
            channelCouponOperationLog.setOriginResponse(JSON.toJSONString(offerCouponOutput));
            channelCouponManager.saveChannelCoupon(channelCoupons, channelCouponOperationLog);

            return offerCouponOutput;
        } catch (BusinessException e) {
            // 该优惠券已发放，无法重复发放，不需要将优惠券回滚到redis
            if (e.getCode() != CCBCodeEnum.COUPON_OFFERED.getInnerCode()) {
                isRollbackStock = true;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("code", e.getCode());
            map.put("msg", e.getMessage());
            channelCouponOperationLog.setSuccessFlag(1);
            channelCouponOperationLog.setOriginResponse(JSON.toJSONString(map));
            channelCouponManager.saveChannelCoupon(null, channelCouponOperationLog);
            if (e.getCode() == -25259999) {
                log.info("tid:{}, 发放优惠券尝试获取锁：{}失败", Trace.currentTraceId(), redisKey);
                throw new BusinessException(CCBCodeEnum.ORDER_PROCESSING.getInnerCode(), "获取锁失败");
            }
            throw e;
        } catch (Exception e) {
            log.error("tid:{}, 发放优惠券失败", Trace.currentTraceId(), e);
            isRollbackStock = true;
            Map<String, Object> map = new HashMap<>();
            map.put("code", CCBCodeEnum.SYSTEM_ERROR.getInnerCode());
            map.put("msg", e.getMessage());
            channelCouponOperationLog.setSuccessFlag(1);
            channelCouponOperationLog.setOriginResponse(JSON.toJSONString(map));
            channelCouponManager.saveChannelCoupon(null, channelCouponOperationLog);
            throw new BusinessException(CCBCodeEnum.SYSTEM_ERROR.getInnerCode(), "发放优惠券失败");
        } finally {
            if (isRollbackStock) {
                // 归还库存
                log.info("tid:{}, 归还库存:{}", Trace.currentTraceId(), JSON.toJSONString(userCouponSeqs));
                if (CollectionUtils.isNotEmpty(userCouponSeqs)) {
                    redisUtil.lPushAll(couponCodeStockKey, new ArrayList<>(userCouponSeqs));
                    log.info("tid:{}, 归还库存成功:{}", Trace.currentTraceId(), JSON.toJSONString(userCouponSeqs));
                }
            }
            if (redisLock != null) {
                redisLockUtil.releaseLock(redisLock);
            }
        }
    }


    /**
     * 兑换码状态
     *          未兑换:
     *              判断兑换码是否过期
     *                  过期：已过期（需要把兑换码状态字段更新成已过期）
     *                  未过期：判断优惠券是否过期
     *                      过期：已过期（不更新字段）
     *                      未过期：未使用
     *          已兑换：
     *              判断优惠券状态字段
     *                  未使用：判断优惠券有效期字段是否过期
     *                          过期：已过期（需要把优惠券状态字段更新成已过期）
     *                          未过期： 未使用
     *                  已使用：已使用，附上相关使用记录
     *                  已作废：已作废
     *                  已过期：已过期（无，老的job UpdateExpiredCouponJob 已废弃，通过有效期即时判断）
     *          已过期：已过期
     *          已作废：已作废
     *
     * 不需要关注是否发放，不论是否发放，判断逻辑一样，
     * 是否发放通过返回的couponOfferFlag判断
     * @param input
     * @return
     * @throws BusinessException
     */
    @Override
    public GetChannelCouponStatusDto getChannelCouponStatus(GetChannelCouponStatusInput input) throws BusinessException {
        log.info("tid:{}, getChannelCouponStatus入参:{}", Trace.currentTraceId(), JSON.toJSONString(input));
        GetChannelCouponStatusDto result = new GetChannelCouponStatusDto();
        String couponCode = input.getCouponCode();
        if (StringUtils.isBlank(couponCode)) {
            throw new BusinessException(CCBCodeEnum.BODY_FIELD_MISSING.getInnerCode(), CCBCodeEnum.BODY_FIELD_MISSING.getMsg());
        }

        // 查询优惠券
        CouponConditionDto couponConditionDto = userCouponListMapper.findByCouponCode(couponCode);
        if (couponConditionDto == null) {
            log.error("tid:{}, 未查询到优惠券，input={}", Trace.currentTraceId(), JSON.toJSONString(input));
            throw new BusinessException(CCBCodeEnum.COUPON_NO_AUTH.getInnerCode(), CCBCodeEnum.COUPON_NO_AUTH.getMsg());
        }

        String opeartor = "md-act-service";
        String opreateTime = DateUtil.dateToString(LocalDateTime.now(), DateUtil.DATE_TYPE3);

        // 优惠券状态
        Integer couponStatus = couponConditionDto.getStatus();
        // 兑换码状态
        Integer couponCodeStatus = couponConditionDto.getCouponCodeStatus();
        // 兑换码 是否过期标记，只有在优惠券兑换码未兑换时，过期标记才生效
        Date couponCodeExpiresTime = couponConditionDto.getCouponCodeExpiresTime();
        boolean couponCodeIsExpired = (couponCodeExpiresTime != null && couponCodeExpiresTime.before(new Date()));

        // 优惠券 是否过期标记
        String expiresDateString = couponConditionDto.getExpiresDate();
        Date expiresDate = DateUtil.parse(expiresDateString, "yyyy-MM-dd");
        boolean couponIsExpired = (expiresDate != null && expiresDate.before(new Date()));

        result.setCouponCode(couponCode);
        result.setCouponCodeExpireTime(couponCodeExpiresTime);
        // 优惠券状态最后一次变动的时间 FIXME: 优惠券状态最后一次变动的时间，需要从数据库中查询，目前没有找到优惠券状态最后一次变动的时间
        if (StringUtils.isNotBlank(couponConditionDto.getUpdatedTime())) {
            result.setUseTm(DateUtil.parse(couponConditionDto.getUpdatedTime(), "yyyyMMddHHmmssSSS"));
        }

        // 查询渠道优惠券
        ChannelCoupon channelCoupon = channelCouponService.lambdaQuery().eq(ChannelCoupon::getCouponCode, couponCode).one();
        // 优惠券发放第三方标记
        result.setCouponOfferFlag(channelCoupon == null ? 0 : 1);
        result.setAuthId(couponConditionDto.getAuthId());
        result.setCouponConditionDto(couponConditionDto);
        result.setChannelCoupon(channelCoupon);

        if (CouponCodeStatusEnum.NOT_EXCHANGE.getType().equals(couponCodeStatus)) {
            //兑换码未兑换
            // 未兑换，判断兑换码有效期是否过期
            if (couponCodeIsExpired) {
                // 兑换码已过期
                log.info("tid:{} 优惠券兑换码未兑换，兑换码已过期，input={}", Trace.currentTraceId(), JSON.toJSONString(input));
                result.setCouponStatus(null); //未兑换优惠券状态不生效
                result.setCouponCodeStatus(CouponCodeStatusEnum.EXPIRED);
                result.setCouponFinalStatus(CCBCouponStatusEnum.EXPIRED.getCode());
                result.setStatusDesc(CCBCouponStatusEnum.EXPIRED.getMsg());

                // 这里更新 优惠券兑换码状态为已过期
                int update = userCouponListMapper.expireCouponCodeStatus(couponConditionDto.getUserCouponSeq(), couponCodeStatus, opreateTime, opeartor);
                log.info("tid:{},优惠券兑换码已经过期，更新优惠券状态，update={},input={}", Trace.currentTraceId(), update, JSON.toJSONString(input));
            } else {
                // 兑换码未过期

                // 优惠券已过期
                if (couponIsExpired) {
                    log.info("tid:{} 优惠券兑换码未兑换，兑换码未过期，优惠券已过期，input={}", Trace.currentTraceId(), JSON.toJSONString(input));
                    result.setCouponStatus(CouponStatusEnum.EXPIRED);
                    result.setCouponCodeStatus(CouponCodeStatusEnum.NOT_EXCHANGE);
                    result.setCouponFinalStatus(CCBCouponStatusEnum.EXPIRED.getCode());
                    result.setStatusDesc(CCBCouponStatusEnum.EXPIRED.getMsg());
                }else{
                    log.info("tid:{} 优惠券兑换码未兑换，兑换码未过期，优惠券未过期，input={}", Trace.currentTraceId(), JSON.toJSONString(input));
                    result.setCouponCodeStatus(CouponCodeStatusEnum.NOT_EXCHANGE);
                    result.setCouponFinalStatus(CCBCouponStatusEnum.NOT_USED.getCode());
                    result.setStatusDesc(CCBCouponStatusEnum.NOT_USED.getMsg());
                }
            }
        } else if (CouponCodeStatusEnum.HAS_EXCHANGED.getType().equals(couponCodeStatus)) {
            // 兑换码已兑换，参考 CouponConditionDto 使用记录
            if (CouponStatusEnum.NOT_USED.getType().equals(couponStatus)) {
                // 优惠券未使用
                if (couponIsExpired) {
                    // 优惠券已过期
                    log.info("tid:{} 优惠券兑换码已兑换，优惠券未使用，优惠券已过期，input={}", Trace.currentTraceId(), JSON.toJSONString(input));
                    result.setCouponStatus(CouponStatusEnum.EXPIRED); // 已过期
                    result.setCouponCodeStatus(CouponCodeStatusEnum.HAS_EXCHANGED);
                    result.setCouponFinalStatus(CCBCouponStatusEnum.EXPIRED.getCode());
                    result.setStatusDesc(CCBCouponStatusEnum.EXPIRED.getMsg());

                    // 这里更新 优惠券状态为已过期
                    int update = userCouponListMapper.expireCouponStatus(couponConditionDto.getUserCouponSeq(), couponStatus, opreateTime, opeartor);
                    log.info("tid:{},优惠券兑换码已兑换,优惠券已过期，更新优惠券状态，update={},input={}", Trace.currentTraceId(), update, JSON.toJSONString(input));
                } else {
                    // 优惠券未过期
                    log.info("tid:{} 优惠券兑换码已兑换，优惠券未使用，优惠券未过期，input={}", Trace.currentTraceId(), JSON.toJSONString(input));
                    result.setCouponStatus(CouponStatusEnum.NOT_USED);
                    result.setCouponCodeStatus(CouponCodeStatusEnum.HAS_EXCHANGED);
                    result.setCouponFinalStatus(CCBCouponStatusEnum.NOT_USED.getCode());
                    result.setStatusDesc(CCBCouponStatusEnum.NOT_USED.getMsg());
                }
            } else if (CouponStatusEnum.USED.getType().equals(couponStatus)) {
                // 优惠券已使用
                log.info("tid:{} 优惠券兑换码已兑换，优惠券已使用，input={}", Trace.currentTraceId(), JSON.toJSONString(input));
                result.setCouponStatus(CouponStatusEnum.USED);
                result.setCouponCodeStatus(CouponCodeStatusEnum.HAS_EXCHANGED);
                result.setCouponFinalStatus(CCBCouponStatusEnum.HAS_USED.getCode());
                result.setStatusDesc(CCBCouponStatusEnum.HAS_USED.getMsg());

                // 设置已使用的 相关字段
                String orderSeq = couponConditionDto.getOrderSeq();
                if (StringUtils.isNotBlank(orderSeq)) {
                    List<String> orderSeqs = Arrays.asList(orderSeq);
                    CouponUseQueryRes couponUseQueryRes = mdOrderService.couponUseQuery(CouponUseQueryReq.newBuilder().addAllContractIds(orderSeqs).build());
                    if (couponUseQueryRes.getRetCode() == 0) {
                        List<CouponUseQueryInfo> couponUseQueryInfos = couponUseQueryRes.getListList();
                        if (CollectionUtils.isNotEmpty(couponUseQueryInfos)) {
                            CouponUseQueryInfo couponUseQueryInfo = couponUseQueryInfos.get(0);
                            result.setContractId(couponUseQueryInfo.getContractId());
                            result.setTotalAmount(couponUseQueryInfo.getTotalAmount());
                            result.setRealPayAmount(couponUseQueryInfo.getRealAmount());
                            result.setCouponDiscountAmount(couponUseQueryInfo.getCouponDeductionAmount());
                            result.setPickUpStoreId(couponUseQueryInfo.getPickStoreId());
                        }
                    }
                }
            } else if (CouponStatusEnum.DEPRECATED.getType().equals(couponStatus)) {
                // 已兑换后，优惠券已作废，只能是evcard发起的作废
                log.info("tid:{} 优惠券兑换码已兑换，优惠券已作废，input={}", Trace.currentTraceId(), JSON.toJSONString(input));
                result.setCouponStatus(CouponStatusEnum.DEPRECATED);
                result.setCouponCodeStatus(CouponCodeStatusEnum.HAS_EXCHANGED);
                result.setCouponFinalStatus(CCBCouponStatusEnum.EXPIRED.getCode());
                result.setStatusDesc(CCBCouponStatusEnum.EXPIRED.getMsg());

            } else if (CouponStatusEnum.EXPIRED.getType().equals(couponStatus)) {
                // 优惠券已过期
                log.info("tid:{} 优惠券兑换码已兑换，优惠券已过期，input={}", Trace.currentTraceId(), JSON.toJSONString(input));
                result.setCouponStatus(CouponStatusEnum.EXPIRED);
                result.setCouponCodeStatus(CouponCodeStatusEnum.HAS_EXCHANGED);
                result.setCouponFinalStatus(CCBCouponStatusEnum.EXPIRED.getCode());
                result.setStatusDesc(CCBCouponStatusEnum.EXPIRED.getMsg());
            }
        } else if (CouponCodeStatusEnum.DEPRECATED.getType().equals(couponCodeStatus)) {
            // 建行发起作废、生成兑换后evcard发起作废。
            // 兑换码已作废
            log.info("tid:{} 优惠券兑换码已作废，input={}", Trace.currentTraceId(), JSON.toJSONString(input));
            result.setCouponCodeStatus(CouponCodeStatusEnum.DEPRECATED);
            result.setCouponStatus(CouponStatusEnum.getByType(couponStatus));
            // 这里不好区分建行还是evcard发起的作废，统一给 03
            result.setCouponFinalStatus(CCBCouponStatusEnum.EXPIRED.getCode());
            result.setStatusDesc(CCBCouponStatusEnum.EXPIRED.getMsg());
        } else if (CouponCodeStatusEnum.EXPIRED.getType().equals(couponCodeStatus)) {
            // 兑换码已过期
            log.info("tid:{} 优惠券兑换码已过期，input={}", Trace.currentTraceId(), JSON.toJSONString(input));
            result.setCouponStatus(null); // 优惠券状态无效
            result.setCouponCodeStatus(CouponCodeStatusEnum.EXPIRED);
            result.setCouponFinalStatus(CCBCouponStatusEnum.EXPIRED.getCode());
            result.setStatusDesc(CCBCouponStatusEnum.EXPIRED.getMsg());
        }
        log.info("tid:{}, getChannelCouponStatus end 入参:{},出参{}", Trace.currentTraceId(), JSON.toJSONString(input), JSON.toJSONString(result));
        return result;
    }

    /**
     * 作废优惠券
     * 查询优惠券状态
     *      只有未使用的状态，并且未兑换的优惠券，才可以作废
     *
     *
     *
     * @param input
     * @return
     */
    @Override
    public BaseResponse invalidChannelCoupon(InvalidChannelCouponInput input) {
        log.info("tid:{}, invalidChannelCoupon start 入参:{}", Trace.currentTraceId(), JSON.toJSONString(input));
        BaseResponse response = new BaseResponse();
        ChannelCouponOperationLog channelCouponOperationLog;
        try {
            GetChannelCouponStatusInput getChannelCouponStatusInput = new GetChannelCouponStatusInput();
            getChannelCouponStatusInput.setCouponCode(input.getCouponCode());
            GetChannelCouponStatusDto getChannelCouponStatusDto = this.getChannelCouponStatus(getChannelCouponStatusInput);
            // 发放优惠券记录
            ChannelCoupon channelCoupon = getChannelCouponStatusDto.getChannelCoupon();

            String couponFinalStatus = getChannelCouponStatusDto.getCouponFinalStatus();
            if (CCBCouponStatusEnum.NOT_USED.getCode().equals(couponFinalStatus)) {
                String authId = getChannelCouponStatusDto.getAuthId();
                if (StringUtils.isBlank(authId)) {
                    // 兑换码未兑换，允许作废
                    response.setCode(CCBCodeEnum.SUCCESS.getInnerCode());
                    response.setMessage(CCBCodeEnum.SUCCESS.getMsg());
                    channelCouponOperationLog = getChannelCouponOperationLog(input, channelCoupon, response);
                    // 作废优惠券，记录日志
                    CouponConditionDto couponConditionDto = getChannelCouponStatusDto.getCouponConditionDto();
                    channelCouponManager.deprecatedChannelCoupon(couponConditionDto, channelCouponOperationLog);
                    return response;
                } else {
                    // 兑换码已兑换，不允许作废
                    response.setCode(CCBCodeEnum.COUPON_NOT_SUPPORT_INVALID.getInnerCode());
                    response.setMessage(CCBCodeEnum.COUPON_NOT_SUPPORT_INVALID.getMsg());
                }
            } else if (CCBCouponStatusEnum.HAS_USED.getCode().equals(couponFinalStatus)) {
                response.setCode(CCBCodeEnum.COUPON_USED.getInnerCode());
                response.setMessage(CCBCodeEnum.COUPON_USED.getMsg());
            } else if (CCBCouponStatusEnum.DEPRECATED.getCode().equals(couponFinalStatus)) {
                response.setCode(CCBCodeEnum.COUPON_INVALID.getInnerCode());
                response.setMessage(CCBCodeEnum.COUPON_INVALID.getMsg());
            } else if (CCBCouponStatusEnum.EXPIRED.getCode().equals(couponFinalStatus)) {
                response.setCode(CCBCodeEnum.COUPON_INVALID.getInnerCode());
                response.setMessage(CCBCodeEnum.COUPON_INVALID.getMsg());
            }
            // 只记录日志表
            channelCouponOperationLog = getChannelCouponOperationLog(input, channelCoupon, response);
            channelCouponManager.deprecatedChannelCoupon(null, channelCouponOperationLog);
        } catch (BusinessException e) {
            response.setCode(e.getCode());
            response.setMessage(e.getMessage());
            log.error("tid:{},invalidChannelCoupon 优惠券作废失败，input={},e", Trace.currentTraceId(), JSON.toJSONString(input), e);
        } catch (Exception e) {
            response.setCode(CCBCodeEnum.SYSTEM_ERROR.getInnerCode());
            response.setMessage(CCBCodeEnum.SYSTEM_ERROR.getMsg());
            log.error("tid:{},invalidChannelCoupon 优惠券作废失败，input={},e", Trace.currentTraceId(), JSON.toJSONString(input), e);
        }
        log.info("tid:{}, invalidChannelCoupon end 入参:{},res={}", Trace.currentTraceId(), JSON.toJSONString(input),JSON.toJSONString(response));
        return response;
    }

    /**
     * 优惠券作废日志
     * @param input
     * @param channelCoupon
     * @param response
     * @return
     */
    private ChannelCouponOperationLog getChannelCouponOperationLog(InvalidChannelCouponInput input, ChannelCoupon channelCoupon, BaseResponse response) {
        ChannelCouponOperationLog channelCouponOperationLog = new ChannelCouponOperationLog();
        if (channelCoupon != null) {
            BeanUtils.copyProperties(channelCoupon, channelCouponOperationLog);
            channelCouponOperationLog.setChannelCouponId(String.valueOf(channelCoupon.getId()));
        }

        channelCouponOperationLog.setId(null);

        String operation = input.getOperation();
        if (StringUtils.isNotBlank(operation) && operation.equals("2")) {
            channelCouponOperationLog.setOperationType(CCBOperationEnum.CHANNEL_USER_MANUAL_RETURN.getType());
        } else {
            channelCouponOperationLog.setOperationType(CCBOperationEnum.GETCHANNEL_EXPIRE_AUTO_RETURN.getType());
        }
        channelCouponOperationLog.setOriginRequest(JSON.toJSONString(input));
        channelCouponOperationLog.setOriginResponse(JSON.toJSONString(response));
        channelCouponOperationLog.setSuccessFlag(response.getCode() == 0 ? 0 : 1);
        channelCouponOperationLog.setOfferNum(1);

        LocalDateTime now = LocalDateTime.now();
        channelCouponOperationLog.setCreateTime(now);
        channelCouponOperationLog.setUpdateTime(now);
        channelCouponOperationLog.setUpdateOperName("cbb发起作废");
        channelCouponOperationLog.setCreateOperName("cbb发起作废");

        return channelCouponOperationLog;
    }

    /**
     * 状态码回调接口
     *
     * @param userCouponSeq
     * @param type          1-优惠券，2-兑换码
     * @param status        0：未兑换/未使用 1：已兑换/已使用 2：已作废 3: 已过期
     */
    @Override
    public CouponStatusDto callBackCouponStatus(Long userCouponSeq, Integer type, Integer status) {
        CouponStatusDto couponStatusDto = new CouponStatusDto();
        String orderAmt = null;
        String payAmt = null;
        String prftAmt = null;
        String orderSeq = null;
        String pickStoreId = "0000";

        List<CouponConditionDto> couponConditionDtos = userCouponListMapper.selectListByIds(Arrays.asList(userCouponSeq));
        if (CollectionUtils.isNotEmpty(couponConditionDtos)) {
            CouponConditionDto couponConditionDto = couponConditionDtos.get(0);
            String actionId = couponConditionDto.getActionId();

            //查询兑换码信息
            ChannelCoupon channelCoupon = channelCouponMapper.selectOne(new LambdaQueryWrapper<ChannelCoupon>()
                    .eq(ChannelCoupon::getUserCouponSeq, userCouponSeq)
                    .eq(ChannelCoupon::getIsDeleted, 0)
            );

            if (channelCoupon != null) {
                couponStatusDto.setChannelId(channelCoupon.getChannelId());
                couponStatusDto.setChannelOrderId(channelCoupon.getChannelOrderId());
                couponStatusDto.setChannelActivityId(channelCoupon.getChannelActivityId());
                couponStatusDto.setCouponCode(channelCoupon.getCouponCode());
                if (type == 2){
                    //兑换码已兑换，使用建行返回的订单号
                    orderSeq = channelCoupon.getChannelOrderId();
                }

                //订单信息查询
                CouponUseQueryRes couponUseQueryRes = mdOrderService.couponUseQuery(CouponUseQueryReq.newBuilder().addAllContractIds(Arrays.asList(couponConditionDto.getOrderSeq())).build());
                if (couponUseQueryRes.getRetCode() == 0) {
                    List<CouponUseQueryInfo> listList = couponUseQueryRes.getListList();
                    if (CollectionUtils.isNotEmpty(listList)) {
                        CouponUseQueryInfo couponUseQueryInfo = listList.get(0);
                        orderAmt = couponUseQueryInfo.getTotalAmount();
                        payAmt = couponUseQueryInfo.getRealAmount();
                        prftAmt = couponUseQueryInfo.getCouponDeductionAmount();
                        pickStoreId = couponUseQueryInfo.getPickStoreId() == 0 ? "0000" : String.valueOf(couponUseQueryInfo.getPickStoreId());
                        orderSeq = couponUseQueryInfo.getContractId();//优惠券核销使用这个订单号
                        couponUseQueryInfo.getContractId();
                    }
                }
            } else {
                //channelCoupon 为null，代表未同步给第三方，不进行回调
                log.error("优惠券未同步第三方，不进行回调，userCouponSeq->{},actionId->{},type->{},status->{}", userCouponSeq, actionId, type, status);
                return null;
            }
            //查询使用时间
            Integer recordType = null;
            if (type == 1){
                recordType = TRANSACTION_RECORD_TYPE_USE;
                //优惠券，查询使用数据
            } else if (type == 2) {
                //兑换码，查询兑换数据
                recordType = TRANSACTION_RECORD_TYPE_EXCHANGE;
            }
            UserCouponTransactionRecord userCouponTransactionRecord = userCouponTransactionRecordMapper.selectByTypeAndSeq(recordType, String.valueOf(userCouponSeq));
            couponStatusDto.setMmpThirdCouponId(channelCoupon.getMmpThirdCouponId());
            couponStatusDto.setType(type);
            couponStatusDto.setStatus(status);
            couponStatusDto.setOrderAmt(orderAmt);
            couponStatusDto.setPayAmt(payAmt);
            couponStatusDto.setPrftAmt(prftAmt);
            couponStatusDto.setPickStoreId(pickStoreId);
            couponStatusDto.setChannelUserId(orderSeq);
            couponStatusDto.setUseTime(userCouponTransactionRecord != null ? DateUtil.dateToString(userCouponTransactionRecord.getCreateTime(),DateUtil.DATE_TYPE4) : DateUtil.dateToString(new Date(),DateUtil.DATE_TYPE4));

        } else {
            log.error("未查询到合适信息,userCouponSeq->{},type->{},status->{}", userCouponSeq, type, status);
            return null;
        }

        return couponStatusDto;
    }

    @Override
    public List<ReconciliationFileDto> pushReconciliationFile(String pushDate) {
        List<ReconciliationFileDto> reconciliationFileDtos = new ArrayList<>();
        List<Long> thirdActivityIds = getThirdActivityIdsByThirdCouponIds(null);
        if (StringUtils.isBlank(pushDate)){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.DATE,-1);
            pushDate = DateUtils.dateToString(calendar.getTime(), DateUtils.DATE_TYPE5);
        }

        //查询前一天登记过的第三方优惠券，且状态从未使用到已使用的数据
        List<UserCouponTransactionRecord> userCouponTransactionRecords = userCouponTransactionRecordMapper.selectByTypeAndTime(TRANSACTION_RECORD_TYPE_USE, pushDate);
        List<Long> userCouponSeqList = userCouponTransactionRecords.stream().map(UserCouponTransactionRecord::getUserCouponSeq).collect(Collectors.toList());
        Map<Long, UserCouponTransactionRecord> userCouponTransactionRecordMap = userCouponTransactionRecords.stream().collect(Collectors.toMap(UserCouponTransactionRecord::getUserCouponSeq, a -> a));
        if (CollectionUtils.isEmpty(userCouponTransactionRecords)){
            return reconciliationFileDtos;
        }
        List<CouponConditionDto> couponConditionDtos = userCouponListMapper.selectListByIds(userCouponSeqList);
        //校验是否配置，非配置不推送
        couponConditionDtos = couponConditionDtos.stream().filter(s ->{
            String actionIdStr = s.getActionId();
            try {
                Long actionId = StringUtils.isNotBlank(actionIdStr) ? Long.valueOf(actionIdStr) : null;
                return thirdActivityIds.contains(actionId);
            }catch (Exception e){
                return Boolean.FALSE;
            }
        }).collect(Collectors.toList());

        //查询order获取全量订单信息
        List<String> orderSeqList = couponConditionDtos.stream().map(CouponConditionDto::getOrderSeq).collect(Collectors.toList());
        CouponUseQueryRes couponUseQueryRes = mdOrderService.couponUseQuery(CouponUseQueryReq.newBuilder().addAllContractIds(orderSeqList).build());
        Map<String, CouponUseQueryInfo> couponUseQueryInfoMap = new HashMap<>();
        if (couponUseQueryRes.getRetCode() == 0){
            List<CouponUseQueryInfo> couponUseQueryInfoList = couponUseQueryRes.getListList();
            if (CollectionUtils.isNotEmpty(couponUseQueryInfoList)){
                couponUseQueryInfoMap = couponUseQueryInfoList.stream().collect(Collectors.toMap(CouponUseQueryInfo::getOrderNo, a -> a));
            }
        }


        if (CollectionUtils.isNotEmpty(couponConditionDtos)){
            for (CouponConditionDto couponConditionDto : couponConditionDtos) {
                //查询兑换码信息
                ChannelCoupon channelCoupon = channelCouponMapper.selectOne(new LambdaQueryWrapper<ChannelCoupon>()
                        .eq(ChannelCoupon::getUserCouponSeq, couponConditionDto.getUserCouponSeq())
                        .eq(ChannelCoupon::getIsDeleted, 0)
                );
                if (channelCoupon == null){
                    continue;
                }
                String orderSeq = couponConditionDto.getOrderSeq();//couponConditionDto.getOrderSeq()
                String pickUpStoreId = "0000";
                ReconciliationFileDto reconciliationFileDto = new ReconciliationFileDto();
                reconciliationFileDto.setMmpThirdActivityId(channelCoupon.getMmpThirdCouponId().toString());
                reconciliationFileDto.setCouponCode(couponConditionDto.getCouponCode());
                reconciliationFileDto.setStatus(new BigDecimal(couponConditionDto.getStatus()));
                CouponUseQueryInfo couponUseQueryInfo = couponUseQueryInfoMap.get(couponConditionDto.getOrderSeq());
                if (couponUseQueryInfo != null && couponUseQueryInfo.getPickStoreId() != 0){
                    pickUpStoreId = String.valueOf(couponUseQueryInfo.getPickStoreId());
                    orderSeq = couponUseQueryInfo.getContractId();
                }
                reconciliationFileDto.setOrderSeq(orderSeq);
                reconciliationFileDto.setPickUpStoreId(pickUpStoreId);
                UserCouponTransactionRecord userCouponTransactionRecord = userCouponTransactionRecordMap.get(couponConditionDto.getUserCouponSeq());
                reconciliationFileDto.setUseTime(userCouponTransactionRecord != null ? DateUtil.dateToString(userCouponTransactionRecord.getCreateTime(),DateUtil.DATE_TYPE4):couponConditionDto.getUpdatedTime().substring(0,14));
                reconciliationFileDtos.add(reconciliationFileDto);
            }
        }

        return reconciliationFileDtos;
    }

    @Override
    public BaseResponse statementOfAccountCallBack(StatementOfAccountCallBackInput input) throws BusinessException {
        String batchNum = input.getBatchNum();
        String msg = input.getMsg();
        String result = input.getResult();

        if (StringUtils.isBlank(batchNum)) {
            throw new BusinessException(CCBCodeEnum.BODY_FIELD_MISSING.getInnerCode(), "批次号不能为空");
        }
        if (StringUtils.isBlank(result)) {
            throw new BusinessException(CCBCodeEnum.BODY_FIELD_MISSING.getInnerCode(), "结果不能为空");
        }

        return handleStatementOfAccountCallBack(input);

    }

    public abstract BaseResponse handleStatementOfAccountCallBack(StatementOfAccountCallBackInput input);

    @Override
    public List<Long> removeCouponStock(List<Long> userCouponSeqs) throws BusinessException {
        if (CollectionUtils.isEmpty(userCouponSeqs)) {
            throw new BusinessException(CCBCodeEnum.BODY_FIELD_MISSING.getInnerCode(), "userCouponSeqs不能为空");
        }

        List<Long> removedUserCouponSeqs = new ArrayList<>();

        List<CouponConditionDto> couponConditionDtos = userCouponListMapper.selectListByIds(userCouponSeqs);

        for (CouponConditionDto couponConditionDto : couponConditionDtos) {
            try {
                Long couponSeq = couponConditionDto.getCouponSeq();

                MmpPackNightActivity mmpPackNightActivity = mmpPackNightActivityMapper.selectById(Long.valueOf(couponConditionDto.getActionId()));
                if (mmpPackNightActivity == null) {
                    continue;
                }

                Long thirdActivityId = mmpPackNightActivity.getThirdActivityId();

                MmpThirdCoupon mmpThirdCoupon = mmpThirdCouponService.getOne(new LambdaQueryWrapper<MmpThirdCoupon>()
                        .eq(MmpThirdCoupon::getCouponSeq, couponSeq)
                        .eq(MmpThirdCoupon::getThirdActivityId, thirdActivityId)
                        .eq(MmpThirdCoupon::getCouponName, couponConditionDto.getCouponOrigin())
                );

                if (mmpThirdCoupon == null) {
                    continue;
                }
                List<Long> mmpThirdCouponIds = getMmpThirdCouponIds();
                if (!mmpThirdCouponIds.contains(mmpThirdCoupon.getId())) {
                    continue;
                }

                // 扣除库存
                String couponCodeStockKey = getCouponStockRedisKey(mmpThirdCoupon.getId());
                long cnt = redisUtil.lRemove(couponCodeStockKey, 1, couponConditionDto.getUserCouponSeq());
                if (cnt != 0) {
                    removedUserCouponSeqs.add(couponConditionDto.getUserCouponSeq());
                }
            } catch (Exception e) {
                log.error("tid:{}, removeCouponStock error, userCouponSeq:{}", Trace.currentTraceId(), couponConditionDto.getUserCouponSeq(), e);
            }
        }
        return removedUserCouponSeqs;
    }

    @Override
    public void initCouponStock(long mmpThirdCouponId) {
        log.info("tid:{},刷新优惠券库存开始,优惠券模版ID：{}.", Trace.currentTraceId(), mmpThirdCouponId);
        MmpThirdCoupon mmpThirdCoupon = mmpThirdCouponService.getById(mmpThirdCouponId);
        if (mmpThirdCoupon == null) {
            log.info("tid:{}, mmpThirdCouponService.getById({})未查询到.", Trace.currentTraceId(), mmpThirdCouponId);
            return;
        }
        MmpPackNightActivity mmpPackNightActivity = mmpPackNightActivityMapper.selectActivityByThirdActivityId(mmpThirdCoupon.getThirdActivityId());
        if (mmpPackNightActivity == null) {
            log.info("tid:{}, mmpPackNightActivityMapper.selectActivityByThirdActivityId({})未查询到.", Trace.currentTraceId(), mmpThirdCoupon.getThirdActivityId());
            return;
        }
        String couponCodeStockKey = getCouponStockRedisKey(mmpThirdCouponId);
        String isLoadKey = couponCodeStockKey + ":" + "isLoad";
        RedisLock couponCodeStockLock = null;
        for (int i = 1; i <= 5; i++) {
            try {
                Thread.sleep(3000);
                couponCodeStockLock = redisLockUtil.acquireLock2(couponCodeStockKey, 60 * 1000);
                log.info("tid:{}, 获取锁成功, 第{}次刷新优惠券库存开始, 优惠券模版ID：{}.", Trace.currentTraceId(), i, mmpThirdCouponId);

                List<CouponConditionDto> couponConditionDtos = userCouponListMapper.queryAllUnissuedCoupon(mmpThirdCoupon.getCouponSeq(), String.valueOf(mmpPackNightActivity.getId()), mmpThirdCoupon.getCouponName());
                List<Long> userCouponSeqs = couponConditionDtos.stream().map(CouponConditionDto::getUserCouponSeq).collect(Collectors.toList());

                //redisUtil.set(isLoadKey, 0);
                redisUtil.del(couponCodeStockKey);
                if (CollectionUtils.isNotEmpty(userCouponSeqs)) {
                    redisUtil.lSet(couponCodeStockKey, new ArrayList<>(userCouponSeqs));
                }
                redisUtil.set(isLoadKey, 1);
                log.info("tid:{},刷新优惠券库存成功, 优惠券模版ID：{}，共计{}张.", Trace.currentTraceId(), mmpThirdCouponId, userCouponSeqs.size());
                break;
            } catch (Exception e) {
                log.info("tid:{},第{}次刷新优惠券库存失败, 优惠券模版ID：{}.", Trace.currentTraceId(), i, mmpThirdCouponId);
            } finally {
                if (couponCodeStockLock != null) {
                    redisLockUtil.releaseLock(couponCodeStockLock);
                }
            }
        }
    }
}
