package com.saicmobility.evcard.md.act.service;


import com.extracme.evcard.rpc.exception.BusinessException;

import java.util.List;

/**
 * 同步 自营、渠道活动
 */
public interface ISynActivityService {


    /**
     * 同步车型活动信息
     * @param storeId
     * @param mdModelIds
     */
    void  doSyncActivityVehicle(Long storeId, List<Long> mdModelIds,String orgCode,boolean selfOperatedChannels,List<String> channelList) throws BusinessException;



    /**
     * 同步车型活动信息
     * @param storeId  门店id
     * @param channelId  渠道id
     * @param orgCode    机构代码
     */
    void  doSyncActivityStore(Long storeId,boolean selfOperatedChannels, List<String> channelId,String orgCode) throws BusinessException;


}
