package com.saicmobility.evcard.md.act.domain.packages;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 套餐配置列表BO
 * @Author: fsh
 * @Date: 2022/4/15 15:28
 */
@Data
public class PackageConfigBo implements Serializable {

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 运营公司
     */
    private String orgCode;

    /**
     * 门店
     */
    private Long storeId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 车型
     */
    private Long goodsModelId;

    /**
     * 用车天数
     */
    private Integer daysNumber;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 生效时间
     */
    private Date startTime;

    /**
     * 失效时间
     */
    private Date endTime;

    /**
     * 续租是否可用 0否 1是
     */
    private Integer renewUseFlag;

    /**
     * 配置状态 1待生效 2生效中 3已失效 4已作废
     */
    private Integer configState;

}
