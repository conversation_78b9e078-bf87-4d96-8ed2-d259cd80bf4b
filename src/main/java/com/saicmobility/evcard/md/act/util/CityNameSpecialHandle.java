package com.saicmobility.evcard.md.act.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.saicmobility.common.bpe.FlowMap;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 城市名称特殊处理
 */
public class CityNameSpecialHandle {

    public static final String APP_VERSION_5_14_0 = "5.14.0";

    private static final String REMOVE_SUFFIX = "市";

    private static final Map<String,String> abbrMap = new HashMap<>();

    static{
        abbrMap.put("大理白族自治州","大理");
        abbrMap.put("西双版纳傣族自治州","西双版纳");
    }

    public static  List<FlowMap> getAbbrCityNames(List<FlowMap> list){
        if(CollectionUtil.isEmpty(list)){
            return null;
        }
        List<FlowMap> respList = BeanUtil.copyToList(list, FlowMap.class);
        for (FlowMap flowMap : respList) {
            String abbrCityName = getAbbrCityName(flowMap.s("cityName"));
            if(StringUtils.isNotBlank(abbrCityName)){
                flowMap.append("abbrCityName",abbrCityName);
            }
        }
        return respList;
    }


    /**
     *
     * @param cityName
     * @return  abbrCityName
     */
    public static String getAbbrCityName(String cityName){
        if(StringUtils.isBlank(cityName)){
            return cityName;
        }
        if(abbrMap.containsKey(cityName)){
            return abbrMap.get(cityName);
        }
        int index = cityName.lastIndexOf(REMOVE_SUFFIX);
        if(index != -1){
            return cityName.substring(0,index);
        }
        return cityName;
    }

}
