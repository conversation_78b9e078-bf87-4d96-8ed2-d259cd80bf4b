package com.saicmobility.evcard.md.act.service.rest.entity.coupon;

import com.extracme.evcard.rpc.coupon.dto.CouponListDto;
import com.saicmobility.evcard.md.act.util.NumberUtils;
import com.saicmobility.evcard.md.mdactservice.api.CouponViewForApp;
import com.saicmobility.evcard.md.mdactservice.api.GetCouponViewRes;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Optional;

@Data
@EqualsAndHashCode(callSuper = false)
public class GetCouponViewResponse extends CouponListDto {
    public GetCouponViewRes toRes(String mid) {
        CouponViewForApp couponView = CouponViewForApp.newBuilder()
                .setUserCouponSeq(this.getUserCouponSeq())
                .setMid(mid)
                .setCouponType(this.getCouponType())
                .setCouponSeq(this.getCouponSeq())
                .setCouponValue(NumberUtils.getStr(this.getCouponValue()))
                .setTransactionType(Optional.ofNullable(this.getTransactionType()).orElse(this.getCouponType()))
                .setServiceType(this.getServiceType())
                .setDiscountRate(Optional.ofNullable(this.getDiscountRate()).orElse(100))
                .setStatus(this.getStatus())
                .setCouponOrigin(this.getCouponOrigin())
                .setDes(this.getDes())
                .setExpiresDate(this.getExpiresDate()).setStartDate(this.getStartDate())
                .setDurationLimit(NumberUtils.getStr(this.getDurationLimit()))
                .setMinAmount(NumberUtils.getStr(this.getMinAmount()))
                .setCreatedTime(this.getCreatedTime())
                .setRentMethod(this.getRentMethod())
                .setRentMethodGroup(this.getRentMethodGroup())
                .setUseMethod(this.getUseMethod())
                .setHasLimitCondition(this.isDetails())
                .setSpace1(this.getSpace1())
                .addAllSpace2(this.getSpace2())
                .addAllSpace3(this.getSpace3())
                .addAllTags(this.getTags())
                .setRemainTimeTag(this.getRemainTimeTag())
                .setVehicleLimitDesc(this.getVehicleLimitDesc())
                .setAvailability(this.getAvailability())
                //TODO 暂时注释actionId
                //.setActionId(this.getActionId())
                //.setOrgCode(this.getOrgSeq())
                .setOrderNo(this.getOrderSeq())
                .setDiscount(NumberUtils.getStr(this.getDiscount())).build();

        GetCouponViewRes res = GetCouponViewRes.newBuilder().setCoupon(couponView).build();
        return res;
    }
}
