package com.saicmobility.evcard.md.act.job;

import com.saicmobility.evcard.md.act.constant.SystemConst;
import com.saicmobility.evcard.md.act.domain.ReduceActivityBo;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.entity.OperateLog;
import com.saicmobility.evcard.md.act.entity.ReduceActivity;
import com.saicmobility.evcard.md.act.mapper.act.OperateLogMapper;
import com.saicmobility.evcard.md.act.mapper.act.ReduceActivityMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * 每天更新前一天时间到期,自动下线的立减活动的下线日志
  */

@Slf4j
@Component
@JobHandler("InsertActivityLogJob")
public class InsertActivityLogJob extends IJobHandler {

    @Autowired
    private ReduceActivityMapper reduceActivityMapper;

    @Autowired
    private OperateLogMapper operateLogMapper;

    //下线状态 2 已下线
    private static final Integer OFFLINE_STATUS = 2;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("============> 开始执行 下线立减活动日志 定时任务 <============");

        UserDTO jobUser = SystemConst.ELASTIC_JOB_USER;


        List<ReduceActivityBo> offlineActivityList = reduceActivityMapper.queryOfflineReduceActivity();
        for (ReduceActivityBo reduceActivityBo : offlineActivityList) {
            ReduceActivity reduceActivity = new ReduceActivity();
            reduceActivity.setId(reduceActivityBo.getId());
            reduceActivity.setOfflineFlag(OFFLINE_STATUS);
            reduceActivity.setUpdateOperId(jobUser.getId());
            reduceActivity.setUpdateOperName(jobUser.getName());
            reduceActivityMapper.updateById(reduceActivity);

            OperateLog operateLog = new OperateLog();
            operateLog.setForeignId(String.valueOf(reduceActivityBo.getId()));
            operateLog.setOperateType(2);
            operateLog.setOperateContent("立减活动已下线");
            operateLog.setCreateOperId(jobUser.getId());
            operateLog.setCreateOperName(jobUser.getName());
            operateLog.setCreateOperOrgName("环球车享汽车租赁有限公司");
            operateLogMapper.insert(operateLog);
        }

        log.info("============> 结束执行 下线立减活动日志 定时任务 <============");
        return ReturnT.SUCCESS;
    }
}
