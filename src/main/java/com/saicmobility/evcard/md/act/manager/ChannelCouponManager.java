package com.saicmobility.evcard.md.act.manager;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.domain.coupon.CouponConditionDto;
import com.saicmobility.evcard.md.act.entity.siac.ChannelCoupon;
import com.saicmobility.evcard.md.act.entity.siac.ChannelCouponOperationLog;
import com.saicmobility.evcard.md.act.enums.channelcoupon.CCBCodeEnum;
import com.saicmobility.evcard.md.act.mapper.siac.UserCouponListMapper;
import com.saicmobility.evcard.md.act.service.coupun.IChannelCouponOperationLogService;
import com.saicmobility.evcard.md.act.service.coupun.IChannelCouponService;
import com.saicmobility.evcard.md.act.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ChannelCouponManager {

    @Autowired
    private UserCouponListMapper userCouponListMapper;
    @Autowired
    private IChannelCouponService channelCouponService;
    @Autowired
    private IChannelCouponOperationLogService couponOperationLogService;

    @Transactional(rollbackFor = Exception.class)
    public void saveChannelCoupon(List<ChannelCoupon> channelCoupons, ChannelCouponOperationLog channelCouponOperationLog) throws BusinessException {
        if (CollectionUtils.isNotEmpty(channelCoupons)) {
            boolean save = channelCouponService.saveBatch(channelCoupons);
            if (!save) {
                throw new BusinessException(CCBCodeEnum.COUPON_OFFER_FAIL.getInnerCode(), "DB保存优惠券发放记录失败");
            }
            for (ChannelCoupon channelCoupon : channelCoupons) {
                if (userCouponListMapper.updateCouponOfferFlag(channelCoupon.getUserCouponSeq()) != 1) {
                    throw new BusinessException(CCBCodeEnum.COUPON_OFFER_FAIL.getInnerCode(), "更新优惠券发放记录失败，可能原因该优惠券已经被发放过了");
                }
            }
            String channelCouponIds = channelCoupons.stream().map(ChannelCoupon::getId).map(String::valueOf).collect(Collectors.joining(","));
            channelCouponOperationLog.setChannelCouponId(channelCouponIds);
        }
        if (channelCouponOperationLog != null) {
            boolean save = couponOperationLogService.save(channelCouponOperationLog);
            if (!save) {
                throw new BusinessException(CCBCodeEnum.COUPON_OFFER_FAIL.getInnerCode(), "DB保存优惠券发放记录日志失败");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deprecatedChannelCoupon(CouponConditionDto couponConditionDto, ChannelCouponOperationLog channelCouponOperationLog) throws Exception {
        LocalDateTime createTime = channelCouponOperationLog.getCreateTime();
        String now = DateUtil.dateToString(createTime,DateUtil.DATE_TYPE3);

        if (couponConditionDto != null) {
            if (userCouponListMapper.deprecatedCouponCodeStatus(couponConditionDto.getUserCouponSeq(), couponConditionDto.getCouponCodeStatus(), now, channelCouponOperationLog.getCreateOperName()) == 0) {
                log.error("deprecatedChannelCoupon - updateCouponStatus更新失败,couponConditionDto={}", JSON.toJSONString(couponConditionDto));
                throw new Exception("couponConditionDto 更新失败");
            }
        }

        if (channelCouponOperationLog != null) {
            boolean save = couponOperationLogService.save(channelCouponOperationLog);
            if (!save) {
                log.error("deprecatedChannelCoupon - channelCouponOperationLog 新增失败,channelCouponOperationLog={}", JSON.toJSONString(channelCouponOperationLog));
                throw new Exception("channelCouponOperationLog 新增失败");
            }
        }
    }
}
