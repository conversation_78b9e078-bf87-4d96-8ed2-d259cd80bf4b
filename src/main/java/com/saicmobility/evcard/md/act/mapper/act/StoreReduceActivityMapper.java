package com.saicmobility.evcard.md.act.mapper.act;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.saicmobility.evcard.md.act.domain.GetStoreAvailableReduceActivityBo;
import com.saicmobility.evcard.md.act.domain.StoreReduceActivityBo;
import com.saicmobility.evcard.md.act.entity.StoreReduceActivity;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface StoreReduceActivityMapper extends BaseMapper<StoreReduceActivity> {

    List<GetStoreAvailableReduceActivityBo> getStoreAvailableReduceActivity(@Param("storeId") Long storeId,
                                                                            @Param("goodsModelId") Long goodsModelId);

    Page<StoreReduceActivityBo> SearchStoreUnParticipateActivity(@Param("orgCode") String orgCode,
                                                                 @Param("activityStatus") Integer activityStatus,
                                                                 @Param("storeId") Long storeId,
                                                                 @Param("activityName") String activityName,
                                                                 @Param("activityId") Long activityId,
                                                                 Page<StoreReduceActivityBo> page);

    Page<StoreReduceActivityBo> SearchStoreParticipateActivity(@Param("orgCode") String orgCode,
                                                               @Param("activityStatus") Integer activityStatus,
                                                               @Param("storeId") Long storeId,
                                                               @Param("activityName") String activityName,
                                                               @Param("activityId") Long activityId,
                                                               @Param("goodsModelId") Long goodsModelId,
                                                               Page<StoreReduceActivityBo> page);

    List<Long> SearchStoreParticipateGoodsModelId(@Param("activityId") Long activityId,@Param("storeId") Long storeId);

    /**
     *门店参与活动时校验与已参与活动的时间是否重叠
     */
    Integer checkActivityEffectTime(@Param("storeId") Long storeId,
                                    @Param("activityStartTime") String activityStartTime,
                                    @Param("activityEndTime") String activityEndTime);
}
