package com.saicmobility.evcard.md.act.enums.coupon;

/**
 * 优惠券 使用状态
 *  0：未使用 1：已使用 2：已作废 3: 已过期
 *
 */
public enum CouponStatusEnum {
    NOT_USED(0, "未使用"),
    USED(1, "已使用"),
    DEPRECATED(2, "已作废"),
    EXPIRED(3, "已过期"),
    ;
    private Integer type;
    private String msg;

    CouponStatusEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }


    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

    public static CouponStatusEnum getByType(Integer type) {
        for (CouponStatusEnum couponStatusEnum : CouponStatusEnum.values()) {
            if (couponStatusEnum.getType().equals(type)) {
                return couponStatusEnum;
            }
        }
        return null;
    }

}
