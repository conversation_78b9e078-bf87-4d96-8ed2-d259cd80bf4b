package com.saicmobility.evcard.md.act.enums.channelcoupon;

/**
 * 建行优惠券兑换码状态
 */
public enum CCBCouponStatusEnum {
    NOT_USED("00", "未使用"),
    HAS_USED("01", "已使用"),
    // 建行发起的作废、
    DEPRECATED("02", "已作废"),
    // evcard这边的各种失效，包括过期、会员系统发起的作废
    EXPIRED("03", "已失效"),
    ;
    private String code;
    private String msg;

    CCBCouponStatusEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
