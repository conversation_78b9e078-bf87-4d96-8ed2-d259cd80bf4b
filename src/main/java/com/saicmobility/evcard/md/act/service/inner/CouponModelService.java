package com.saicmobility.evcard.md.act.service.inner;

import cn.hutool.core.util.StrUtil;
import com.saicmobility.evcard.md.act.service.extern.GoodsModelService;
import com.saicmobility.evcard.md.act.service.extern.StoreService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CouponModelService {


    private static final String CN_COMMA = "，";
    private static final String CN_STOP = "、";
    public static final String EN_COMMA = ",";

    @Resource
    private GoodsModelService goodsModelService;

    @Resource
    private StoreService storeService;

    /**
     * 根据城市id字符串转换为城市名称字符串
     */
    public String shopCityToDes(String shopCity) {
        if (StrUtil.isBlank(shopCity)) {
            return "";
        }
        StringBuilder shopCityDes = new StringBuilder();
        String[] shopCitys = shopCity.split(EN_COMMA);
        for (int i = 0; i < shopCitys.length; i++) {
            if (i != 0) {
                shopCityDes.append(CN_COMMA);
            }
            Long cityId = Long.parseLong(shopCitys[i]);
            //shopCityDes.append(cityService.getCityNameByCityId(cityId));
        }
        return shopCityDes.toString();
    }


    /**
     * 根据商品车型id字符串转换为商品车型名称字符串
     */
    public String goodsModelIdToDes(String goodsVehicleModel) {
        if (StrUtil.isBlank(goodsVehicleModel)) {
            return "";
        }
        StringBuilder goodsVehicleModelDes = new StringBuilder();
        String[] goodsModelIds = goodsVehicleModel.split(EN_COMMA);
        for (int i = 0; i < goodsModelIds.length; i++) {
            if (i != 0) {
                goodsVehicleModelDes.append(CN_COMMA);
            }
            Long goodsModelId = Long.parseLong(goodsModelIds[i]);
            goodsVehicleModelDes.append(goodsModelService.getGoodsModelNameById(goodsModelId));
        }
        return goodsVehicleModelDes.toString();
    }


    /**
     * 根据门店id字符串转换为门店名称字符串
     */
    public String storeIdToDes(String storeIds) {
        if (StrUtil.isBlank(storeIds)) {
            return "";
        }
        StringBuilder storeNameDes = new StringBuilder();
        String[] goodsModelIds = storeIds.split(EN_COMMA);
        for (int i = 0; i < goodsModelIds.length; i++) {
            if (i != 0) {
                storeNameDes.append(CN_COMMA);
            }
            Long storeId = Long.parseLong(goodsModelIds[i]);
            storeNameDes.append(storeService.getStoreNameByStoreId(storeId));
        }
        return storeNameDes.toString();
    }


}
