package com.saicmobility.evcard.md.act.job;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.gson.Gson;
import com.saicmobility.evcard.md.act.constant.SystemConst;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.entity.BrandModelActivity;
import com.saicmobility.evcard.md.act.entity.OperateLog;
import com.saicmobility.evcard.md.act.entity.PackageConfiguration;
import com.saicmobility.evcard.md.act.enums.BrandModelOperStateEnum;
import com.saicmobility.evcard.md.act.enums.IsDeletedEnum;
import com.saicmobility.evcard.md.act.mapper.act.BrandModelActivityMapper;
import com.saicmobility.evcard.md.act.mapper.act.OperateLogMapper;
import com.saicmobility.evcard.md.act.mapper.act.PackageConfigurationMapper;
import com.saicmobility.evcard.md.act.service.IBrandModelLogService;
import com.saicmobility.evcard.md.act.service.rest.MessagePushRestClient;
import com.saicmobility.evcard.md.act.service.rest.entity.messagepush.SendEmailRequest;
import com.saicmobility.evcard.md.act.util.DateUtils;
import com.saicmobility.evcard.md.mdactservice.api.CurrentUser;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.saicmobility.evcard.md.act.enums.BrandModelOperStateEnum.*;

/**
 * 更新品牌活动状态
 * 套餐到指定日期时分秒 自动生效,失效
 */
@Slf4j
@Component
@JobHandler("UpdateBrandModelActivityJob")
public class UpdateBrandModelActivityJob extends IJobHandler {

    @Autowired
    private BrandModelActivityMapper brandModelActivityMapper;

    @Autowired
    private IBrandModelLogService brandModelLogService;
    @Autowired
    private MessagePushRestClient messagePushRestClient;

    @Value("${ccb.callback.email}")
    private List<String> emails;
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("============> 开始执行 更新品牌活动状态 定时任务 <============");
        Gson gson = new Gson();
        //当前时间
        LocalDateTime nowDate = DateUtils.getSystemDate();
        CurrentUser currentUser = CurrentUser.newBuilder()
                .setUserId(0)
                .setName("系统自动更新")
                .build();
        List<Object> errorList = new ArrayList<>();
        /*============> 查询需要上线的数据 <============*/
        //查询待上线的数据
        LambdaQueryWrapper<BrandModelActivity> onLineWrapper = new LambdaQueryWrapper<BrandModelActivity>()
                .eq(BrandModelActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .eq(BrandModelActivity::getActivityStatus,STATE_TOBE_ONLINE.getType())
                .le(BrandModelActivity::getActivityStartDate,nowDate)
                .ge(BrandModelActivity::getActivityEndDate,nowDate);
        List<BrandModelActivity> onLineActivities = brandModelActivityMapper.selectList(onLineWrapper);
        if (CollectionUtil.isNotEmpty(onLineActivities)){
            onLineActivities.forEach(brandModelActivity->{
                try {
                    //当前时间在品牌车型开始及结束时间之间，状态改为上线中
                    brandModelActivity.setActivityStatus(STATE_ONLINEING.getType());
                    int flag = brandModelActivityMapper.updateById(brandModelActivity);
                    if (flag > 0){
                        //日志记录
                        brandModelLogService.saveOperateLog(brandModelActivity.getId(), BrandModelOperStateEnum.OPER_ONLINE,"原状态为："+STATE_TOBE_ONLINE.getMsg()+" -> 现状态为："+STATE_ONLINEING.getMsg(),currentUser);
                    }

                }catch (Exception e){
                    log.error("定时任务更新品牌活动状态异常:{},ErrMsg:{}",gson.toJson(brandModelActivity),e.getMessage());
                    errorList.add(brandModelActivity);
                }
            });
        }
        /*============> 查询需要上线的数据 <============*/
        //查询上写着、暂停中、待发布的数据
        LambdaQueryWrapper<BrandModelActivity> offLineWrapper = new LambdaQueryWrapper<BrandModelActivity>()
                .eq(BrandModelActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .in(BrandModelActivity::getActivityStatus, Arrays.asList(STATE_TOBE_RELEASED.getType(),STATE_ONLINEING.getType(),STATE_PAUSEING.getType()))
                .lt(BrandModelActivity::getActivityEndDate,nowDate);
        List<BrandModelActivity> offLineActivities = brandModelActivityMapper.selectList(offLineWrapper);
        if (CollectionUtil.isNotEmpty(offLineActivities)){
            offLineActivities.forEach(brandModelActivity->{
                try {
                    //结束时间在当前时间之前的数据，状态改为已下线
                    BrandModelOperStateEnum oldEnum = getEnumByType(2,brandModelActivity.getActivityStatus());
                    brandModelActivity.setActivityStatus(STATE_OFFLINED.getType());
                    int flag = brandModelActivityMapper.updateById(brandModelActivity);
                    if (flag > 0){
                        //日志记录
                        brandModelLogService.saveOperateLog(brandModelActivity.getId(), BrandModelOperStateEnum.OPER_OFFLINE,"原状态为："+oldEnum.getMsg()+" -> 现状态为："+STATE_OFFLINED.getMsg(),currentUser);
                    }
                }catch (Exception e){
                    log.error("定时任务更新品牌活动状态异常:{},ErrMsg:{}",gson.toJson(brandModelActivity),e.getMessage());
                    errorList.add(brandModelActivity);
                }
            });
        }
        /*============> 更新操作已结束 <============*/
        if (CollectionUtil.isNotEmpty(errorList)){
            try {
                SendEmailRequest sendEmailRequest = new SendEmailRequest();
                sendEmailRequest.setSubject("定时任务更新品牌活动状态异常数据");
                sendEmailRequest.setEmailMsg("错误数据:" + gson.toJson(errorList));
                for (String email : emails) {
                    sendEmailRequest.setEmail(email);
                    //messagePushRestClient.syncSendEmail(sendEmailRequest);
                }
            } catch (Exception e) {
                log.error("tid:{}, 发送邮件失败.", Trace.currentTraceId());
            }
        }

        log.info("============> 结束执行 更新套餐状态 定时任务 <============");
        return ReturnT.SUCCESS;
    }

}
