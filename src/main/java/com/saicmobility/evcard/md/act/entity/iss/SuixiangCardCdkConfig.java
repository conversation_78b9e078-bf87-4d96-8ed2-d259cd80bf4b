package com.saicmobility.evcard.md.act.entity.iss;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 随享卡兑换码配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("suixiang_card_cdk_config")
@ApiModel(value="SuixiangCardCdkConfig对象", description="随享卡兑换码配置表")
public class SuixiangCardCdkConfig extends Model<SuixiangCardCdkConfig> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "随享卡基础表id")
    private Long cardBaseId;

    @ApiModelProperty(value = "配置名称")
    private String actName;

    @ApiModelProperty(value = "随享卡名称")
    private String cardName;

    @ApiModelProperty(value = "总张数")
    private Integer totalQuantity;

    @ApiModelProperty(value = "是否生成过二维码标记（0：未生成 1：生成过）")
    private Integer createQrCodeFlag;

    @ApiModelProperty(value = "状态（0=正常 1=已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人名称")
    private String createOperName;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateOperId;

    @ApiModelProperty(value = "更新人名称")
    private String updateOperName;


}
