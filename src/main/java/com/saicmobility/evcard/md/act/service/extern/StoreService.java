package com.saicmobility.evcard.md.act.service.extern;

import com.saicmobility.evcard.md.act.domain.StoreInfoCombobox;
import com.saicmobility.evcard.md.act.service.extern.ConfigLoader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;


/**
 * TODO 替换为全部门店数据的配置缓存 configLoader 定时更新
 */
@Service
public class StoreService {

    /*@Resource
    MdStoreService mdStoreService;*/

    @Autowired
    ConfigLoader configLoader;

    //private Cache<Long, String> storeCache = CacheBuilder.newBuilder().maximumSize(1000).expireAfterWrite(10, TimeUnit.MINUTES).build();

    private static final ConcurrentHashMap<Long, String> storeMap = new ConcurrentHashMap<>();

    public String getStoreNameByStoreId(Long storeId) {
        if (null == storeId) {
            return null;
        }
        /*GetStoreBaseInfoReq req = GetStoreBaseInfoReq.newBuilder().setId(storeId).build();
        GetStoreBaseInfoRes storeBaseInfo = mdStoreService.getStoreBaseInfo(req);
        if (storeBaseInfo.getRetCode() != 0) {
            return null;
        }

        return storeBaseInfo.getStoreName();*/
        return configLoader.getStoreName(storeId);
    }

    /**
     * 根据门店id获取门店所属运营城市
     */
    public Long getStoreCityByStoreId(Long storeId) {
        if (null == storeId) {
            return null;
        }
        /*GetStoreBaseInfoReq req = GetStoreBaseInfoReq.newBuilder().setId(storeId).build();
        GetStoreBaseInfoRes storeBaseInfo = mdStoreService.getStoreBaseInfo(req);
        if (storeBaseInfo.getRetCode() != 0 || storeBaseInfo.getOperCityId() <= 0) {
            return null;
        }
        return storeBaseInfo.getOperCityId();*/
        StoreInfoCombobox store = configLoader.getStore(storeId);
        if (null == store) {
            return null;
        }
        return store.getOperCityId();
    }

    /**
     * 根据门店id获取门店信息
     */
    public String getStoreCityAndNameByStoreId(Long storeId) {
        if (null == storeId) {
            return null;
        }
        /*GetStoreBaseInfoReq req = GetStoreBaseInfoReq.newBuilder().setId(storeId).build();
        GetStoreBaseInfoRes storeBaseInfo = mdStoreService.getStoreBaseInfo(req);
        if (storeBaseInfo.getRetCode() != 0 || StrUtil.isBlank(storeBaseInfo.getOperCityName())
                || StrUtil.isBlank(storeBaseInfo.getStoreName())) {
            return null;
        }*/
        StoreInfoCombobox store = configLoader.getStore(storeId);
        if (null == store) {
            return null;
        }
        String storeCityAndName = store.getOperCityName() + " --" + store.getStoreName();
        return storeCityAndName;
    }

}
