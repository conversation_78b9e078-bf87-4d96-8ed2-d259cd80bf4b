package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 扩展属性配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_configuration_attr")
@ApiModel(value="ConfigurationAttr对象", description="扩展属性配置表")
public class ConfigurationAttr extends BaseEntity { //extends Model<ConfigurationAttr> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "1: 套餐配置 2：立减活动配置")
    private Integer type;

    @ApiModelProperty(value = "配置ID")
    private Long configurationId;

    @ApiModelProperty(value = "属性名")
    private String attrCode;

    @ApiModelProperty(value = "简单属性使用字符串存储")
    private String simpleValue;

    @ApiModelProperty(value = "复杂属性使用json存储")
    private String complexValue;
}
