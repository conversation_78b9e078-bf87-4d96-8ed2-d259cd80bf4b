package com.saicmobility.evcard.md.act.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.entity.OperateLog;
import com.saicmobility.evcard.md.act.mapper.act.OperateLogMapper;
import com.saicmobility.evcard.md.act.mapper.act.ReduceActivityParticipateLogMapper;
import com.saicmobility.evcard.md.act.service.OperateLogService;
import com.saicmobility.evcard.md.act.util.ComUtils;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.mdactservice.api.*;
import com.saicmobility.evcard.md.mdempservice.api.GetUserByUserNameReq;
import com.saicmobility.evcard.md.mdempservice.api.GetUserByUserNameRes;
import com.saicmobility.evcard.md.mdempservice.api.MdEmpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 操作日志表 服务实现类
 */
@Service
public class OperateLogServiceImpl extends ServiceImpl<OperateLogMapper, OperateLog> implements OperateLogService {

    @Resource
    private ReduceActivityParticipateLogMapper reduceActivityParticipateLogMapper;

    @Autowired
    private MdEmpService mdEmpService;

    @Override
    public SearchOperateLogRes searchOperateLog(SearchOperateLogReq req) {
        try {
            SearchOperateLogRes.Builder res = SearchOperateLogRes.newBuilder();
            Page<OperateLog> page = new Page<>(req.getPageNum(), req.getPageSize(), true);
            Page<OperateLog> result = page(page,
                    new LambdaQueryWrapper<OperateLog>()
                            .eq(StrUtil.isNotBlank(req.getForeignId()), OperateLog::getForeignId, req.getForeignId())
                            .eq(req.getOperateType() != 0, OperateLog::getOperateType, req.getOperateType())
                            .orderByDesc(OperateLog::getCreateTime));
            if (CollectionUtil.isEmpty(result.getRecords())) {
                return res.build();
            }

            //获取所有的操作人，并去重
            Set<String> operateNames = result.getRecords().stream().map(OperateLog::getCreateOperName).collect(Collectors.toSet());

            //保存操作人姓名
            Map<String, String> operateNameMap = new HashMap<>();
            for (String operateName:operateNames) {
                GetUserByUserNameReq getUserByUserNameReq = GetUserByUserNameReq.newBuilder().setUserName(operateName).build();
                GetUserByUserNameRes getUserByUserNameRes = mdEmpService.getUserByUserName(getUserByUserNameReq);
                operateNameMap.put(operateName, getUserByUserNameRes.getName());
            }

            List<com.saicmobility.evcard.md.mdactservice.api.OperateLog> list = result.getRecords().stream().map(log -> {
                com.saicmobility.evcard.md.mdactservice.api.OperateLog.Builder builder = com.saicmobility.evcard.md.mdactservice.api.OperateLog.newBuilder();
                builder.setOperateType(log.getOperateType());
                builder.setForeignId(log.getForeignId());
                builder.setCreateTime(DateUtil.dateToString(log.getCreateTime(), DateUtil.DATE_TYPE1));
                builder.setCreateOperName(log.getCreateOperName());
                builder.setOperateContent(log.getOperateContent());
                builder.setCreateOperOrgName(log.getCreateOperOrgName());
                builder.setName(log.getUpdateOperName());
                if(log.getOperateType() == 1){//套餐日志
                    builder.setName(operateNameMap.get(log.getCreateOperName()));
                }
                return builder.build();
            }).collect(Collectors.toList());
            return res.addAllLog(list).setTotal((int) result.getTotal()).build();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return SearchOperateLogRes.failed(25340501, e.getMessage());
        }
    }

    @Override
    public SearchActivityParticipateLogRes searchActivityParticipateLog(SearchActivityParticipateLogReq req) {
        SearchActivityParticipateLogRes.Builder res = SearchActivityParticipateLogRes.newBuilder();
        if (req.getActivityId() < 1) {
            return SearchActivityParticipateLogRes.failed(10050,"活动id不能为空");
        }
        Page<OperateLog> page = new Page<>(req.getPageNum(), req.getPageSize(), true);
        Page<OperateLog> result = reduceActivityParticipateLogMapper.searchAllParticipateLog(req.getStoreId(), req.getActivityId(), String.valueOf(req.getActivityId()), req.getGoodsModelId(), page);
        List<OperateLog> operateLogPages = result.getRecords();
        if (CollectionUtil.isEmpty(operateLogPages)) {
            return res.build();
        }

        List<com.saicmobility.evcard.md.mdactservice.api.OperateLog> list = operateLogPages.stream().map(log -> {
            com.saicmobility.evcard.md.mdactservice.api.OperateLog.Builder builder = com.saicmobility.evcard.md.mdactservice.api.OperateLog.newBuilder();
            return builder.setId(log.getId())
                    .setOperateContent(log.getOperateContent())
                    .setCreateTime(DateUtil.dateToString(log.getCreateTime(), DateUtil.DATE_TYPE1))
                    .setCreateOperName(log.getCreateOperName())
                    .setCreateOperOrgName(log.getCreateOperOrgName())
                    .build();
        }).collect(Collectors.toList());
        return res.addAllLog(list).setTotal((int) result.getTotal()).build();
    }

    @Override
    public boolean saveOperateLog(String log, String foreignId, Integer type, CurrentUser currentUser) {
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(foreignId);
        operateLog.setOperateType(type);
        operateLog.setOperateContent(log);

        operateLog.setCreateOperId(currentUser.getUserId());
        operateLog.setCreateOperName(ComUtils.splitStr(currentUser.getUserName(), 128));
        operateLog.setCreateOperOrgName(ComUtils.splitStr(currentUser.getOrgName(), 128));
        operateLog.setUpdateOperName(ComUtils.splitStr(currentUser.getName(), 128));
        return save(operateLog);
    }

}
