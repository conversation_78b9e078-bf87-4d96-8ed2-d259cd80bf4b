package com.saicmobility.evcard.md.act.entity.siac;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class UserCouponAccount {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_account.id
     *
     * @mbggenerated Tue Apr 16 10:12:44 CST 2019
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_account.user_id
     *
     * @mbggenerated Tue Apr 16 10:12:44 CST 2019
     */
    private String userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_account.account_type
     *
     * @mbggenerated Tue Apr 16 10:12:44 CST 2019
     */
    private Integer accountType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_account.transaction_type
     *
     * @mbggenerated Tue Apr 16 10:12:44 CST 2019
     */
    private Integer transactionType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_account.total_amount
     *
     * @mbggenerated Tue Apr 16 10:12:44 CST 2019
     */
    private BigDecimal totalAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_account.status
     *
     * @mbggenerated Tue Apr 16 10:12:44 CST 2019
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_account.misc_desc
     *
     * @mbggenerated Tue Apr 16 10:12:44 CST 2019
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_account.create_time
     *
     * @mbggenerated Tue Apr 16 10:12:44 CST 2019
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_account.create_oper_id
     *
     * @mbggenerated Tue Apr 16 10:12:44 CST 2019
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_account.create_oper_name
     *
     * @mbggenerated Tue Apr 16 10:12:44 CST 2019
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_account.update_time
     *
     * @mbggenerated Tue Apr 16 10:12:44 CST 2019
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_account.update_oper_id
     *
     * @mbggenerated Tue Apr 16 10:12:44 CST 2019
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_coupon_account.update_oper_name
     *
     * @mbggenerated Tue Apr 16 10:12:44 CST 2019
     */
    private String updateOperName;

}