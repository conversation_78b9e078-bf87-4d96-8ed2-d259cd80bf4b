package com.saicmobility.evcard.md.act.service.rest.entity.coupon;

import com.extracme.evcard.rpc.coupon.dto.CheckCouponOrderInput;
import com.extracme.evcard.rpc.coupon.dto.OrderCouponDto;
import com.saicmobility.evcard.md.act.constant.BusinessConst;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.act.util.NumberUtils;
import com.saicmobility.evcard.md.mdactservice.api.CheckOrderCouponReq;
import com.saicmobility.evcard.md.mdactservice.api.OrderCouponCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
public class CheckOrderCouponRequest extends CheckCouponOrderInput {
    public static CheckOrderCouponRequest fromRes(CheckOrderCouponReq req, String authId) {
        CheckOrderCouponRequest request = new CheckOrderCouponRequest();
        OrderCouponDto orderCouponDto = new OrderCouponDto();
        OrderCouponCondition orderCondition = req.getOrderCouponCondition();
        orderCouponDto.setPickshopCity(orderCondition.getPickUpCity());
        orderCouponDto.setReturnshopCity(orderCondition.getReturnCity());
        orderCouponDto.setAmount(NumberUtils.getBigDecimal(orderCondition.getAmount()));
        orderCouponDto.setActivityType(orderCondition.getActivityType());
        orderCouponDto.setVehicleNo(orderCondition.getVehicleNo());
        orderCouponDto.setOrderSeq(orderCondition.getOrderNo());
        orderCouponDto.setAuthId(authId);
        orderCouponDto.setRentMethod(orderCondition.getRentMethod());
        Date pickDateTime = DateUtil.getDateFromTimeStr(orderCondition.getPickupTime(), DateUtil.DATE_TYPE1);
        orderCouponDto.setPickupTime(pickDateTime);
        if (StringUtils.isNotBlank(orderCondition.getReturnTime())) {
            Date returnDateTime = DateUtil.getDateFromTimeStr(orderCondition.getReturnTime(), DateUtil.DATE_TYPE1);
            orderCouponDto.setReturnTime(returnDateTime);
        }
        // 订单使用时间 直接 使用入参的
        if (orderCondition.getCostTime() != 0) {
            orderCouponDto.setCostTime(orderCondition.getCostTime());
        }

        orderCouponDto.setPickupStoreId(orderCondition.getPickUpStoreId());
        orderCouponDto.setReturnStoreId(orderCondition.getReturnStoreId());
        orderCouponDto.setGoodsModelId(orderCondition.getGoodsModelId());
        orderCouponDto.setServiceType(orderCondition.getServiceType());
        orderCouponDto.setServiceTags(orderCondition.getServiceTags());
        orderCouponDto.setCallService(BusinessConst.CALL_SERVICE_TYPE);
        // 优惠券模板升级
        orderCouponDto.setUseSuiXiangCardId(orderCondition.getUseSuiXiangCardId());
        orderCouponDto.setUesSelfActivityId(orderCondition.getUseSelfActId());
        orderCouponDto.setStoreVehicleModelId(orderCondition.getStoreVehicleModelId());
        orderCouponDto.setUseMemberCardId(orderCondition.getUseMemberCardId());

        request.setOrderCouponCondition(orderCouponDto);
        request.setUserCouponSeq(String.valueOf(req.getUserCouponSeq()));
        return request;
    }
}
