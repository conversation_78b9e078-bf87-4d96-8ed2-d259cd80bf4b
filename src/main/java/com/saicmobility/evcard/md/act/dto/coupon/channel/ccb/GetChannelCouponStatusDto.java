package com.saicmobility.evcard.md.act.dto.coupon.channel.ccb;

import com.saicmobility.evcard.md.act.domain.coupon.CouponConditionDto;
import com.saicmobility.evcard.md.act.entity.siac.ChannelCoupon;
import com.saicmobility.evcard.md.act.enums.channelcoupon.CCBCouponStatusEnum;
import com.saicmobility.evcard.md.act.enums.coupon.CouponCodeStatusEnum;
import com.saicmobility.evcard.md.act.enums.coupon.CouponStatusEnum;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.mdactservice.api.GetChannelCouponStatusForCCBRes;
import lombok.Data;

import java.util.Date;

@Data
public class GetChannelCouponStatusDto {
    private String couponCode; // 券码，优惠券的唯一标识，原值回传
    private CouponCodeStatusEnum couponCodeStatus; //  优惠券兑换码 状态 兑换码状态 0=未兑换 1=已兑换 2=已作废 3=已过期
    private CouponStatusEnum couponStatus; // 优惠券状态  0：未使用 1：已使用 2：已作废 3: 已过期
    /**
     * 综合上面2个状态
     * 券状态 00-可使用 01-已使用 02-已失效-由建行主动调用4.6接口作废 03-已失效-供应商应自身原因自行作废
     *
     * 具体业务逻辑可以自己根据上面2个状态判断
     */
    private String couponFinalStatus;
    private String statusDesc; // 状态描述
    private int couponOfferFlag; // 优惠券是否已发放标记 0：未发放 1：已发放
    private String authId; // 兑换优惠券人 authid
    private Date useTm; // 优惠券状态最后一次变动的时间
    private Date couponCodeExpireTime; // 券码到期时间


    // 已使用
    private String contractId; // 使用优惠券时的核销订单号（用券订单号）
    private String totalAmount; // 使用优惠券时的订单金额，示例：10.00
    private String realPayAmount; // 使用优惠券时的用户支付金额，示例：8.88
    private String couponDiscountAmount; // 使用优惠券时所享受到的优惠金额，示例：1.12
    private long pickUpStoreId; // 取车门店id


    // 辅助使用
    private ChannelCoupon channelCoupon;
    private CouponConditionDto couponConditionDto;

    public GetChannelCouponStatusDto() {
    }

    public GetChannelCouponStatusForCCBRes toGetChannelCouponStatusForCCBRes() {
        GetChannelCouponStatusForCCBRes.Builder builder = GetChannelCouponStatusForCCBRes.newBuilder();
        builder.setCouponCode(this.getCouponCode())
                .setCouponStatus(this.getCouponFinalStatus())
                .setStatusDesc(this.getStatusDesc())
                .setUseTm(DateUtil.dateToString(this.getUseTm(), DateUtil.DATE_TYPE4)); // yyyyMMddhh24miss
                // 非必传
                //.setExpireTm(this.getCouponCodeExpireTime() != null ? DateUtil.dateToString(this.getCouponCodeExpireTime(), DateUtil.DATE_TYPE4) : "99990101000000"); // yyyyMMddhh24miss

        if (CCBCouponStatusEnum.HAS_USED.getCode().equals(this.couponFinalStatus)) {
            builder.setUseOrderId(this.getContractId())
                    .setOrderAmt(this.getTotalAmount())
                    .setPayAmt(this.getRealPayAmount())
                    .setPrftAmt(this.getCouponDiscountAmount())
                    .setStoreId1(this.getPickUpStoreId() + "")
                    .setStoreId2(this.getPickUpStoreId() + "");
        }
        return builder.build();
    }
}
