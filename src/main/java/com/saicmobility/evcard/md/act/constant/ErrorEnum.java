package com.saicmobility.evcard.md.act.constant;

public enum ErrorEnum {
    REQUEST_ERROR(-2534000, "请求失败"),

    PARAM_LACK(-2534001, "参数[%s]缺失"),

    PARAMS_ERR_LACK_MID(-2534002, "用户编号缺失"),

    INVALID_STORE_ID(-2534003, "非法的门店编号"),
    //用户未注册
    MEMBER_NOT_FOUND(-2534001, "用户不存在"),

    INVALID_COUPON_SEQ(-2534003, "无效的优惠券编号"),

    INVALID_APPKEY(-2534004, "无效的二级渠道类型"),

    INVALID_ACTNAME(-2534005, "活动名称为空"),

    INVALID_ACTTYPE(-2534007, "无效的活动类型"),

    INVALID_DIS_LATITUDE(-2534008, "无效的优惠纬度"),

    INVALID_DIS_METHOD(-2534009, "无效的优惠方式"),

    EMPTY_RENTDAYS(-2534010, "最大租期或最小租期为空，请输入"),

    INVALID_COST_BEAR_PARTY(-2534011, "无效的成本承担方"),

    INVALID_COST_ALLOCATION(-2534012, "无效的成本分摊方式"),

    EMPTY_ACT_START_DATE(-2534013, "活动开始时间为空"),

    EMPTY_ACT_END_DATE(-2534014, "活动开结束时间为空"),

    EMPTY_PICK_START_DATE(-2534015, "取车开始时间为空"),

    EMPTY_PICK_END_DATE(-2534016, "取车结束开始时间为空"),

    EMPTY_RETURN_START_DATE(-2534017, "还车开始时间为空"),

    EMPTY_RETURN_END_DATE(-2534018, "还车结束时间为空"),

    OVER_SIZE_UNAVAILABLE_LIST(-2534019, "不可以时间范围超过10条"),

    REPEATED_ERROR1(-2534020, "该配置内容已存在，请不要重复创建！"),

    ADD_ACT_FAILED(-2534021, "向t_activity_channel插入数据失败"),

    ADD_LOG_FAILED(-2534022, "新增数据时记录日志失败"),

    INVALID_ORGCODE(-2534023, "参与机构为空"),

    DISCOUNT_CODE_OVERSIZE(-2534024, "优惠码超过50个字符"),

    EMPTY_CONDITION1(-2534025, "优惠条件1为空，请输入"),

    EMPTY_CONDITION2(-2534026, "优惠条件2为空，请输入"),

    UPDATE_ERROR1(-2534027,"渠道不可更改"),

    UPDATE_ERROR2(-2534028,"活动类型不可更改"),

    UPDATE_ERROR3(-2534029,"优惠纬度不可更改"),

    UPDATE_ERROR4(-2534030,"优惠方式不可更改"),

    UPDATE_ERROR5(-2534031,"是否限制优惠不可更改"),

    UPDATE_ERROR6(-2534032,"成本承担方不可更改"),

    UPDATE_ERROR7(-2534033,"逻辑删除原记录失败"),

    UPDATE_ERROR8(-2534034,"更新数据失败"),

    OPERATORLOG_ERROR1(-2534035,"将被逻辑删除的数据日志增加到新数据的日志中失败"),

    OPERATORLOG_ERROR2(-2534036,"更新操作日志失败"),

    DELETE_ERROR1(-2534037,"根据id未查询到数据, 请重新输入"),

    DELETE_ERROR2(-2534038,"逻辑删除数据失败"),

    INVALID_ACTSTATUS(-2534040, "活动状态不合法，请重新输入"),

    PAGENUM_ERROR(-2534041, "当前页码缺失"),

    PAGESIZE_ERROR(-2534042, "每页显示条数缺失"),

    LIST_ERROR1(-2534043, "未查询到营销活动（分页）"),

    GET_DETAIL_ERROR1(-2534044, "未查询到渠道活动详情"),

    DISCOUNT_ERROR(-2534045, "优惠条件不合法,请检查 活动类型、优惠方式、优惠条件1、优惠条件2"),

    DATE_ERROR(-2534046, "结束时间小于开始时间"),

    UNAVAILABLETIME_OVER(-2534047, "该不可用时间超出取车时间范围"),

    GET_DETAIL_ERROR2(-2534048, "根据id获取失败"),

    REPEATED_ERROR2(-2534049, "该配置内容已存在，不需要更新！"),

    UPDATE_ERROR9(-2534039, "成本分摊方式不可更改"),

    DATA_ERROR(-2534050, "商家承担、平台承担百分比设置不合法，请检查"),

    DISCOUNT_CODE_NULL(-2534051, "优惠码前缀为空，请配置"),

    SYNC_QINGLU_FAIL(-2534052, "营销同步给擎路失败"),

    DISCOUNT_CODE_ILLEAGL(-2534053, "优惠码无效"),

    EMPTY_ACTIVITY_TAG(-2534054, "活动标签为空"),

    EMPTY_VEHICLE_MODEL_IDS(-2534055, "车型id列表为空"),

    EMPTY_SIGN_UP_START_DATE(-2534056, "报名开始时间为空"),

    EMPTY_SIGN_UP_END_DATE(-2534057, "报名开结束时间为空"),

    EMPTY_PICK_UP_DATE(-2534058, "取车时间为空"),

    EMPTY_RETURN_DATE(-2534059, "还车时间为空"),

    EMPTY_FULL_MINUS_STANDARD_PRICING(-2534060, "满减规范定价为空"),

    EMPTY_FULL_MINUS_FLEXIBLE_PRICING(-2534061, "满减灵活定价为空"),

    EMPTY_DISCOUNT_STANDARD_PRICING(-2534062, "打折规范定价为空"),

    EMPTY_DISCOUNT_FLEXIBLE_PRICING(-2534063, "打折灵活定价为空"),

    CHAR_TOO_LONG(-2534064, "字符串长度太长"),

    INVALID_PRICING_TYPE(-2534065, "无效的定价类型"),

    EMPTY_MIN_RENT_DAYS(-2534066, "最小租期有误，请输入"),

    INVALID_HOLIDAYS_TYPE(-2534067, "无效的节假日类型"),

    ADD_ACTIVITY_FAILED(-2534068, "向t_proprietary_activity插入数据失败"),

    INVALID_ACTIVITY_ID(-2534069, "无效的活动id"),

    NO_FOUNT_ACTIVITY_DETAIL(-2534070, "未查询到自营活动详情"),

    HAS_CANCEL(-2534071, "该自营活动已作废,不能重复作废"),

    CANCEL_FAIL(-2534072, "作废自营活动失败"),

    NOT_MODIFY(-2534073, "只有未开始状态才可修改详情"),

    NO_FOUNT_SIGN_UP_ACTIVITY_DETAIL(-2534074, "未查询到报名自营活动详情"),

    INVALID_SIGN_UP_ACTIVITY_ID(-2534075, "无效的报名id"),

    DISCOUNT_CODE_REPEAT(-2534076, "该渠道已存在相同优惠码!"),

    ACT_EXPIRE_NOT_QUIT(-2534100, "该自营活动已过期，不可退出"),

    ACT_CANCEL_NOT_QUIT(-2534101, "该自营活动已作废，不可退出"),

    INVALID_ACTIVITY_SIGNUP_ID(-2534102, "无效的活动报名id"),

    INVALID_SAME_DAYS(-2534103, "存在天数相同的数据"),

    INVALID_DISCOUNT_AMOUNT(-2534104, "优惠金额，后一项必须大于等于前一项"),

    INVALID_DISCOUNT(-2534105, "折扣值，后一项必须小于等于前一项"),

    INVALID_ZERO_DAYS(-2534106, "天数不合法，必须大于0"),

    FAILED_QUERY1(-2534107, "根据报名id查自营活动id失败"),

    FAILED_QUERY2(-2534108, "调用getProprietaryActivityInfo() 失败"),

    HELLO_ERROR1(-2534109,"Hello/铁行/车生活/同程租车活动的活动类型不能是减至"),

    HELLO_ERROR2(-2534110, "Hello/铁行/车生活/同程租车活动的优惠纬度不能是订单整单"),

    HELLO_ERROR3(-2534111, "Hello/铁行/车生活/同程租车活动的优惠方式不能是针对金额"),

    HELLO_ERROR4(-2534112, "Hello/铁行/车生活/同程租车活动的成本承担方必须是商家全部承担"),

    HELLO_ERROR5(-2534113, "Hello/铁行/车生活/同程租车活动为满减时 只能是 满天减...金额"),

    HELLO_ERROR6(-2534114, "Hello/铁行/车生活/同程租车活动为满减时 只能是 满天打...折"),

    DATA_FORMAT_ERROR(-2534115, "数据装换失败"),

    EMPTY_MAX_RENTDAYS(-2534116, "最大租期为空，请输入"),
    EMPTY_MIN_RENTDAYS(-2534117, "最小租期为空，请输入"),
    ZERO_RENT_DAYS_ERROR(-2534118, "请输入大于等于0的整数"),
    MAX_RENT_DAYS_COMPARE_ERROR(-2534119, "最大租期请输入大于等于最小租期的整数"),
    EMPTY_SAME_DAY_USE_FLAG_ERROR(-2534120, "是否仅限当日取车为空，请选择"),

    EMPTY_STORE_IDS(-2534121, "门店id列表为空"),
    GET_WELFARE_FAIL(-2534122, "获取福利失败"),

    CDK_HAS_EXCHANGE(-2534123, "该兑换码已被领取"),
    ALREADY_RECEIVED(-2534124, "您已领过该卡券啦"),
    MOBILE_HAS_REACHED_LIMIT(-2534125, "该手机号已达领取次数上限！"),
    CDK_HAS_INVALIDATION(-2534126, "码已失效"),
    SUBMIT_FREQUENT(-2534127, "请勿频繁提交！"),

    NOT_FOUND_WELFARE_INFO(-2534128, "未查询到福利信息"),
    CDK_HAS_EXPIRED(-2534129, "兑换码已过期")
    ;

    ErrorEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 异常描述信息
     */
    private Integer code;

    /**
     * 描述
     */
    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public String getFormatMsg(String name) {
        return String.format(msg, name);
    }


    /**
     * 获取福利枚举类
     * @param code
     * @return
     */
    public static ErrorEnum getWelfareFailEnumByCode(Integer code) {
        ErrorEnum errCodeEnum = ErrorEnum.GET_WELFARE_FAIL;
        switch (code) {
            case -3009002:
            case -3009003:
                errCodeEnum = ErrorEnum.CDK_HAS_EXCHANGE;
                break;
            case -3015001:
                errCodeEnum = ErrorEnum.CDK_HAS_EXCHANGE;
                break;
            case -3015002:
                errCodeEnum = ErrorEnum.ALREADY_RECEIVED;
                break;
            case -3015003:
                errCodeEnum = ErrorEnum.MOBILE_HAS_REACHED_LIMIT;
                break;
            case -3015004:
                errCodeEnum = ErrorEnum.CDK_HAS_INVALIDATION;
                break;
            case -3009004:
                errCodeEnum = ErrorEnum.CDK_HAS_EXPIRED;
                break;
            case -3009005:
            case -3015006:
                errCodeEnum = ErrorEnum.SUBMIT_FREQUENT;
        }
        return errCodeEnum;
    }
}
