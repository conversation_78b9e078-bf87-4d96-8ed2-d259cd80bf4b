package com.saicmobility.evcard.md.act.annotation;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.common.envconfig.EnvGlobal;
import com.saicmobility.evcard.md.act.service.rest.MessagePushRestClient;
import com.saicmobility.evcard.md.act.service.rest.entity.messagepush.SendEmailRequest;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class ErrorNotifyAspect {

    @Autowired
    private MessagePushRestClient messagePushRestClient;

    @AfterThrowing(pointcut = "@annotation(errorNotify)", throwing = "e")
    public void afterThrowing(ErrorNotify errorNotify, Exception e) {
        try {
            if (!EnvGlobal.globalProfile.equals("prd")) {
                return;
            }
            if (e instanceof BusinessException) {
                return;
            }
            String[] emails = errorNotify.emails();
            SendEmailRequest sendEmailRequest = new SendEmailRequest();
            sendEmailRequest.setSubject("md-act-service服务异常，tid:" + Trace.currentTraceId());
            sendEmailRequest.setEmailMsg("异常消息：" + e.getMessage() + "\n" + "异常:" + JSON.toJSONString(e.getStackTrace()));
            for (String email : emails) {
                sendEmailRequest.setEmail(email);
                messagePushRestClient.syncSendEmail(sendEmailRequest);
            }
        } catch (Exception ex) {
            log.info("ErrorNotifyAspect error.", ex);
        }
    }

}
