package com.saicmobility.evcard.md.act.service;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.bo.proprietary.GetProprietaryActivityInfoBo;
import com.saicmobility.evcard.md.act.bo.proprietary.GetSignUpProprietaryActivityInfoBo;
import com.saicmobility.evcard.md.act.bo.proprietary.QueryProprietaryActivityListBo;
import com.saicmobility.evcard.md.act.bo.proprietary.QuerySignUpProprietaryActivityListBo;
import com.saicmobility.evcard.md.act.dto.proprietary.AddProprietaryActivityDto;
import com.saicmobility.evcard.md.act.dto.proprietary.QueryProprietaryActivityListDto;
import com.saicmobility.evcard.md.act.dto.proprietary.UpdateProprietaryActivityDto;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivity;
import com.saicmobility.evcard.md.act.service.inner.IBaseService;
import com.saicmobility.evcard.md.mdactservice.api.CancelProprietaryActivityReq;
import com.saicmobility.evcard.md.mdactservice.api.CancelProprietaryActivityRes;
import com.saicmobility.evcard.md.mdactservice.api.GetSignUpProprietaryActivityInfoReq;
import com.saicmobility.evcard.md.mdactservice.api.QuerySignUpProprietaryActivityListByActivityIdReq;

/**
 * <p>
 * 自营活动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
public interface IProprietaryActivityService extends IBaseService<ProprietaryActivity> {
    /**
     * 新增自营活动
     *
     * @param dto
     * @return
     */
    void addProprietaryActivity(AddProprietaryActivityDto dto) throws BusinessException;

    /**
     * 新增自营活动
     *
     * @param dto
     * @return
     */
    QueryProprietaryActivityListBo queryProprietaryActivityList(QueryProprietaryActivityListDto dto) throws BusinessException;

    /**
     * 根据id查询自营活动详情
     *
     * @param id
     * @return
     * @throws BusinessException
     */
    GetProprietaryActivityInfoBo getProprietaryActivityInfo(long id) throws BusinessException;

    /**
     * 作废自营活动
     *
     * @param req
     * @return
     * @throws BusinessException
     */
    CancelProprietaryActivityRes cancelProprietaryActivity(CancelProprietaryActivityReq req);

    /**
     * 修改自营活动
     *
     * @param dto
     * @return
     */
    void updateProprietaryActivity(UpdateProprietaryActivityDto dto) throws BusinessException;

    /**
     * 根据活动id 查询报名自营活动列表
     *
     * @param querySignUpProprietaryActivityListByActivityIdReq
     * @return
     * @throws BusinessException
     */
    QuerySignUpProprietaryActivityListBo querySignUpProprietaryActivityListByActivityId(QuerySignUpProprietaryActivityListByActivityIdReq querySignUpProprietaryActivityListByActivityIdReq) throws BusinessException;

    /**
     * 获取报名后自营活动详情
     *
     * @param req
     * @return
     * @throws BusinessException
     */
    GetSignUpProprietaryActivityInfoBo getSignUpProprietaryActivityInfo(GetSignUpProprietaryActivityInfoReq req) throws BusinessException;

}
