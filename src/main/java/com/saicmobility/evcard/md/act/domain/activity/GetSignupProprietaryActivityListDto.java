package com.saicmobility.evcard.md.act.domain.activity;

import com.saicmobility.evcard.md.mdactservice.api.GetSignupProprietaryActivityListReq;
import lombok.Data;

@Data
public class GetSignupProprietaryActivityListDto {
    private String activityName; // 活动名称
    private String activityTag; // 活动标签
    private int activityStatus; // 活动状态：1-未开始、2-生效中、3-已过期、4-已作废
    private int activityType; // 活动类型：1-满减、2-打折
    private int pricingType; // 定价类型：1-灵活定价、2-规范定价
    private String signUpStartDate; //报名开始时间 yyyy-MM-dd
    private String signUpEndDate; //报名结束时间 yyyy-MM-dd
    private int signupStatus; // 报名状态 1-未参加 2-已参加
    private String orgCode; // 机构编号，必传
    private String storeId;//门店id
    private int pageNum; // 页码
    private int pageSize; // 每页大小


    public static GetSignupProprietaryActivityListDto parse(GetSignupProprietaryActivityListReq req) {
        GetSignupProprietaryActivityListDto dto = new GetSignupProprietaryActivityListDto();
        dto.setActivityName(req.getActivityName());
        dto.setActivityTag(req.getActivityTag());
        dto.setActivityStatus(req.getActivityStatus());
        dto.setActivityType(req.getActivityType());
        dto.setPricingType(req.getPricingType());
        dto.setSignUpStartDate(req.getSignUpStartDate());
        dto.setSignUpEndDate(req.getSignUpEndDate());
        dto.setSignupStatus(req.getSignupStatus());
        dto.setOrgCode(req.getOrgCode());
        dto.setStoreId(req.getStoreId());
        dto.setPageNum(req.getPageNum());
        dto.setPageSize(req.getPageSize());
        return dto;
    }
}
