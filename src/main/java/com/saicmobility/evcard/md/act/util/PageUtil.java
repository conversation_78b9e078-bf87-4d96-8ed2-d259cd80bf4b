package com.saicmobility.evcard.md.act.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.saicmobility.evcard.md.act.domain.common.PageResult;

public class PageUtil {

    public static <T> PageResult<T> trans(Page<T> page) {
        PageResult<T> tPageResult = new PageResult<>();
        tPageResult.setDatas(page.getRecords());
        tPageResult.setTotal(page.getTotal());

        return tPageResult;
    }
}
