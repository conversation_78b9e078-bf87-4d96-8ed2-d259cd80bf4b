package com.saicmobility.evcard.md.act.service.suixiangcard;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.bo.suixiangcard.SuiXiangCardThirdSaleInfoBo;
import com.saicmobility.evcard.md.act.dto.ServiceFeeCfgDto;
import com.saicmobility.evcard.md.act.dto.suixiangcard.SuiXiangCardThirdSaleInfoInput;
import com.saicmobility.evcard.md.act.dto.suixiangcard.SuixiangCardCdkThirdSaleDto;
import com.saicmobility.evcard.md.act.dto.welfare.SuiXiangCardWelfareInfo;
import com.saicmobility.evcard.md.act.entity.iss.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
public class SuixiangCardServiceImpl implements ISuixiangCardService {

    @Autowired
    private ISuixiangCardCdkService suixiangCardCdkService;

    @Autowired
    private ISuixiangCardRentDaysService suixiangCardRentDaysService;

    @Autowired
    private ISuixiangCardBaseService suixiangCardBaseService;

    @Autowired
    private ISuixiangCardUseService suixiangCardUseService;

    @Autowired
    private ISuixiangCardPurchaseRecordService suixiangCardPurchaseRecordService;
    @Autowired
    private ISuixiangCardPriceService suixiangCardPriceService;


    @Override
    public SuiXiangCardWelfareInfo getSuiXiangCardWelfareInfo(String cdkey) {
        SuiXiangCardWelfareInfo suiXiangCardWelfareInfo = new SuiXiangCardWelfareInfo();

        SuixiangCardCdk suixiangCardCdk = suixiangCardCdkService.getOne(new LambdaQueryWrapper<SuixiangCardCdk>().eq(SuixiangCardCdk::getCdkey, cdkey));
        Long cardBaseId = suixiangCardCdk.getCardBaseId();
        SuixiangCardBase suixiangCardBase = suixiangCardBaseService.getById(cardBaseId);
        Long cardRentId = suixiangCardCdk.getCardRentId();

        SuixiangCardRentDays suixiangCardRentDays = suixiangCardRentDaysService.getOne(new LambdaQueryWrapper<SuixiangCardRentDays>()
                .eq(SuixiangCardRentDays::getId, cardRentId)
                .eq(SuixiangCardRentDays::getCardBaseId, cardBaseId)
        );
        // 费用类型：1-日租服务费、2-畅行服务费、3-车辆整备费、4-加油服务费、5-充电服务费、6-夜间服务费
        StringBuilder serviceFee = new StringBuilder();
        serviceFee.append("租车费");
        List<ServiceFeeCfgDto> serviceFeeCfgDtos = JSON.parseArray(suixiangCardRentDays.getServiceFees(), ServiceFeeCfgDto.class);
        if (serviceFeeCfgDtos != null) {
            for (ServiceFeeCfgDto serviceFeeCfgDto : serviceFeeCfgDtos) {
                // 1-日租服务费、2-畅行服务费、3-车辆整备费、4-加油服务费、5-充电服务费、6-夜间服务费
                if (serviceFeeCfgDto.getFeeType() == 1) {
                    serviceFee.append("、基础服务费");
                } else if (serviceFeeCfgDto.getFeeType() == 2) {
                    serviceFee.append("、优享服务费");
                } else if (serviceFeeCfgDto.getFeeType() == 3) {
                    serviceFee.append("、车行手续费");
                } else if (serviceFeeCfgDto.getFeeType() == 4) {
                    serviceFee.append("、加油服务费");
                } else if (serviceFeeCfgDto.getFeeType() == 5) {
                    serviceFee.append("、充电服务费");
                } else if (serviceFeeCfgDto.getFeeType() == 6) {
                    serviceFee.append("、夜间服务费");
                }
            }
        }
        suiXiangCardWelfareInfo.setTip("* 用⻋卡可用于抵扣" + serviceFee + "，如需其它增值服务需另行支付。具体使用规则以下单结算时为准。");
        suiXiangCardWelfareInfo.setName(suixiangCardBase.getCardName());
        suiXiangCardWelfareInfo.setTotalDays(suixiangCardRentDays.getRentDays());
        suiXiangCardWelfareInfo.setValidDaysType(suixiangCardBase.getValidDaysType());
        return suiXiangCardWelfareInfo;
    }

    @Override
    public SuiXiangCardThirdSaleInfoBo getSuiXiangCardThirdSaleInfo(SuiXiangCardThirdSaleInfoInput input) throws BusinessException {
        SuiXiangCardThirdSaleInfoBo bo = new SuiXiangCardThirdSaleInfoBo();
        Integer type = input.getType();
        Long purchaseId = input.getPurchaseId();
        Long useCardId = input.getUseCardId();
        if ((type != 1 && type != 2) || (purchaseId <= 0L && useCardId <= 0L)) {
            throw new BusinessException(-2, "入参格式不正确");
        }

        if (type == 1 && purchaseId == 0L) {
            throw new BusinessException(-2, "入参格式不正确");
        }

        if (type == 2 && useCardId == 0L) {
            throw new BusinessException(-2, "入参格式不正确");
        }

        if (type == 2) {
            SuixiangCardUse suixiangCardUse = suixiangCardUseService.getById(useCardId);
            purchaseId = suixiangCardUse.getPurchaseId();
        }

        SuixiangCardPurchaseRecord suixiangCardPurchaseRecord = suixiangCardPurchaseRecordService.getById(purchaseId);
        if (suixiangCardPurchaseRecord == null) {
            throw new BusinessException(-2, "未查询到随享卡购买记录");
        }

        BigDecimal totalPrice = null;
        Integer issueType = suixiangCardPurchaseRecord.getIssueType();
        // 兑换码
        if (issueType.equals(3)) {
            Long cdkId = suixiangCardPurchaseRecord.getCdkId();
            SuixiangCardCdkThirdSaleDto cdkThirdSaleDto = suixiangCardCdkService.getCdkThirdSaleDto(cdkId);
            if (cdkThirdSaleDto != null) {
                bo.setCdkPurpose(cdkThirdSaleDto.getCdkPurpose());
                if (cdkThirdSaleDto.getCdkPurpose() == 2) {
                    totalPrice = cdkThirdSaleDto.getThirdSalesPrice();
                }
            }
        }

        // 天数
        SuixiangCardRentDays suixiangCardRentDays = suixiangCardRentDaysService.getById(suixiangCardPurchaseRecord.getCardRentId());
        Integer rentDays = suixiangCardRentDays.getRentDays();
        bo.setTotalDay(rentDays);

        // 总价格
        if (totalPrice == null) {
            SuixiangCardPrice suixiangCardPrice = suixiangCardPriceService.getById(suixiangCardPurchaseRecord.getCardPriceId());
            totalPrice = suixiangCardPrice.getSalesPrice();
        }
        // 单价
        BigDecimal price = totalPrice.divide(new BigDecimal(rentDays), 2, BigDecimal.ROUND_HALF_UP);

        bo.setPrice(price);
        bo.setTotalPrice(totalPrice);
        bo.setIssueType(issueType);
        return bo;
    }
}
