package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * 自营活动表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_proprietary_activity_signup_relation")
@ApiModel(value="自营活动报名关系对象", description="自营活动报名关系对象")
public class ProprietaryActivitySignupRelation extends BaseEntity {

    @ApiModelProperty(value = "活动id")
    private Long activityId;
    @ApiModelProperty(value = "机构代码")
    private String orgCode;
    @ApiModelProperty(value = "是否全部门店(0:否,1:是)")
    private Integer isAllStore;
    @ApiModelProperty(value = "是否全部车型(0:否,1:是)")
    private Integer isAllVehicle;
    @ApiModelProperty(value = "报名车型")
    private String vehicleModelIds;
    @ApiModelProperty(value = "灵活定价")
    private String flexiblePricing;

}