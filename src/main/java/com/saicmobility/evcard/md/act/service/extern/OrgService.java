package com.saicmobility.evcard.md.act.service.extern;

import cn.hutool.core.util.StrUtil;
import com.saicmobility.evcard.md.act.service.extern.ConfigLoader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OrgService {

    /*@Resource
    private OrgInfoMapper orgInfoMapper;*/

    @Autowired
    ConfigLoader configLoader;

    public String getOrgNameByOrgCode(String orgCode) {
        if (StrUtil.isBlank(orgCode)) {
            return null;
        }
       return configLoader.getOrgName(orgCode);
        //return orgInfoMapper.getOrgNameByOrgId(orgCode);
    }
}
