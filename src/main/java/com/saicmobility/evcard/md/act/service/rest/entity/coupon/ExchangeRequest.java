package com.saicmobility.evcard.md.act.service.rest.entity.coupon;

import lombok.Data;

import java.io.Serializable;

@Data
public class ExchangeRequest implements Serializable {

    /**
     * 0 用户已注册
     *
     * 1  用户未注册（预绑定）
     *
     *
     */
    private int type;

    /**
     * 0:兑换输入
     * 1：扫码
     */
    private int operateType;

    // 优惠券兑换码、口令
    private String couponCode;

    //优惠券唯一标识 暂时不用
    private String userCouponSeq;

    // 用户标识 type 为0 时有值
    private String authId;

    // 预绑定手机号 type 为1 时有值
    private String mobilePhone;

    // 操作人
    private String optUser;
}
