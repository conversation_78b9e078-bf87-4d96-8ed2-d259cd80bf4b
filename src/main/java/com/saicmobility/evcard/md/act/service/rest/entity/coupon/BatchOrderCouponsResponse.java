package com.saicmobility.evcard.md.act.service.rest.entity.coupon;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.coupon.dto.BatchCouponViewDesDto;
import com.extracme.evcard.rpc.coupon.dto.CouponListDto;
import com.saicmobility.evcard.md.mdactservice.api.BatchCouponViewDes;
import com.saicmobility.evcard.md.mdactservice.api.BatchOrderCouponRes;
import com.saicmobility.evcard.md.mdactservice.api.CouponViewDes;
import com.saicmobility.evcard.md.mdactservice.api.OrderCouponRes;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Data
@Slf4j
@EqualsAndHashCode(callSuper = false)
public class BatchOrderCouponsResponse extends ArrayList<BatchCouponViewDesDto> {

    public BatchOrderCouponRes toResBuilder(String mid) {
        List<BatchCouponViewDes> viewDes = new ArrayList<>();
        this.stream().forEach(view ->{
            try {
                List<CouponListDto> couponViewList = view.getCouponView();
                OrderCouponsResponse response = new OrderCouponsResponse();
                response.addAll(couponViewList);
                OrderCouponRes res = response.toResBuilder(mid).build();
                List<CouponViewDes> couponViewDesList = res.getCouponViewDesList();

                BatchCouponViewDes couponViewDes = BatchCouponViewDes.newBuilder()
                        .setStoreVehicleModelId(view.getStoreVehicleModelId())
                        .addAllCouponView(couponViewDesList)
                        .build();
                viewDes.add(couponViewDes);
            } catch (Exception e) {
                log.error("BatchOrderCouponsResponse toResBuilder 异常，view={}", JSON.toJSONString(view),e);
            }
        });

        BatchOrderCouponRes res = BatchOrderCouponRes.newBuilder()
                .addAllViewDes(viewDes)
                .build();
        return res;
    }
}
