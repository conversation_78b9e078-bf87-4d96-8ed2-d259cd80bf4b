package com.saicmobility.evcard.md.act.dto.coupon.channel;

import lombok.Data;

import java.util.List;

@Data
public class GetCouponModelDetailOutput {
    /**
     * 分页查询的每页笔数
     */
    private int pageSize;

    /**
     * 分页查询的指定页码，页码从1开始
     */
    private int pageNo;

    /**
     * 总记录条数
     */
    private int total;

    /**
     * 总页数
     */
    private int totalPage;

    /**
     * 产品列表
     */
    private List<CouponModelDto> couponModelDtos;
}
