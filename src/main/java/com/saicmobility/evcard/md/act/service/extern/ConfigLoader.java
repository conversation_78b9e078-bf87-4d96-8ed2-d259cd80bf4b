package com.saicmobility.evcard.md.act.service.extern;

import com.alibaba.fastjson.JSON;
import com.saicmobility.common.envconfig.EnvGlobal;
import com.saicmobility.evcard.md.act.domain.QlSecondChannelBo;
import com.saicmobility.evcard.md.act.domain.StoreInfoCombobox;
import com.saicmobility.evcard.md.act.domain.coupon.OrgNameInfo;
import com.saicmobility.evcard.md.act.dto.external.StoreVehicleModelInfoDto;
import com.saicmobility.evcard.md.act.mapper.siac.OrgInfoMapper;
import com.saicmobility.evcard.md.act.util.Md5Utils;
import com.saicmobility.evcard.md.mdgoodsservice.api.Dict;
import com.saicmobility.evcard.md.mdgoodsservice.api.GetDictReq;
import com.saicmobility.evcard.md.mdgoodsservice.api.GetDictRes;
import com.saicmobility.evcard.md.mdgoodsservice.api.MdGoodsService;
import com.saicmobility.evcard.md.mdstoreservice.api.*;
import com.saicmobility.evcard.md.mduserservice.api.*;
import krpc.rpc.bootstrap.RpcAppInitBean;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ConfigLoader {

    @Autowired
    RpcAppInitBean rpcAppInitBean;  // 确保krpc加载完毕

    @Autowired
    MdGoodsService mdGoodsService;

    @Autowired
    MdStoreService mdStoreService;

    @Autowired
    OrgInfoMapper orgInfoMapper;

    @Resource
    MdUserService mdUserService;

    //擎路一级渠道车型map
    private Map<String, String> vehicleModeIdMaps = new HashMap<>();

    // 内存里的配置数据， 注意使用 volatile 修饰  ,注意一定要整体替换， 不要修改原来的对象
    private Map<Long, StoreInfoCombobox> storeMap = new HashMap<>();
    private Map<String, OrgNameInfo> orgMap = new HashMap<>();
    private Map<String, String> goodsModelMap = new HashMap<>();
    private Map<String, Long> channelActMap = new HashMap<>();
    private Map<Long, List<StoreInfoCombobox>> storesMapByCity = new HashMap<>();
    // key 二级渠道 value bo
    private Map<String, QlSecondChannelBo> secondKeyAndBoMap = new HashMap<>();

    private Map<Long, StoreVehicleModelInfoDto> storeVehicleMap = new HashMap<>();
    // 这里是用rpc, 改成 实际的访问数据配置的类 或其他rpc服务

    Scheduler scheduler;

    @PostConstruct
    void init() throws Exception {

        loadData(true); // load in main thread

        Properties p = new Properties();
        p.put("org.quartz.threadPool.threadCount", "1");
        p.put("org.quartz.scheduler.instanceName", "config_loader_timer");
        StdSchedulerFactory factory = new StdSchedulerFactory();
        factory.initialize(p);
        scheduler = factory.getScheduler();

        JobDataMap map = new JobDataMap();
        map.put("service", this);

        JobDetail job1 = JobBuilder.newJob(LoadDataJob.class).withIdentity("job4").setJobData(map).build();
        CronTrigger cronTrigger1 = TriggerBuilder.newTrigger().withIdentity("trigger4").withSchedule(CronScheduleBuilder.cronSchedule("55 0/1 * * * ? ")).build();
        scheduler.scheduleJob(job1, cronTrigger1);

        scheduler.start();
    }

    static public class LoadDataJob implements Job {

        @Override
        public void execute(JobExecutionContext ctx) {
            ConfigLoader service = (ConfigLoader) ctx.getJobDetail().getJobDataMap().get("service");
            try {
                service.loadData(false);
            } catch (Exception e) {
                log.error("loadData exception", e);
            }
        }

    }

    @PreDestroy
    void close() {
        try {
            scheduler.shutdown();
        } catch (Exception e) {
        }
    }

    void loadData(boolean init) {
        // 以下是加载配置的方法， 根据实际情况改成去访问数据库或RPC调用
        // 商品车型
        loadGoodsModel(init);
        // 机构
        loadOrgInfo(init);
        // 门店
        loadStoreInfo(init);
        // 渠道活动
        loadChannelInfo(init);
        //车辆信息
        loadStoreVehicleInfo(init);
        //车辆与擎路关系
        loadChannelVehicleShip(init);
    }

    private void loadChannelVehicleShip(boolean init) {
        int retCode = loadChannelVehicleShip();
        if (retCode == 0) return;
        if (!init) return;
        if (!EnvGlobal.globalProfile.equals("prd")) return;

        // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                log.error("loadChannelInfo Thread.sleep() InterruptedException!", e);
                Thread.currentThread().interrupt();
            }
            log.error("cannot load app key info, retCode={}", retCode);
            retCode = loadChannelVehicleShip();
            if (retCode == 0) break;
        }
    }

    private void loadChannelInfo(boolean init) {
        int retCode = loadChannelInfo();
        if (retCode == 0) return;
        if (!init) return;
        if (!EnvGlobal.globalProfile.equals("prd")) return;

        // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                log.error("loadChannelInfo Thread.sleep() InterruptedException!", e);
                Thread.currentThread().interrupt();
            }
            log.error("cannot load app key info, retCode={}", retCode);
            retCode = loadChannelInfo();
            if (retCode == 0) break;
        }
    }

    private int loadChannelInfo() {
        QueryAllSecondChannelRes res = mdUserService.queryAllSecondChannel(QueryAllSecondChannelReq.newBuilder().setSecondStatus(2).build());
        /*QueryAllQLSecondChannelRes res = mdUserService.queryAllQLSecondChannel(QueryAllQLSecondChannelReq.newBuilder().setSecondStatus(2).build());*/
        List<QlSecondChannel> list = res.getSecondChannelList();
        channelActMap = list.stream().collect(Collectors.toMap(QlSecondChannel::getSecondChannelKey, QlSecondChannel::getThirdId));
        secondKeyAndBoMap = list.stream().map(channel -> {
            QlSecondChannelBo bo = new QlSecondChannelBo();
            bo.setSecondAppKey(channel.getSecondChannelKey());
            bo.setThirdId(channel.getThirdId());
            bo.setDisCountCode(channel.getDisCountCode());
            bo.setSecondChannelName(channel.getSecondChannelName());
            return bo;
        }).collect(Collectors.toMap(QlSecondChannelBo::getSecondAppKey, a -> a));
        return res.getRetCode();
    }

    private void loadStoreInfo(boolean init) {
        int retCode = loadStoreInfo();
        if (retCode == 0) return;
        if (!init) return;
        if (!EnvGlobal.globalProfile.equals("prd")) return;

        // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                log.error("loadStoreInfo Thread.sleep() InterruptedException!", e);
                Thread.currentThread().interrupt();
            }
            log.error("cannot load app key info, retCode={}", retCode);
            retCode = loadStoreInfo();
            if (retCode == 0) break;
        }
    }

    private int loadStoreInfo() {
        GetStoreListByCdRes storeList = mdStoreService.getStoreListByCd(GetStoreListByCdReq.newBuilder().build());
        if (storeList.getRetCode() == 0) {
            List<StoreInfo> storeInfos = storeList.getInfoList();
            Map<Long, StoreInfoCombobox> map = storeInfos.stream().map(store -> {
                StoreInfoCombobox storeInfo = new StoreInfoCombobox();
                storeInfo.setStoreId(store.getId());
                storeInfo.setStoreName(store.getStoreName());
                storeInfo.setOperOrgCode(store.getOperOrgCode());
                storeInfo.setOperOrgName(store.getOperOrgName());
                storeInfo.setOperCityId(store.getOperCityId());
                storeInfo.setOperCityName(store.getOperCityName());
                return storeInfo;
            }).collect(Collectors.toMap(StoreInfoCombobox::getStoreId, a -> a));
            //新增城市下全部门店信息
            Map<Long, List<StoreInfoCombobox>> mapByCity = storeInfos.stream().map(store -> {
                StoreInfoCombobox storeInfo = new StoreInfoCombobox();
                storeInfo.setStoreId(store.getId());
                storeInfo.setStoreName(store.getStoreName());
                storeInfo.setOperOrgCode(store.getOperOrgCode());
                storeInfo.setOperOrgName(store.getOperOrgName());
                storeInfo.setOperCityId(store.getOperCityId());
                storeInfo.setOperCityName(store.getOperCityName());
                return storeInfo;
            }).collect(Collectors.groupingBy(StoreInfoCombobox::getOperCityId));
            if (!Md5Utils.toMd5(map).equals(Md5Utils.toMd5(storeMap))) {
                storeMap = map;
                storesMapByCity = mapByCity;
            }
        }
        return storeList.getRetCode();
    }

    private void loadOrgInfo(boolean init) {
        int retCode = loadOrgInfo();
        if (retCode == 0) return;
        if (!init) return;
        if (!EnvGlobal.globalProfile.equals("prd")) return;

        // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                log.error("loadOrgInfo Thread.sleep() InterruptedException!", e);
                Thread.currentThread().interrupt();
            }
            log.error("cannot load app key info, retCode={}", retCode);
            retCode = loadOrgInfo();
            if (retCode == 0) break;
        }
    }

    private int loadOrgInfo() {
        List<OrgNameInfo> orgList = orgInfoMapper.getOrgList();
        Map<String, OrgNameInfo> newOrgMap = orgList.stream().collect(Collectors.toMap(OrgNameInfo::getOrgId, a -> a));
        if (!Md5Utils.toMd5(newOrgMap).equals(Md5Utils.toMd5(orgMap))) {
            orgMap = orgList.stream().collect(Collectors.toMap(OrgNameInfo::getOrgId,
                    a -> a));
        }
        return 0;
    }

    private void loadGoodsModel(boolean init) {
        int retCode = loadGoodsModel();
        if (retCode == 0) return;
        if (!init) return;
        if (!EnvGlobal.globalProfile.equals("prd")) return;

        // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                log.error("loadGoodsModel Thread.sleep() InterruptedException!", e);
                Thread.currentThread().interrupt();
            }
            log.error("cannot load app key info, retCode={}", retCode);
            retCode = loadGoodsModel();
            if (retCode == 0) break;
        }
    }

    private int loadGoodsModel() {

        GetDictRes goodsRes = mdGoodsService.getDict(GetDictReq.newBuilder().setType("goodsModel").build());
        if (goodsRes.getRetCode() == 0) {
            List<Dict> dictList = goodsRes.getDictList();
            Map<String, String> newGoodsModelMap = dictList.stream().collect(Collectors.toMap(Dict::getValue,Dict::getText));

            if (!Md5Utils.toMd5(newGoodsModelMap).equals(Md5Utils.toMd5(goodsModelMap))) {
                goodsModelMap = newGoodsModelMap;
            }
        }

        return goodsRes.getRetCode();
    }

    private void loadStoreVehicleInfo(boolean init) {
        int retCode = loadStoreVehicleInfo();
        if (retCode == 0) return;
        if (!init) return;
        if (!EnvGlobal.globalProfile.equals("prd")) return;

        // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                log.error("loadStoreVehicleInfo InterruptedException!", e);
                Thread.currentThread().interrupt();
            }
            log.error("cannot load app key info, retCode={}", retCode);
            retCode = loadStoreVehicleInfo();
            if (retCode == 0) break;
        }
    }

    private int loadStoreVehicleInfo() {
        GetAllStoreVehicleModelInfoRes res = mdStoreService.getAllStoreVehicleModelInfo(GetAllStoreVehicleModelInfoReq.newBuilder().build());
        if (res.getRetCode() == 0) {
            List<StoreVehicleModelInfo> storeVehicleModelInfoList = res.getInfoList();
            Map<Long, StoreVehicleModelInfoDto> map = storeVehicleModelInfoList.stream().map(item -> {
                StoreVehicleModelInfoDto dto = new StoreVehicleModelInfoDto();
                BeanUtils.copyProperties(item, dto);
                return dto;
            }).collect(Collectors.toMap(StoreVehicleModelInfoDto::getId, a -> a));
            storeVehicleMap = map;
        }
        return res.getRetCode();
    }

    public StoreInfoCombobox getStore(Long storeId) {
        return storeMap.get(storeId);
    }

    public String getStoreName(Long storeId) {
        //全部车型特殊处理
        if(storeId == -1){
            return "全部门店";
        }
        StoreInfoCombobox storeInfoCombobox = storeMap.get(storeId);
        if (null == storeInfoCombobox) {
            return null;
        }
        return storeInfoCombobox.getStoreName();
    }

    public List<String> listStoreName(List<Long> ids){
        List<String> list = new ArrayList<>();
        for(Long id : ids){
            list.add(getStoreName(id));
        }
        return list;
    }

    public List<StoreInfoCombobox> getStoresByCityId(Long cityId){
        if (cityId == null){
            return new ArrayList<>();
        }
        List<StoreInfoCombobox> storeInfoComboboxes = storesMapByCity.get(cityId);
        return storeInfoComboboxes;
    }
    public List<Long> getStoreIdsByCityId(Long cityId){
        List<StoreInfoCombobox> storeInfoComboboxList = this.getStoresByCityId(cityId);
        if (CollectionUtils.isNotEmpty(storeInfoComboboxList)){
            return storeInfoComboboxList.stream().map(StoreInfoCombobox::getStoreId).collect(Collectors.toList());
        }else {
            return new ArrayList<>();
        }
    }


    public Collection<StoreInfoCombobox> getStore() {
        return storeMap.values();
    }

    public OrgNameInfo getOrgInfo(String orgCode) {
        return orgMap.get(orgCode);
    }

    public String getOrgName(String orgCode) {
        OrgNameInfo nameInfo = orgMap.get(orgCode);
        if (null == nameInfo) {
            return null;
        }
        return nameInfo.getOrgName();
    }

    public List<String> listOrgName(List<String> codes){
        List<String> list = new ArrayList<>();
        for(String code : codes){
            list.add(getOrgName(code));
        }
        return list;
    }
    public Collection<OrgNameInfo> getOrgInfo() {
        return orgMap.values();
    }

    public String getGoodsModelInfo(String goodsModelId) {
        return goodsModelMap.get(goodsModelId);
    }

    public String getGoodsModelName(Long goodsModelId) {
        return goodsModelMap.get(goodsModelId.toString());
    }

    public Collection<String> getGoodsModelInfo() {
        return goodsModelMap.values();
    }

    public long getThirdId(String appKey) {
        return channelActMap.get(appKey);
    }

    public QlSecondChannelBo getQlSecondChannelBo(String appKey) {
        QlSecondChannelBo qlSecondChannelBo = secondKeyAndBoMap.get(appKey);
        if (qlSecondChannelBo == null) {
            log.info("secondKeyAndBoMap，channelIdAndCodeMap={},appKey={}", JSON.toJSONString(secondKeyAndBoMap),appKey);
        }
        return qlSecondChannelBo;
    }

    public String getVehicleName(Long id){
        //全部车型特殊处理
        if(id == -1){
            return "全部车型";
        }
        StoreVehicleModelInfoDto storeVehicleModelInfoDto = storeVehicleMap.get(id);
        if(storeVehicleModelInfoDto == null){
            log.info("tid:{},获取车型名称为空:carModelId:{}", Trace.currentTraceId(),id);
            return null;
        }
        return storeVehicleMap.get(id).getStoreVehicleModelName();
    }
    //批量查询
    public List<String> listVehicleName(List<Long> ids){
        List<String> list = new ArrayList<>();
        for(Long id : ids){
            list.add(getVehicleName(id));
        }
        return list;
    }

    private int loadChannelVehicleShip(){
        Map<String, String> vehicleModeIdMap = new HashMap<>();
        GetStoreVehicleModelInfoByPlatformRes res = mdStoreService.getStoreVehicleModelInfoByPlatform(GetStoreVehicleModelInfoByPlatformReq.newBuilder()
                .setPlatformName(String.valueOf(100)).build());
        if(res.getRetCode() == 0){
            if(res != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(res.getInfoList())){
                for(StoreVehicleModelInfo info: res.getInfoList()) {
                    if(org.apache.commons.collections.CollectionUtils.isNotEmpty(info.getQinluChannelKeysList())){
                        for(String qinluChannelKey:info.getQinluChannelKeysList()) {
                            String mapKey = info.getId()+"_"+qinluChannelKey;
                            vehicleModeIdMap.put(mapKey,mapKey);
                        }
                    }

                }
            }
            log.info("loadChannelVehicleShip--vehicleModeIdMap"+ JSON.toJSONString(vehicleModeIdMap));
            vehicleModeIdMaps = vehicleModeIdMap;
        }

        return res.getRetCode();
    }


    public boolean containsVehicleModeValue(long modelId, String secondAppkey){
       return StringUtils.isNotBlank(vehicleModeIdMaps.get(modelId+"_"+secondAppkey)) ;
    }
}
