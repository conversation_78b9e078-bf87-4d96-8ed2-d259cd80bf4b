package com.saicmobility.evcard.md.act.bo.market;

import com.saicmobility.evcard.md.mdactservice.api.ListChannelAct;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ListChannelActBo {
    private List<ChannelActInfo> list;
    private int total;

    public List<ListChannelAct> toListChannelAct(List<ChannelActInfo> list) {
        List<ListChannelAct> acts = new ArrayList<>();
        for (ChannelActInfo info : list) {
            ListChannelAct act = ListChannelAct.newBuilder()
                    .setId(info.getId())
                    .setDiscountCode(info.getDiscountCode())
                    .setActName(info.getActName())
                    .setActType(info.getActType())
                    .setStartDate(info.getStartDate())
                    .setEndDate(info.getEndDate())
                    .setActStatus(info.getActStatus())
                    .setChannel(info.getChannel())
                    .setChannelText(info.getChannelText())
                    .build();
            acts.add(act);
        }
        return acts;
    }
}
