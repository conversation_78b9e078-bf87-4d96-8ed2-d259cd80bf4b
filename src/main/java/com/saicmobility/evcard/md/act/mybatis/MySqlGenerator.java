package com.saicmobility.evcard.md.act.mybatis;

import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.system.ApplicationHome;

import java.util.Scanner;

public class MySqlGenerator {

    public static void main(String[] args) {
        String jdbcUrl = "************************************************************************************************************************";
        String username = "devuser";
        String password = "lb3mTJsa";
        ApplicationHome applicationHome = new ApplicationHome(MySqlGenerator.class);
        // 这边有多个getParentFile取决于你生成 class 文件的路径 。  gradle 和 maven 是不一样的。
        String projectPath = applicationHome.getSource().getParentFile().getParentFile().getParentFile().getParentFile().getPath();
        System.out.println(projectPath);

        CoderGeneratorPath coderGeneratorPath = new CoderGeneratorPath();
        coderGeneratorPath.setDaoOutputPath(projectPath + "/src/main/java/com/saicmobility/evcard/md/act/dao/");
        coderGeneratorPath.setEntityOutputPath(projectPath + "/src/main/java/com/saicmobility/evcard/md/act/entity/");
        coderGeneratorPath.setMapperOutputPath(projectPath + "/src/main/java/com/saicmobility/evcard/md/act/mapper/");
        coderGeneratorPath.setProjectPath(projectPath);
        coderGeneratorPath.setServiceOutputPath(projectPath + "/src/main/java/com/saicmobility/evcard/md/act/service/inner/");
        coderGeneratorPath.setServiceImplOutputPath(projectPath + "/src/main/java/com/saicmobility/evcard/md/act/service/inner/");
        coderGeneratorPath.setXmlOutputPath(projectPath + "/src/main/java/com/saicmobility/evcard/md/act/mapper/xml/");


        CodeGenerator mpg = CodeGenerator.builder(coderGeneratorPath)
                .dataSourceConfig(jdbcUrl, "com.mysql.cj.jdbc.Driver", username, password)
                .globalConfig("generator")
                .packageConfig("/com/saicmobility/evcard/md/act")
                .build();

        StrategyConfig strategy = new StrategyConfig();
        //设置命名规则下划线转驼峰
        strategy.setNaming(NamingStrategy.underline_to_camel);
        //实体的列名
        strategy.setColumnNaming(NamingStrategy.underline_to_camel);
        //是否生成lombok注解
        strategy.setEntityLombokModel(true);
        //逻辑删除字段配置
        // strategy.setLogicDeleteFieldName("deleted");
        //开启驼峰命名
        strategy.setRestControllerStyle(true);
        //指定要映射的数据库表，可以写多个表，用“，”隔开
        strategy.setInclude(scanner("表名，多个英文逗号分割").split(","));
        //下划线转驼峰
        strategy.setControllerMappingHyphenStyle(true);
        //去掉这个是表的前缀比如sys_user，要去掉前缀就输入sys_
        strategy.setTablePrefix("t_");
        mpg.setStrategy(strategy);
        mpg.setTemplateEngine(new FreemarkerTemplateEngine());
        mpg.execute();
    }

    public static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in);
        StringBuilder help = new StringBuilder();
        help.append("请输入" + tip + "：");
        System.out.println(help.toString());
        if (scanner.hasNext()) {
            String ipt = scanner.next();
            if (StringUtils.isNotEmpty(ipt)) {
                return ipt;
            }
        }
        throw new MybatisPlusException("请输入正确的" + tip + "！");
    }
}
