package com.saicmobility.evcard.md.act.util;

import com.saicmobility.evcard.md.act.enums.channelcoupon.CCBCodeEnum;
import com.saicmobility.evcard.md.mdactservice.api.ReconciliationFileToRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/1/15 13:32
 * @Description: 生成文件工具类
 */
@Slf4j
public class FileUtils {

    public static Boolean createTxtFile(String path,String fileName,String content){
        if (StringUtils.isBlank(fileName) || !fileName.endsWith(".txt")){
            log.error("txt文件失败，命名不规范");
            return Boolean.FALSE;
        }
        FileWriter fileWriter = null;
        try {
            String fullPath = path.concat("/").concat(fileName);
            fileWriter = new FileWriter(fullPath);
            fileWriter.write(content);
            return Boolean.TRUE;
        }catch (Exception e){
            return Boolean.FALSE;
        }finally {
            try {
                if (fileWriter != null){
                    fileWriter.close();
                }
            } catch (IOException e) {
                return Boolean.FALSE;
            }
        }
    }
    public static Boolean deleteTxtFile(String path){
        try{
            // 创建File对象
            File file = new File(path);
            // 检查文件是否存在
            if (file.exists()) {
                // 尝试删除文件
                boolean success = file.delete();

                if (success) {
                    return Boolean.TRUE;
                } else {
                    return Boolean.FALSE;
                }
            } else {
                return Boolean.TRUE;
            }
        }catch (Exception e){
            return Boolean.FALSE;
        }


    }
}
