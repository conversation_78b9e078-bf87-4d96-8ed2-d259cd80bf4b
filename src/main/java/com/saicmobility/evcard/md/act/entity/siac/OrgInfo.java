package com.saicmobility.evcard.md.act.entity.siac;

import lombok.Data;

@Data
public class OrgInfo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.id
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_ID
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String orgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_NAME
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String orgName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_KIND
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String orgKind;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_CLASS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double orgClass;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CONTACT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String contact;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.TEL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String tel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.MOBILE_PHONE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String mobilePhone;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ADDRESS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String address;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.MAIL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String mail;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.LICENSE_NO
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String licenseNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.FAX
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String fax;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.COUNTY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String county;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CITY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double city;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.PROVINCE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double province;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CORPORATE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String corporate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.LOCATION
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String location;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.RTOLN
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String rtoln;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.LICENSE_NO_IMG_URL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String licenseNoImgUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.TAX_REGISTRATION_IMG_URL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String taxRegistrationImgUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_CODE_IMG_URL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String orgCodeImgUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.REMARK
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.PAY_WAY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double payWay;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CREATED_USER
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String createdUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CREATED_TIME
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String createdTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.UPDATED_USER
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String updatedUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.UPDATED_TIME
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String updatedTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.STATUS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORIGIN
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double origin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.DEPOSIT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double deposit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.RESERVE_AMOUNT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double reserveAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.RENT_MINS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Double rentMins;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.AGENCY_ID
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String agencyId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CITY_SHORT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String cityShort;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.INSIDE_FLAG
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Integer insideFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_ALIAS
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String orgAlias;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CHECK_DATE
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String checkDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.CHECK_ALERT
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Integer checkAlert;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.BALANCE_MAIL
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private String balanceMail;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_PROPERTY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Byte orgProperty;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column org_info.ORG_PROTRETY
     *
     * @mbggenerated Mon Mar 05 21:36:22 CST 2018
     */
    private Boolean orgProtrety;

}