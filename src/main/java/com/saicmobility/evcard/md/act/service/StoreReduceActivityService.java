package com.saicmobility.evcard.md.act.service;

import com.saicmobility.evcard.md.mdactservice.api.*;
import org.springframework.stereotype.Component;

@Component
public interface StoreReduceActivityService {

    /**
     * 获取门店可用的立减活动(app接口)
     */
     GetStoreAvailableReduceActivityRes getStoreAvailableReduceActivity(GetStoreAvailableReduceActivityReq req);

    /**
     * 查询门店未参与的活动
     */
    SearchStoreUnParticipateActivityRes searchStoreUnParticipateActivity(SearchStoreUnParticipateActivityReq req);

    /**
     * 查询门店已参与的活动
     */
    SearchStoreParticipateActivityRes searchStoreParticipateActivity(SearchStoreParticipateActivityReq req);

    /**
     * 门店参与活动
     */
    ParticipateStoreReduceActivityRes participateStoreReduceActivity(ParticipateStoreReduceActivityReq req);

    /**
     * 门店退出活动
     */
    CancelStoreReduceActivityRes cancelStoreReduceActivity(CancelStoreReduceActivityReq req);

}
