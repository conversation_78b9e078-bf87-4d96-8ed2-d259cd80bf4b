package com.saicmobility.evcard.md.act.enums;

/**
 * <AUTHOR>
 * @date 2025/2/10 16:34
 * @Description: 品牌车型操作及状态枚举类
 */
public enum BrandModelOperStateEnum {
    //1-新增,2-上线,3-修改,4-暂停,5-下线
    OPER_INSERT(1, "新增",1),
    OPER_ONLINE(2, "上线",1),
    OPER_UPDATE(3, "修改",1),
    OPER_PAUSE(4, "暂停",1),
    OPER_OFFLINE(5, "下线",1),
    CANCEL_PAUSE(6, "取消暂停",1),
    //1-待发布，2-待上线，3-上线中，4-暂停中，5-已下线
    STATE_TOBE_RELEASED(1, "待发布",2),
    STATE_TOBE_ONLINE(2, "待上线",2),
    STATE_ONLINEING(3, "上线中",2),
    STATE_PAUSEING(4, "暂停中",2),
    STATE_OFFLINED(5, "已下线",2),
            ;
    private int type;
    private String msg;
    private int enumType;//1-操作类型，2-状态类型

    BrandModelOperStateEnum(int type, String msg,int enumType) {
        this.type = type;
        this.msg = msg;
        this.enumType = enumType;
    }

    public static BrandModelOperStateEnum getEnumByType(Integer enumType, Integer type){
        BrandModelOperStateEnum[] values = BrandModelOperStateEnum.values();
        for (BrandModelOperStateEnum value : values) {
            if (value.getEnumType() == enumType && value.getType() == type){
                return value;
            }
        }
        return null;
    }

    public int getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

    public int getEnumType() {
        return enumType;
    }
}
