package com.saicmobility.evcard.md.act.service.suixiangcard;

import com.saicmobility.evcard.md.act.dto.suixiangcard.SuixiangCardCdkThirdSaleDto;
import com.saicmobility.evcard.md.act.entity.iss.SuixiangCardCdk;
import com.saicmobility.evcard.md.act.mapper.iss.SuixiangCardCdkMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 随享卡兑换表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Service
public class SuixiangCardCdkServiceImpl extends ServiceImpl<SuixiangCardCdkMapper, SuixiangCardCdk> implements ISuixiangCardCdkService {

    @Override
    public SuixiangCardCdkThirdSaleDto getCdkThirdSaleDto(Long cdkId) {
        return getBaseMapper().getCdkThirdSaleDto(cdkId);
    }
}
