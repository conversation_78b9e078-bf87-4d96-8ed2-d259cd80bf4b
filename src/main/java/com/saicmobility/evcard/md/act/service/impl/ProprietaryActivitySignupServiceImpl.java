package com.saicmobility.evcard.md.act.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.bo.proprietary.GetProprietaryActivityInfoBo;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.domain.StoreInfoCombobox;
import com.saicmobility.evcard.md.act.domain.activity.GetSignUpProprietaryActivityByVehicleModelIdsBo;
import com.saicmobility.evcard.md.act.domain.activity.GetSignupProprietaryActivityDetailBo;
import com.saicmobility.evcard.md.act.domain.activity.GetSignupProprietaryActivityListDto;
import com.saicmobility.evcard.md.act.domain.activity.SignupDiscountFlexiblePricing;
import com.saicmobility.evcard.md.act.domain.activity.SignupFullMinusFlexiblePricing;
import com.saicmobility.evcard.md.act.domain.activity.SignupProprietaryActivityBo;
import com.saicmobility.evcard.md.act.domain.activity.SignupProprietaryActivityDto;
import com.saicmobility.evcard.md.act.domain.activity.UpdateSignupProprietaryActivityDto;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.dto.market.TimeRange;
import com.saicmobility.evcard.md.act.dto.proprietary.DiscountFlexiblePricing;
import com.saicmobility.evcard.md.act.dto.proprietary.FullMinusFlexiblePricing;
import com.saicmobility.evcard.md.act.entity.BaseEntity;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivity;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivitySignup;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivitySignupRelation;
import com.saicmobility.evcard.md.act.enums.IsDeletedEnum;
import com.saicmobility.evcard.md.act.enums.proprietary.ActivitySignupStatusEnum;
import com.saicmobility.evcard.md.act.enums.proprietary.ActivityStatusEnum;
import com.saicmobility.evcard.md.act.enums.proprietary.PricingTypeEnum;
import com.saicmobility.evcard.md.act.manager.SignupProprietaryActivityManager;
import com.saicmobility.evcard.md.act.mapper.act.ProprietaryActivityMapper;
import com.saicmobility.evcard.md.act.mapper.act.ProprietaryActivitySignupMapper;
import com.saicmobility.evcard.md.act.service.IProprietaryActivityService;
import com.saicmobility.evcard.md.act.service.IProprietaryActivitySignupRelationService;
import com.saicmobility.evcard.md.act.service.IProprietaryActivitySignupService;
import com.saicmobility.evcard.md.act.service.extern.ConfigLoader;
import com.saicmobility.evcard.md.act.service.extern.StoreService;
import com.saicmobility.evcard.md.act.service.inner.BaseService;
import com.saicmobility.evcard.md.act.util.CommonUtils;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.mdactservice.api.*;
import com.saicmobility.evcard.md.mdstoreservice.api.GetStoreListByCdReq;
import com.saicmobility.evcard.md.mdstoreservice.api.MdStoreService;
import krpc.common.Json;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.TextStyle;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 自营活动报名表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */

@Service
@Slf4j
public class ProprietaryActivitySignupServiceImpl extends BaseService<ProprietaryActivitySignupMapper, ProprietaryActivitySignup> implements IProprietaryActivitySignupService {
    @Resource
    private ProprietaryActivitySignupMapper proprietaryActivitySignupMapper;

    @Resource
    private SignupProprietaryActivityManager signupProprietaryActivityManager;

    @Resource
    private IProprietaryActivityService proprietaryActivityService;

    @Resource
    private ProprietaryActivityMapper proprietaryActivityMapper;

    @Resource
    private ConfigLoader configLoader;

    @Resource
    private IProprietaryActivitySignupRelationService signupRelationService;

    /*报名*/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void signupProprietaryActivity(SignupProprietaryActivityDto dto) throws BusinessException {
        if (dto.getActivityId() <= 0) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "活动id必须大于0");
        }
        GetProprietaryActivityInfoBo activityInfo = proprietaryActivityService.getProprietaryActivityInfo(dto.getActivityId());
        CurrentUser currentUser = dto.getCurrentUser();
        if (activityInfo == null) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "活动不存在");
        }
        if (currentUser == null) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "用户信息为空");
        }
        if (activityInfo.getActivityStatus() != ActivityStatusEnum.EFFECT.getType()) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "只有生效中的活动才能报名");
        }

        // 可报名门店是否包含该门店
        List<Long> notContainsStoreList = new ArrayList<>();
        if (activityInfo.getAllStore() != 1) { // 全部门店  1-全部  2-不是全部
            for (Long storeId : dto.getStoreIdList()) {
                if (!activityInfo.getStoreIdList().contains(storeId)) {
                    notContainsStoreList.add(storeId);
                }
            }
        }
        if (!CollectionUtils.isEmpty(notContainsStoreList)) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "门店：" + configLoader.listStoreName(notContainsStoreList) + "，不在活动报名门店范围");
        }

        // 可报名车型是否包含该车型
        List<Long> notContainsVmid = new ArrayList<>();
        if (activityInfo.getAllModelIds() != 1) { // 全部车型  1-全部  2-不是全部
            for (Long vehicleModel : dto.getVehicleModelIds()) {
                if (!activityInfo.getVehicleModelIds().contains(vehicleModel)) {
                    notContainsVmid.add(vehicleModel);
                }
            }
        }
        if (!CollectionUtils.isEmpty(notContainsVmid)) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "车型：" + configLoader.listVehicleName(notContainsVmid) + "，不在活动报名车型范围");
        }

        // 当前日期 >= 报名开始日期 && 当前日期 <= 报名结束日期
        String curStr = DateUtil.dateToString(new Date(), DateUtil.DATE_TYPE5);
        LocalDate curDate = DateUtil.getLocalDateFromStr(curStr, DateUtil.DATE_TYPE5);
        LocalDate signUpStartDate = DateUtil.getLocalDateFromStr(activityInfo.getSignUpStartDate(), DateUtil.DATE_TYPE5);
        LocalDate signUpEndDate = DateUtil.getLocalDateFromStr(activityInfo.getSignUpEndDate(), DateUtil.DATE_TYPE5);
        if (curDate.compareTo(signUpStartDate) < 0) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "未到活动报名开始日期");
        }
        if (signUpEndDate.compareTo(curDate) < 0) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "活动报名已结束");
        }
        if (CollectionUtils.isEmpty(dto.getStoreIdList())) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "报名门店不能为空");
        }
        if (CollectionUtils.isEmpty(dto.getVehicleModelIds())) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "报名车型不能空");
        }
        if (activityInfo.getPricingType() == PricingTypeEnum.FLEXIBLE_PRICING.getType()) {
            if (activityInfo.getActivityType() == 1) {
                if (CollectionUtils.isEmpty(dto.getFullMinusFlexiblePricing()) || dto.getFullMinusFlexiblePricing().size() != activityInfo.getFullMinusFlexiblePricing().size()) {
                    throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "灵活定价，必须输入满减");
                }
            } else {
                if (CollectionUtils.isEmpty(dto.getDiscountFlexiblePricing()) || dto.getDiscountFlexiblePricing().size() != activityInfo.getDiscountFlexiblePricing().size()) {
                    throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "灵活定价，必须输入折扣");
                }
            }
        }
        if (activityInfo.getPricingType() == PricingTypeEnum.FLEXIBLE_PRICING.getType()) {
            if (activityInfo.getActivityType() == 1) {
                for (SignupFullMinusFlexiblePricing o : dto.getFullMinusFlexiblePricing()) {
                    Double discountAmount = Double.valueOf(o.getDiscountAmount());
                    Double minDiscountAmount = Double.valueOf(o.getMinDiscountAmount());
                    Double maxDiscountAmount = Double.valueOf(o.getMaxDiscountAmount());
                    if (discountAmount < minDiscountAmount || discountAmount > maxDiscountAmount) {
                        throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "灵活定价，输入的满减金额不在范围内");
                    }
                }

                if (dto.getFullMinusFlexiblePricing().size() > 1) {
                    dto.getFullMinusFlexiblePricing().sort(Comparator.comparing(o -> Integer.valueOf(o.getDays()))); // 根据天数从小到大排序（升序）
                    SignupFullMinusFlexiblePricing oldPrice = dto.getFullMinusFlexiblePricing().get(0);
                    for (int i = 1; i < dto.getFullMinusFlexiblePricing().size(); i++) {
                        SignupFullMinusFlexiblePricing newPrice = dto.getFullMinusFlexiblePricing().get(i);
                        BigDecimal oldDiscountAmount = new BigDecimal(oldPrice.getDiscountAmount());
                        BigDecimal newDiscountAmount = new BigDecimal(newPrice.getDiscountAmount());
                        if (newDiscountAmount.compareTo(oldDiscountAmount) >= 0) {
                            oldPrice = newPrice;
                        } else {
                            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "满减金额必须按照天数阶梯上升");
                        }
                    }
                }
            } else {
                for (SignupDiscountFlexiblePricing o : dto.getDiscountFlexiblePricing()) {
                    Double discount = Double.valueOf(o.getDiscount());
                    Double minDiscount = Double.valueOf(o.getMinDiscount());
                    Double maxDiscount = Double.valueOf(o.getMaxDiscount());
                    if (discount < minDiscount || discount > maxDiscount) {
                        throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "灵活定价，输入的折扣不在范围内");
                    }
                }

                if (dto.getDiscountFlexiblePricing().size() > 1) {
                    dto.getDiscountFlexiblePricing().sort(Comparator.comparing(o -> Integer.valueOf(o.getDays()))); // 根据天数从小到大排序（升序）
                    SignupDiscountFlexiblePricing oldPrice = dto.getDiscountFlexiblePricing().get(0);
                    for (int i = 1; i < dto.getDiscountFlexiblePricing().size(); i++) {
                        SignupDiscountFlexiblePricing newPrice = dto.getDiscountFlexiblePricing().get(i);
                        BigDecimal oldDiscount = new BigDecimal(oldPrice.getDiscount());
                        BigDecimal newDiscount = new BigDecimal(newPrice.getDiscount());
                        if (oldDiscount.compareTo(newDiscount) >= 0) {
                            oldPrice = newPrice;
                        } else {
                            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "折扣必须按照天数阶梯上升");
                        }
                    }
                }
            }
        }

        // 判断该门店是否已经报名
        LambdaQueryWrapper<ProprietaryActivitySignup> queryWrapper = new LambdaQueryWrapper<ProprietaryActivitySignup>()
                .in(ProprietaryActivitySignup::getStoreId, dto.getStoreIdList())
                .eq(ProprietaryActivitySignup::getActivityId, dto.getActivityId())
                .eq(ProprietaryActivitySignup::getIsDeleted, IsDeletedEnum.NORMAL.getType());
        // 批量查询报名记录
        List<ProprietaryActivitySignup> signupInfo = proprietaryActivitySignupMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(signupInfo)) {
            List<Long> storeIdList = signupInfo.stream().map(o -> o.getStoreId()).collect(Collectors.toList());
            throw new BusinessException(ErrorEnum.INVALID_ACTNAME.getCode(), "门店：" + configLoader.listStoreName(storeIdList) + " 已报名该活动");
        }

        String vehicleModelIds = String.join(",", dto.getVehicleModelIds().stream().map(o -> String.valueOf(o)).collect(Collectors.toList()));
        List<ProprietaryActivitySignup> actSignupList = new ArrayList<>();
        Set<String> orgCodeList = new HashSet<>();
        for (Long storeId : dto.getStoreIdList()) {
            // 数组组装
            ProprietaryActivitySignup activitySignup = new ProprietaryActivitySignup();
            activitySignup.setActivityId(dto.getActivityId());
            activitySignup.setStoreId(storeId);
            StoreInfoCombobox store = configLoader.getStore(storeId);
            activitySignup.setOrgCode(store.getOperOrgCode());
            orgCodeList.add(store.getOperOrgCode());
            activitySignup.setSignupStatus(ActivitySignupStatusEnum.SIGNUP.getType()); // 已参加
            String fullMinusFlexiblePricing = "";
            if (!CollectionUtils.isEmpty(dto.getFullMinusFlexiblePricing())) {
                fullMinusFlexiblePricing = JSON.toJSONString(dto.getFullMinusFlexiblePricing());
            }
            String discountFlexiblePricing = "";
            if (!CollectionUtils.isEmpty(dto.getDiscountFlexiblePricing())) {
                discountFlexiblePricing = JSON.toJSONString(dto.getDiscountFlexiblePricing());
            }
            activitySignup.setVehicleModelIds(vehicleModelIds);
            activitySignup.setFullMinusFlexiblePricing(fullMinusFlexiblePricing);
            activitySignup.setDiscountFlexiblePricing(discountFlexiblePricing);
            activitySignup.setCreateOperName(currentUser.getUserName());
            activitySignup.setCreateOperId(currentUser.getUserId());
            activitySignup.setIsAllVehicle(dto.getIsAllVehicle());
            actSignupList.add(activitySignup);
        }
        String flexiblePricing = null;
        if (!CollectionUtils.isEmpty(dto.getFullMinusFlexiblePricing())) {
            flexiblePricing = JSON.toJSONString(dto.getFullMinusFlexiblePricing());
        }else if(!CollectionUtils.isEmpty(dto.getDiscountFlexiblePricing())){
            flexiblePricing = JSON.toJSONString(dto.getDiscountFlexiblePricing());
        }
        // 日志log content
        String logContent = "新增报名 " + "报名门店：" + configLoader.listStoreName(dto.getStoreIdList())+ "； 报名车型：" + configLoader.listVehicleName(dto.getVehicleModelIds());
        StringBuilder sb = new StringBuilder(logContent);
        if (!CollectionUtils.isEmpty(dto.getFullMinusFlexiblePricing())) {
            sb.append("; 满减灵活定价：" + JSON.toJSONString(dto.getFullMinusFlexiblePricing()) + "；");
        }
        if (!CollectionUtils.isEmpty(dto.getDiscountFlexiblePricing())) {
            sb.append("; 打折灵活定价：" + JSON.toJSONString(dto.getDiscountFlexiblePricing()) + "；");
        }

        String operationLog = sb.toString();
        if (operationLog.length() > 1024) {
            operationLog = operationLog.substring(0, 1021) + "...";
        }
        signupProprietaryActivityManager.insertSignupActivity(actSignupList, dto, activityInfo, currentUser, operationLog);
        signupRelationService.addOrUpdateSignupRelation(orgCodeList,dto.getActivityId(),dto.getIsAllStore(),dto.getIsAllVehicle()
                ,currentUser.getUserId(),currentUser.getName(),vehicleModelIds,flexiblePricing);
    }

    /*编辑报名*/
    @Override
    public UpdateSignupProprietaryActivityRes updateSignupProprietaryActivity(UpdateSignupProprietaryActivityDto dto) throws BusinessException {
        try {
            CurrentUser currentUser = dto.getCurrentUser();
            // 参数校验
            if (currentUser == null) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "用户信息为空");
            }
            if (CollectionUtils.isEmpty(dto.getVehicleModelIds())) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "报名车型不能空");
            }

            // 查询老报名数据
            LambdaQueryWrapper<ProprietaryActivitySignup> queryWrapper = new LambdaQueryWrapper<ProprietaryActivitySignup>()
                    .eq(ProprietaryActivitySignup::getId, dto.getId())
                    .eq(ProprietaryActivitySignup::getIsDeleted, IsDeletedEnum.NORMAL.getType());
            // 查询报名信息
            ProprietaryActivitySignup oldSignupAct = proprietaryActivitySignupMapper.selectOne(queryWrapper);
            if (oldSignupAct == null) {
                return UpdateSignupProprietaryActivityRes.failed(ErrorEnum.REQUEST_ERROR.getCode(), "未查到报名信息");
            }

            // 活动信息
            GetProprietaryActivityInfoBo act = proprietaryActivityService.getProprietaryActivityInfo(oldSignupAct.getActivityId());
            if (act == null) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "活动不存在");
            }
            if (act.getActivityStatus() != ActivityStatusEnum.EFFECT.getType()) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "只有生效中的活动才能编辑");
            }
            if (!dto.getStoreId().equals(oldSignupAct.getStoreId())) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "编辑门店与报名门店不一致");
            }
            // 可报名门店是否包含该门店
            List<Long> notContainsStoreIdList = new ArrayList<>();
            if (act.getAllStore() != 1) { // 全部门店  1-全部  2-不是全部
                for (Long storeId : act.getStoreIdList()) {
                    if (!act.getStoreIdList().contains(storeId)) {
                        notContainsStoreIdList.add(storeId);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(notContainsStoreIdList)) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "门店：" + configLoader.listStoreName(notContainsStoreIdList) + "，不在活动报名门店范围");
            }

            // 可报名车型是否包含该车型
            List<Long> notContainsVmid = new ArrayList<>();
            if (act.getAllModelIds() != 1) { // 全部车型  1-全部  2-不是全部
                for (Long vehicleModel : dto.getVehicleModelIds()) {
                    if (!act.getVehicleModelIds().contains(vehicleModel)) {
                        notContainsVmid.add(vehicleModel);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(notContainsVmid)) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "车型：" + configLoader.listVehicleName(notContainsVmid) + "，不在活动报名车型范围");
            }

            // 当前日期 >= 报名开始日期 && 当前日期 <= 报名结束日期
            String curStr = DateUtil.dateToString(new Date(), DateUtil.DATE_TYPE5);
            LocalDate curDate = DateUtil.getLocalDateFromStr(curStr, DateUtil.DATE_TYPE5);
            LocalDate signUpStartDate = DateUtil.getLocalDateFromStr(act.getSignUpStartDate(), DateUtil.DATE_TYPE5);
            LocalDate signUpEndDate = DateUtil.getLocalDateFromStr(act.getSignUpEndDate(), DateUtil.DATE_TYPE5);
            if (curDate.compareTo(signUpStartDate) < 0) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "未到活动报名开始日期");
            }
            if (signUpEndDate.compareTo(curDate) < 0) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "活动报名已结束");
            }
            if (act.getPricingType() == PricingTypeEnum.FLEXIBLE_PRICING.getType()) {
                if (act.getActivityType() == 1) {
                    if (CollectionUtils.isEmpty(dto.getFullMinusFlexiblePricing()) || dto.getFullMinusFlexiblePricing().size() != act.getFullMinusFlexiblePricing().size()) {
                        throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "灵活定价，必须输入满减");
                    }
                } else {
                    if (CollectionUtils.isEmpty(dto.getDiscountFlexiblePricing()) || dto.getDiscountFlexiblePricing().size() != act.getDiscountFlexiblePricing().size()) {
                        throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "灵活定价，必须输入折扣");
                    }
                }
            }
            if (act.getPricingType() == PricingTypeEnum.FLEXIBLE_PRICING.getType()) {
                if (act.getActivityType() == 1) {
                    List<SignupFullMinusFlexiblePricing> list = dto.getFullMinusFlexiblePricing();

                    for (SignupFullMinusFlexiblePricing o : list) {
                        Double discountAmount = Double.valueOf(o.getDiscountAmount());
                        Double minDiscountAmount = Double.valueOf(o.getMinDiscountAmount());
                        Double maxDiscountAmount = Double.valueOf(o.getMaxDiscountAmount());
                        if (discountAmount < minDiscountAmount || discountAmount > maxDiscountAmount) {
                            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "灵活定价，输入的满减金额不在范围内");
                        }
                    }

                    if (list.size() > 1) {
                        list.sort(Comparator.comparing(o -> Integer.valueOf(o.getDays()))); // 根据天数从小到大排序（升序）
                        SignupFullMinusFlexiblePricing oldPrice = list.get(0);
                        for (int i = 1; i < list.size(); i++) {
                            SignupFullMinusFlexiblePricing newPrice = list.get(i);
                            BigDecimal oldDiscountAmount = new BigDecimal(oldPrice.getDiscountAmount());
                            BigDecimal newDiscountAmount = new BigDecimal(newPrice.getDiscountAmount());
                            if (newDiscountAmount.compareTo(oldDiscountAmount) >= 0) {
                                oldPrice = newPrice;
                            } else {
                                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "满减金额必须按照天数阶梯上升");
                            }
                        }
                    }
                } else {
                    List<SignupDiscountFlexiblePricing> list = dto.getDiscountFlexiblePricing();

                    for (SignupDiscountFlexiblePricing o : list) {
                        Double discount = Double.valueOf(o.getDiscount());
                        Double minDiscount = Double.valueOf(o.getMinDiscount());
                        Double maxDiscount = Double.valueOf(o.getMaxDiscount());
                        if (discount < minDiscount || discount > maxDiscount) {
                            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "灵活定价，输入的折扣不在范围内");
                        }
                    }

                    if (list.size() > 1) {
                        list.sort(Comparator.comparing(o -> Integer.valueOf(o.getDays()))); // 根据天数从小到大排序（升序）
                        SignupDiscountFlexiblePricing oldPrice = list.get(0);
                        for (int i = 1; i < list.size(); i++) {
                            SignupDiscountFlexiblePricing newPrice = list.get(i);
                            BigDecimal oldDiscount = new BigDecimal(oldPrice.getDiscount());
                            BigDecimal newDiscount = new BigDecimal(newPrice.getDiscount());
                            if (oldDiscount.compareTo(newDiscount) >= 0) {
                                oldPrice = newPrice;
                            } else {
                                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "折扣必须按照天数阶梯上升");
                            }
                        }
                    }
                }
            }

            ProprietaryActivitySignup newSignupAct = new ProprietaryActivitySignup();
            newSignupAct.setActivityId(oldSignupAct.getActivityId());
            String vehicleModelIds = String.join(",", dto.getVehicleModelIds().stream().map(o -> String.valueOf(o)).collect(Collectors.toList()));
            String fullMinusFlexiblePricing = "";
            if (!CollectionUtils.isEmpty(dto.getFullMinusFlexiblePricing())) {
                fullMinusFlexiblePricing = JSON.toJSONString(dto.getFullMinusFlexiblePricing());
            }
            String discountFlexiblePricing = "";
            if (!CollectionUtils.isEmpty(dto.getDiscountFlexiblePricing())) {
                discountFlexiblePricing = JSON.toJSONString(dto.getDiscountFlexiblePricing());
            }
            StoreInfoCombobox store = configLoader.getStore(dto.getStoreId());
            newSignupAct.setOrgCode(store.getOperOrgCode());
            newSignupAct.setStoreId(dto.getStoreId());
            newSignupAct.setSignupStatus(ActivitySignupStatusEnum.SIGNUP.getType());
            newSignupAct.setVehicleModelIds(vehicleModelIds);
            newSignupAct.setFullMinusFlexiblePricing(fullMinusFlexiblePricing);
            newSignupAct.setDiscountFlexiblePricing(discountFlexiblePricing);
            newSignupAct.setCreateOperName(currentUser.getUserName());
            newSignupAct.setCreateOperId(currentUser.getUserId());
            if(dto.getIsAllVehicle() != null){
                newSignupAct.setIsAllVehicle(dto.getIsAllVehicle());
            }
            // 判断信息是否有做更改
            if (oldSignupAct.getVehicleModelIds().equals(newSignupAct.getVehicleModelIds()) &&
                    oldSignupAct.getFullMinusFlexiblePricing().equals(newSignupAct.getFullMinusFlexiblePricing()) &&
                    oldSignupAct.getDiscountFlexiblePricing().equals(newSignupAct.getDiscountFlexiblePricing())) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "编辑信息没有变更");
            }

            // 日志
            String logContent = setLogContent(oldSignupAct, newSignupAct);
            if (logContent.length() > 1024) {
                logContent = logContent.substring(0, 1021) + "...";
            }

            signupProprietaryActivityManager.updateSignupActivity(newSignupAct, oldSignupAct.getId(), currentUser, logContent);
            return UpdateSignupProprietaryActivityRes.ok();
        } catch (BusinessException e) {
            log.error("updateSignupProprietaryActivity 编辑报名失败, id = {}", dto.getId());
            return UpdateSignupProprietaryActivityRes.failed(e.getCode(), e.getMessage());
        }
    }

    /*退出报名*/
    @Override
    public QuitProprietaryActivityRes quitProprietaryActivity(QuitProprietaryActivityReq req) {
        try {
            CurrentUser currentUser = req.getCurrentUser();
            long id = req.getId();
            if (id <= 0) {
                log.error("无效的活动报名id, id = {}", id);
                throw new BusinessException(ErrorEnum.INVALID_ACTIVITY_SIGNUP_ID.getCode(), ErrorEnum.INVALID_ACTIVITY_SIGNUP_ID.getMsg());
            }
            if (currentUser == null) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "用户信息为空");
            }
            // 查询老报名数据
            LambdaQueryWrapper<ProprietaryActivitySignup> queryWrapper = new LambdaQueryWrapper<ProprietaryActivitySignup>()
                    .eq(ProprietaryActivitySignup::getId, req.getId())
                    .eq(ProprietaryActivitySignup::getIsDeleted, IsDeletedEnum.NORMAL.getType());
            // 查询报名信息
            ProprietaryActivitySignup sinnupInfo = proprietaryActivitySignupMapper.selectOne(queryWrapper);
            if (sinnupInfo == null) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "未查到报名信息");
            }
            GetProprietaryActivityInfoBo act = proprietaryActivityService.getProprietaryActivityInfo(sinnupInfo.getActivityId());
            if (act == null) {
                log.error("未查询到活动信息, id = {}", id);
                throw new BusinessException(ErrorEnum.NO_FOUNT_ACTIVITY_DETAIL.getCode(), ErrorEnum.NO_FOUNT_ACTIVITY_DETAIL.getMsg());
            }
            if (act.getActivityStatus() != ActivityStatusEnum.EFFECT.getType()) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "只有生效中的活动才能退出报名");
            }

            // 退出报名
            String logContent = "门店 " + configLoader.getStoreName(sinnupInfo.getStoreId()) + " 退出活动报名；";
            signupProprietaryActivityManager.quitSignupActivity(sinnupInfo, act, currentUser, logContent);

            return QuitProprietaryActivityRes.ok();
        } catch (BusinessException e) {
            log.error("quitProprietaryActivity 退出活动失败,id = {}", req.getId());
            return QuitProprietaryActivityRes.failed(e.getCode(), e.getMessage());
        }
    }

    @Override
    public GetSignupProprietaryActivityDetailBo getSignupProprietaryActivityDetail(long id) throws BusinessException {
        try {
            if (id <= 0) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "报名id不能小于等于0");
            }
            LambdaQueryWrapper<ProprietaryActivitySignup> queryWrapper = new LambdaQueryWrapper<ProprietaryActivitySignup>()
                    .eq(ProprietaryActivitySignup::getId, id);
            // 查询报名信息
            ProprietaryActivitySignup signupInfo = proprietaryActivitySignupMapper.selectOne(queryWrapper);
            if (signupInfo == null) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "查询报名详情失败");
            }

            // 查询活动详情
            GetProprietaryActivityInfoBo act = proprietaryActivityService.getProprietaryActivityInfo(signupInfo.getActivityId());
            if (act == null) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "查询活动详情失败");
            }

            GetSignupProprietaryActivityDetailBo bo = new GetSignupProprietaryActivityDetailBo();
            bo.setActivityName(act.getActivityName());
            bo.setActivityTag(act.getActivityTag());
            if (!CollectionUtils.isEmpty(act.getOrgCodes())) {
                bo.setOrgCodes(act.getOrgCodes());
            }
            if(!CollectionUtils.isEmpty(act.getStoreIdList())){
                bo.setStoreIdList(act.getStoreIdList());
            }
            if (!CollectionUtils.isEmpty(act.getVehicleModelIds())) {
                bo.setVehicleModelIds(act.getVehicleModelIds());
            }
            bo.setAllStore(act.getAllStore());
            bo.setActivityType(act.getActivityType());
            bo.setPricingType(act.getPricingType());
            bo.setDiscountLatitude(act.getDiscountLatitude());
            bo.setFullMinusStandardPricing(act.getFullMinusStandardPricing());
            bo.setFullMinusFlexiblePricing(act.getFullMinusFlexiblePricing());
            bo.setDiscountStandardPricing(act.getDiscountStandardPricing());
            bo.setDiscountFlexiblePricing(act.getDiscountFlexiblePricing());
            bo.setMinRentDays(act.getMinRentDays());
            bo.setMaxRentDays(act.getMaxRentDays());
            bo.setSameDayUseFlag(act.getSameDayUseFlag());
            bo.setAvailableOnHolidays(act.getAvailableOnHolidays());
            bo.setSignUpStartDate(act.getSignUpStartDate());
            bo.setSignUpEndDate(act.getSignUpEndDate());
            bo.setPickUpDate(act.getPickUpDate());
            bo.setReturnDate(act.getReturnDate());
            bo.setActivityStartDate(act.getActivityStartDate());
            bo.setActivityEndDate(act.getActivityEndDate());
            bo.setUnavailableDateRanges(act.getUnavailableDateRanges());
            bo.setActivityId(act.getId());
            bo.setId(signupInfo.getId());
            bo.setSignupFullMinusFlexiblePricing(JSON.parseArray(signupInfo.getFullMinusFlexiblePricing(), SignupFullMinusFlexiblePricing.class));
            bo.setSignupDiscountFlexiblePricing(JSON.parseArray(signupInfo.getDiscountFlexiblePricing(), SignupDiscountFlexiblePricing.class));
            bo.setSignupOrgCode(signupInfo.getOrgCode());
            bo.setSignupVehicleModelIds(Arrays.stream(signupInfo.getVehicleModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList()));
            bo.setAllOrgCodes(act.getAllOrgCodes());
            bo.setAllModelIds(act.getAllModelIds());
            bo.setSignupStoreId(signupInfo.getStoreId());
            bo.setSpecifyDateFlag(act.getSpecifyDateFlag());
            bo.setSpecifyDate(act.getSpecifyDate());
            bo.setBlockHolidayFlag(act.getBlockHolidayFlag());
            bo.setIntersectionFlag(act.getIntersectionFlag());
            return bo;
        } catch (Exception e) {
            throw new BusinessException(ErrorEnum.INVALID_ACTNAME.getCode(), e.getMessage());
        }
    }

    @Override
    public GetSignUpProprietaryActivityByVehicleModelIdsRes getSignUpProprietaryActivityByVehicleModelIds(GetSignUpProprietaryActivityByVehicleModelIdsBo bo) throws BusinessException {
        // 参数合法性校验
        if (CollectionUtils.isEmpty(bo.getStoreVehicleModelId())) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "车型参数不能为空");
        }
        if (StringUtils.isEmpty(bo.getStoreId())) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "门店参数不能为空");
        }
        if (StringUtils.isEmpty(bo.getPlanPickupDateTime())) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "取车时间参数不能为空");
        }
        if (bo.getRentDay() <= 0) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "租期不能为0天");
        }

        LocalDate pickupDt = DateUtil.getLocalDateFromStr(bo.getPlanPickupDateTime().substring(0, 8), DateUtil.DATE_TYPE8);
        LocalDate returnDt = null;
        if (!StringUtils.isEmpty(bo.getPlanReturnDateTime())) {
            returnDt = DateUtil.getLocalDateFromStr(bo.getPlanReturnDateTime().substring(0, 8), DateUtil.DATE_TYPE8);
        }
        String curStr = DateUtil.dateToString(new Date(), DateUtil.DATE_TYPE5);
        LocalDate curDt = DateUtil.getLocalDateFromStr(curStr, DateUtil.DATE_TYPE5); // 下单日期

        // 查询该门店已报名的活动
        LambdaQueryWrapper<ProprietaryActivitySignup> queryWrapper = new LambdaQueryWrapper<ProprietaryActivitySignup>()
                .eq(ProprietaryActivitySignup::getStoreId, bo.getStoreId())
                .eq(ProprietaryActivitySignup::getIsDeleted, IsDeletedEnum.NORMAL.getType());
        List<ProprietaryActivitySignup> list = proprietaryActivitySignupMapper.selectList(queryWrapper);

        // 数据组装
        GetSignUpProprietaryActivityByVehicleModelIdsRes.Builder builder = GetSignUpProprietaryActivityByVehicleModelIdsRes.newBuilder();
        Map<Long, ProprietaryActivityList> values = new HashMap<>();

        // 查询活动详情
        Set<Long> activityId = list.stream().map(ProprietaryActivitySignup::getActivityId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(activityId)) {
            return builder.putAllResMap(new HashMap<>()).build();
        }
        LambdaQueryWrapper<ProprietaryActivity> queryWrapper2 = new LambdaQueryWrapper<ProprietaryActivity>()
                .in(ProprietaryActivity::getId, activityId)
                .eq(ProprietaryActivity::getActivityStatus, ActivityStatusEnum.EFFECT.getType())
                .eq(ProprietaryActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType());
        List<ProprietaryActivity> proprietaryActivities = proprietaryActivityMapper.selectList(queryWrapper2);
        Map<Long, ProprietaryActivity> ProprietaryActivityMap = proprietaryActivities.stream().collect(Collectors.toMap(ProprietaryActivity::getId, o -> o));

        // 符合的车型过滤
        for (Long vmid : bo.getStoreVehicleModelId()) {
            ProprietaryActivityList.Builder builder1 = ProprietaryActivityList.newBuilder();
            List<ProprietaryActivityInfo> tmpList = new ArrayList();
            for (ProprietaryActivitySignup singnInfo : list) {
                // 参与报名的车型
                List<Long> vehicleModelIds = Arrays.stream(singnInfo.getVehicleModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
                if (vehicleModelIds.contains(vmid)) {
                    // 查询活动详情
                    ProprietaryActivity act = ProprietaryActivityMap.get(singnInfo.getActivityId());
                    /*
                    LambdaQueryWrapper<ProprietaryActivity> queryWrapper2 = new LambdaQueryWrapper<ProprietaryActivity>()
                            .in(ProprietaryActivity::getId, singnInfo.getActivityId())
                            .eq(ProprietaryActivity::getActivityStatus, ActivityStatusEnum.EFFECT.getType())
                            .eq(ProprietaryActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType());
                    ProprietaryActivity act = proprietaryActivityMapper.selectOne(queryWrapper2);
                    */
                    /************************  校验活动是否可用  **********************/
                    if (checkActCanUse(vmid, act, returnDt, pickupDt, curDt, bo)) {
                        ProprietaryActivityInfo.Builder b = ProprietaryActivityInfo.newBuilder();
                        b.setActivityId(act.getId());
                        b.setSignupId(singnInfo.getId());
                        b.setActivityName(act.getActivityName());
                        b.setActivityTag(act.getActivityTag());
                        b.setActivityType(act.getActivityType());
                        b.setPricingType(act.getPricingType());
                        b.setMaxRentDays(act.getMaxRentDays() == null ? 0 :act.getMaxRentDays());
                        b.setSameDayUseFlag(act.getSameDayUseFlag() == null ? 0 : act.getSameDayUseFlag());
                        List<com.saicmobility.evcard.md.act.dto.proprietary.FullMinusStandardPricing> fullMinusStandardPricingList = JSON.parseArray(act.getFullMinusStandardPricing(), com.saicmobility.evcard.md.act.dto.proprietary.FullMinusStandardPricing.class);
                        if (!CollectionUtils.isEmpty(fullMinusStandardPricingList)) {
                            List<FullMinusStandardPricing> l = fullMinusStandardPricingList.stream().map(o -> {
                                return FullMinusStandardPricing.newBuilder()
                                        .setDays(o.getDays())
                                        .setDiscountAmount(o.getDiscountAmount())
                                        .build();
                            }).collect(Collectors.toList());
                            b.addAllFullMinusStandardPricing(l);
                        }
                        List<com.saicmobility.evcard.md.act.dto.proprietary.DiscountStandardPricing> discountStandardPricingList = JSON.parseArray(act.getDiscountStandardPricing(), com.saicmobility.evcard.md.act.dto.proprietary.DiscountStandardPricing.class);
                        if (!CollectionUtils.isEmpty(discountStandardPricingList)) {
                            List<DiscountStandardPricing> l = discountStandardPricingList.stream().map( o -> {
                                return DiscountStandardPricing.newBuilder()
                                        .setDays(o.getDays())
                                        .setDiscount(o.getDiscount())
                                        .build();
                            }).collect(Collectors.toList());
                            b.addAllDiscountStandardPricing(l);
                        }
                        List<SignupFullMinusFlexiblePricing> signupFullMinusFlexiblePricingList = JSON.parseArray(singnInfo.getFullMinusFlexiblePricing(), SignupFullMinusFlexiblePricing.class);
                        if (!CollectionUtils.isEmpty(signupFullMinusFlexiblePricingList)) {
                            List<com.saicmobility.evcard.md.mdactservice.api.SignupFullMinusFlexiblePricing> l = signupFullMinusFlexiblePricingList.stream().map(o -> {
                                return com.saicmobility.evcard.md.mdactservice.api.SignupFullMinusFlexiblePricing.newBuilder()
                                        .setDays(o.getDays())
                                        .setDiscountAmount(o.getDiscountAmount())
                                        .setMaxDiscountAmount(o.getMaxDiscountAmount())
                                        .setMinDiscountAmount(o.getMinDiscountAmount())
                                        .build();
                            }).collect(Collectors.toList());

                            b.addAllSignupFullMinusFlexiblePricing(l);
                        }
                        List<SignupDiscountFlexiblePricing> signupDiscountFlexiblePricingList = JSON.parseArray(singnInfo.getDiscountFlexiblePricing(), SignupDiscountFlexiblePricing.class);
                        if (!CollectionUtils.isEmpty(signupDiscountFlexiblePricingList)) {
                            List<com.saicmobility.evcard.md.mdactservice.api.SignupDiscountFlexiblePricing> l = signupDiscountFlexiblePricingList.stream().map(o -> {
                                return com.saicmobility.evcard.md.mdactservice.api.SignupDiscountFlexiblePricing.newBuilder()
                                        .setDays(o.getDays())
                                        .setDiscount(o.getDiscount())
                                        .setMaxDiscount(o.getMaxDiscount())
                                        .setMinDiscount(o.getMinDiscount())
                                        .build();
                            }).collect(Collectors.toList());
                            b.addAllSignupDiscountFlexiblePricing(l);
                        }
                        tmpList.add(b.build());
                    } else {
                        continue;
                    }
                } else {
                    continue;
                }
            }
            if (!CollectionUtils.isEmpty(tmpList)) {
                builder1.addAllInfo(tmpList);
                values.put(vmid, builder1.build());
            }
        }

        builder.putAllResMap(values);
        return builder.build();
    }

    private boolean checkActCanUse(Long vehicleModelId, ProprietaryActivity act, LocalDate returnDt, LocalDate pickupDt, LocalDate curDt, GetSignUpProprietaryActivityByVehicleModelIdsBo bo) {
        if (act == null) {
            return false;
        }
        // 校验参与门店是否发生变化，参与机构是否包含入参storeId
        if (!"-1".equals(act.getStoreIds())) {
            List<Long> storeIdList = CommonUtils.storeIdsSplit(act.getStoreIds());
            if (!storeIdList.contains(bo.getStoreId())) {
                return false;
            }
        }
        if (!"-1".equals(act.getVehicleModelIds())) {
            // 校验参与活动的车型，如果活动参与车型发生变化，没有该车型就跳过
            List<Long> vmIds = Arrays.stream(act.getVehicleModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            if (!vmIds.contains(vehicleModelId)) {
                return false;
            }
        }

        boolean isPickup = false;
        boolean isOrder = false;
        boolean isAbleTime = true;
        boolean isMinRent = false;
        boolean isAvailableOnHolidays = true;
        boolean isSameDateFlag = true;
        boolean isSpecifyDateFlag = true;

        // 取还车时间是否在
        if (returnDt == null) {
            if (pickupDt.compareTo(act.getPickUpDate()) >= 0) {
                isPickup = true;
            }
        } else {
            // 取还车时间交集开关，1=取还车都必须在时间范围内 2=取还车任一时间在时间范围内
            if (act.getIntersectionFlag() == 1) {
                if (pickupDt.compareTo(act.getPickUpDate()) >= 0 && returnDt.compareTo(act.getReturnDate()) <= 0) {
                    isPickup = true;
                }
            }
            else {
                if ((pickupDt.compareTo(act.getPickUpDate()) >= 0 && pickupDt.compareTo(act.getReturnDate()) <= 0)
                        || (returnDt.compareTo(act.getPickUpDate()) >= 0 && returnDt.compareTo(act.getReturnDate()) <= 0)) {
                    isPickup = true;
                }
            }
        }

        // 下单时间在活动开始结束日期
        if (curDt.compareTo(act.getActivityStartDate()) >= 0 && curDt.compareTo(act.getActivityEndDate()) <= 0) {
            isOrder = true;
        }

        // 节假日  节假日是否可用：1-可用、2-不可用
        if (act.getAvailableOnHolidays() == 2 && bo.getIsHoliday() == 1) {
            isAvailableOnHolidays = false;
        }

        // 最小租期
        if (act.getMinRentDays() <= bo.getRentDay()) {
            isMinRent = true;
        }

        // 不可用日期
        List<com.saicmobility.evcard.md.act.dto.market.TimeRange> timeRanges = JSON.parseArray(act.getUnavailableDateRanges(), com.saicmobility.evcard.md.act.dto.market.TimeRange.class);
        for (com.saicmobility.evcard.md.act.dto.market.TimeRange timeRange : timeRanges) {
            LocalDate start = DateUtil.getLocalDateFromStr(timeRange.getStartDate(), DateUtil.DATE_TYPE5);
            LocalDate end = DateUtil.getLocalDateFromStr(timeRange.getEndDate(), DateUtil.DATE_TYPE5);
            if (returnDt == null) {
                if (pickupDt.compareTo(start) >= 0 && pickupDt.compareTo(end) <= 0) {
                    isAbleTime = false;
                }
            } else {
                if ((pickupDt.compareTo(start) >= 0 && pickupDt.compareTo(end) <= 0) || (returnDt.compareTo(start) >= 0 && returnDt.compareTo(end) <= 0)) {
                    isAbleTime = false;
                }
            }
        }

        //尾单折扣，仅限当天下单可用判断
        if (act.getSameDayUseFlag() != null && act.getSameDayUseFlag() == 1){
            //为1时代表当天取车可用进行判断
            if (pickupDt.compareTo(curDt) == 0){
                //下单日期与取车日期相同时，返回true
                isSameDateFlag = true;
            }else {
                //下单日期与取车日期不相同时，返回false
                isSameDateFlag = false;
            }

        }
        //判断是否符合指定日期
        if (act.getSpecifyDateFlag() != null && act.getSpecifyDateFlag() == 1 ){
            if (StringUtils.isEmpty(bo.getFirstOrderCreateTime())) {
                //未传首单下单日期
                isSpecifyDateFlag = false;
            }
            LocalDate firstOrderCreateTimeDt = DateUtil.getLocalDateFromStr(bo.getFirstOrderCreateTime().substring(0, 8), DateUtil.DATE_TYPE8);
            DayOfWeek dayOfWeek = firstOrderCreateTimeDt.getDayOfWeek();
            if (!StringUtils.isEmpty(act.getSpecifyDate())){
                //不为空，代表有指定日期
                if (!checkSpecifyDate(act.getSpecifyDate(),dayOfWeek)){
                    isSpecifyDateFlag = false;
                }
            }
            if (act.getBlockHolidayFlag() != null && act.getBlockHolidayFlag() == 1 && bo.getFirstOrderCreateTimeIsHoliday() == 1){
                //屏蔽节假日且首单下单日期为节假日，则屏蔽
                isSpecifyDateFlag = false;
            }

        }


        if (isPickup && isOrder && isAbleTime && isMinRent && isAvailableOnHolidays && isSameDateFlag && isSpecifyDateFlag) {
            return true;
        }

        return false;
    }

    /**
     * SQL写法查询报名列表
     */
    @Override
    public GetSignupProprietaryActivityListRes getSignupProprietaryActivityList(GetSignupProprietaryActivityListDto dto) throws BusinessException {
        // 参数校验
        LocalDate signUpStartDate = null;
        LocalDate signUpEndDate = null;
        if (!StringUtils.isEmpty(dto.getSignUpStartDate())) {
            signUpStartDate = DateUtil.getLocalDateFromStr(dto.getSignUpStartDate(), DateUtil.DATE_TYPE5);
        }
        if (!StringUtils.isEmpty(dto.getSignUpEndDate())) {
            signUpEndDate = DateUtil.getLocalDateFromStr(dto.getSignUpEndDate(), DateUtil.DATE_TYPE5);
        }
        if (signUpStartDate != null && signUpEndDate != null) {
            if (signUpEndDate.compareTo(signUpStartDate) < 0) {
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "结束日期不能小于开始日期");
            }
        }
        if (StringUtils.isEmpty(dto.getOrgCode())) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "缺少筛选机构");
        }
        if (dto.getPageNum() == 0) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "缺少分页页码");
        }
        if (dto.getPageSize() == 0) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "缺少分页数量");
        }
        try {
            Page<SignupProprietaryActivityBo> pageResult = null;
            //无门店查询
            Page<SignupProprietaryActivityBo> page = new Page<>(dto.getPageNum(), dto.getPageSize(), true);
            if(StringUtils.isEmpty(dto.getStoreId())){
                pageResult = proprietaryActivitySignupMapper.querySignupProprietaryActivityList(
                        dto.getActivityName(),
                        dto.getActivityTag(),
                        dto.getActivityStatus(),
                        dto.getActivityType(),
                        dto.getPricingType(),
                        dto.getSignupStatus(),
                        signUpStartDate,
                        signUpEndDate,
                        dto.getOrgCode(),
                        page);
            }else{
                pageResult = proprietaryActivitySignupMapper.querySignupProprietaryActivityListGroupOrgCode(
                        dto.getActivityName(),
                        dto.getActivityTag(),
                        dto.getActivityStatus(),
                        dto.getActivityType(),
                        dto.getPricingType(),
                        dto.getSignupStatus(),
                        signUpStartDate,
                        signUpEndDate,
                        dto.getOrgCode(),
                        dto.getStoreId(),
                        page);
            }
            if (pageResult == null) {
                log.error("未查询到活动报名数据（分页）");
                throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "未查询到活动报名数据");
            }
            if(!StringUtils.isEmpty(dto.getStoreId()) && !pageResult.getRecords().isEmpty()){
                List<SignupProprietaryActivityBo> activityStoreList = new ArrayList<>(pageResult.getRecords().size());
                for (int i = 0; i < pageResult.getRecords().size(); i++) {
                    SignupProprietaryActivityBo bo = pageResult.getRecords().get(i);
                    LambdaQueryWrapper<ProprietaryActivitySignup> queryWrapper = new LambdaQueryWrapper<ProprietaryActivitySignup>()
                            .eq(ProprietaryActivitySignup::getStoreId, dto.getStoreId())
                            .eq(ProprietaryActivitySignup::getActivityId,bo.getActivityId())
                            .eq(ProprietaryActivitySignup::getOrgCode, dto.getOrgCode())
                            .eq(ProprietaryActivitySignup::getIsDeleted, IsDeletedEnum.NORMAL.getType());
                    ProprietaryActivitySignup activitySignup = proprietaryActivitySignupMapper.selectOne(queryWrapper);
                    SignupProprietaryActivityBo addActivitySignup = new SignupProprietaryActivityBo();
                    BeanUtil.copyProperties(bo,addActivitySignup);
                    if(activitySignup == null){
                        addActivitySignup.setSignupStatus(1);//未签约
                        addActivitySignup.setCreateTime(new Date(0));
                    }else {
                        addActivitySignup.setStoreId(activitySignup.getStoreId());
                        addActivitySignup.setSignupStatus(activitySignup.getSignupStatus());
                        addActivitySignup.setCreateTime(activitySignup.getCreateTime());
                        addActivitySignup.setId(activitySignup.getId());
                    }
                    activityStoreList.add(addActivitySignup);
                }
                pageResult.getRecords().clear();
                pageResult.getRecords().addAll(activityStoreList.stream()
                        .sorted(Comparator.comparing(SignupProprietaryActivityBo::getActivityStatus)
                                .thenComparing(SignupProprietaryActivityBo::getCreateTime).reversed()
                                .thenComparing(SignupProprietaryActivityBo::getActivityId).reversed())
                        .collect(Collectors.toList()));
            }

            List<SignupProprietaryActivityInfo> list = new ArrayList<>();
            for (SignupProprietaryActivityBo bo : pageResult.getRecords()) {
                SignupProprietaryActivityInfo.Builder b = SignupProprietaryActivityInfo.newBuilder();
                b.setActivityId(bo.getActivityId());
                b.setActivityName(bo.getActivityName());
                b.setActivityTag(bo.getActivityTag());
                b.setActivityType(bo.getActivityType());
                b.setSignUpStartDate(bo.getSignUpStartDate().toString());
                b.setSignUpEndDate(bo.getSignUpEndDate().toString());
                b.setPricingType(bo.getPricingType());
                b.setActivityStatus(bo.getActivityStatus());
                b.setId(bo.getId());
                b.setSignupStatus(bo.getSignupStatus());
                Long storeId = bo.getStoreId();
                b.setStoreName("未参加");
                if(storeId!=null){
                    String storeName = configLoader.getStoreName(storeId);
                    b.setStoreName(storeName);
                    b.setStoreId(storeId);
                }
                list.add(b.build());
            }
            GetSignupProprietaryActivityListRes.Builder builder = GetSignupProprietaryActivityListRes.newBuilder();
            return builder.addAllList(list).setTotal((int) pageResult.getTotal()).build();
        } catch (Exception e) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), e.getMessage());
        }
    }

    @Override
    public GetActSignUpDetailByVehicleModelIdRes getActSignUpDetailByVehicleModelId(GetActSignUpDetailByVehicleModelIdReq req) throws BusinessException {
        Long storeId = req.getStoreId();
        String planPickupDateTime = req.getPlanPickupDateTime();
        String firstOrderCreateTime = req.getFirstOrderCreateTime();
        Long storeVehicleModelId = req.getStoreVehicleModelId();
        int isHoliday = req.getIsHoliday();

        // 参数合法性校验
        if (storeVehicleModelId <= 0) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "车型参数不能为空");
        }
        if (storeId == null) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "门店参数不能为空");
        }
        if (StringUtils.isEmpty(planPickupDateTime)) {
            throw new BusinessException(ErrorEnum.REQUEST_ERROR.getCode(), "取车时间参数不能为空");
        }

        LocalDate pickupDt = DateUtil.getLocalDateFromStr(planPickupDateTime.substring(0, 8), DateUtil.DATE_TYPE8);
        String curStr = DateUtil.dateToString(new Date(), DateUtil.DATE_TYPE5);
        LocalDate curDt = DateUtil.getLocalDateFromStr(curStr, DateUtil.DATE_TYPE5); // 下单日期

        // 查询该门店已报名的活动
        LambdaQueryWrapper<ProprietaryActivitySignup> queryWrapper = new LambdaQueryWrapper<ProprietaryActivitySignup>()
                .eq(ProprietaryActivitySignup::getStoreId, storeId)
                .eq(ProprietaryActivitySignup::getIsDeleted, IsDeletedEnum.NORMAL.getType());
        List<ProprietaryActivitySignup> list = proprietaryActivitySignupMapper.selectList(queryWrapper);

        // 符合的活动过滤
        List<ActSignUpDetailInfo> infoList = new ArrayList();
        for (ProprietaryActivitySignup singnInfo : list) {
            // 参与报名的车型
            List<Long> vehicleModelIds = Arrays.stream(singnInfo.getVehicleModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            if (vehicleModelIds.contains(storeVehicleModelId)) {
                // 查询活动详情
                LambdaQueryWrapper<ProprietaryActivity> queryWrapper2 = new LambdaQueryWrapper<ProprietaryActivity>()
                        .in(ProprietaryActivity::getId, singnInfo.getActivityId())
                        .eq(ProprietaryActivity::getActivityStatus, ActivityStatusEnum.EFFECT.getType())
                        .eq(ProprietaryActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType());
                ProprietaryActivity act = proprietaryActivityMapper.selectOne(queryWrapper2);
                if (act == null) {
                    continue;
                }
                // 校验参与机构是否发生变化，参与机构是否包含入参门店
                if (!"-1".equals(act.getStoreIds())) {
                    List<Long> storeIdList = CommonUtils.storeIdsSplit(act.getStoreIds());
                    if (!storeIdList.contains(storeId)) {
                        continue;
                    }
                }
                // 校验参与活动的车型，如果活动参与车型发生变化，没有该车型就跳过
                if (!"-1".equals(act.getVehicleModelIds())) {
                    List<Long> vmIds = Arrays.stream(act.getVehicleModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
                    if (!vmIds.contains(storeVehicleModelId)) {
                        continue;
                    }
                }
                // 校验取车日期
                if (pickupDt.compareTo(act.getPickUpDate()) < 0) {
                    continue;
                }
                //尾单折扣，仅限当天下单可用判断
                if (act.getSameDayUseFlag() != null && act.getSameDayUseFlag() == 1){
                    //为1时代表当天取车可用,进行判断
                    if (!(pickupDt.compareTo(curDt) == 0)){
                        //下单日期与取车日期相同时，返回true

                        //下单日期与取车日期不相同时，返回false
                        continue;
                    }
                }
                //判断是否符合指定日期
                if (act.getSpecifyDateFlag() != null && act.getSpecifyDateFlag() == 1 ){
                    if (StringUtils.isEmpty(firstOrderCreateTime)) {
                        //未传首单下单日期
                        log.error("storeId -> {},activityName -> {},activityTag -> {},首单下单时间未传，无法判断该活动是否匹配",storeId,act.getActivityName(),act.getActivityTag());
                        continue;
                    }
                    LocalDate firstOrderCreateTimeDt = DateUtil.getLocalDateFromStr(firstOrderCreateTime.substring(0, 8), DateUtil.DATE_TYPE8);
                    DayOfWeek dayOfWeek = firstOrderCreateTimeDt.getDayOfWeek();
                    if (!StringUtils.isEmpty(act.getSpecifyDate())){
                        //不为空，代表有指定日期
                        if (!checkSpecifyDate(act.getSpecifyDate(),dayOfWeek)){
                            continue;
                        }
                    }
                    if (act.getBlockHolidayFlag() != null && act.getBlockHolidayFlag() == 1 && isHoliday == 1){
                        //屏蔽节假日且首单下单日期为节假日，则屏蔽
                        continue;
                    }

                }

                // 加入数组
                infoList.add(setActSignUpDetailInfo(singnInfo, act));
            } else {
                continue;
            }
        }

        GetActSignUpDetailByVehicleModelIdRes.Builder builder = GetActSignUpDetailByVehicleModelIdRes.newBuilder();
        return builder.addAllInfo(infoList).build();
    }
    //校验下单日期和活动匹配是否一致
    //返回TRUE为匹配，False为不匹配
    private Boolean checkSpecifyDate(String specifyDate,DayOfWeek dayOfWeek){
        if (StringUtils.isEmpty(specifyDate)){
            return Boolean.FALSE;
        }
        switch (dayOfWeek) {
            case SUNDAY:
                if (specifyDate.contains("7")){
                    return Boolean.TRUE;
                }
                break;
            case MONDAY:
                if (specifyDate.contains("1")){
                    return Boolean.TRUE;
                }
                break;
            case TUESDAY:
                if (specifyDate.contains("2")){
                    return Boolean.TRUE;
                }
                break;
            case WEDNESDAY:
                if (specifyDate.contains("3")){
                    return Boolean.TRUE;
                }
                break;
            case THURSDAY:
                if (specifyDate.contains("4")){
                    return Boolean.TRUE;
                }
                break;
            case FRIDAY:
                if (specifyDate.contains("5")){
                    return Boolean.TRUE;
                }
                break;
            case SATURDAY:
                if (specifyDate.contains("6")){
                    return Boolean.TRUE;
                }
                break;
        }
        return Boolean.FALSE;
    }

    private ActSignUpDetailInfo setActSignUpDetailInfo(ProprietaryActivitySignup singnInfo, ProprietaryActivity act) {
        ActSignUpDetailInfo.Builder b = ActSignUpDetailInfo.newBuilder();
        b.setActivityName(act.getActivityName());
        b.setActivityTag(act.getActivityTag());
        if ("-1".equals(act.getOrgCodes())) {
            b.addAllOrgCodes(new ArrayList<String>());
            b.setAllOrgCodes(1);
        } else {
            b.setAllOrgCodes(2);
            List<String> l = CommonUtils.orgCodesSplit(act.getOrgCodes());
            b.addAllOrgCodes(l);
        }
        int allStore = com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils.equals(act.getStoreIds(),"-1")? 1:2;
        List<Long> storeIdList = com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils.equals(act.getStoreIds(),"-1")? new ArrayList<>():CommonUtils.storeIdsSplit(act.getStoreIds());
        b.setAllStore(allStore);
        b.addAllStoreIdList(storeIdList);
        if ("-1".equals(act.getVehicleModelIds())) {
            b.addAllVehicleModelIds(new ArrayList<Long>());
            b.setAllModelIds(1);
        } else {
            b.setAllModelIds(2);
            List<Long> vehicleModelIds = Arrays.stream(act.getVehicleModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(vehicleModelIds)) {
                b.addAllVehicleModelIds(vehicleModelIds);
            }
        }
        b.setActivityType(act.getActivityType());
        b.setPricingType(act.getPricingType());
        b.setDiscountLatitude(act.getDiscountLatitude());
        // 满减规范定价
        List<com.saicmobility.evcard.md.act.dto.proprietary.FullMinusStandardPricing> fullMinusStandardPricingList = JSON.parseArray(act.getFullMinusStandardPricing(), com.saicmobility.evcard.md.act.dto.proprietary.FullMinusStandardPricing.class);
        if (!CollectionUtils.isEmpty(fullMinusStandardPricingList)) {
            List<FullMinusStandardPricing> l = fullMinusStandardPricingList.stream().map(o -> {
                return FullMinusStandardPricing.newBuilder()
                        .setDays(o.getDays())
                        .setDiscountAmount(o.getDiscountAmount())
                        .build();
            }).collect(Collectors.toList());
            b.addAllFullMinusStandardPricing(l);
        }
        // 报名前满减灵活定价
        List<com.saicmobility.evcard.md.act.dto.proprietary.FullMinusFlexiblePricing> fullMinusFlexiblePricingList = JSON.parseArray(act.getFullMinusFlexiblePricing(), com.saicmobility.evcard.md.act.dto.proprietary.FullMinusFlexiblePricing.class);
        if (!CollectionUtils.isEmpty(fullMinusFlexiblePricingList)) {
            List<com.saicmobility.evcard.md.mdactservice.api.FullMinusFlexiblePricing> l = fullMinusFlexiblePricingList.stream().map(o -> {
                return com.saicmobility.evcard.md.mdactservice.api.FullMinusFlexiblePricing.newBuilder()
                        .setDays(o.getDays())
                        .setMaxDiscountAmount(o.getMaxDiscountAmount())
                        .setMinDiscountAmount(o.getMinDiscountAmount())
                        .build();
            }).collect(Collectors.toList());
            b.addAllFullMinusFlexiblePricing(l);
        }
        // 打折规范定价
        List<com.saicmobility.evcard.md.act.dto.proprietary.DiscountStandardPricing> discountStandardPricingList = JSON.parseArray(act.getDiscountStandardPricing(), com.saicmobility.evcard.md.act.dto.proprietary.DiscountStandardPricing.class);
        if (!CollectionUtils.isEmpty(discountStandardPricingList)) {
            List<DiscountStandardPricing> l = discountStandardPricingList.stream().map( o -> {
                return DiscountStandardPricing.newBuilder()
                        .setDays(o.getDays())
                        .setDiscount(o.getDiscount())
                        .build();
            }).collect(Collectors.toList());
            b.addAllDiscountStandardPricing(l);
        }
        // 报名前打折灵活定价
        List<DiscountFlexiblePricing> discountFlexiblePricingList = JSON.parseArray(act.getDiscountFlexiblePricing(), DiscountFlexiblePricing.class);
        if (!CollectionUtils.isEmpty(discountFlexiblePricingList)) {
            List<com.saicmobility.evcard.md.mdactservice.api.DiscountFlexiblePricing> l = discountFlexiblePricingList.stream().map(o -> {
                return com.saicmobility.evcard.md.mdactservice.api.DiscountFlexiblePricing.newBuilder()
                        .setDays(o.getDays())
                        .setMaxDiscount(o.getMaxDiscount())
                        .setMinDiscount(o.getMinDiscount())
                        .build();
            }).collect(Collectors.toList());
            b.addAllDiscountFlexiblePricing(l);
        }
        b.setMinRentDays(act.getMinRentDays());
        b.setMaxRentDays(act.getMaxRentDays() == null ? 0 : act.getMaxRentDays());
        b.setSameDayUseFlag(act.getSameDayUseFlag() == null ? 0 : act.getSameDayUseFlag());
        b.setAvailableOnHolidays(act.getAvailableOnHolidays());
        b.setSignUpStartDate(act.getSignUpStartDate().toString());
        b.setSignUpEndDate(act.getSignUpEndDate().toString());
        b.setPickUpDate(act.getPickUpDate().toString());
        b.setReturnDate(act.getReturnDate().toString());
        b.setActivityStartDate(act.getActivityStartDate().toString());
        b.setActivityEndDate(act.getActivityEndDate().toString());
        // 不可用时间范围
        List<TimeRange> unavailableDateRanges = JSON.parseArray(act.getUnavailableDateRanges(), TimeRange.class);
        if (!CollectionUtils.isEmpty(unavailableDateRanges)) {
            List<com.saicmobility.evcard.md.mdactservice.api.TimeRange> l = unavailableDateRanges.stream().map(o -> {
                return com.saicmobility.evcard.md.mdactservice.api.TimeRange.newBuilder()
                        .setStartDate(o.getStartDate())
                        .setEndDate(o.getEndDate())
                        .build();
            }).collect(Collectors.toList());
            b.addAllUnavailableDateRanges(l);
        }

        b.setActivityId(act.getId());
        b.setId(singnInfo.getId());
        // 报名后满减灵活定价 (报名后会有值)
        List<SignupFullMinusFlexiblePricing> signupFullMinusFlexiblePricingList = JSON.parseArray(singnInfo.getFullMinusFlexiblePricing(), SignupFullMinusFlexiblePricing.class);
        if (!CollectionUtils.isEmpty(signupFullMinusFlexiblePricingList)) {
            List<com.saicmobility.evcard.md.mdactservice.api.SignupFullMinusFlexiblePricing> l = signupFullMinusFlexiblePricingList.stream().map(o -> {
                return com.saicmobility.evcard.md.mdactservice.api.SignupFullMinusFlexiblePricing.newBuilder()
                        .setDays(o.getDays())
                        .setDiscountAmount(o.getDiscountAmount())
                        .setMaxDiscountAmount(o.getMaxDiscountAmount())
                        .setMinDiscountAmount(o.getMinDiscountAmount())
                        .build();
            }).collect(Collectors.toList());

            b.addAllSignupFullMinusFlexiblePricing(l);
        }
        // 报名后打折灵活定价 (报名后会有值)
        List<SignupDiscountFlexiblePricing> signupDiscountFlexiblePricingList = JSON.parseArray(singnInfo.getDiscountFlexiblePricing(), SignupDiscountFlexiblePricing.class);
        if (!CollectionUtils.isEmpty(signupDiscountFlexiblePricingList)) {
            List<com.saicmobility.evcard.md.mdactservice.api.SignupDiscountFlexiblePricing> l = signupDiscountFlexiblePricingList.stream().map(o -> {
                return com.saicmobility.evcard.md.mdactservice.api.SignupDiscountFlexiblePricing.newBuilder()
                        .setDays(o.getDays())
                        .setDiscount(o.getDiscount())
                        .setMaxDiscount(o.getMaxDiscount())
                        .setMinDiscount(o.getMinDiscount())
                        .build();
            }).collect(Collectors.toList());
            b.addAllSignupDiscountFlexiblePricing(l);
        }
        List<Long> signupVehicleModelIds = Arrays.stream(singnInfo.getVehicleModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        b.setSignupOrgCode(singnInfo.getOrgCode());
        // 报名的车型id列表
        if (!CollectionUtils.isEmpty(signupVehicleModelIds)) {
            b.addAllSignupVehicleModelIds(signupVehicleModelIds);
        }
        b.setSpecifyDateFlag(act.getSpecifyDateFlag());
        b.setSpecifyDate(act.getSpecifyDate());
        b.setBlockHolidayFlag(act.getBlockHolidayFlag());
        b.setIntersectionFlag(act.getIntersectionFlag());
        return b.build();
    }


    /**
     * 编辑时，日志修改内容
     */
    private String setLogContent(ProprietaryActivitySignup oldSignupAct, ProprietaryActivitySignup newSignupAct) {
        StringBuilder sb = new StringBuilder("");
        sb.append("编辑报名 门店：" + configLoader.getStoreName(oldSignupAct.getStoreId()) + ";");
        if (!oldSignupAct.getVehicleModelIds().equals(newSignupAct.getVehicleModelIds())) {
            List<Long> oldList = Arrays.stream(oldSignupAct.getVehicleModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<Long> newList = Arrays.stream(newSignupAct.getVehicleModelIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            sb.append(" 报名车型由 " + configLoader.listVehicleName(oldList) + " 更新为 " + configLoader.listVehicleName(newList) + "；");
        }
        if (!oldSignupAct.getFullMinusFlexiblePricing().equals(newSignupAct.getFullMinusFlexiblePricing())) {
            List<SignupFullMinusFlexiblePricing> oldList = JSON.parseArray(oldSignupAct.getFullMinusFlexiblePricing(), SignupFullMinusFlexiblePricing.class);
            List<SignupFullMinusFlexiblePricing> newList = JSON.parseArray(newSignupAct.getFullMinusFlexiblePricing(), SignupFullMinusFlexiblePricing.class);
            List<SignupFullMinusFlexiblePricing> tmpOldList = new ArrayList<>();
            List<SignupFullMinusFlexiblePricing> tmpNewList = new ArrayList<>();
            for (SignupFullMinusFlexiblePricing oldObj : oldList) {
                for (SignupFullMinusFlexiblePricing newObj : newList) {
                    if (oldObj.getDays().equals(newObj.getDays())) {
                        // 同一天的金额有变更
                        if (!oldObj.getDiscountAmount().equals(newObj.getDiscountAmount())) {
                            tmpOldList.add(oldObj);
                            tmpNewList.add(newObj);
                        }
                    }
                }
            }
            sb.append(" 满减灵活定价由 " + JSON.toJSONString(tmpOldList) + " 更新为 " + JSON.toJSONString(tmpNewList) + "；");
        }
        if (!oldSignupAct.getDiscountFlexiblePricing().equals(newSignupAct.getDiscountFlexiblePricing())) {
            List<SignupDiscountFlexiblePricing> oldList = JSON.parseArray(oldSignupAct.getDiscountFlexiblePricing(), SignupDiscountFlexiblePricing.class);
            List<SignupDiscountFlexiblePricing> newList = JSON.parseArray(newSignupAct.getDiscountFlexiblePricing(), SignupDiscountFlexiblePricing.class);
            List<SignupDiscountFlexiblePricing> tmpOldList = new ArrayList<>();
            List<SignupDiscountFlexiblePricing> tmpNewList = new ArrayList<>();
            for (SignupDiscountFlexiblePricing oldObj : oldList) {
                for (SignupDiscountFlexiblePricing newObj : newList) {
                    if (oldObj.getDays().equals(newObj.getDays())) {
                        // 同一天的折扣有变更
                        if (!oldObj.getDiscount().equals(newObj.getDiscount())) {
                            tmpOldList.add(oldObj);
                            tmpNewList.add(newObj);
                        }
                    }
                }
            }
            sb.append(" 打折灵活定价由 " + JSON.toJSONString(tmpOldList) + " 更新为 " + JSON.toJSONString(tmpNewList) + "；");
        }
        return sb.toString();
    }

    @Override
    public void syncVehicle(Long storeId, String storeIdStr, List<String> storeIdsList, String vehicleModelIdsStr) {
        // 1. 查询生效的包含当前门店的自营活动
        List<ProprietaryActivity> activitiesList = proprietaryActivityService.lambdaQuery()
                .eq(ProprietaryActivity::getActivityStatus, ActivityStatusEnum.EFFECT.getType())
                .eq(BaseEntity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .and(o -> o.eq(ProprietaryActivity::getStoreIds, "-1")
                        .or(o1 -> o1.like(ProprietaryActivity::getStoreIds, "," + storeIdStr + ","))
                        .or(o1 -> o1.like(ProprietaryActivity::getStoreIds, "," + storeIdStr))
                        .or(o1 -> o1.eq(ProprietaryActivity::getStoreIds, storeIdStr)))
                .list();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(activitiesList)) {
            log.info("tid:{},同步自营活动车型信息,无当前参与门店生效的自营活动,storeId:{}", Trace.currentTraceId(), storeId);
            return ;
        }
        List<Long> activitiesIdList = activitiesList.stream()
                .filter(o -> storeIdsList.contains(o.getStoreIds()))
                .map(ProprietaryActivity::getId)
                .collect(Collectors.toList());
        //1.2查询自营活动报名门店
        List<ProprietaryActivitySignup> activitySignupsList = lambdaQuery()
                .in(ProprietaryActivitySignup::getActivityId, activitiesIdList)
                .eq(ProprietaryActivitySignup::getStoreId, storeId)
                .eq(BaseEntity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .eq(ProprietaryActivitySignup::getIsAllVehicle, 1)
                .list();
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(activitySignupsList)){
            log.info("tid:{},同步自营活动车型信息,无当前报名门店生效的全部车型的报名自营活动,storeId:{}",Trace.currentTraceId(), storeId);
            return ;
        }
        List<String> mdModelIdsList = Arrays.asList(vehicleModelIdsStr.split(","));
        List<ProprietaryActivitySignup>  updateList = new ArrayList<>();
        //1.3更新自营活动报名门店车型
        activitySignupsList.forEach(o ->{
                if(StringUtils.isEmpty(o.getVehicleModelIds())){
                    o.setVehicleModelIds(vehicleModelIdsStr);
                }else{
                    List<String> vehicleModelList = Arrays.asList(o.getVehicleModelIds().split(","));
                    List<String> addMdModelIds = mdModelIdsList.stream().filter(s -> !vehicleModelList.contains(s)).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(addMdModelIds)){
                        o.setVehicleModelIds(o.getVehicleModelIds()+","+ org.apache.commons.lang3.StringUtils.join(addMdModelIds, ","));
                        o.setUpdateTime(new Date());
                        o.setUpdateOperId(-1L);
                        o.setUpdateOperName("system");
                        updateList.add(o);
                    }
                }
        });
        if(!CollectionUtils.isEmpty(updateList)){
            updateBatchById(activitySignupsList);
            log.info("tid:{},同步自营活动车型信息,成功,storeId:{},vehicleModelIdsStr:{}",Trace.currentTraceId(), storeId,vehicleModelIdsStr);
        }else{
            log.info("tid:{},同步自营活动车型信息,当前报名已存在车型,vehicleModelIdsStr:{},无变更数据",Trace.currentTraceId(),vehicleModelIdsStr);
        }
    }

    @Override
    public void syncStore(Long storeId, String orgCode) {
        if(storeId == null || StringUtils.isEmpty(orgCode)){
            log.info("tid:{},同步自营活动门店信息,入参为空,storeId{},orgCode:{}", Trace.currentTraceId(),storeId,orgCode);
            return ;
        }
        // 1. 查询生效的包含当前机构的自营活动
        List<ProprietaryActivity> activitiesList = proprietaryActivityService.lambdaQuery()
                .eq(BaseEntity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .eq(ProprietaryActivity::getActivityStatus, ActivityStatusEnum.EFFECT.getType())
                .and(o -> o.eq(ProprietaryActivity::getOrgCodes,"-1")
                        .or( o1 -> o1.like(ProprietaryActivity::getOrgCodes,"("+orgCode+")"))
                ).list();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(activitiesList)) {
            log.info("tid:{},同步自营活动门店信息,未查到生效的自营活动,orgCode:{}", Trace.currentTraceId(),orgCode);
            return ;
        }
        List<Long> activitiesIdList = activitiesList.stream()
                .map(ProprietaryActivity::getId)
                .collect(Collectors.toList());
        Map<Long,ProprietaryActivity> activitiesMap = activitiesList.stream()
                .collect(Collectors.toMap(ProprietaryActivity::getId, Function.identity()));
        //1.3查询自营活动报名门店
        List<ProprietaryActivitySignup> activitySignupsList = lambdaQuery()
                .in(ProprietaryActivitySignup::getActivityId, activitiesIdList)
                .eq(ProprietaryActivitySignup::getOrgCode, orgCode)
                .eq(BaseEntity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .list();
        if(CollectionUtils.isEmpty(activitySignupsList)){
            log.info("tid:{},同步自营活动门店信息,未查到当前报名机构自营活动,orgCode:{}", Trace.currentTraceId(),orgCode);
            return ;
        }
        //循环activitySignupsList 根据activityId分组 map<activityId,List<ProprietaryActivitySignup>>
        Map<Long, List<ProprietaryActivitySignup>> activitySignupsMap = activitySignupsList.stream()
                .collect(Collectors.groupingBy(ProprietaryActivitySignup::getActivityId));
        List<Long> relationActivitiesIdList = new ArrayList<>();
        for (Map.Entry<Long, List<ProprietaryActivitySignup>> entry : activitySignupsMap.entrySet()) {
            if(CollectionUtils.isEmpty(entry.getValue())){
                continue;
            }
            //判断当前门店是否已添加
            if (entry.getValue().stream().anyMatch(o -> o.getStoreId().equals(storeId))) {
                continue;
            }
            relationActivitiesIdList.add(entry.getKey());
        }
        if(CollectionUtils.isEmpty(relationActivitiesIdList)){
            log.info("tid:{},同步自营活动门店信息,活动已报名当前门店,orgCode:{},storeId:{}", Trace.currentTraceId(),orgCode,storeId);
            return;
        }

        //1.3查询 报名关系表是否设置的全部门店
        List<ProprietaryActivitySignupRelation> signupRelationList = signupRelationService.lambdaQuery()
                .in(ProprietaryActivitySignupRelation::getActivityId, relationActivitiesIdList)
                .eq(ProprietaryActivitySignupRelation::getOrgCode, orgCode)
                .eq(BaseEntity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .eq(ProprietaryActivitySignupRelation::getIsAllStore,1)
                .list();
        if(CollectionUtils.isEmpty(signupRelationList)){
            log.info("tid:{},同步自营活动门店信息,未查到选择所有门店的报名关系,不进行同步,activities:{},storeId:{}"
                    , Trace.currentTraceId(),JSONObject.toJSONString(relationActivitiesIdList),storeId);
            return;
        }
        List<ProprietaryActivitySignup> addList = new ArrayList<>(signupRelationList.size());
        signupRelationList.forEach(o->{
                // 数组组装
                ProprietaryActivity proprietaryActivity = activitiesMap.get(o.getActivityId());
                ProprietaryActivitySignup activitySignup = new ProprietaryActivitySignup();
                activitySignup.setActivityId(o.getActivityId());
                activitySignup.setOrgCode(orgCode);
                activitySignup.setStoreId(storeId);
                activitySignup.setSignupStatus(ActivitySignupStatusEnum.SIGNUP.getType()); // 已参加
                activitySignup.setVehicleModelIds(o.getVehicleModelIds());
                activitySignup.setIsAllVehicle(o.getIsAllVehicle());
                activitySignup.setFullMinusFlexiblePricing("");
                activitySignup.setDiscountFlexiblePricing("");
                //"活动类型：1-满减、2-打折"
                if(!StringUtils.isEmpty(o.getFlexiblePricing())){
                    if(1 == proprietaryActivity.getActivityType() ){
                        activitySignup.setFullMinusFlexiblePricing(o.getFlexiblePricing());
                    }else{
                        activitySignup.setDiscountFlexiblePricing(o.getFlexiblePricing());
                    }
                }
                addList.add(activitySignup);
        });
        if(CollectionUtils.isEmpty(addList)){
            return;
        }
        UserDTO userDTO = new UserDTO();
        userDTO.setId(-1L);
        userDTO.setName("system");
        userDTO.setUsername("system");
        try {
            saveBatch(addList, addList.size(), userDTO, new Date());
        } catch (Exception e) {
            log.error("tid:{},保存自营活动门店信息失败,orgCode:{}, storeId:{}, error:{}", Trace.currentTraceId(), orgCode, storeId, e.getMessage(), e);
        }
    }
    /**
     * 按城市Id获取最优活动-周租月租使用
     * @param getPrimeActByCityReq
     * @return
     */
    @Override
    public GetPrimeActByCityRes getPrimeActByCity(GetPrimeActByCityReq getPrimeActByCityReq) {
        //通过城市Id获取到所有门店
        Long cityId= getPrimeActByCityReq.getCityId();
        BigDecimal weekDiscount = new BigDecimal(100);
        BigDecimal monthDiscount = new BigDecimal(100);
        List<Long> storeIds = configLoader.getStoreIdsByCityId(cityId);
        if (!CollectionUtils.isEmpty(storeIds)){
            List<ProprietaryActivity> proprietaryActivityList = proprietaryActivityMapper.queryProprietaryActByStoreIds(storeIds);
            if (!CollectionUtils.isEmpty(proprietaryActivityList)){
                for (ProprietaryActivity proprietaryActivity : proprietaryActivityList) {
                    Integer pricingType = proprietaryActivity.getPricingType();
                    if (pricingType == 1){
                        //1-灵活定价
                        List<SignupDiscountFlexiblePricing> discountList = JSON.parseArray(proprietaryActivity.getDiscountFlexiblePricing(), SignupDiscountFlexiblePricing.class);
                        for (SignupDiscountFlexiblePricing signupDiscountFlexiblePricing : discountList) {
                            String days = signupDiscountFlexiblePricing.getDays();
                            BigDecimal minDiscount = new BigDecimal(signupDiscountFlexiblePricing.getMinDiscount());
                            if (Integer.valueOf(days) == 7  && minDiscount.compareTo(weekDiscount)<0){
                                weekDiscount = minDiscount;
                            }else if (Integer.valueOf(days) == 30 && minDiscount.compareTo(monthDiscount)<0){
                                monthDiscount = minDiscount;
                            }


                        }
                    }else if(pricingType == 2){
                        //2-规范定价
                        List<com.saicmobility.evcard.md.act.dto.proprietary.DiscountStandardPricing> discountStandardPricingList = JSON.parseArray(proprietaryActivity.getDiscountStandardPricing(), com.saicmobility.evcard.md.act.dto.proprietary.DiscountStandardPricing.class);
                        for (com.saicmobility.evcard.md.act.dto.proprietary.DiscountStandardPricing discountStandardPricing : discountStandardPricingList) {
                            String days = discountStandardPricing.getDays();
                            BigDecimal minDiscount = new BigDecimal(discountStandardPricing.getDiscount());
                            if (Integer.valueOf(days) == 7 && minDiscount.compareTo(weekDiscount)<0){
                                weekDiscount = minDiscount;
                            }else if (Integer.valueOf(days) == 30 && minDiscount.compareTo(monthDiscount)<0){
                                monthDiscount = minDiscount;
                            }
                        }
                    }
                }
            }
        }

        return GetPrimeActByCityRes.newBuilder()
                .setIsBothExist((weekDiscount.compareTo(new BigDecimal(100)) < 0 && monthDiscount.compareTo(new BigDecimal(100)) < 0) ? Boolean.TRUE : Boolean.FALSE)
                .setWeekDiscount(weekDiscount.compareTo(new BigDecimal(100)) < 0 ? weekDiscount.toPlainString(): null)
                .setMonthDiscount(monthDiscount.compareTo(new BigDecimal(100)) < 0 ? monthDiscount.toPlainString(): null)
                .build();
    }
}
