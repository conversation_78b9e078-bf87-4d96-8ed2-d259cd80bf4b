package com.saicmobility.evcard.md.act.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OperateLogBo implements Serializable {
    /**
     * 操作日志类型：1. 套餐配置  2. 立减活动
     */
    private Integer operateType;

    @ApiModelProperty(value = "操作时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime;

    @ApiModelProperty(value = "操作人")
    private String operatorName;

    @ApiModelProperty(value = "操作内容")
    private String operateContent;
}
