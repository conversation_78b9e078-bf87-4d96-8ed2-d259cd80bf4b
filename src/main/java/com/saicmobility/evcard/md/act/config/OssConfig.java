package com.saicmobility.evcard.md.act.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OssConfig {

    private static String accessKeyId;
    private static String accessKeySecret;
    private static String bucketName;
    private static String endpoint;
    private static String ENV;
    private static String basePath;

    private static String httpsBasePath;


    public static String getHttpsBasePath() {
        return httpsBasePath;
    }

    @Value("${ali.oss.httpsBasePath:https://evcard.oss-cn-shanghai.aliyuncs.com}")
    public void setHttpsBasePath(String value) {
        httpsBasePath = value;
    }

    @Value("${ali.oss.accessId:LTAI5tJumdVNXHPLszk49yAk}")
    public void setAccessKeyId(String value) {
        accessKeyId = value;
    }

    @Value("${ali.oss.accessKey:******************************}")
    public void setAccessKeySecret(String value) {
        accessKeySecret = value;
    }

    @Value("${ali.oss.ossBucket:evcard}")
    public void setBucketName(String value) {
        bucketName = value;
    }

    @Value("${ali.ons.endPoint:http://oss-cn-shanghai.aliyuncs.com}")
    public void setEndpoint(String value) {
        endpoint = value;
    }

    @Value("${ali.oss.env}")
    public void setENV(String value) {
        ENV = value;
    }

    @Value("${ali.ons.basePath:http://evcard.oss-cn-shanghai.aliyuncs.com}")
    public void setBasePath(String value) {
        basePath = value;
    }


    public static String getAccessKeyId() {
        return accessKeyId;
    }

    public static String getAccessKeySecret() {
        return accessKeySecret;
    }

    public static  String getBucketName() {
        return bucketName;
    }

    public static String getEndpoint() {
        return endpoint;
    }

    public static String getENV() {
        return ENV;
    }

    public static String getBasePath() {
        return basePath;
    }


}
