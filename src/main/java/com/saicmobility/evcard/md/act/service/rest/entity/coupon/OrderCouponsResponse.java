package com.saicmobility.evcard.md.act.service.rest.entity.coupon;

import com.extracme.evcard.rpc.coupon.dto.CouponListDto;
import com.saicmobility.evcard.md.act.constant.BusinessConst;
import com.saicmobility.evcard.md.act.util.NumberUtils;
import com.saicmobility.evcard.md.mdactservice.api.CouponInfo;
import com.saicmobility.evcard.md.mdactservice.api.CouponViewDes;
import com.saicmobility.evcard.md.mdactservice.api.OrderCouponRes;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrderCouponsResponse extends ArrayList<CouponListDto> {

    public OrderCouponRes.Builder toResBuilder(String mid) {
        List<CouponViewDes> list = this.stream().map(couponDto -> {
            CouponInfo.Builder infoBuilder = CouponInfo.newBuilder()
                    .setUserCouponSeq(couponDto.getUserCouponSeq())
                    .setCouponSeq(couponDto.getCouponSeq())
                    .setMid(mid)
                    /**
                     * 1. storeNames需要组织到tags中，故在coupon中已需要完成，此处不再组织
                     * 2. 城市编号与名称是统一的，不需要为门店模式单独组织
                     */
                    .setGoodsModelId(couponDto.getGoodsVehicleModel())
                    .setGoodsModelName(couponDto.getVehicleModelName())
                    .setPickUpCity(couponDto.getPickshopCity())
                    .setPickUpCityName(couponDto.getPickshopCityName())
                    .setReturnCity(couponDto.getReturnshopCity())
                    .setReturnCityName(couponDto.getReturnshopCityName())
                    .setTransactionType(Optional.ofNullable(couponDto.getTransactionType()).orElse(couponDto.getCouponType()))
                    .setUserCouponSeq(couponDto.getUserCouponSeq())
                    .setCouponSeq(Optional.of(couponDto.getCouponSeq()).orElse(0L))
                    .setStartDate(couponDto.getStartDate())
                    .setExpiresDate(couponDto.getExpiresDate())
                    .setStatus(Optional.of(couponDto.getStatus()).orElse(0))
                    .setCouponOrigin(couponDto.getCouponOrigin())
                    .setCouponValue(NumberUtils.getStr(couponDto.getCouponValue()))
                    .setDes(couponDto.getDes())
                    .setCouponType(couponDto.getCouponType())
                    .setServiceType(couponDto.getServiceType())
                    .setMinAmount(NumberUtils.getStr(couponDto.getMinAmount()))
                    .setTimeType(Optional.ofNullable(couponDto.getTimeType()).orElse(0))
                    .setStartTime(couponDto.getStartTime())
                    .setEndTime(couponDto.getEndTime())
                    .setVehicleNo(couponDto.getVehicleNo())
                    .setDiscountRate(Optional.ofNullable(couponDto.getDiscountRate()).orElse(0))
                    .setOrgCode(null == couponDto.getOrgSeq() ? "" : couponDto.getOrgSeq().toString())
                    .setOrderNo(couponDto.getOrderSeq())
                    .setDiscount(null == couponDto.getDiscount() ? "0.00" : couponDto.getDiscount().toString())
                    .setRentMethod(couponDto.getRentMethod())
                    .setRentMethodGroup(couponDto.getRentMethodGroup())
                    .setUseMethod(couponDto.getUseMethod())
                    .setDurationLimit(NumberUtils.getStr(couponDto.getDurationLimit()));
            //.setActivityOverlap(Optional.ofNullable(couponDto.getActivityOverlap()).orElse(0))
                    //.setPackageIds(couponDto.getPackageIds())
                    //.setHolidaysAvailable(Optional.ofNullable(couponDto.getHolidaysAvailable()).orElse(0))
                    //.setAvailableDaysOfWeek(couponDto.getAvailableDaysOfWeek())
                    //.setAgencyId(couponDto.getAgencyId())
                    //.setActionId(couponDto.getActionId());

            if (BusinessConst.STORE_TYPE.equals(couponDto.getShopLimitType())) {
                infoBuilder .setPickUpStoreId(couponDto.getPickshopSeq())
                        .setPickUpStoreName(couponDto.getPickshopName())
                        .setReturnStoreId(couponDto.getReturnshopSeq())
                .setReturnStoreName(couponDto.getReturnshopName());
            }

            CouponViewDes.Builder builder = CouponViewDes.newBuilder()
                    .addAllTags(couponDto.getTags())
                    .setSpace1(couponDto.getSpace1())
                    .addAllSpace2(couponDto.getSpace2())
                    .addAllSpace3(couponDto.getSpace3())
                    .setAvailability(couponDto.getAvailability())
                    .setRemainTimeTag(couponDto.getRemainTimeTag())
                    .setVehicleLimitDesc(couponDto.getVehicleLimitDesc())
                    .setCoupon(infoBuilder.build());
            return builder.build();
        }).collect(Collectors.toList());
        return OrderCouponRes.newBuilder().addAllCouponViewDes(list);
    }

}
