package com.saicmobility.evcard.md.act.enums.channelcoupon;

/**
 * 应答码分类
 * 0000	请求接受处理成功
 * 1xxx	请求鉴权校验失败
 * 2xxx	参数不合法
 * 3xxx	业务场景。注：39xx有特殊的含义，具体见各个接口的说明
 * 4xxx	权益供应商自定义的应答码
 * 9xxx	内部系统错误
 *
 * 应答码
 * 0000	处理成功
 * 1201	报文验签不通过
 * 1202	报文解密失败
 * 1203	请求头请求时间（Request-Time）不合法或不是当前时间
 * 1301	接口已下线
 * 1302	渠道暂不支持该交易
 * 2000	请求头字段缺失或不符合预期
 * 2002	请求头交易码（Trans-Code）与请求URL不匹配
 * 2003	请求头交易跟踪号（Trace-Id）为空
 * 2100	报文体缺失
 * 2101	报文体明文数据格式不符合预期
 * 2102	报文体缺失关键字段
 * 2103	报文体字段格式不合法
 * 3001	对账文件批次号不存在
 * 3002	供应商优惠券剩余库存为0
 * 3003	用户达到的每人领取优惠券上限
 * 3004	供应商每日剩余优惠券库存为0
 * 3005	用户达到的每人每天领取优惠券上限
 * 3010	黑名单用户拒绝领券
 * 3011	优惠券未到领取时间
 * 3012	优惠券已过领取时间
 * 3013	优惠券未在可领取（购买）时间或日期内
 * 3101	供应商产品编号不存在
 * 3201	优惠券编号未认证
 * 3901	指定要作废的优惠券状态是已失效
 * 3902	指定要作废的优惠券状态是已使用
 * 3904	权益产品不支持作废
 * 3905	兑换码经兑换后不再支持作废（适用于兑换码场景）
 * 3999	订单处理中，适用于订单结果异步返回的幂等查询场景
 * 8001	优惠券未在可使用时间或日期内
 * 8002	单日使用数量超过限制
 * 8003	渠道限制在建行生活使用
 * 9000	系统内部错误（明确处理失败），请稍后重试
 * 9100	交易流控
 */
public enum CCBCodeEnum {
    /**
     * 2539 partnergtw
     * 2534 mdactservice
     */
    SUCCESS(0, "0000", "处理成功"),
    SIGN_ERROR(-2539001, "1201", "报文验签不通过"),
    DECRYPT_ERROR(-2539002, "1202", "报文解密失败"),
    REQUEST_TIME_ERROR(-2539003, "1203", "请求头请求时间（Request-Time）不合法或不是当前时间"),
    INTERFACE_OFFLINE(-2539004, "1301", "接口已下线"),
    CHANNEL_NOT_SUPPORT(-2539005, "1302", "渠道暂不支持该交易"),
    HEADER_FIELD_MISSING(-2539006, "2000", "请求头字段缺失或不符合预期"),
    HEADER_TRANS_CODE_MISSING(-2539007, "2002", "请求头交易码（Trans-Code）与请求URL不匹配"),
    HEADER_TRACE_ID_MISSING(-2539008, "2003", "请求头交易跟踪号（Trace-Id）为空"),
    BODY_MISSING(-2534001, "2100", "报文体缺失"),
    BODY_FORMAT_ERROR(-2534002, "2101", "报文体明文数据格式不符合预期"),
    BODY_FIELD_MISSING(-2534003, "2102", "报文体缺失关键字段"),
    BODY_FIELD_FORMAT_ERROR(-2534004, "2103", "报文体字段格式不合法"),
    BILL_BATCH_NO_NOT_EXIST(-2534005, "3001", "对账文件批次号不存在"),
    COUPON_STOCK_ZERO(-2534006, "3002", "供应商优惠券剩余库存为0"),
    USER_RECEIVE_LIMIT(-2534007, "3003", "用户达到的每人领取优惠券上限"),
    SUPPLIER_STOCK_ZERO(-2534008, "3004", "供应商每日剩余优惠券库存为0"),
    USER_DAY_RECEIVE_LIMIT(-2534009, "3005", "用户达到的每人每天领取优惠券上限"),
    BLACK_LIST_REFUSE(-2534010, "3010", "黑名单用户拒绝领券"),
    COUPON_NOT_RECEIVE_TIME(-2534011, "3011", "优惠券未到领取时间"),
    COUPON_EXPIRED(-2534012, "3012", "优惠券已过领取时间"),
    COUPON_NOT_RECEIVE_DATE(-2534013, "3013", "优惠券未在可领取（购买）时间或日期内"),
    PRODUCT_NO_EXIST(-2534014, "3101", "供应商产品编号不存在"),
    COUPON_NO_AUTH(-2534015, "3201", "优惠券编号未认证"),
    COUPON_INVALID(-2534016, "3901", "指定要作废的优惠券状态是已失效"),
    COUPON_USED(-2534017, "3902", "指定要作废的优惠券状态是已使用"),
    PRODUCT_NOT_SUPPORT_INVALID(-2534018, "3904", "权益产品不支持作废"),
    COUPON_NOT_SUPPORT_INVALID(-2534019, "3905", "兑换码经兑换后不再支持作废（适用于兑换码场景）"),
    ORDER_PROCESSING(-2534020, "3999", "订单处理中，适用于订单结果异步返回的幂等查询场景"),
    COUPON_NOT_USE_DATE(-2534021, "8001", "优惠券未在可使用时间或日期内"),
    DAY_USE_LIMIT(-2534022, "8002", "单日使用数量超过限制"),
    CHANNEL_LIMIT(-2534023, "8003", "渠道限制在建行生活使用"),
    SYSTEM_ERROR(-2534024, "9000", "系统内部错误，请稍后重试"),
    TRANSACTION_FLOW_CONTROL(-2534025, "9100", "交易流控"),
    COUPON_NOT_NEED_CALLBACK(-2534026, "4001", "优惠券无需回调"),
    RECONCILIATION_FILE_NOT_NEED_PUSH(-2534027, "4002", "对账文件无需推送"),
    CCB_CONN_ERROR(-2534028, "4003", "推送建行连接失败"),
    COUPON_OFFER_FAIL(-2534029, "4004", "优惠券领取失败，请稍后重新尝试"),
    COUPON_OFFERED(-2534030, "4004", "优惠券已被领取过，请稍后重新尝试")
    ;


    CCBCodeEnum(Integer innerCode, String ccbCode, String msg) {
        this.innerCode = innerCode;
        this.ccbCode = ccbCode;
        this.msg = msg;
    }

    /**
     * 内部错误码
     */
    private Integer innerCode;

    /**
     * 建行错误码
     */
    private String ccbCode;

    /**
     * 消息含义
     */
    private String msg;

    public Integer getInnerCode() {
        return innerCode;
    }

    public String getCcbCode() {
        return ccbCode;
    }

    public String getMsg() {
        return msg;
    }
}
