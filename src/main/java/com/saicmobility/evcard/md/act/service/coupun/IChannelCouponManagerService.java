package com.saicmobility.evcard.md.act.service.coupun;

import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.dto.coupon.channel.*;
import com.saicmobility.evcard.md.act.dto.coupon.channel.ccb.*;

import java.util.List;

public interface IChannelCouponManagerService {

    // 查询优惠券配置详情列表
    GetCouponModelDetailOutput getCouponModelDetail(GetCouponModelDetailInput input) throws BusinessException;

    // 库存余量查询
    CouponStockDto getCouponStock(String productId, String channelActivityId) throws BusinessException;

    // 优惠券领取
    OfferCouponOutput offerCoupon(OfferCouponInput input) throws BusinessException;

    //优惠券状态回调
    CouponStatusDto callBackCouponStatus(Long userCouponSeq, Integer type, Integer status);

    // 获取 优惠券状态信息
    GetChannelCouponStatusDto getChannelCouponStatus(GetChannelCouponStatusInput input) throws BusinessException;

    // 失效 优惠券
    BaseResponse invalidChannelCoupon(InvalidChannelCouponInput input);

    // 推送对账文件
    List<ReconciliationFileDto> pushReconciliationFile(String pushDate);

    // 推送对账文件 回调
    BaseResponse statementOfAccountCallBack(StatementOfAccountCallBackInput input) throws BusinessException;

    // 移除库存
    List<Long> removeCouponStock(List<Long> userCouponSeqs) throws BusinessException;

    void initCouponStock(long mmpThirdCouponId);

    String getApolloInitRedisKey(long mmpThirdCouponId);
}
