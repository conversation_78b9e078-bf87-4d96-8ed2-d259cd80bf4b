package com.saicmobility.evcard.md.act.enums.welfare;

public enum WelfareTypeEnum {

    suixiangcard(1, "随享卡"),
    coupon(2, "优惠券"),
    ;

    private int type;
    private String desc;

    WelfareTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    public int getType() {
        return type;
    }

    public static WelfareTypeEnum getWelfareTypeEnum(int type) {
        WelfareTypeEnum[] values = values();
        for (WelfareTypeEnum welfareTypeEnum : values) {
            if (welfareTypeEnum.getType() == type) {
                return welfareTypeEnum;
            }
        }
        return null;
    }
}
