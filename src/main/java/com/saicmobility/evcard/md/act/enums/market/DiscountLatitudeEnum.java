package com.saicmobility.evcard.md.act.enums.market;

public enum DiscountLatitudeEnum {
    CARRENT(1, "车辆租金"),
    WHOLEORDER(2, "订单整单");

    private Integer type;
    private String msg;

    DiscountLatitudeEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }
}
