package com.saicmobility.evcard.md.act.job;

import com.saicmobility.evcard.md.act.constant.SystemConst;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivity;
import com.saicmobility.evcard.md.act.enums.proprietary.ActivityStatusEnum;
import com.saicmobility.evcard.md.act.service.IProprietaryActivityService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/1/9 18:19
 */
@Slf4j
@Component
@JobHandler("ExpireProprietaryActJob")
public class ExpireProprietaryActJob extends IJobHandler {
    @Resource
    private IProprietaryActivityService proprietaryActivityService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try{
            log.info("============> 开始执行 自营活动自动过期批处理 定时任务 <============");

            UserDTO jobUser = SystemConst.ELASTIC_JOB_USER;
            LocalDate currDate = LocalDate.now();

            proprietaryActivityService.lambdaUpdate()
                    .set(ProprietaryActivity::getActivityStatus, ActivityStatusEnum.EXPIRE.getType())
                    .set(ProprietaryActivity::getUpdateOperId, jobUser.getId())
                    .set(ProprietaryActivity::getUpdateOperName, jobUser.getUsername())
                    .set(ProprietaryActivity::getUpdateTime, currDate)
                    .eq(ProprietaryActivity::getActivityStatus, ActivityStatusEnum.EFFECT.getType())
                    .lt(ProprietaryActivity::getActivityEndDate, currDate)
                    .update();

            log.info("============> 结束执行 自营活动自动过期批处理 定时任务 <============");
        }catch (Exception e){
            log.error("定时任务自营活动自动过期批处理："+e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }
}
