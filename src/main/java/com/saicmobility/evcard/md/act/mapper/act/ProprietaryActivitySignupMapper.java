package com.saicmobility.evcard.md.act.mapper.act;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.saicmobility.evcard.md.act.domain.activity.SignupProprietaryActivityBo;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivitySignup;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 自营活动报名表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */

public interface ProprietaryActivitySignupMapper extends BaseMapper<ProprietaryActivitySignup> {

    /**
     * 模糊分页查询记录
     */
    Page<SignupProprietaryActivityBo> querySignupProprietaryActivityList(@Param("activityName") String activityName,
                                                                         @Param("activityTag") String activityTag,
                                                                         @Param("activityStatus") int activityStatus,
                                                                         @Param("activityType") int activityType,
                                                                         @Param("pricingType") int pricingType,
                                                                         @Param("signupStatus") int signupStatus,
                                                                         @Param("signUpStartDate") LocalDate signUpStartDate,
                                                                         @Param("signUpEndDate") LocalDate signUpEndDate,
                                                                         @Param("orgCode") String orgCode,
                                                                         Page<SignupProprietaryActivityBo> page);

    /**
     * 批量修改门店id
     * @param updateSignupList
     * @return
     */
    int updateBatchStoreId(@Param("list") List<ProprietaryActivitySignup> updateSignupList);

    /**
     * 批量新增报名活动信息
     * @param addSignupList
     * @return
     */
    int insertBatch(@Param("list") List<ProprietaryActivitySignup> addSignupList);


    /**
     * 模糊分页查询记录
     */
    Page<SignupProprietaryActivityBo> querySignupProprietaryActivityListGroupOrgCode(@Param("activityName") String activityName,
                                                                         @Param("activityTag") String activityTag,
                                                                         @Param("activityStatus") int activityStatus,
                                                                         @Param("activityType") int activityType,
                                                                         @Param("pricingType") int pricingType,
                                                                         @Param("signupStatus") int signupStatus,
                                                                         @Param("signUpStartDate") LocalDate signUpStartDate,
                                                                         @Param("signUpEndDate") LocalDate signUpEndDate,
                                                                         @Param("orgCode") String orgCode,
                                                                         @Param("storeId") String storeId,
                                                                         Page<SignupProprietaryActivityBo> page);
}
