package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店参与的立减活动关联商品车型表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_store_reduce_activity_goods_model")
public class StoreReduceActivityGoodsModel extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "门店参与活动id")
    private Long storeReduceActivityId;

    @ApiModelProperty(value = "商品车型id")
    private Long goodsModelId;

}
