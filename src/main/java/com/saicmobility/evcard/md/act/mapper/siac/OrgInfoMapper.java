package com.saicmobility.evcard.md.act.mapper.siac;

import com.saicmobility.evcard.md.act.domain.coupon.OrgNameInfo;
import com.saicmobility.evcard.md.act.entity.siac.OrgInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface OrgInfoMapper {

    Long getOrgSeqByOrgCode(String orgId);

    @Select("select org_id as orgId, org_name as orgName from isv.org_info where org_class = 1 or org_class = 2")
    List<OrgNameInfo> getOrgList();

    @Select("select org_name as orgName from isv.org_info where org_id = #{orgCode} ")
    String getOrgNameByOrgId(@Param("orgCode") String orgCode);
}