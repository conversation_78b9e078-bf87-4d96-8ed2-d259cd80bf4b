package com.saicmobility.evcard.md.act.domain.common;

public class Page {
    private static final long serialVersionUID = 5206274717063166166L;
    private Integer pageNo;
    private Integer pageSize = 20;
    private Integer offSet;
    private Integer limitSet;
    private Boolean countFlag = false;
    private Integer count;
    private String orderByColumn;
    private String orderByRule;

    public Page() {
    }

    public Page(Integer pageNo, Integer pageSize) {
        this.initialize(pageNo, pageSize);
    }

    public Page(Integer pageNo, Integer pageSize, Boolean countFlag) {
        this.initialize(pageNo, pageSize);
        this.countFlag = countFlag;
    }

    public Page(Integer pageNo, Integer pageSize, Boolean countFlag, String orderByColumn, String orderByRule) {
        this.initialize(pageNo, pageSize);
        this.countFlag = countFlag;
        this.orderByColumn = orderByColumn;
        this.orderByRule = orderByRule;
    }

    public void initialize(Integer pageNo, Integer pageSize) {
        this.pageNo = pageNo == null ? 1 : pageNo;
        this.pageSize = pageSize == null ? this.pageSize : pageSize;
        this.offSet = this.pageSize * (this.pageNo - 1);
        this.limitSet = this.pageSize;
    }

    public Integer getPageNo() {
        return this.pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getOffSet() {
        return this.offSet == null ? this.pageSize * (this.pageNo - 1) : this.offSet;
    }

    public void setOffSet(Integer offSet) {
        this.offSet = offSet;
    }

    public Integer getLimitSet() {
        return this.limitSet == null ? this.pageSize : this.limitSet;
    }

    public void setLimitSet(Integer limitSet) {
        this.limitSet = limitSet;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Boolean getCountFlag() {
        return this.countFlag;
    }

    public void setCountFlag(Boolean countFlag) {
        this.countFlag = countFlag;
    }

    public Integer getCount() {
        return this.count;
    }

    public String getOrderByColumn() {
        return this.orderByColumn;
    }

    public void setOrderByColumn(String orderByColumn) {
        this.orderByColumn = orderByColumn;
    }

    public String getOrderByRule() {
        return this.orderByRule;
    }

    public void setOrderByRule(String orderByRule) {
        this.orderByRule = orderByRule;
    }
}
