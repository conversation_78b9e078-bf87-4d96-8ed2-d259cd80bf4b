package com.saicmobility.evcard.md.act.dto.coupon.channel;


import lombok.Data;

@Data
public class OfferCouponInput {
    /**
     * 权益派发的用户识别Id，一般为手机号
     */
    private String userId;

    /**
     * 产品编号，权益产品的唯一标识
     */
    private String productId;

    /**
     * 领券订单号，要求支持幂等
     */
    private String orderId;

    /**
     * 幂等重试次数，发送原始报文时该值为0，第一次重试时送1，第二次重试时送2，以此类推。
     */
    private int retryCnt;

    /**
     * 领券张数，默认为1。当权益供应商支持一次接口调用发放多张优惠券时有效
     */
    private int num;

    /**
     * 建行侧活动编号，需权益供应商记录下来，在对账时使用
     */
    private String dccpAvyId;

    /**
     * 用户在渠道买券的支付信息
     */
    private String paySummary;

    /**
     * 用户所在纬度，可能会带有负号（南纬），预留字段暂未启用
     */
    private String latitude;

    /**
     * 用户所在经度，可能会带有负号（西经），预留字段暂未启用
     */
    private String longitude;

    /**
     * 用户当前IP，可用于获取城市信息，预留字段暂未启用
     */
    private String userRealIp;

    /**
     * 额外业务数据（符合JSON格式的字符串），详见表格下方说明
     */
    private String extrBsnData;
}
