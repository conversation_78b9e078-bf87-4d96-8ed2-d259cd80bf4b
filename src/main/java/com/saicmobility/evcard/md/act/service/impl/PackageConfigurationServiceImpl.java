package com.saicmobility.evcard.md.act.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saicmobility.evcard.md.act.constant.BusinessConst;
import com.saicmobility.evcard.md.act.domain.packages.PackageNameDto;
import com.saicmobility.evcard.md.act.domain.packages.QueryAvailPackageDto;
import com.saicmobility.evcard.md.act.entity.OperateLog;
import com.saicmobility.evcard.md.act.entity.PackageConfiguration;
import com.saicmobility.evcard.md.act.mapper.act.OperateLogMapper;
import com.saicmobility.evcard.md.act.mapper.act.PackageConfigurationMapper;
import com.saicmobility.evcard.md.act.service.PackageConfigurationService;
import com.saicmobility.evcard.md.act.service.extern.GoodsModelService;
import com.saicmobility.evcard.md.act.service.extern.OrgService;
import com.saicmobility.evcard.md.act.service.extern.StoreService;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.mdactservice.api.AddPackageReq;
import com.saicmobility.evcard.md.mdactservice.api.AddPackageRes;
import com.saicmobility.evcard.md.mdactservice.api.CurrentUser;
import com.saicmobility.evcard.md.mdactservice.api.GetPackageByIdReq;
import com.saicmobility.evcard.md.mdactservice.api.GetPackageByIdRes;
import com.saicmobility.evcard.md.mdactservice.api.OfflinePackageReq;
import com.saicmobility.evcard.md.mdactservice.api.OfflinePackageRes;
import com.saicmobility.evcard.md.mdactservice.api.PackageConfig;
import com.saicmobility.evcard.md.mdactservice.api.PackageConfigForApp;
import com.saicmobility.evcard.md.mdactservice.api.PackageNameInfo;
import com.saicmobility.evcard.md.mdactservice.api.SearchAllPackageReq;
import com.saicmobility.evcard.md.mdactservice.api.SearchAllPackageRes;
import com.saicmobility.evcard.md.mdactservice.api.SearchAvailablePackageReq;
import com.saicmobility.evcard.md.mdactservice.api.SearchAvailablePackageRes;
import com.saicmobility.evcard.md.mdactservice.api.SearchPackageNameReq;
import com.saicmobility.evcard.md.mdactservice.api.SearchPackageNameRes;
import com.saicmobility.evcard.md.mdactservice.api.SearchPackageReq;
import com.saicmobility.evcard.md.mdactservice.api.SearchPackageRes;
import com.saicmobility.evcard.md.mdgoodsservice.api.MdGoodsService;
import com.saicmobility.evcard.md.mdstoreservice.api.GetStoreListForPowerReq;
import com.saicmobility.evcard.md.mdstoreservice.api.GetStoreListForPowerRes;
import com.saicmobility.evcard.md.mdstoreservice.api.MdStoreService;
import com.saicmobility.evcard.md.mdstoreservice.api.StoreInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 套餐配置 服务实现类
 */
@Slf4j
@Service
public class PackageConfigurationServiceImpl extends ServiceImpl<PackageConfigurationMapper, PackageConfiguration> implements PackageConfigurationService {

    @Resource
    private PackageConfigurationMapper packageConfigurationMapper;

    @Resource
    private MdGoodsService mdGoodsService;

    @Resource
    private OperateLogMapper operateLogMapper;

    @Resource
    private OrgService orgService;

    @Resource
    private StoreService storeService;

    @Resource
    private GoodsModelService goodsModelService;

    @Resource
    MdStoreService mdStoreService;

    //套餐生效状态
    private static final Integer EFFECT_STATUS = 1;

    //套餐待生效状态
    private static final Integer WAIT_EFFECT_STATUS = 2;

    //套餐已失效状态
    private static final Integer NO_EFFECT_STATUS = 3;

    //套餐作废状态
    private static final Integer CANCEL = 4;

    @Override
    public SearchPackageRes searchPackage(SearchPackageReq req) {
        SearchPackageRes.Builder builder = SearchPackageRes.newBuilder();
        Page<PackageConfiguration> page = new Page<>(req.getPageNum(), req.getPageSize(), true);

        //查询条件里的机构编号
        String orgCode = req.getOrgCode();
        if (StringUtils.isBlank(orgCode)) {
            //如果未传机构编号,根据登录人所属的机构限制权限
            orgCode = req.getCurrentUser().getOrgCode();
        }
        CurrentUser currentUser = req.getCurrentUser();
        // 帐户标记 1-门店 2-城市
        int globalFlag = currentUser.getGlobalFlag();
        String userOrgCode = currentUser.getOrgCode();

        List<Long> userStoreIds = new ArrayList<>();
        if (!("00".equals(userOrgCode) && globalFlag == 2)) {
            com.saicmobility.evcard.md.mdstoreservice.api.CurrentUser user = com.saicmobility.evcard.md.mdstoreservice.api.CurrentUser
                    .newBuilder()
                    .setUserId(currentUser.getUserId())
                    .setUserName(currentUser.getUserName())
                    .setOrgCode(currentUser.getOrgCode())
                    .setSystemFlag(currentUser.getSystemFlag())
                    .setEId(currentUser.getEId())
                    .setGlobalFlag(currentUser.getGlobalFlag()).build();
            GetStoreListForPowerReq powerReq = GetStoreListForPowerReq.newBuilder().setCurrentUser(user).build();
            GetStoreListForPowerRes res = mdStoreService.getStoreListForPower(powerReq);
            log.info("res.getRetCode() 查询结果======{}",res.getRetCode());
            if (res.getRetCode() == 0) {
                userStoreIds = res.getInfoList().stream().map(StoreInfo::getId).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(userStoreIds)){
                    userStoreIds.add(-10L);
                }
            }
        }
        log.info("userStoreIds 机构结果======{}",JSON.toJSONString(userStoreIds));
        Page<PackageConfiguration> result = page(page,
                new LambdaQueryWrapper<PackageConfiguration>()
                        .likeRight(StringUtils.isNotBlank(orgCode),
                                PackageConfiguration::getOrgCode, orgCode)
                        .eq(req.getStoreId() > 0,
                                PackageConfiguration::getStoreId, req.getStoreId())
                        .eq(req.getConfigState() != 0,
                                PackageConfiguration::getConfigState, req.getConfigState())
                        .eq(req.getRenewUseFlag() != 0,
                                PackageConfiguration::getRenewUseFlag, req.getRenewUseFlag())
                        .eq(req.getGoodsModelId() > 0,
                                PackageConfiguration::getGoodsModelId, req.getGoodsModelId())
                        .like(req.getPackageName() != null,
                                PackageConfiguration::getPackageName, req.getPackageName())
                        .in(CollectionUtil.isNotEmpty(userStoreIds),PackageConfiguration::getStoreId,userStoreIds)
                        .orderByAsc(PackageConfiguration::getConfigState)
                        .orderByAsc(PackageConfiguration::getStartTime)
                        .orderByAsc(PackageConfiguration::getId));

        List<PackageConfiguration> packageList = result.getRecords();
        if (CollectionUtil.isNotEmpty(packageList)) {
            List<PackageConfig> list = packageList.stream().map(this::convertPackageConfig).collect(Collectors.toList());
            builder.addAllPackageConfig(list).setTotal((int) result.getTotal());
        }

        return builder.build();
    }

    private PackageConfig convertPackageConfig(PackageConfiguration packageConfiguration) {
        PackageConfig.Builder builder = PackageConfig.newBuilder();
        if (StrUtil.isNotBlank(packageConfiguration.getEndTime())) {
            builder.setEndTime(packageConfiguration.getEndTime());
        }
        if (BusinessConst.EARLY_PACKAGE.equals(packageConfiguration.getEarlyFlag())) {
            builder.setEarlyStartDate(packageConfiguration.getEarlyStartDate());
            builder.setEarlyEndDate(packageConfiguration.getEarlyEndDate());
            builder.setEarlyPrice(packageConfiguration.getEarlyPrice().toString());
        }

        return
                builder.setId(packageConfiguration.getId())
                        .setOrgCode(packageConfiguration.getOrgCode())
                        .setOrgName(orgService.getOrgNameByOrgCode(packageConfiguration.getOrgCode()))
                        .setStoreId(packageConfiguration.getStoreId())
                        .setStoreName(storeService.getStoreNameByStoreId(packageConfiguration.getStoreId()))
                        .setGoodsModelId(packageConfiguration.getGoodsModelId())
                        .setGoodsModelName(goodsModelService.getGoodsModelNameById(packageConfiguration.getGoodsModelId()))
                        .setPackageName(packageConfiguration.getPackageName())
                        .setDaysNumber(packageConfiguration.getDaysNumber())
                        .setTotalPrice(packageConfiguration.getTotalPrice().toString())
                        .setRenewUseFlag(packageConfiguration.getRenewUseFlag())
                        .setUseStartDate(packageConfiguration.getUseStartDate().substring(0, 10))
                        .setUseEndDate(packageConfiguration.getUseEndDate().substring(0, 10))
                        .setStartTime(packageConfiguration.getStartTime())
                        .setConfigState(packageConfiguration.getConfigState())
                        .setEarlyFlag(packageConfiguration.getEarlyFlag())
                        .build();
    }

    @Override
    public AddPackageRes addPackage(AddPackageReq req) {
        AddPackageRes.Builder builder = AddPackageRes.newBuilder();

        if (StrUtil.isBlank(req.getPackageName())) {
            return builder.setRetCode(-1000).setRetMsg("请输入套餐名称").build();
        }

        if (StrUtil.isBlank(req.getOrgCode())) {
            return builder.setRetCode(-1001).setRetMsg("请选择运营公司").build();
        }

        if (req.getStoreId() <= 0 && CollectionUtils.isEmpty(req.getStoreIdsList())) {
            return builder.setRetCode(-1002).setRetMsg("请选择门店").build();
        }

        if (req.getGoodsModelId() <= 0) {
            return builder.setRetCode(-1003).setRetMsg("请选择商品车型").build();
        }

        if (req.getDaysNumber() < 1 || req.getDaysNumber() > 999) {
            return builder.setRetCode(-1004).setRetMsg("用车天数不在可选范围内").build();
        }

        if (StrUtil.isBlank(req.getUseStartDate()) || StrUtil.isBlank(req.getUseEndDate())) {
            return builder.setRetCode(-1005).setRetMsg("用车日期区间不能为空").build();
        }

        if (StrUtil.isBlank(req.getStartTime())) {
            return builder.setRetCode(-1005).setRetMsg("生效时间不能为空").build();
        }

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTimeLocal = LocalDateTime.parse(req.getStartTime(), df);
        LocalDateTime useStartDateLocal = LocalDateTime.parse(req.getUseStartDate(), df);
        LocalDateTime today = LocalDateTime.now();
        if (startTimeLocal.isAfter(useStartDateLocal)) {
            return builder.setRetCode(-25001).setRetMsg("生效日期不可晚于用车开始日期").build();
        }

        if (!startTimeLocal.isAfter(today)) {
            return builder.setRetCode(-25002).setRetMsg("生效日期不可早于当前日期").build();
        }

        int daysNumber = req.getDaysNumber();
        // 结束时间，前端送过来的是23:59:59，这里需要加1秒后，再进行计算，不然1秒差距会导致1天的误差
        LocalDateTime useEndDateLocal = LocalDateTime.parse(req.getUseEndDate(), df).plusSeconds(1);
        long minutesOffset = Duration.between(useStartDateLocal, useEndDateLocal).toMinutes();
        if (minutesOffset / (60 * 24) < daysNumber) {
            return builder.setRetCode(-25001).setRetMsg("用车天数不足套餐生效开始结束时间，请修改天数或日期").build();
        }

        minutesOffset = Duration.between(LocalDateTime.now(), useEndDateLocal).toMinutes();
        if (minutesOffset / (60 * 24) < daysNumber) {
            return builder.setRetCode(-25001).setRetMsg("用车天数不足套餐生效开始结束时间，请修改天数或日期").build();
        }

        //早鸟套餐
        if (StrUtil.isAllNotBlank(req.getEarlyStartDate(), req.getEarlyEndDate(), req.getEarlyPrice())) {
            LocalDateTime earlyEndDate = LocalDateTime.parse(req.getEarlyEndDate(), df);
            LocalDateTime useEndDate = LocalDateTime.parse(req.getUseEndDate(), df);
            useEndDate = useEndDate.minusDays(req.getDaysNumber());
            if (earlyEndDate.isAfter(useEndDate)){
                return builder.setRetCode(-2534006).setRetMsg("早鸟结束时间需早于套餐失效时间").build();
            }
        }

        LambdaQueryWrapper<PackageConfiguration> samePackageWrapper = new LambdaQueryWrapper<PackageConfiguration>()
                .eq(PackageConfiguration::getOrgCode, req.getOrgCode())
                .eq(PackageConfiguration::getStoreId, req.getStoreId())
                .eq(PackageConfiguration::getGoodsModelId, req.getGoodsModelId())
                .eq(PackageConfiguration::getDaysNumber, req.getDaysNumber())
                .in(PackageConfiguration::getConfigState, EFFECT_STATUS, WAIT_EFFECT_STATUS)
                .eq(PackageConfiguration::getIsDeleted, 0);

        List<PackageConfiguration> samePackageList = packageConfigurationMapper.selectList(samePackageWrapper);

        if (CollectionUtil.isNotEmpty(samePackageList)) {
            for (PackageConfiguration packageConfiguration : samePackageList) {
                //用车日期区间重叠，生效日期也重叠。不可以，需要报错。
                if (packageConfiguration.getUseStartDate().equals(req.getUseStartDate())
                        && packageConfiguration.getUseEndDate().equals(req.getUseEndDate())
                        && packageConfiguration.getStartTime().equals(req.getStartTime())) {
                    return builder.setRetCode(-25001).setRetMsg("当前用车区间和生效时间已存在套餐,请切换用车区间").build();
                }
            }
        }

        List<Long> storeIds = new ArrayList<>();
        if (req.getStoreId() != 0) {
            storeIds.add(req.getStoreId());
        }
        storeIds.addAll(req.getStoreIdsList());

        CurrentUser currentUser = req.getCurrentUser();
        for (long storeId : storeIds) {
            PackageConfiguration packageConfiguration = new PackageConfiguration();
            packageConfiguration.setPackageName(req.getPackageName());
            packageConfiguration.setOrgCode(req.getOrgCode());
            packageConfiguration.setStoreId(storeId);
            packageConfiguration.setGoodsModelId(req.getGoodsModelId());
            packageConfiguration.setDaysNumber(req.getDaysNumber());
            packageConfiguration.setTotalPrice(new BigDecimal(req.getTotalPrice()));
            packageConfiguration.setUseStartDate(req.getUseStartDate());
            packageConfiguration.setUseEndDate(req.getUseEndDate());
            packageConfiguration.setStartTime(req.getStartTime());
            packageConfiguration.setEndTime(req.getUseEndDate());
            packageConfiguration.setRenewUseFlag(req.getRenewUseFlag());
            packageConfiguration.setConfigState(WAIT_EFFECT_STATUS);
            packageConfiguration.setCreateOperId(currentUser.getUserId());
            packageConfiguration.setCreateOperName(currentUser.getUserName());
            if (StrUtil.isAllNotBlank(req.getEarlyStartDate(), req.getEarlyEndDate(), req.getEarlyPrice())) {
                packageConfiguration.setEarlyStartDate(req.getEarlyStartDate());
                packageConfiguration.setEarlyEndDate(req.getEarlyEndDate());
                packageConfiguration.setEarlyPrice(new BigDecimal(req.getEarlyPrice()));
                packageConfiguration.setEarlyFlag(BusinessConst.EARLY_PACKAGE);
            }
            int insert = packageConfigurationMapper.insert(packageConfiguration);
            if (insert > 0) {
                builder.setId(packageConfiguration.getId());
            }
            com.saicmobility.evcard.md.act.entity.OperateLog operateLog = new OperateLog();
            operateLog.setForeignId(String.valueOf(packageConfiguration.getId()));
            operateLog.setOperateType(1);
            operateLog.setOperateContent("新增套餐");
            operateLog.setCreateOperId(currentUser.getUserId());
            operateLog.setCreateOperName(currentUser.getUserName());
            operateLog.setCreateOperOrgName(currentUser.getOrgName());
            operateLogMapper.insert(operateLog);
        }
        return builder.build();
    }

    public static void main(String[] args) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        int daysNumber = 8;
        LocalDateTime useStartDateLocal = LocalDateTime.parse("2023-09-29 00:00:00", df);
        LocalDateTime useEndDateLocal = LocalDateTime.parse("2023-10-06 23:59:59", df);
        long secondsOffset = Duration.between(useStartDateLocal, useEndDateLocal.plusSeconds(1)).toMinutes();
        if (secondsOffset / (60 * 24) < daysNumber) {
            System.out.println("true");
        }else{
            System.out.println("false");
        }
    }

    @Override
    public OfflinePackageRes offlinePackage(OfflinePackageReq req) {
        OfflinePackageRes.Builder builder = OfflinePackageRes.newBuilder();
        CurrentUser currentUser = req.getCurrentUser();
        long packageId = req.getPackageId();
        PackageConfiguration packageConfiguration = packageConfigurationMapper.selectById(packageId);
        if (packageConfiguration == null) {
            builder.setRetCode(-25001).setRetMsg("未查询到套餐信息");
            return builder.build();
        }
        Integer configState = packageConfiguration.getConfigState();
        if (!EFFECT_STATUS.equals(configState) && !WAIT_EFFECT_STATUS.equals(configState)) {
            builder.setRetCode(-25002).setRetMsg("下线套餐失败");
            return builder.build();
        }

        //生效中套餐下线,设为已失效状态,失效时间为当天
        if (EFFECT_STATUS.equals(configState)) {
            packageConfiguration.setConfigState(NO_EFFECT_STATUS);
            packageConfiguration.setEndTime(DateUtil.dateToString(LocalDateTime.now(), DateUtil.DATE_TYPE1));
        }

        //待生效套餐下线,设为作废状态
        if (WAIT_EFFECT_STATUS.equals(configState)) {
            packageConfiguration.setConfigState(CANCEL);
            packageConfiguration.setEndTime(null);
        }

        packageConfiguration.setUpdateOperId(currentUser.getUserId());
        packageConfiguration.setUpdateOperName(currentUser.getUserName());
        int result = packageConfigurationMapper.updateById(packageConfiguration);
        if (result <= 0) {
            builder.setRetCode(-25002).setRetMsg("下线套餐失败");
            return builder.build();
        }
        com.saicmobility.evcard.md.act.entity.OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(String.valueOf(packageConfiguration.getId()));
        operateLog.setOperateType(1);
        operateLog.setOperateContent("下线套餐");
        operateLog.setCreateOperId(currentUser.getUserId());
        operateLog.setCreateOperName(currentUser.getUserName());
        operateLog.setCreateOperOrgName(currentUser.getOrgName());
        operateLogMapper.insert(operateLog);
        return builder.build();
    }

    @Override
    public SearchPackageNameRes searchPackageName(SearchPackageNameReq req) {
        SearchPackageNameRes.Builder builder = SearchPackageNameRes.newBuilder();
        List<PackageNameDto> packageNames = packageConfigurationMapper.searchPackageName(req.getPackageName());
        if (CollectionUtil.isNotEmpty(packageNames)) {
            List<PackageNameInfo> nameList = packageNames.stream().map(this::convertPackageNameInfo).collect(Collectors.toList());
            builder.addAllPackageNameInfo(nameList).build();
        }
        return builder.build();
    }

    private PackageNameInfo convertPackageNameInfo(PackageNameDto packageNameDto) {
        return PackageNameInfo.newBuilder()
                .setId(packageNameDto.getId())
                .setPackageName(packageNameDto.getPackageName()).build();
    }


    @Override
    public SearchAvailablePackageRes searchAvailablePackage(SearchAvailablePackageReq req) {
        SearchAvailablePackageRes.Builder builder = SearchAvailablePackageRes.newBuilder();

        int daysNumber = 0;
        try {
            daysNumber = getDaysNumber(req.getUseStartDate(), req.getUseEndDate());
        } catch (ParseException e) {
            builder.setRetCode(-25001).setRetMsg("日期格式不正确");
        }

        //调用方会将开始时间，结束时间相减补齐为整数天数，但是数据库存的开始时间是从0时0分00秒开始的，所以需要转换下
        String useStartDateStr = req.getUseStartDate().substring(0, 8) + "000000";
        LocalDateTime useStartDate = DateUtil.getDateFromStr(useStartDateStr, DateUtil.DATE_TYPE4);
        // 由于用车结束时间不包括最后1秒，这里需要减1秒
        String calEndTimeStr = DateUtil.dateToString(useStartDate.plusDays(daysNumber).minusSeconds(1), DateUtil.DATE_TYPE4);
        String useEndDateStr = calEndTimeStr.substring(0, 8) + "235959";
        LocalDateTime useEndDate = DateUtil.getDateFromStr(useEndDateStr, DateUtil.DATE_TYPE4);
        List<Long> availableIds = null;

        //当前条件可用套餐

        QueryAvailPackageDto queryAvailPackageDto = new QueryAvailPackageDto();
        queryAvailPackageDto.setOrgCode(req.getOrgCode());
        queryAvailPackageDto.setStoreId(req.getStoreId());
        queryAvailPackageDto.setGoodsModelId(req.getGoodsModelId());
        queryAvailPackageDto.setRenewUseFlag(0);
        if (req.getUseScene() == 2) {
            queryAvailPackageDto.setRenewUseFlag(1);
        }
        queryAvailPackageDto.setDaysNumber(daysNumber);
        queryAvailPackageDto.setUseStartDate(useStartDate);
        queryAvailPackageDto.setUseEndDate(useEndDate);
        List<PackageConfiguration> availablePackage = packageConfigurationMapper.getAvailablePackage(queryAvailPackageDto);
        if (CollectionUtil.isNotEmpty(availablePackage)) {
            List<PackageConfiguration> removePackages = new ArrayList<>();
            for (PackageConfiguration packageConf : availablePackage) {
                Integer daysNumberConf = packageConf.getDaysNumber();
                LocalDateTime useStartDateConf = DateUtil.getDateFromStr(packageConf.getUseStartDate(), DateUtil.DATE_TYPE1);
                LocalDateTime useEndDateConf = DateUtil.getDateFromStr(packageConf.getUseEndDate(), DateUtil.DATE_TYPE1);
                //三种情况
                // 计算时间差的方法getDaysNum()的endDate的入参，都需要加1秒，从23:59:59变成第2天的0点
                if ((useStartDateConf.isBefore(useStartDate) || useStartDateConf.equals(useStartDate))
                        && (useEndDateConf.isBefore(useEndDate) || useEndDateConf.equals(useEndDate))
                        && getDaysNum(useStartDate, useEndDateConf.plusSeconds(1)) < daysNumberConf) {
                    removePackages.add(packageConf);
                }
                if ((useStartDateConf.isAfter(useStartDate) || useStartDateConf.equals(useStartDate))
                        && (useEndDateConf.isAfter(useEndDate) || useEndDateConf.equals(useEndDate))
                        && getDaysNum(useStartDateConf, useEndDate.plusSeconds(1)) < daysNumberConf) {
                    if (!removePackages.contains(packageConf)) {
                        removePackages.add(packageConf);
                    }
                }
            }

            if (CollectionUtil.isNotEmpty(removePackages)) {
                availablePackage.removeAll(removePackages);
            }

            List<PackageConfigForApp> packageForApps = availablePackage.stream().map(this::convertPackageConfigForApp).collect(Collectors.toList());
            availableIds = availablePackage.stream().map(PackageConfiguration::getId).collect(Collectors.toList());
            if (BusinessConst.EARLY_PACKAGE.equals(req.getEarlyFlag())){
                List<PackageConfigForApp> earlyPackage = packageForApps.stream().filter((a) -> a.getEarlyFlag() == 1).collect(Collectors.toList());
                builder.addAllAvailablePackage(earlyPackage);
            }else {
                builder.addAllAvailablePackage(packageForApps);
            }
        }

        //升级可用套餐
        QueryWrapper<PackageConfiguration> upgradeQuery = Wrappers.<PackageConfiguration>query()
                .eq("org_code", req.getOrgCode())
                .eq("store_id", req.getStoreId())
                .eq(req.getGoodsModelId() != 0L, "goods_model_id", req.getGoodsModelId())
                .eq(req.getUseScene() == 2, "renew_use_flag", 1)
                .eq("config_state", 1)
                .notIn((availableIds != null && CollectionUtil.isNotEmpty(availableIds)), "id", availableIds)
                .orderByDesc("days_number");


        List<PackageConfiguration> upgradePackages = packageConfigurationMapper.selectList(upgradeQuery);

        //List<PackageConfiguration> upgradePackages = packageConfigurationMapper.getUpgradePackage(queryAvailPackageDto);

        if (CollectionUtil.isNotEmpty(upgradePackages)) {
            List<PackageConfigForApp> upgradePackagesForApp = upgradePackages.stream().map(this::convertUpgradePackageConfigForApp).collect(Collectors.toList());
            builder.addAllUpgradePackage(upgradePackagesForApp);
        }

        return builder.build();
    }

    private int getDaysNum(LocalDateTime startTime, LocalDateTime endTime) {
        Duration days = Duration.between(startTime, endTime);
        return (int) days.toDays();
    }

    private PackageConfigForApp convertPackageConfigForApp(PackageConfiguration packageConfiguration) {
        PackageConfigForApp.Builder builder = PackageConfigForApp.newBuilder();
        builder.setEarlyFlag(BusinessConst.NOT_EARLY_PACKAGE);
        if (BusinessConst.EARLY_PACKAGE.equals(packageConfiguration.getEarlyFlag())) {
            //LocalDateTime earlyStartTime = DateUtil.getDateFromStr(packageConfiguration.getEarlyStartDate(), DateUtil.DATE_TYPE1);
            LocalDateTime earlyEndTime = DateUtil.getDateFromStr(packageConfiguration.getEarlyEndDate(), DateUtil.DATE_TYPE1);
            LocalDateTime nowTime = LocalDateTime.now();
            if (nowTime.isBefore(earlyEndTime) || nowTime.equals(earlyEndTime)) {
                builder.setEarlyFlag(BusinessConst.EARLY_PACKAGE);
                builder.setEarlyPrice(packageConfiguration.getEarlyPrice().toString());
                builder.setEarlyStartDate(DateUtil.dateToString(DateUtil.getDateFromStr(packageConfiguration.getEarlyStartDate(), DateUtil.DATE_TYPE1), DateUtil.DATE_TYPE4));
                builder.setEarlyEndDate(DateUtil.dateToString(DateUtil.getDateFromStr(packageConfiguration.getEarlyEndDate(), DateUtil.DATE_TYPE1), DateUtil.DATE_TYPE4));
            }
        }

        String useStartDate = packageConfiguration.getUseStartDate().substring(0, 10);
        String useEndDate = packageConfiguration.getUseEndDate().substring(0, 10);
        Integer daysNumber = packageConfiguration.getDaysNumber();
        String desc = "限" + useStartDate + "~" + useEndDate + "满" + daysNumber + "天可用";

        return builder
                .setId(packageConfiguration.getId())
                .setPackageName(packageConfiguration.getPackageName())
                .setDaysNumber(daysNumber)
                .setTotalPrice(packageConfiguration.getTotalPrice().toString())
                .setDesc(desc)
                .build();
    }

    private PackageConfigForApp convertUpgradePackageConfigForApp(PackageConfiguration packageConfiguration) {
        PackageConfigForApp.Builder builder = PackageConfigForApp.newBuilder();
        builder.setEarlyFlag(BusinessConst.NOT_EARLY_PACKAGE);
        if (BusinessConst.EARLY_PACKAGE.equals(packageConfiguration.getEarlyFlag())) {
            LocalDateTime earlyEndTime = DateUtil.getDateFromStr(packageConfiguration.getEarlyEndDate(), DateUtil.DATE_TYPE1);
            LocalDateTime nowTime = LocalDateTime.now();
            if (nowTime.isBefore(earlyEndTime) || nowTime.equals(earlyEndTime)) {
                builder.setEarlyFlag(BusinessConst.EARLY_PACKAGE);
                builder.setEarlyPrice(packageConfiguration.getEarlyPrice().toString());
                builder.setEarlyStartDate(DateUtil.dateToString(DateUtil.getDateFromStr(packageConfiguration.getEarlyStartDate(), DateUtil.DATE_TYPE1), DateUtil.DATE_TYPE4));
                builder.setEarlyEndDate(DateUtil.dateToString(DateUtil.getDateFromStr(packageConfiguration.getEarlyEndDate(), DateUtil.DATE_TYPE1), DateUtil.DATE_TYPE4));
            }
        }

        String useStartDate = packageConfiguration.getUseStartDate().substring(0, 10);
        String useEndDate = packageConfiguration.getUseEndDate().substring(0, 10);
        Integer daysNumber = packageConfiguration.getDaysNumber();
        String desc = "限" + useStartDate + "~" + useEndDate + "满" + daysNumber + "天可用";

        return builder
                .setId(packageConfiguration.getId())
                .setPackageName(packageConfiguration.getPackageName())
                .setDaysNumber(daysNumber)
                .setTotalPrice(packageConfiguration.getTotalPrice().toString())
                .setDesc(desc)
                .build();
    }

    private PackageConfigForApp convertUpgradePackageForApp(PackageConfiguration packageConfiguration) {
        Integer daysNumber = packageConfiguration.getDaysNumber();
        String desc = "用车时长" + "满" + daysNumber + "天可用";

        return PackageConfigForApp.newBuilder()
                .setId(packageConfiguration.getId())
                .setPackageName(packageConfiguration.getPackageName())
                .setDaysNumber(daysNumber)
                .setTotalPrice(packageConfiguration.getTotalPrice().toString())
                .setDesc(desc)
                .build();
    }

    private int getDaysNumber(String useStartDateStr, String useEndDateStr) throws ParseException {
        LocalDateTime startTime = DateUtil.getDateFromStr(useStartDateStr, DateUtil.DATE_TYPE4);
        LocalDateTime endTime = DateUtil.getDateFromStr(useEndDateStr, DateUtil.DATE_TYPE4);

        long betweenMinutes = Duration.between(startTime, endTime).toMinutes();
        return (int) Math.ceil(betweenMinutes / (24 * 60D));

        /*SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        Date endDate = simpleFormat.parse(useEndDate);
        Date startDate = simpleFormat.parse(useStartDate);
        long endTime = endDate.getTime();
        long startTime = startDate.getTime();
        int days = (int) ((endTime - startTime) / (1000 * 60 * 60 * 24));
        return days;*/

    }

    @Override
    public GetPackageByIdRes getPackageById(GetPackageByIdReq req) {
        GetPackageByIdRes.Builder builder = GetPackageByIdRes.newBuilder();
        long packageId = req.getPackageId();
        PackageConfiguration packageConfiguration = packageConfigurationMapper.selectById(packageId);
        if (ObjectUtil.isNull(packageConfiguration)) {
            return GetPackageByIdRes.failed(-1000, "未查询到套餐信息");
        }
        String useStartDate = packageConfiguration.getUseStartDate().substring(0, 10);
        String useEndDate = packageConfiguration.getUseEndDate().substring(0, 10);
        Integer daysNumber = packageConfiguration.getDaysNumber();
        String desc = "限" + useStartDate + "~" + useEndDate + "满" + daysNumber + "天可用";
        if (StrUtil.isNotBlank(packageConfiguration.getEndTime())) {
            builder.setEndTime(DateUtil.convertDatetimeStr(packageConfiguration.getEndTime()));
        }
        if (BusinessConst.EARLY_PACKAGE.equals(packageConfiguration.getEarlyFlag())) {
            builder.setEarlyStartDate(packageConfiguration.getEarlyStartDate());
            builder.setEarlyEndDate(packageConfiguration.getEarlyEndDate());
            builder.setEarlyPrice(packageConfiguration.getEarlyPrice().toString());
        }
        builder.setId(packageConfiguration.getId())
                .setOrgCode(packageConfiguration.getOrgCode())
                .setOrgName(orgService.getOrgNameByOrgCode(packageConfiguration.getOrgCode()))
                .setStoreId(packageConfiguration.getStoreId())
                .setStoreName(storeService.getStoreNameByStoreId(packageConfiguration.getStoreId()))
                .setGoodsModelId(packageConfiguration.getGoodsModelId())
                .setGoodsModelName(goodsModelService.getGoodsModelNameById(packageConfiguration.getGoodsModelId()))
                .setPackageName(packageConfiguration.getPackageName())
                .setDaysNumber(packageConfiguration.getDaysNumber())
                .setTotalPrice(packageConfiguration.getTotalPrice().toString())
                .setRenewUseFlag(packageConfiguration.getRenewUseFlag())
                .setUseStartDate(DateUtil.convertDatetimeStr(packageConfiguration.getUseStartDate()))
                .setUseEndDate(DateUtil.convertDatetimeStr(packageConfiguration.getUseEndDate()))
                .setStartTime(DateUtil.convertDatetimeStr(packageConfiguration.getStartTime()))
                .setConfigState(packageConfiguration.getConfigState())
                .setEarlyFlag(packageConfiguration.getEarlyFlag())
                .setDesc(desc);
        return builder.build();
    }

    @Override
    public GetPackageByIdRes getPackageDetail(GetPackageByIdReq req) {
        GetPackageByIdRes.Builder builder = GetPackageByIdRes.newBuilder();
        long packageId = req.getPackageId();
        PackageConfiguration packageConfiguration = packageConfigurationMapper.selectById(packageId);
        if (ObjectUtil.isNull(packageConfiguration)) {
            return GetPackageByIdRes.failed(-1000, "未查询到套餐信息");
        }
        String useStartDate = packageConfiguration.getUseStartDate().substring(0, 10);
        String useEndDate = packageConfiguration.getUseEndDate().substring(0, 10);
        if (StrUtil.isNotBlank(packageConfiguration.getEndTime())) {
            builder.setEndTime(packageConfiguration.getEndTime());
        }
        if (BusinessConst.EARLY_PACKAGE.equals(packageConfiguration.getEarlyFlag())) {
            builder.setEarlyStartDate(packageConfiguration.getEarlyStartDate().substring(0, 10));
            builder.setEarlyEndDate(packageConfiguration.getEarlyEndDate().substring(0, 10));
            builder.setEarlyPrice(packageConfiguration.getEarlyPrice().toString());
        }
        builder.setId(packageConfiguration.getId())
                .setOrgCode(packageConfiguration.getOrgCode())
                .setOrgName(orgService.getOrgNameByOrgCode(packageConfiguration.getOrgCode()))
                .setStoreId(packageConfiguration.getStoreId())
                .setStoreName(storeService.getStoreNameByStoreId(packageConfiguration.getStoreId()))
                .setGoodsModelId(packageConfiguration.getGoodsModelId())
                .setGoodsModelName(goodsModelService.getGoodsModelNameById(packageConfiguration.getGoodsModelId()))
                .setPackageName(packageConfiguration.getPackageName())
                .setDaysNumber(packageConfiguration.getDaysNumber())
                .setTotalPrice(packageConfiguration.getTotalPrice().toString())
                .setRenewUseFlag(packageConfiguration.getRenewUseFlag())
                .setUseStartDate(useStartDate)
                .setUseEndDate(useEndDate)
                .setStartTime(packageConfiguration.getStartTime())
                .setConfigState(packageConfiguration.getConfigState())
                .setEarlyFlag(packageConfiguration.getEarlyFlag());
        return builder.build();
    }

    @Override
    public SearchAllPackageRes searchAllPackage(SearchAllPackageReq req) {
        SearchAllPackageRes.Builder builder = SearchAllPackageRes.newBuilder();
        LambdaQueryWrapper<PackageConfiguration> queryWrapper = new LambdaQueryWrapper<PackageConfiguration>()
                .eq(PackageConfiguration::getIsDeleted, 0)
                .in(PackageConfiguration::getConfigState, 1, 2, 3);
        List<PackageConfiguration> packageList = packageConfigurationMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(packageList)) {
            List<PackageConfig> list = packageList.stream().map(this::convertPackageInfoForApp).collect(Collectors.toList());
            builder.addAllPackageConfig(list);
        }

        return builder.setCfgMd5(req.getCfgMd5()).build();
    }

    private PackageConfig convertPackageInfoForApp(PackageConfiguration packageConfiguration) {
        PackageConfig.Builder builder = PackageConfig.newBuilder();
        if (StrUtil.isNotBlank(packageConfiguration.getEndTime())) {
            builder.setEndTime(DateUtil.convertDatetimeStr(packageConfiguration.getEndTime()));
        }
        if (BusinessConst.EARLY_PACKAGE.equals(packageConfiguration.getEarlyFlag())) {
            builder.setEarlyStartDate(packageConfiguration.getEarlyStartDate());
            builder.setEarlyEndDate(packageConfiguration.getEarlyEndDate());
            builder.setEarlyPrice(packageConfiguration.getEarlyPrice().toString());
        }

        return
                builder.setId(packageConfiguration.getId())
                        .setOrgCode(packageConfiguration.getOrgCode())
                        .setOrgName(orgService.getOrgNameByOrgCode(packageConfiguration.getOrgCode()))
                        .setStoreId(packageConfiguration.getStoreId())
                        .setStoreName(storeService.getStoreNameByStoreId(packageConfiguration.getStoreId()))
                        .setGoodsModelId(packageConfiguration.getGoodsModelId())
                        .setGoodsModelName(goodsModelService.getGoodsModelNameById(packageConfiguration.getGoodsModelId()))
                        .setPackageName(packageConfiguration.getPackageName())
                        .setDaysNumber(packageConfiguration.getDaysNumber())
                        .setTotalPrice(packageConfiguration.getTotalPrice().toString())
                        .setRenewUseFlag(packageConfiguration.getRenewUseFlag())
                        .setUseStartDate(DateUtil.convertDatetimeStr(packageConfiguration.getUseStartDate()))
                        .setUseEndDate(DateUtil.convertDatetimeStr(packageConfiguration.getUseEndDate()))
                        .setStartTime(DateUtil.convertDatetimeStr(packageConfiguration.getStartTime()))
                        .setConfigState(packageConfiguration.getConfigState())
                        .setEarlyFlag(packageConfiguration.getEarlyFlag())
                        .build();
    }

}
