package com.saicmobility.evcard.md.act.dto.welfare;

import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.mdactservice.api.CouponDetailInfo;
import com.saicmobility.evcard.md.mdactservice.api.GetWelfareDetailInfoRes;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class CouponWelfareInfo implements Serializable {

    private String name; // 优惠券名称  iss.mmp_third_coupon.coupon_name
    private int type; // 优惠券类型 1:直扣 2：折扣    siac.coupon_def.COUPON_TYPE

    /**
     * 折扣金额 type = 1时有值
     * siac.coupon_def.COUPON_VALUE
     */
    private String disCountAmount;

    /**
     * 折扣额度 type = 2时有值
     * siac.coupon_def.DISCOUNT_RATE
     */
    private String disCount;

    /**
     * 使用条件描述
     * type = 2 最多抵60元租金  siac.coupon_def.COUPON_VALUE  ,
     * type = 1  满180元可用   siac.coupon_def.MIN_AMOUNT   siac.coupon_def.duration_limit
     *
     */
    private String conditionDesc;
    private String startDate; // 有效期开始时间 YYYY.MM.DD   iss.mmp_third_coupon.START_DATE
    private String endDate; // 有效期结束时间 YYYY.MM.DD     iss.mmp_third_coupon.EXPIRES_DATE
    private String tip; // 使用注意事项

    // 有效期描述
    private String expirationDateTip;

    public static GetWelfareDetailInfoRes listToRes(List<CouponWelfareInfo> couponWelfareDetailInfoList) {
        if (CollectionUtils.isNotEmpty(couponWelfareDetailInfoList)) {
            List<CouponDetailInfo> couponDetailInfoList = couponWelfareDetailInfoList.stream().map(CouponWelfareInfo::toRes).collect(Collectors.toList());
            return GetWelfareDetailInfoRes.newBuilder().addAllCouponDetailInfos(couponDetailInfoList).build();
        }
        return GetWelfareDetailInfoRes.failed(ErrorEnum.NOT_FOUND_WELFARE_INFO.getCode(), ErrorEnum.NOT_FOUND_WELFARE_INFO.getMsg());
    }

    public static CouponDetailInfo toRes(CouponWelfareInfo welfareInfo) {
        CouponDetailInfo couponDetailInfo = CouponDetailInfo
                .newBuilder()
                .setName(welfareInfo.getName())
                .setType(welfareInfo.getType())
                .setDisCount(welfareInfo.getDisCount())
                .setDisCountAmount(welfareInfo.getDisCountAmount())
                .setConditionDesc(welfareInfo.getConditionDesc())
                .setStartDate(welfareInfo.getStartDate())
                .setEndDate(welfareInfo.getEndDate())
                .setTip(welfareInfo.getTip())
                .setExpirationDateTip(welfareInfo.getExpirationDateTip())
                .build();
        return couponDetailInfo;
    }
}
