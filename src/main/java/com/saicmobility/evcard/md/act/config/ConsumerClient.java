package com.saicmobility.evcard.md.act.config;

import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.aliyun.openservices.ons.api.bean.OrderConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import com.aliyun.openservices.ons.api.order.MessageOrderListener;
import com.saicmobility.evcard.md.act.mq.PickupVehicleListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Configuration
public class ConsumerClient {

    @Autowired
    PickupVehicleListener pickupVehicleListener;

    @Autowired
    private OnsConfiguration mqConfig;


    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public OrderConsumerBean normalConsumer() {
        OrderConsumerBean consumerBean = new OrderConsumerBean();
        //配置文件
        Properties properties = mqConfig.getMqProperties();
        properties.setProperty(PropertyKeyConst.GROUP_ID, mqConfig.getContractGroupId());
        //将消费者线程数固定为20个 20为默认值
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "20");
        consumerBean.setProperties(properties);
        //订阅关系
        Map<Subscription, MessageOrderListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic(mqConfig.getContractNormalTopic());
        String tag = "PICKUP_VEHICLE";
        subscription.setExpression(tag);
        subscriptionTable.put(subscription, pickupVehicleListener);

        consumerBean.setSubscriptionTable(subscriptionTable);
        return consumerBean;
    }

}
