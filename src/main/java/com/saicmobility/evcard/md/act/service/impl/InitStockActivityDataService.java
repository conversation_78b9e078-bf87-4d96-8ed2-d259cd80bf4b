package com.saicmobility.evcard.md.act.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.saicmobility.evcard.md.act.domain.StoreInfoCombobox;
import com.saicmobility.evcard.md.act.entity.BaseEntity;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivity;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivitySignup;
import com.saicmobility.evcard.md.act.enums.IsDeletedEnum;
import com.saicmobility.evcard.md.act.mapper.act.ProprietaryActivityMapper;
import com.saicmobility.evcard.md.act.mapper.act.ProprietaryActivitySignupMapper;
import com.saicmobility.evcard.md.act.service.extern.ConfigLoader;
import com.saicmobility.evcard.md.act.util.CommonUtils;
import com.saicmobility.evcard.md.mdstoreservice.api.GetStoreListByCdReq;
import com.saicmobility.evcard.md.mdstoreservice.api.GetStoreListByCdRes;
import com.saicmobility.evcard.md.mdstoreservice.api.MdStoreService;
import com.saicmobility.evcard.md.mdstoreservice.api.StoreInfo;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InitStockActivityDataService {

    @Resource
    private ProprietaryActivityMapper proprietaryActivityMapper;
    @Resource
    private ProprietaryActivitySignupMapper proprietaryActivitySignupMapper;

    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Autowired
    private MdStoreService mdStoreService;

    //批量新增/修改执行总条数
    private final int TOTAL_NUMBER = 500;

    @Transactional(rollbackFor = Exception.class)
    public void doInit()  {
        Map<String, List<StoreInfoCombobox>> storeMap = getStoreMap();
        log.info("tid:{} ------------初始化存量自营活动门店数据开始----------", Trace.currentTraceId());
        List<ProprietaryActivity> list = new LambdaQueryChainWrapper<>(proprietaryActivityMapper)
                //.eq(BaseEntity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .isNull(ProprietaryActivity::getStoreIds)
                .list();
        log.info("tid:{} ------------批量更新【自营活动】门店数据 size:{}------------", Trace.currentTraceId(),list.size());
        if (CollectionUtils.isNotEmpty(list)) {
            List<ProprietaryActivity>  updateList = new ArrayList<>(list.size());
            for (int i=0; i<list.size() ; i++) {
                setStoreId(list, i, storeMap, updateList);
                if(updateList.size()>=TOTAL_NUMBER || (i == list.size()-1 && !updateList.isEmpty())){
                    try{
                        int rows = proprietaryActivityMapper.updateBatchStoreId(updateList);
                        updateList.clear();
                        log.info("tid:{}------------批量更新【自营活动】门店数据 更新行:rows{}------------,data:{}"
                                ,Trace.currentTraceId(),rows,JSONObject.toJSONString(updateList));
                    }catch (Exception e){
                        log.error(" 批量更新【自营活动】门店数据异常, tId:{},data:{},e:"
                                ,Trace.currentTraceId(),JSONObject.toJSONString(updateList),e);
                        throw e;
                    }

                }
            }
        }
        List<ProprietaryActivitySignup> signupList = new LambdaQueryChainWrapper<>(proprietaryActivitySignupMapper)
               // .eq(BaseEntity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .isNull(ProprietaryActivitySignup::getStoreId)
                .list();
        log.info("tid:{} ------------批量更新【参与自营活动】门店数据 size:{}------------", Trace.currentTraceId(),signupList.size());
        if(CollectionUtils.isNotEmpty(signupList)){
            List<ProprietaryActivitySignup>  updateSignupList = new ArrayList<>();
            List<ProprietaryActivitySignup>  addSignupList = new ArrayList<>();
            for (int i = 0; i < signupList.size(); i++) {
                setSignup(signupList, i, storeMap, updateSignupList, addSignupList);
                if(updateSignupList.size()>=TOTAL_NUMBER || (i == signupList.size()-1 && !updateSignupList.isEmpty())){
                    try {
                        int rows = proprietaryActivitySignupMapper.updateBatchStoreId(updateSignupList);
                        updateSignupList.clear();
                        log.info("tid:{}------------批量更新【参与自营活动】门店数据 更新行:rows{}------------,data:{}"
                                ,Trace.currentTraceId(),rows,JSONObject.toJSONString(updateSignupList));
                    }catch (Exception e){
                        log.error("批量更新【参与自营活动】门店数据, tId:{},data:{},e:",Trace.currentTraceId()
                                ,JSONObject.toJSONString(updateSignupList),e);
                        throw e;
                    }
                 
                }
                if(addSignupList.size()>=TOTAL_NUMBER || ( i == signupList.size()-1 && !addSignupList.isEmpty())){
                    try {
                        int rows = proprietaryActivitySignupMapper.insertBatch(addSignupList);
                        addSignupList.clear();
                        log.info("tid:{}------------批量更新【参与自营活动】门店数据 新增行:rows{}------------,data:{}"
                                ,Trace.currentTraceId(),rows,JSONObject.toJSONString(addSignupList));
                    }catch (Exception e){
                        log.error("批量插入【参与自营活动】门店数据, tId:{},data:{},e:",Trace.currentTraceId()
                                ,JSONObject.toJSONString(addSignupList),e);
                        throw e;
                    }
                }
            }
        }
        log.info("tid:{} ------------初始化存量自营活动门店数据結束----------", Trace.currentTraceId());
    }

    private Map<String, List<StoreInfoCombobox>> getStoreMap() {
        log.info("tid:{} ------------初始化存量自营活动门店数,获取门店开始----------", Trace.currentTraceId());
        //获取所有门店并根据机构分组  key:机构,value:门店列表
        GetStoreListByCdRes storeList = mdStoreService.getStoreListByCd(GetStoreListByCdReq.newBuilder().build());
        List<StoreInfo> storeInfos = storeList.getInfoList();
        //StoreStatus门店状态(1上线，2下线)    StoreNewStatus  门店新状态(1正常、2已关闭)
        Map<String, List<StoreInfoCombobox>> storeMap  = storeInfos
                .stream()
                .filter(o-> 1 == o.getStoreStatus() &&  1 == o.getStoreNewStatus())
                .map(store -> {
                    StoreInfoCombobox storeInfo = new StoreInfoCombobox();
                    storeInfo.setStoreId(store.getId());
                    storeInfo.setStoreName(store.getStoreName());
                    storeInfo.setOperOrgCode(store.getOperOrgCode());
                    storeInfo.setOperOrgName(store.getOperOrgName());
                    storeInfo.setOperCityId(store.getOperCityId());
                    storeInfo.setOperCityName(store.getOperCityName());
                    return storeInfo;
                })
                .collect(Collectors.groupingBy(StoreInfoCombobox::getOperOrgCode));
        log.info("tid:{} ------------初始化存量自营活动门店数据,获取门店结束----------,storeMap:{}", Trace.currentTraceId(), JSONObject.toJSONString(storeMap));
        return storeMap;
    }

    private  void setSignup(List<ProprietaryActivitySignup> signupList, int i, Map<String, List<StoreInfoCombobox>> storeMap,
                                     List<ProprietaryActivitySignup> updateSignupList, List<ProprietaryActivitySignup> addSignupList) {
        ProprietaryActivitySignup signup = signupList.get(i);
        List<StoreInfoCombobox> storeList = storeMap.get(signup.getOrgCode());
        if (CollectionUtils.isEmpty(storeList)) {
            log.error("tid:{} ------------批量更新【参与自营活动】门店数据->根据机构代码获取门店 数据为空 orgCode:{}------------",
                    Trace.currentTraceId(), signup.getOrgCode());
            return ;
        }
        //查询数据是否已经存在
        List<ProprietaryActivitySignup> existSignupList = new LambdaQueryChainWrapper<>(proprietaryActivitySignupMapper)
                //.eq(BaseEntity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .eq(ProprietaryActivitySignup::getActivityId, signup.getActivityId())
                .eq(ProprietaryActivitySignup::getOrgCode, signup.getOrgCode())
                .isNotNull(ProprietaryActivitySignup::getStoreId)
                .list();
        List<Long> collect = existSignupList.stream().map(ProprietaryActivitySignup::getStoreId).collect(Collectors.toList());
        List<StoreInfoCombobox> noExistList = new ArrayList<>();
        for (StoreInfoCombobox storeInfoCombobox : storeList) {
            if(!collect.contains(storeInfoCombobox.getStoreId())){
                noExistList.add(storeInfoCombobox);
            }
        }
        if (CollectionUtils.isEmpty(noExistList)) {
            log.error("tid:{} ------------批量更新【参与自营活动】门店数据-> 门店数据已存在 ------------",Trace.currentTraceId());
            return ;
        }
        // 第一个门店设置为更新
        signup.setStoreId(noExistList.get(0).getStoreId());
        updateSignupList.add(signup);

        // 对于剩余的门店，创建新的 signup 对象并添加到新增列表
        for (int k = 1; k < noExistList.size(); k++) {
            addSignupList.add(createSignupForStore(signup, noExistList.get(k)));
        }

    }

    private static void setStoreId(List<ProprietaryActivity> list, int index, Map<String, List<StoreInfoCombobox>> storeMap,
                                   List<ProprietaryActivity> updateList) {
        ProprietaryActivity proprietaryActivity = list.get(index);
        String orgCodes = proprietaryActivity.getOrgCodes();

        //当前自营活动的报名机构为所有时直接修改门店列表为所有-1
        if (StringUtils.equals(orgCodes, "-1")) {
            proprietaryActivity.setStoreIds("-1");
            updateList.add(proprietaryActivity);
            return;
        }

        // 分割机构代码并获取门店ID
        Set<Long> storeIdSet = getStoreIdsForOrgCodes(orgCodes, storeMap);

        // 如果找到了门店ID，则更新活动
        if (!storeIdSet.isEmpty()) {
            proprietaryActivity.setStoreIds(CommonUtils.toStringFromList(new ArrayList<>(storeIdSet)));
            updateList.add(proprietaryActivity);
        }
    }

    private static ProprietaryActivitySignup createSignupForStore(ProprietaryActivitySignup originalSignup, StoreInfoCombobox store) {
        ProprietaryActivitySignup newSignup = new ProprietaryActivitySignup();
        BeanUtil.copyProperties(originalSignup, newSignup);
        newSignup.setId(null); // 确保新对象是独立的
        newSignup.setStoreId(store.getStoreId()); // 设置新的门店 ID
        return newSignup;
    }

    private static Set<Long> getStoreIdsForOrgCodes(String orgCodes, Map<String, List<StoreInfoCombobox>> storeMap) {
        Set<Long> storeIdSet = new HashSet<>();
        List<String> orgCodesSplit = CommonUtils.orgCodesSplit(orgCodes);

        for (String orgCode : orgCodesSplit) {
            List<StoreInfoCombobox> storeList = storeMap.get(orgCode);
            if (CollectionUtils.isEmpty(storeList)) {
                log.error("tid:{} ------------批量更新【参与自营活动】门店数据->根据机构代码获取门店 数据为空 orgCode:{}------------",
                        Trace.currentTraceId(), orgCode);
                continue;
            }

            List<Long> storeIdList = storeList.stream().map(StoreInfoCombobox::getStoreId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(storeIdList)) {
                storeIdSet.addAll(storeIdList);
            }
        }

        return storeIdSet;
    }
}
