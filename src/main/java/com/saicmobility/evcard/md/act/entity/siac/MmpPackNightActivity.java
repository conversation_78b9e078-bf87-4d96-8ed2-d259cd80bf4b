package com.saicmobility.evcard.md.act.entity.siac;

import lombok.Data;


@Data
public class MmpPackNightActivity {

    private Long id;

    /**
     *活动名称
     */
    private String activityName;

    /**
     *活动状态(0:待发布 1:待上线 2:进行中 3:已停止 4:暂停中)
     */
    private Long activityStatus;

    /**
     *组织机构ID, 多个以逗号分割
     */
    private String orgId;

    /**
     *参与组织机构ID, 多个以逗号分割
     */
    private String orgIds;

    /**
     *活动开始日期
     */
    private String activityStartDate;

    /**
     *活动结束日期
     */
    private String activityEndDate;

    /**
     *活动开始时间
     */
    private String activityStartTime;

    /**
     *活动结束时间
     */
    private String activityEndTime;

    /**
     *活动车牌
     */
    private String activityLicensePlate;

    /**
     *包夜活动每周
     */
    private String activityWeeds;

    /**
     *备注
     */
    private String remark;

    /**
     *创建时间
     */
    private String createTime;

    /**
     *创建人ID
     */
    private Long createOperId;

    /**
     *创建人姓名
     */
    private String createOperName;

    /**
     *修改时间
     */
    private String updateTime;

    /**
     *修改人ID
     */
    private Long updateOperId;

    /**
     *修改人姓名
     */
    private String updateOperName;

    /**
     *活动类型 0-包夜租 1-第三方发券 2-邀请好友 3-充值E币发券 4-渠道注册奖励
     * 5-优惠券批量导入-短模板 6-生成兑换码 7-扫码送券 8-品牌合作发券活动 9-订单分享活动 10-随E停活动
     * 11-优惠券批量导入-长模板 12-无门槛券导入 13调度红包活动 14首单减免 15客服赠券 16口令红包
     * 17订单完成奖励 18增值服务补偿券 19新版邀新活动  20年度账单
     */
    private Integer type;

    /**
     *活动送券配置表id
     */
    private Long thirdActivityId;

    /**
     *加密后的id
     */
    private String signId;

    /**
     *扣除额度的运营机构ID
     */
    private String quotaOrgId;

    /**
     *红包口令
     */
    private String password;

    /**
     *发券限制组合类别
     */
    private String groupId;

    /**
     *模板文件地址
     */
    private String templateFileUrl;


}