package com.saicmobility.evcard.md.act.dto.proprietary;

import com.alibaba.fastjson.annotation.JSONField;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.mdactservice.api.CurrentUser;
import com.saicmobility.evcard.md.mdactservice.api.UpdateProprietaryActivityReq;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/1/5 13:49
 */
@Data
@Slf4j
public class UpdateProprietaryActivityDto{
    private Long id;
    private AddProprietaryActivityDto addProprietaryActivityDto;

    @JSONField(serialize = false)
    private CurrentUser currentUser; // 当前用户
    /**
     * req->dto 入参为空校验
     * 日期范围校验
     * 不可用时间在取车时间范围内
     * @param req
     * @return
     */
    public UpdateProprietaryActivityDto parse(UpdateProprietaryActivityReq req) throws BusinessException {
        if(req.getId() <= 0){
            log.error("无效的活动id, id = {}", id);
            throw new BusinessException(ErrorEnum.INVALID_ACTIVITY_ID.getCode(), ErrorEnum.INVALID_ACTIVITY_ID.getMsg());
        }

        UpdateProprietaryActivityDto updateDto = new UpdateProprietaryActivityDto();
        AddProprietaryActivityDto addDto = new AddProprietaryActivityDto();
        addDto = addDto.parse(req.getAddProprietaryActivityReq());

        updateDto.setId(req.getId());
        updateDto.setAddProprietaryActivityDto(addDto);
        updateDto.setCurrentUser(req.getCurrentUser());
        return updateDto;
    }
}
