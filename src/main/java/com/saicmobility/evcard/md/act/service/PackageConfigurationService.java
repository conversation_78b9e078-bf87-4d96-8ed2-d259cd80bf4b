package com.saicmobility.evcard.md.act.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saicmobility.evcard.md.act.domain.common.PageResult;
import com.saicmobility.evcard.md.act.domain.packages.QueryPackageConfigInput;
import com.saicmobility.evcard.md.act.entity.PackageConfiguration;
import com.saicmobility.evcard.md.act.service.inner.IBaseService;
import com.saicmobility.evcard.md.mdactservice.api.*;

/**
 * 套餐配置 服务类
 */
public interface PackageConfigurationService extends IService<PackageConfiguration> {

    /**
     * 查询套餐配置信息
     */
    SearchPackageRes searchPackage(SearchPackageReq req);

    /**
     * 新增套餐
     */
    AddPackageRes addPackage(AddPackageReq req);

    /**
     * 下架套餐
     */
    OfflinePackageRes offlinePackage(OfflinePackageReq req);

    /**
     * 查询套餐名称(前端模糊搜索)
     */
    SearchPackageNameRes searchPackageName(SearchPackageNameReq req);


    /**
     * 根据用车条件获取可用套餐列表
     */
    SearchAvailablePackageRes searchAvailablePackage(SearchAvailablePackageReq req);

    /**
     * 根据ID查询套餐信息(app接口、客服)
     */
    GetPackageByIdRes getPackageById(GetPackageByIdReq req);

    /**
     * 根据ID查询套餐信息(配置后台)
     */
    GetPackageByIdRes getPackageDetail(GetPackageByIdReq req);

    /**
     * 获取全部套餐数据(缓存使用)
     */
    SearchAllPackageRes searchAllPackage(SearchAllPackageReq req);

}
