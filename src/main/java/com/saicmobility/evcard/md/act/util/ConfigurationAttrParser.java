package com.saicmobility.evcard.md.act.util;

import com.alibaba.fastjson.JSON;
import com.saicmobility.evcard.md.act.exception.BusinessRuntimeException;
import org.apache.commons.lang3.time.DateUtils;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 配置属性解析
 */
public class ConfigurationAttrParser {


    public static Object parse(String value, Type clazz, boolean isJson) {
        if (isJson) {
            return JSON.parseObject(value, clazz);
        }
        if (String.class == clazz) {
            return value;
        } else if (Integer.class == clazz) {
            return Integer.valueOf(value);
        } else if (Long.class == clazz) {
            return Long.valueOf(value);
        } else if (BigDecimal.class == clazz) {
            return new BigDecimal(value);
        } else if (Date.class == clazz) {
            return new Date(value);
        } else if (Boolean.class == clazz) {
            return Boolean.valueOf(value);
        } else if (Double.class == clazz) {
            return Double.valueOf(value);
        } else if (Float.class == clazz) {
            return Float.valueOf(value);
        }
        throw new BusinessRuntimeException(-1, "未找到对应类型，无法进行解析");
    }

    public static String toString(Object fieldValue, boolean isJson) {
        if (fieldValue == null) {
            return null;
        }
        if (isJson) {
            return JSON.toJSONString(fieldValue);
        }
        if (fieldValue instanceof Date) {
            return DateUtil.format((Date) fieldValue, "yyyy-MM-dd HH:mm:ss.SSS");
        }
        return fieldValue.toString();
    }
}
