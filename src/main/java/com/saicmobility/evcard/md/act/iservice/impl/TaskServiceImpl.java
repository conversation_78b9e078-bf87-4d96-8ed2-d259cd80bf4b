package com.saicmobility.evcard.md.act.iservice.impl;

import com.saicmobility.evcard.md.act.constant.Constants;
import com.saicmobility.evcard.md.act.entity.Task;
import com.saicmobility.evcard.md.act.iservice.ITaskService;
import com.saicmobility.evcard.md.act.mapper.act.TaskMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * Task表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
@Service
public class TaskServiceImpl extends BaseServiceImpl<TaskMapper, Task> implements ITaskService {

    public void updateToFinished(Integer taskType, String taskParam, String lastRunMsg) {
        lambdaUpdate()
                .set(Task :: getTaskStatus, Constants.TASK_STATUS_FINISHED)
                .set(Task :: getLastRunTime, new Date())
                .set(Task :: getLastRunMsg, lastRunMsg)
                .set(Task:: getUpdateTime, new Date())
                .set(Task :: getUpdateOperId, "-1")
                .set(Task :: getUpdateOperName, "system")
                .eq(Task :: getTaskType, taskType)
                .eq(Task :: getTaskParam, taskParam)
                .eq(Task :: getTaskStatus, Constants.TASK_STATUS_PENDING )
                .update();
    }

    public void updateToFinished(Integer taskType, String taskParam, String lastRunMsg, Long id) {
        lambdaUpdate()
                .set(Task :: getTaskStatus, Constants.TASK_STATUS_FINISHED)
                .set(Task :: getLastRunTime, new Date())
                .set(Task :: getLastRunMsg, lastRunMsg)
                .set(Task:: getUpdateTime, new Date())
                .set(Task :: getUpdateOperId, "-1")
                .set(Task :: getUpdateOperName, "system")
                .eq(Task :: getTaskType, taskType)
                .eq(Task :: getTaskParam, taskParam)
                .eq(Task :: getTaskStatus, Constants.TASK_STATUS_PENDING )
                .eq(id != null && id > 0, Task :: getId, id)
                .update();
    }

    public void updateToFailed(Integer taskType, String taskParam, String lastRunMsg) {
        lambdaUpdate()
                .set(Task :: getTaskStatus, Constants.TASK_STATUS_FAILED)
                .set(Task :: getLastRunTime, new Date())
                .set(Task :: getLastRunMsg, lastRunMsg)
                .set(Task:: getUpdateTime, new Date())
                .set(Task :: getUpdateOperId, "-1")
                .set(Task :: getUpdateOperName, "system")
                .eq(Task :: getTaskType, taskType)
                .eq(Task :: getTaskParam, taskParam)
                .eq(Task :: getTaskStatus, Constants.TASK_STATUS_PENDING )
                .update();
    }

    public void updateToFailed(Integer taskType, String taskParam, String lastRunMsg, Long id) {
        lambdaUpdate()
                .set(Task :: getTaskStatus, Constants.TASK_STATUS_FAILED)
                .set(Task :: getLastRunTime, new Date())
                .set(Task :: getLastRunMsg, lastRunMsg)
                .set(Task:: getUpdateTime, new Date())
                .set(Task :: getUpdateOperId, "-1")
                .set(Task :: getUpdateOperName, "system")
                .eq(Task :: getTaskType, taskType)
                .eq(Task :: getTaskParam, taskParam)
                .eq(Task :: getTaskStatus, Constants.TASK_STATUS_PENDING )
                .eq(id != null && id > 0, Task :: getId, id)
                .update();
    }

    public void updateNextRunTime(Integer taskType, String taskParam, Date nextRunTime, String lastRunMsg) {
        lambdaUpdate()
                .setSql(" failed_times = failed_times + 1 ")
                .set(Task :: getNextRunTime, nextRunTime)
                .set(Task :: getLastRunTime, new Date())
                .set(Task :: getLastRunMsg, lastRunMsg)
                .set(Task:: getUpdateTime, new Date())
                .set(Task :: getUpdateOperId, "-1")
                .set(Task :: getUpdateOperName, "system")
                .eq(Task :: getTaskType, taskType)
                .eq(Task :: getTaskParam, taskParam)
                .eq(Task :: getTaskStatus, Constants.TASK_STATUS_PENDING)
                .update();
    }

    public void updateNextRunTime(Integer taskType, String taskParam, Date nextRunTime, String lastRunMsg, Long id) {
        lambdaUpdate()
                .setSql(" failed_times = failed_times + 1 ")
                .set(Task :: getNextRunTime, nextRunTime)
                .set(Task :: getLastRunTime, new Date())
                .set(Task :: getLastRunMsg, lastRunMsg)
                .set(Task:: getUpdateTime, new Date())
                .set(Task :: getUpdateOperId, "-1")
                .set(Task :: getUpdateOperName, "system")
                .eq(Task :: getTaskType, taskType)
                .eq(Task :: getTaskParam, taskParam)
                .eq(Task :: getTaskStatus, Constants.TASK_STATUS_PENDING)
                .eq(id != null && id > 0, Task :: getId, id)
                .update();
    }

    @Override
    public List<Task> listPendingTasks() {
        return lambdaQuery()
                .select(Task::getId, Task::getTaskType, Task::getTaskParam,Task::getFailedTimes)
                .eq(Task :: getTaskStatus, Constants.TASK_STATUS_PENDING)
                .le(Task :: getNextRunTime, new Date())
                .list();
    }

}