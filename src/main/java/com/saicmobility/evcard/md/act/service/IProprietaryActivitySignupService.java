package com.saicmobility.evcard.md.act.service;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.domain.activity.GetSignUpProprietaryActivityByVehicleModelIdsBo;
import com.saicmobility.evcard.md.act.domain.activity.GetSignupProprietaryActivityDetailBo;
import com.saicmobility.evcard.md.act.domain.activity.GetSignupProprietaryActivityListDto;
import com.saicmobility.evcard.md.act.domain.activity.SignupProprietaryActivityDto;
import com.saicmobility.evcard.md.act.domain.activity.UpdateSignupProprietaryActivityDto;
import com.saicmobility.evcard.md.act.entity.ProprietaryActivitySignup;
import com.saicmobility.evcard.md.act.service.inner.IBaseService;
import com.saicmobility.evcard.md.mdactservice.api.*;

import java.util.List;

/**
 * <p>
 * 自营活动报名表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
public interface IProprietaryActivitySignupService extends IBaseService<ProprietaryActivitySignup> {

    void signupProprietaryActivity(SignupProprietaryActivityDto dto) throws BusinessException;

    UpdateSignupProprietaryActivityRes updateSignupProprietaryActivity(UpdateSignupProprietaryActivityDto dto) throws BusinessException;

    QuitProprietaryActivityRes quitProprietaryActivity(QuitProprietaryActivityReq req);

    GetSignupProprietaryActivityListRes getSignupProprietaryActivityList(GetSignupProprietaryActivityListDto dto) throws BusinessException;

    GetSignupProprietaryActivityDetailBo getSignupProprietaryActivityDetail(long id) throws BusinessException;

    GetSignUpProprietaryActivityByVehicleModelIdsRes getSignUpProprietaryActivityByVehicleModelIds(GetSignUpProprietaryActivityByVehicleModelIdsBo bo) throws BusinessException;

    GetActSignUpDetailByVehicleModelIdRes getActSignUpDetailByVehicleModelId(GetActSignUpDetailByVehicleModelIdReq req) throws BusinessException;


    /**
     * 同步自营活动报名车型数据
     * @param storeId
     * @param storeIdStr
     * @param storeIdsList
     * @param vehicleModelIdsStr
     */
    void syncVehicle(Long storeId, String storeIdStr, List<String> storeIdsList, String vehicleModelIdsStr);


    /**
     * 同步子一个活动报名门店信息
     * @param storeId
     * @param orgCode
     */
    void syncStore(Long storeId,  String orgCode);

    /**
     * 按城市Id获取最优活动-周租月租使用
     * @param getPrimeActByCityIdReq
     * @return
     */
    GetPrimeActByCityRes getPrimeActByCity(GetPrimeActByCityReq getPrimeActByCityReq);
}
