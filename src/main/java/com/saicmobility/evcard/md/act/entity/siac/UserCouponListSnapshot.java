package com.saicmobility.evcard.md.act.entity.siac;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户优惠券列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="UserCouponListSnapshot对象", description="用户优惠券列表")
public class UserCouponListSnapshot extends Model<UserCouponListSnapshot> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户优惠券SEQ")
    @TableId(value = "USER_COUPON_SEQ", type = IdType.AUTO)
    private Long userCouponSeq;

    @ApiModelProperty(value = "证件号")
    @TableField("AUTH_ID")
    private String authId;

    @ApiModelProperty(value = "优惠券定义编号")
    @TableField("COUPON_SEQ")
    private BigDecimal couponSeq;

    @ApiModelProperty(value = "有效开始时间  YYYY-MM-DD")
    @TableField("START_DATE")
    private String startDate;

    @ApiModelProperty(value = "有效结束时间  YYYY-MM-DD")
    @TableField("EXPIRES_DATE")
    private String expiresDate;

    @ApiModelProperty(value = "前一天 0：未使用 1：已使用 2：已作废 3: 已过期 ")
    @TableField("BEFORE_STATUS")
    private BigDecimal beforeStatus;

    @ApiModelProperty(value = "0：未使用 1：已使用 2：已作废 3: 已过期 ")
    @TableField("STATUS")
    private BigDecimal status;

    @ApiModelProperty(value = "状态变动时间  YYYY-MM-DD")
    @TableField("STATUS_CHANGE_DATE")
    private String statusChangeDate;

    @TableField("CREATED_TIME")
    private String createdTime;

    @TableField("CREATED_USER")
    private String createdUser;

    @TableField("UPDATED_TIME")
    private String updatedTime;

    @TableField("UPDATED_USER")
    private String updatedUser;

    @ApiModelProperty(value = "优惠券名称")
    @TableField("COUPON_ORIGIN")
    private String couponOrigin;

    @ApiModelProperty(value = "兑换码")
    private String couponCode;

    @ApiModelProperty(value = "兑换码有效期开始时间")
    private LocalDateTime couponCodeStartTime;

    @ApiModelProperty(value = "兑换码有效期结束时间")
    private LocalDateTime couponCodeExpiresTime;

    @ApiModelProperty(value = "已读标记 0未读  1|null已读")
    @TableField("CRM_USER_COUPON_SEQ")
    private Integer crmUserCouponSeq;

    @TableField("exchangeTime")
    private LocalDateTime exchangetime;

    private String remark;

    @ApiModelProperty(value = "10 关注赠送 20 注册赠送 30分享点击 35 分享点击 40 分享注册 50会员推荐 60生日赠送 70注册消费 80游戏奖励 81游戏抵扣 90关怀赠送 99手工赠送")
    @TableField("OFFER_TYPE")
    private Integer offerType;

    @ApiModelProperty(value = "活动id")
    @TableField("ACTION_ID")
    private String actionId;

    @ApiModelProperty(value = "优惠券发放机构的id")
    @TableField("ORG_SEQ")
    private Long orgSeq;

    @ApiModelProperty(value = "核销优惠券的订单所属机构的id")
    @TableField("ORDER_ORG_SEQ")
    private Long orderOrgSeq;

    @ApiModelProperty(value = "核销该优惠券的订单编号")
    @TableField("ORDER_SEQ")
    private String orderSeq;

    @ApiModelProperty(value = "实际抵扣金额")
    @TableField("DISCOUNT")
    private Double discount;

    @ApiModelProperty(value = "优惠券关联键值，充值送券-充值交易号，订单分享-shareid")
    @TableField("ORIGIN_REF_SEQ")
    private String originRefSeq;

    @ApiModelProperty(value = "券交易类型 1 （原）直扣券类 2（原）折扣券类 3 非收入类 4 购买类")
    private Integer transactionType;

    @ApiModelProperty(value = "机构Id")
    private String agencyId;

    @ApiModelProperty(value = "冻结状态，0：未冻结；1：已冻结")
    private Integer frozen;

    @ApiModelProperty(value = "微信小程序兑换二维码图片地址")
    private String wechatCouponCodeQrUrl;



}
