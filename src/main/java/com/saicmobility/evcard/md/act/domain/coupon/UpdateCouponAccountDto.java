package com.saicmobility.evcard.md.act.domain.coupon;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class UpdateCouponAccountDto implements Serializable {

    private static final long serialVersionUID = -7772365000749300504L;

    /**
     * 用户authId/企业机构Id'
     */
    private String userId;
    /**
     * 账户类型 1 个人账户 2 企业账户
     */
    private Integer userType;
    /**
     * 券交易类型 1 （原）直扣券类 2（原）折扣券类 3 非收入类 4 购买类
     */
    private Integer transactionType;
    /**
     * 进
     */
    private BigDecimal inAmount;
    /**
     * 出
     */
    private BigDecimal outAmount;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作时间
     */
    private Date operateDateTime;

    public UpdateCouponAccountDto() {
    }

    public UpdateCouponAccountDto(String userId, Integer userType, Integer transactionType, BigDecimal inAmount, BigDecimal outAmount, String operator) {
        this.userId = userId;
        this.userType = userType;
        this.transactionType = transactionType;
        this.inAmount = inAmount;
        this.outAmount = outAmount;
        this.operator = operator;
    }

    public UpdateCouponAccountDto(String userId, Integer userType, Integer transactionType, BigDecimal inAmount, BigDecimal outAmount, String operator, Date operateDateTime) {
        this.userId = userId;
        this.userType = userType;
        this.transactionType = transactionType;
        this.inAmount = inAmount;
        this.outAmount = outAmount;
        this.operator = operator;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Integer getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(Integer transactionType) {
        this.transactionType = transactionType;
    }

    public BigDecimal getInAmount() {
        return inAmount;
    }

    public void setInAmount(BigDecimal inAmount) {
        this.inAmount = inAmount;
    }

    public BigDecimal getOutAmount() {
        return outAmount;
    }

    public void setOutAmount(BigDecimal outAmount) {
        this.outAmount = outAmount;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperateDateTime() {
        return operateDateTime;
    }

    public void setOperateDateTime(Date operateDateTime) {
        this.operateDateTime = operateDateTime;
    }

}
