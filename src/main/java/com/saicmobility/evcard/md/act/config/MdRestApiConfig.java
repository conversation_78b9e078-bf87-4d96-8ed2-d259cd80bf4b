package com.saicmobility.evcard.md.act.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "evcard.inner.api")
public class MdRestApiConfig {
    private String baseUrl;
    private String dubboProxyPath;

    private String orderCoupons;
    private String batchOrderCoupons;
    private String checkOrderCoupon;
    private String useCoupon;
    private String getCouponModelView;
    private String getCouponModelListView;
    private String getCouponDes;

    private String offerThirdCoupons;
    private String getMmpCouponList;
    // 优惠券兑换
    private String exchange;

    private String syncSendEmail;
}
