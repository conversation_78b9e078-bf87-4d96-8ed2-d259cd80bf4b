package com.saicmobility.evcard.md.act.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saicmobility.evcard.md.act.entity.BrandModelLog;
import com.saicmobility.evcard.md.act.enums.BrandModelOperStateEnum;
import com.saicmobility.evcard.md.act.enums.IsDeletedEnum;
import com.saicmobility.evcard.md.act.mapper.act.BrandModelLogMapper;
import com.saicmobility.evcard.md.act.service.IBrandModelLogService;
import com.saicmobility.evcard.md.act.util.ComUtils;
import com.saicmobility.evcard.md.mdactservice.api.BrandModelLogInfo;
import com.saicmobility.evcard.md.mdactservice.api.CurrentUser;
import com.saicmobility.evcard.md.mdactservice.api.QueryBrandModelLogsByActIdReq;
import com.saicmobility.evcard.md.mdactservice.api.QueryBrandModelLogsByActIdRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.saicmobility.evcard.md.act.constant.ErrorEnum.REQUEST_ERROR;

/**
 * <p>
 * 品牌车型操作日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Slf4j
@Service
public class BrandModelLogServiceImpl extends ServiceImpl<BrandModelLogMapper, BrandModelLog> implements IBrandModelLogService {

    @Override
    public Boolean saveOperateLog(Long brandModelId, BrandModelOperStateEnum brandModelEnum, String remark, CurrentUser currentUser) {
        BrandModelLog brandModelLog = new BrandModelLog();
        brandModelLog.setCreateOperId(currentUser.getUserId());
        brandModelLog.setCreateOperName(currentUser.getName());
        brandModelLog.setCreateOperOrgName(ComUtils.splitStr(currentUser.getOrgName(), 128));
        brandModelLog.setUpdateOperId(currentUser.getUserId());
        brandModelLog.setUpdateOperName(currentUser.getName());
        brandModelLog.setUpdateOperOrgName(ComUtils.splitStr(currentUser.getOrgName(), 128));
        brandModelLog.setBrandModelId(brandModelId);
        brandModelLog.setOperateType(brandModelEnum.getType());//1-新增,2-上线,3-修改,4-暂停,5-下线
        brandModelLog.setOperateContent(brandModelEnum.getMsg());
        brandModelLog.setOperateRemark(remark);
        brandModelLog.setCreateOperEmail(ComUtils.splitStr(currentUser.getUserName(), 128));
        brandModelLog.setUpdateOperEmail(ComUtils.splitStr(currentUser.getUserName(), 128));


        return save(brandModelLog);
    }

    @Override
    public QueryBrandModelLogsByActIdRes queryBrandModelLogsByActId(QueryBrandModelLogsByActIdReq queryBrandModelLogsByActIdReq) {
        try {
            QueryBrandModelLogsByActIdRes.Builder res = QueryBrandModelLogsByActIdRes.newBuilder();
            long brandModelId = queryBrandModelLogsByActIdReq.getBrandModelId();
            int pageNum = queryBrandModelLogsByActIdReq.getPageNum();
            int pageSize = queryBrandModelLogsByActIdReq.getPageSize()<= 0 ? 10 : queryBrandModelLogsByActIdReq.getPageSize();

            LambdaQueryWrapper<BrandModelLog> queryWrapper = new LambdaQueryWrapper<BrandModelLog>()
                    .eq(BrandModelLog::getBrandModelId, brandModelId)
                    .eq(BrandModelLog::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                    .orderByAsc(BrandModelLog::getCreateTime);

            Page<BrandModelLog> page = new Page<>(pageNum, pageSize,true);
            Page<BrandModelLog> result = this.page(page, queryWrapper);
            if (CollectionUtil.isEmpty(result.getRecords())) {
                return res.build();
            }else {
                List<BrandModelLogInfo> brandModelLogInfos = new ArrayList<>();
                List<BrandModelLog> records = result.getRecords();
                for (int i = 0; i < records.size(); i++) {
                    BrandModelLog brandModelLog = records.get(i);
                    brandModelLog.setId((long)((i + 1)));
                    brandModelLogInfos.add(BrandModelLog.toRes(brandModelLog));
                }
                return res.addAllBrandModelLogInfos(brandModelLogInfos)
                        .setTotal(result.getTotal())
                        .setSize(result.getSize())
                        .build();
            }
        }catch (Exception e){
            e.printStackTrace();
            return QueryBrandModelLogsByActIdRes.failed(REQUEST_ERROR.getCode(),REQUEST_ERROR.getMsg());
        }




    }
}
