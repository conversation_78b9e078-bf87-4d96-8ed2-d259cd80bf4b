package com.saicmobility.evcard.md.act.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.saicmobility.evcard.md.act.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 立减活动Bo
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_reduce_activity")
public class ReduceActivityBo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "运营公司id")
    private String orgCode;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "生效开始时间")
    private String activityStartTime;

    @ApiModelProperty(value = "生效结束时间")
    private String activityEndTime;

    @ApiModelProperty(value = "用户注册开始时间")
    private String registerStartTime;

    @ApiModelProperty(value = "用户注册结束时间")
    private String registerEndTime;

    @ApiModelProperty(value = "报名截止时间")
    private String signUpDeadline;

    @ApiModelProperty(value = "是否仅产品线内首单可用 0 否 1 是")
    private Integer firstOrderAvailable;

    @ApiModelProperty(value = "用户可参与次数")
    private Integer userParticipateNumber;

    @ApiModelProperty(value = "活动立减金额")
    private BigDecimal activityDiscount;

    @ApiModelProperty(value = "活动规则说明")
    private String activityRuleDescription;

    @ApiModelProperty(value = "活动图片")
    private String activityPicUrl;

    @ApiModelProperty(value = "状态 1 待生效 2 生效中 3 已下线")
    private Integer activityStatus;
}
