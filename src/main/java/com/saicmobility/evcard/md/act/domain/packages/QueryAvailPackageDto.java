package com.saicmobility.evcard.md.act.domain.packages;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class QueryAvailPackageDto {
    /**
     * 运营公司ID
     */
    private String orgCode;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     *配置状态
     */
    private Integer configState;

    /**
     * 续租是否可用
     */
    private Integer renewUseFlag;

    /**
     * 车型编号（商品车型）
     */
    private Long goodsModelId;

    /**
     * 用车开始时间
     */
    private LocalDateTime useStartDate;

    /**
     * 用车结束时间
     */
    private LocalDateTime useEndDate;

    /**
     * 天数
     */
    private Integer daysNumber;
}
