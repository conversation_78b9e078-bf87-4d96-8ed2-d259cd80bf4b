package com.saicmobility.evcard.md.act.domain.activity;

import com.alibaba.fastjson.annotation.JSONField;
import com.saicmobility.evcard.md.mdactservice.api.CurrentUser;
import com.saicmobility.evcard.md.mdactservice.api.SignupProprietaryActivityReq;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class SignupProprietaryActivityDto {
    private long activityId; // 活动id
    private List<String> orgCodes; // 活动报名机构
    private List<Long> vehicleModelIds; // 车型id列表
    private List<Long> storeIdList; //门店id列表
    private List<SignupFullMinusFlexiblePricing> fullMinusFlexiblePricing; // 满减灵活定价
    private List<SignupDiscountFlexiblePricing> discountFlexiblePricing; // 打折灵活定价
    private Integer isAllStore; //是否全部门店 0否，1是
    private Integer isAllVehicle;//是否全部车型 0否，1是
    @JSONField(serialize = false)
    private CurrentUser currentUser; // 当前用户

    public static SignupProprietaryActivityDto parse(SignupProprietaryActivityReq req) {
        SignupProprietaryActivityDto dto = new SignupProprietaryActivityDto();
        dto.setActivityId(req.getActivityId());
        dto.setIsAllStore(req.getIsAllStore());
        dto.setIsAllVehicle(req.getIsAllVehicle());
        // 活动报名门店列表
        if (!CollectionUtils.isEmpty(req.getStoreIdListList())) {
            dto.setStoreIdList(req.getStoreIdListList());
        }
        // 车型id列表
        if (!CollectionUtils.isEmpty(req.getVehicleModelIdsList())) {
            dto.setVehicleModelIds(req.getVehicleModelIdsList());
        }
        //门店列表
        if (!CollectionUtils.isEmpty(req.getStoreIdListList())) {
            dto.setStoreIdList(req.getStoreIdListList());
        }
        // 满减灵活定价
        if (!CollectionUtils.isEmpty(req.getFullMinusFlexiblePricingList())) {
            List<SignupFullMinusFlexiblePricing> list = req.getFullMinusFlexiblePricingList().stream().map(o ->{
                SignupFullMinusFlexiblePricing fullMinusFlexiblePricing = new SignupFullMinusFlexiblePricing();
                fullMinusFlexiblePricing.setDays(o.getDays());
                fullMinusFlexiblePricing.setDiscountAmount(o.getDiscountAmount());
                fullMinusFlexiblePricing.setMaxDiscountAmount(o.getMaxDiscountAmount());
                fullMinusFlexiblePricing.setMinDiscountAmount(o.getMinDiscountAmount());
                return fullMinusFlexiblePricing;
            }).collect(Collectors.toList());
            dto.setFullMinusFlexiblePricing(list);
        }
        // 打折灵活定价
        if (!CollectionUtils.isEmpty(req.getDiscountFlexiblePricingList())) {
            List<SignupDiscountFlexiblePricing> list = req.getDiscountFlexiblePricingList().stream().map(o ->{
                SignupDiscountFlexiblePricing fullMinusFlexiblePricing = new SignupDiscountFlexiblePricing();
                fullMinusFlexiblePricing.setDays(o.getDays());
                fullMinusFlexiblePricing.setDiscount(o.getDiscount());
                fullMinusFlexiblePricing.setMaxDiscount(o.getMaxDiscount());
                fullMinusFlexiblePricing.setMinDiscount(o.getMinDiscount());
                return  fullMinusFlexiblePricing;
            }).collect(Collectors.toList());
            dto.setDiscountFlexiblePricing(list);
        }
        dto.setCurrentUser(req.getCurrentUser());
        return dto;
    }
}
