package com.saicmobility.evcard.md.act.bo.proprietary;

import com.saicmobility.evcard.md.mdactservice.api.SignUpProprietaryActivityListInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:44
 */
@Data
public class QuerySignUpProprietaryActivityListBo {
    private List<QuerySignUpProprietaryActivityListInfo> list;
    private int total;

    public List<SignUpProprietaryActivityListInfo> toList(List<QuerySignUpProprietaryActivityListInfo> list) {
        List<SignUpProprietaryActivityListInfo> acts = new ArrayList<>();
        for (QuerySignUpProprietaryActivityListInfo info : list) {
            SignUpProprietaryActivityListInfo act = SignUpProprietaryActivityListInfo.newBuilder()
                    .setId(info.getId())
                    .setSignUpId(info.getSignUpId())
                    .setActivityName(info.getActivityName())
                    .setOrgCode(info.getOrgCode())
                    .setOrgName(info.getOrgName())
                    .setActivityType(info.getActivityType())
                    .setSignUpDate(info.getSignUpDate())
                    .setPricingType(info.getPricingType())
                    .setStoreId(info.getStoreId())
                    .setStoreName(info.getStoreName())
                    .build();
            acts.add(act);
        }
        return acts;
    }
}
