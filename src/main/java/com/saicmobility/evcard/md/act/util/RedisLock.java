package com.saicmobility.evcard.md.act.util;

import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;


/**
 * 
 * redis做分布式锁的实现
 * 注意：
 * <li>该锁不能作为全局变量使用</li>
 * <li>要求redis为 2.6.12及以上版本</li>
 * <AUTHOR>
 */
public class RedisLock implements Serializable {
	
	private static final long serialVersionUID = -1L;

    private static final int ONE_SECOND = 1000;

    public static final int DEFAULT_EXPIRY_TIME_MILLIS = 5 * ONE_SECOND;

    private final String lockKey;
    private final int lockExpiryInMillis;
    private final String lockUUID;
    private boolean isLocked = false;

    
    /**
     * 生成锁对象
     * @param lockKey
     */
    public RedisLock(String lockKey) {
        this(lockKey, DEFAULT_EXPIRY_TIME_MILLIS);
    }

    /**
     * 生成锁对象
     * @param lockKey
     * @param expiryTimeMillis
     */
    public RedisLock(String lockKey, int expiryTimeMillis) {
        this(lockKey, expiryTimeMillis, UUID.randomUUID().toString());
    }

    /**
     * 生成锁对象
     * @param lockKey
     * @param expiryTimeMillis
     * @param uuid
     */
    public RedisLock(String lockKey, int expiryTimeMillis, String uuid) {
        this.lockKey = lockKey;
        this.lockExpiryInMillis = expiryTimeMillis;
        this.lockUUID = uuid;;
    }
    
    
    public String getLockUUID() {
        return lockUUID;
    }

    
    public String getLockKey() {
        return lockKey;
    }

    /**
     * 加锁.
     * 这里是用的是redis 的set指令来实现加锁，详情请看：http://redis.io/commands/set
     * @return
     */
    public synchronized boolean acquire(RedisTemplate<String, String> temp) {
    	if(!isLocked) {
    		isLocked = temp.opsForValue().setIfAbsent( this.lockKey, this.lockUUID.toString(), this.lockExpiryInMillis, TimeUnit.MILLISECONDS);
    		return isLocked;
    	}
    	return false;
    }

    private final static String LUA_SCRIPT = "if redis.call('get',KEYS[1]) == ARGV[1] then return redis.call('del',KEYS[1]) else return 0 end";
    /**
     * 释放锁
     */
   public synchronized Object releaseLua(RedisTemplate<String, String> temp) {
    	//使用eval指令执行 Lua 脚本
    	//如果redis中该key对应的值已经变掉了，不再执行删除指令。防止偶然情况下线程执行时间过长，导致释放锁的时候把其他线程的锁释放掉
    	if(isLocked) {
    		Object object = Objects.requireNonNull(temp.getConnectionFactory()).getConnection().eval(LUA_SCRIPT.getBytes(), ReturnType.INTEGER, 1, (this.lockKey).getBytes(), this.lockUUID.toString().getBytes());
        	isLocked = false;
        	return object;
    	}
    	return 10;
    }
   
   
   
    /**
     * 检查是否已经加锁
     * @return
     * 
     * <AUTHOR> yibo
     */
    public synchronized boolean isLocked() {
        return this.isLocked;
    }

}