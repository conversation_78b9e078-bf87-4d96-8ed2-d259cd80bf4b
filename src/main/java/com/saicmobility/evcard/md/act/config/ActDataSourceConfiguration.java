package com.saicmobility.evcard.md.act.config;


import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
public class ActDataSourceConfiguration {

    public static final String MAPPER_LOCATION = "classpath:mapper/act/*.xml";

    @Autowired
    MybatisPlusInterceptor mybatisPlusInterceptor;
    /**
     * 主数据源
     * 说明：@Primary 如果有多个同类的Bean，该Bean优先考虑，多数据源时必须配置一个主数据源，用该注解标志
     * @return
     */
    @Primary
    @Bean("actDataSource")
    @ConfigurationProperties("spring.datasource.druid.act")
    public DataSource actDataSource(){
        return DruidDataSourceBuilder.create().build();
    }

    @Primary
    @Bean("actTransactionManager")
    public DataSourceTransactionManager mdTransactionManager() {
        return new DataSourceTransactionManager(actDataSource());
    }

    @Primary
    @Bean("actSqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("actDataSource") DataSource dataSource) throws Exception {
        // 使用到了mybatis plus , 所以这里使用的是mybatis plus组件
        MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION));
        sqlSessionFactoryBean.setPlugins(mybatisPlusInterceptor);
        return sqlSessionFactoryBean.getObject();
    }

    @Primary
    @Bean("actSqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("actDataSource") DataSource dataSource) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory(dataSource));
    }
}