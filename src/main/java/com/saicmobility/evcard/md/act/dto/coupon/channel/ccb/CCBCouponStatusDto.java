package com.saicmobility.evcard.md.act.dto.coupon.channel.ccb;

import com.saicmobility.evcard.md.act.dto.coupon.channel.CouponStatusDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 优惠券状态
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CCBCouponStatusDto {
    //产品编号，权益产品的唯一标识
    private String productId;
    //领券时的订单号
    private String orderId;
    //券码
    private String couponCode;
    /*状态变动对应的操作类型（详见表格下方说明）:
            011019-未使用->已使用（已产生成本，记作结算依据）
            011020-已使用->未使用（因退单而返还优惠券）
            011036-未兑换->已领取（已产生成本，记作结算依据）
            011037-未兑换->已兑换（未产生成本、不作结算依据，中间态）
    */
    private String operation;
    //使用优惠券时的核销订单号（用券订单号）
    //如果用户退单时(011020)没有生成单独的订单号，建议使用对应核销记录的核销订单号
    private String useOrderId;
    //使用优惠券时的订单金额，示例：10.00
    private String orderAmt;
    //使用优惠券时的用户支付金额，示例：8.88
    private String payAmt;
    //使用优惠券时所享受到的优惠金额，示例：1.12
    private String prftAmt;
    //行内核销门店编号
    private String storeId_1;
    //行外核销门店编号
    private String storeId_2;
    //优惠券状态发生变化的时间，yyyyMMddhh24miss
    private String useTm;
    //服务场景编码，特殊场景有效，在未约定规则的情况下请勿传
    private String scnId;









}
