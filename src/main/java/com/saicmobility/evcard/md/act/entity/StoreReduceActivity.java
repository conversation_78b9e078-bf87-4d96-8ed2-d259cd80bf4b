package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 门店参与的立减活动表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_store_reduce_activity")
public class StoreReduceActivity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @ApiModelProperty(value = "立减活动id")
    private Long activityId;

    @ApiModelProperty(value = "运营机构id")
    private String orgCode;

}
