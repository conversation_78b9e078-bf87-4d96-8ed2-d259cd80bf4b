package com.saicmobility.evcard.md.act.service.rest.entity.coupon;

import com.saicmobility.evcard.md.mdactservice.api.GetCouponViewReq;
import lombok.Data;

@Data
public class GetCouponViewRequest {
    private String authId;
    private String userCouponSeq;

    public static GetCouponViewRequest from(GetCouponViewReq req, String authId) {
        GetCouponViewRequest request = new GetCouponViewRequest();
        request.setUserCouponSeq(String.valueOf(req.getUserCouponSeq()));
        request.setAuthId(authId);
        return request;
    }
}
