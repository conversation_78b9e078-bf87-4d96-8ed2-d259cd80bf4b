package com.saicmobility.evcard.md.act.service;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.adapter.dto.MarketingCampaignInfoRequest;
import com.saicmobility.evcard.md.act.bo.market.CacheChannelActBo;
import com.saicmobility.evcard.md.act.bo.market.GetChannelActDetailBo;
import com.saicmobility.evcard.md.act.bo.market.GetChannelActDetailFromDiscountCodeBo;
import com.saicmobility.evcard.md.act.bo.market.ListChannelActBo;
import com.saicmobility.evcard.md.act.dto.market.AddChannelActDto;
import com.saicmobility.evcard.md.act.dto.market.DeleteChannelActDto;
import com.saicmobility.evcard.md.act.dto.market.ListChannelActDto;
import com.saicmobility.evcard.md.act.dto.market.UpdateChannelActDto;
import com.saicmobility.evcard.md.act.entity.ChannelActivity;

public interface MarketingModuleService {
    /**
     * 新增渠道活动
     *
     * @param dto
     * @return
     */
    void addChannelAct(AddChannelActDto dto) throws BusinessException;

    /**
     * 修改渠道活动
     *
     * @param dto
     * @return
     */
    void updateChannelAct(UpdateChannelActDto dto) throws BusinessException;

    /**
     * 查询渠道活动列表(分页查询)
     *
     * @param dto
     * @return
     * @throws BusinessException
     */
    ListChannelActBo listChannelAct(ListChannelActDto dto) throws BusinessException;

    /**
     * 查询渠道活动列表(缓存)
     *
     * @return
     * @throws BusinessException
     */
    CacheChannelActBo cacheChannelAct(String secondAppKey, String md5) throws BusinessException;

    /**
     * 根据id查询营销活动详情
     *
     * @param id
     * @return
     * @throws BusinessException
     */
    GetChannelActDetailBo getChannelActDetail(long id) throws BusinessException;

    /**
     * 根据id删除渠道活动
     *
     * @param dto
     * @throws BusinessException
     */
    void deleteChannelAct(DeleteChannelActDto dto) throws BusinessException;

    /**
     * 根据优惠码查询营销活动详情
     *
     * @param discountCode
     * @return
     */
    GetChannelActDetailFromDiscountCodeBo getChannelActDetailFromDiscountCode(String discountCode) throws BusinessException;

    /**
     * 将ChannelActivity转成擎路入参MarketingCampaignInfoRequest
     *
     * @param activity
     * @return
     */
    MarketingCampaignInfoRequest dataSync(ChannelActivity activity) throws BusinessException;
}
