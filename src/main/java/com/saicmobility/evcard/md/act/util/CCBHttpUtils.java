package com.saicmobility.evcard.md.act.util;

import com.alibaba.fastjson.JSON;
import com.saicmobility.evcard.md.act.config.CcbConfig;
import com.saicmobility.evcard.md.act.constant.Constants;
import com.saicmobility.evcard.md.act.dto.coupon.channel.ccb.CCBHttpResDto;
import com.saicmobility.evcard.md.act.entity.siac.CcbReconciliationRecord;
import com.saicmobility.evcard.md.act.enums.channelcoupon.CCBCodeEnum;
import com.saicmobility.evcard.md.act.service.coupon.ICcbReconciliationRecordService;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.rmi.ServerException;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.util.*;



/**
 * 食用说明
 *
 * 接收建行请求场景：
 *      接收请求：getBodyPlainText()
 *      返回应答：responseWrite()
 *
 * 主动请求建行场景：sendCcb()
 *
 */

@Component
@Slf4j
@DependsOn({"ccbConfig"})
public class CCBHttpUtils {
    public static CCBHttpUtils ccbHttpUtils;
    @Autowired
    private ICcbReconciliationRecordService ccbReconciliationRecordService;

    @PostConstruct
    public void init(){
        ccbHttpUtils = this;
        ccbHttpUtils.ccbReconciliationRecordService=this.ccbReconciliationRecordService;
    }


    private static final char[] ALPHABET = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};


    /**
     * 字符集编码
     */
    private static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;

    /**
     * AES加解密方式
     */
    private static final String ENCRYPT_METHOD_VALUE = "AES/ECB/PKCS5Padding";

    /**
     * 密文报文hash的计算方法
     */
    private static final String HASH_METHOD = "SHA-256";

    /**
     * 签名方法
     */
    private static final String SIGN_METHOD_VALUE = "SHA256withRSA";


    /**
     * HTTP报文头字段
     */
    private static final String VERSION = "Version";
    private static final String CHANNEL_ID = "Channel-Id";
    private static final String TRANS_CODE = "Trans-Code";
    private static final String TRACE_ID = "Trace-Id";
    private static final String REQUEST_TIME = "Request-Time";
    private static final String RESPOND_TIME = "Respond-Time";
    private static final String SIGN_TYPE = "Sign-Type";
    private static final String SIGN_METHOD = "Sign-Method";
    private static final String ENCRYPT_METHOD = "Encrypt-Method";
    private static final String SIGNATURE = "Signature";
    private static final String CONTENT_TYPE = "Content-Type";
    private static final String FORMAT = "Format";
    private static final String channelId = CcbConfig.CCB_CHANNEL_ID;
    public static final String ccbAesKey = CcbConfig.ccbAesKey;
    // 我方私钥
    public static final String ccbRsaPrivateKey = CcbConfig.ccbRsaPrivateKey;
    //建行侧公钥
    public static final String ccbRsaPublicKey = CcbConfig.ccbRsaPublicKey;

    /**
     *
     * @param url
     * @param transCode 交易码：P5STDR002
     * @param traceId         交易跟踪号，由发送方生成，需保证每次唯一， 跟踪日志，无业务含义
     * @param content         发送明文内容，三选一即可
     * @param filePath        发送文件路径入参，三选一即可
     * @param fis             发送文件入参流，三选一即可
     * @return                建行的响应信息(明文)
     * @return
     */
    public static CCBHttpResDto sendCcbMix(String url, String transCode, String traceId, String content,String filePath,InputStream fis,String batchNum){
        url = url.startsWith("http") ? url : CcbConfig.ccbBasePath.concat(url);
        traceId = StringUtils.isBlank(traceId) ? Trace.currentTraceId() : traceId;
        log.info("sendCcbMix param:url->{},transCode->{},traceId->{}",url,transCode,traceId);
        if (StringUtils.isNotBlank(content)){
            return sendCcb(url,channelId,transCode,traceId,ccbAesKey,ccbRsaPrivateKey,ccbRsaPublicKey,content,batchNum);
        }else if (fis != null){
            return sendCcbWithFile(url,channelId,transCode,traceId,ccbAesKey,ccbRsaPrivateKey,ccbRsaPublicKey,null,fis,batchNum);
        }else if (StringUtils.isNotBlank(filePath)){
            return sendCcbWithFile(url,channelId,transCode,traceId,ccbAesKey,ccbRsaPrivateKey,ccbRsaPublicKey,filePath,null,batchNum);
        }else {
            return null;
        }
    }

    /**
     * 接受建行请求时，完成报文的验签及解密，得到明文报文
     *
     * @param request        HttpServletRequest
     * @param AES_KEY        报文加解密密钥，按excel参数表中base64格式传入
     * @param RSA_PUBLIC_KEY 建行侧公钥，按excel参数表中base64格式传入
     * @return               明文报文
     * @throws Exception
     */
    public static String getBodyPlainText(HttpServletRequest request, String AES_KEY, String RSA_PUBLIC_KEY) throws Exception {
        ByteArrayInputStream bis = null;
        ByteArrayOutputStream bos = null;
        try {
            log.info("获取body=================开始");
            //1.检查Header
            log.info("getBodyPlainText.检查headers");

            Map<String, String> headers = verifyReqHeader(request);

            log.info("getBodyPlainText.验签并解密报文");
            InputStream inputStream = request.getInputStream();
            bos = new ByteArrayOutputStream();    //明文报文输出流
            //验签并解密报文
            boolean verfyBool = verifyAndDecrypt(AES_KEY, RSA_PUBLIC_KEY, headers, inputStream, bos);
            if (!verfyBool) {
                throw new ServerException("验签失败");
            }
            String text = bos.toString(DEFAULT_CHARSET.name());
            log.info("getBodyPlainText.明文：" + text);
            return text;
        } finally {
            log.info("获取body=================结束");
            if (null != bis) {
                bis.close();
            }
            if (null != bos) {
                bos.close();
            }
        }
    }

    /**
     * 给建行的请求做出响应，输入明文的响应报文，自动完成加密及签名
     *
     * @param request         HttpServletRequest
     * @param response        HttpServletResponse
     * @param AES_KEY         报文加解密密钥，按excel参数表中base64格式传入
     * @param RSA_PRIVATE_KEY 合作方私钥，按excel参数表中base64格式传入
     * @param content         明文响应信息
     * @throws Exception
     */
    public static void responseWrite(HttpServletRequest request, HttpServletResponse response, String AES_KEY, String RSA_PRIVATE_KEY, String content) throws Exception {
        ByteArrayInputStream bis = null;
        ByteArrayOutputStream bos = null;
        try {
            log.info("响应数据=================开始");
            String outboundStTimeStr = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());

            Map<String, String> headers = new HashMap<>();
            headers.put(VERSION, request.getHeader(VERSION));
            headers.put(CHANNEL_ID, request.getHeader(CHANNEL_ID));
            headers.put(TRANS_CODE, request.getHeader(TRANS_CODE));
            headers.put(TRACE_ID, request.getHeader(TRACE_ID));
            headers.put(RESPOND_TIME, outboundStTimeStr);
            headers.put(SIGN_TYPE, request.getHeader(SIGN_TYPE));
            headers.put(ENCRYPT_METHOD, request.getHeader(ENCRYPT_METHOD));
            headers.put(CONTENT_TYPE, request.getHeader(CONTENT_TYPE));
            headers.put(SIGN_METHOD, request.getHeader(SIGN_METHOD));
            headers.put(FORMAT, request.getHeader(FORMAT));
            for (String key : headers.keySet()) {
                response.setHeader(key, headers.get(key));
                log.info("responseWrite.headers key:" + key + ",value:" + headers.get(key));
            }

            bis = new ByteArrayInputStream(content.getBytes(DEFAULT_CHARSET));    //明文报文输入流
            bos = new ByteArrayOutputStream();    //密文报文输出流
            //加密报文并签名
            encryptAndSign(AES_KEY, RSA_PRIVATE_KEY, headers, bis, bos);
            String mText = headers.get(SIGNATURE);
            response.setHeader(SIGNATURE, mText);
            log.info("responseWrite.签名：" + mText);
            response.setContentLength(bos.toByteArray().length);
            response.getOutputStream().write(bos.toByteArray());
        } finally {
            log.info("响应数据=================结束");
            if (null != bis) {
                bis.close();
            }
            if (null != bos) {
                bos.close();
            }
        }
    }


    /**
     * 主动请求建行，如核销通知等场景
     *
     * @param url             请求地址
     * @param channelId       渠道标识，由建行分配，见excel参数表中的“权益供应商请求建行权益平台的渠道标识(Channel-Id)”
     * @param transCode       交易码，见接口文档说明
     * @param traceId         交易跟踪号，由发送方生成，需保证每次唯一， 跟踪日志，无业务含义
     * @param AES_KEY         报文加解密密钥，按excel参数表中base64格式传入
     * @param RSA_PRIVATE_KEY 合作方私钥，按excel参数表中base64格式传入
     * @param RSA_PUBLIC_KEY  建行侧公钥，按excel参数表中base64格式传入
     * @param content         请求信息(明文)
     * @return                建行的响应信息(明文)
     */
    public static CCBHttpResDto sendCcb(String url, String channelId, String transCode, String traceId, String AES_KEY, String RSA_PRIVATE_KEY, String RSA_PUBLIC_KEY, String content,String batchNum)  {
        log.info("请求ccb=================开始");
        PrintWriter out = null;
        BufferedReader in = null;
        OutputStream os = null;
        HttpURLConnection conn = null;
        ByteArrayInputStream bis = null;
        ByteArrayOutputStream bos = null;
        InputStream inputStream = null;
        //日志记录相关字段
        int logFlag = 1;
        String logMsg = "";

        Map<String, String> headers = setHeaders(channelId, transCode, traceId);
        try {
            // 打开和URL之间的连接
            URL realUrl = new URL(url);
            // 设置通用的请求属性
            conn = (HttpURLConnection) realUrl.openConnection();
            /**
             * 写入headers
             */
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                log.info("sendCcb.headers key:" + entry.getKey() + ",value:" + entry.getValue());
                conn.setRequestProperty(entry.getKey(), entry.getValue());
            }
            // 发送请求参数
            bis = new ByteArrayInputStream(content.getBytes(DEFAULT_CHARSET));    //明文报文输入流
            bos = new ByteArrayOutputStream();    //密文报文输出流
            //加密报文并签名
            encryptAndSign(AES_KEY, RSA_PRIVATE_KEY, headers, bis, bos);
            String mText = headers.get(SIGNATURE);
            log.info("sendCcb.签名：" + mText);
            conn.setRequestProperty(SIGNATURE, mText);

            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(10000);
            conn.setRequestMethod("POST");
            // 获取URLConnection对象对应的输出流
            os = conn.getOutputStream();
            os.write(bos.toByteArray());

            // 定义BufferedReader输入流来读取URL的响应
            int resultCode = conn.getResponseCode();
            log.info("sendCcb.响应状态：" + resultCode);

            //检查header
            Map<String, String> resHeader = verifyResHeader(conn);

            //400
            if (HttpURLConnection.HTTP_OK == resultCode) {

                inputStream = conn.getInputStream();
                bos = new ByteArrayOutputStream();

                //验签并解密报文 明文报文输出流
                boolean verfyBool = verifyAndDecrypt(AES_KEY, RSA_PUBLIC_KEY, resHeader, inputStream, bos);
                log.info("sendCcb.验签结果：" + verfyBool);

                if (!verfyBool) {
                    return new CCBHttpResDto("-1","响应信息验签失败",traceId);
                }
                String errMsg = new String(bos.toByteArray(),StandardCharsets.UTF_8);
                CCBHttpResDto ccbHttpResDto = JSON.parseObject(bos.toString(), CCBHttpResDto.class);

                //日志记录参数赋值
                logFlag = CCBCodeEnum.SUCCESS.getCcbCode().equals(ccbHttpResDto.getCode()) ? 0 : 1;
                logMsg = errMsg;

                log.info("sendCcb.响应明文：" + ccbHttpResDto.getMsg());
                ccbHttpResDto.setTraceId(traceId);
                return ccbHttpResDto;
            }
        } catch (Exception e) {
            //日志记录参数赋值
            logFlag = 1;
            logMsg = e.getMessage();

            log.error("sendCcb.traceId："+ traceId + " 未知异常：" + e.getMessage());
            e.printStackTrace();
            return new CCBHttpResDto("-1","sendCcb.traceId："+ traceId + " 未知异常："+ e.getMessage(),traceId);
        } finally {
            //推送日志记录
            CcbReconciliationRecord ccbReconciliationRecord = new CcbReconciliationRecord();
            ccbReconciliationRecord.setType(0);
            ccbReconciliationRecord.setBatchNum(batchNum);
            ccbReconciliationRecord.setReqJson(content);
            ccbReconciliationRecord.setRespJson(logMsg);
            ccbReconciliationRecord.setFlag(logFlag);
            ccbReconciliationRecord.setCreateOperName(CcbConfig.CCB_CHANNEL_ID);
            ccbReconciliationRecord.setUpdateOperName(CcbConfig.CCB_CHANNEL_ID);
            ccbHttpUtils.ccbReconciliationRecordService.saveOrUpdate(ccbReconciliationRecord);

            log.info("请求ccb=================结束");
            try {
                if (bos != null) {
                    bos.flush();
                    bos.close();
                }
                if (out != null) {
                    out.close();
                }
                if (os != null) {
                    os.close();
                }
                if (in != null) {
                    in.close();
                }
                if (bis != null) {
                    bis.close();
                }

                if (inputStream != null) {
                    inputStream.close();
                }
                if(null != conn){
                    conn.disconnect();
                }
            } catch (Exception ex) {
                log.info("sendCcb.流关闭异常：" + ex.getMessage());
                ex.printStackTrace();
                return new CCBHttpResDto("-1","sendCcb.流关闭异常：" + ex.getMessage(),traceId);
            }
        }
        return new CCBHttpResDto("-1","建行系统对接异常",traceId);
    }

    /**
     * 主动请求建行，如核销通知等场景
     *
     * @param url             请求地址（带参数）
     * @param channelId       渠道标识，由建行分配，见excel参数表中的“权益供应商请求建行权益平台的渠道标识(Channel-Id)”
     * @param transCode       交易码，见接口文档说明
     * @param traceId         交易跟踪号，由发送方生成，需保证每次唯一， 跟踪日志，无业务含义
     * @param AES_KEY         报文加解密密钥，按excel参数表中base64格式传入
     * @param RSA_PRIVATE_KEY 合作方私钥，按excel参数表中base64格式传入
     * @param RSA_PUBLIC_KEY  建行侧公钥，按excel参数表中base64格式传入
     * @param filePath        文件路径（明文）
     * @return                建行的响应信息(明文)
     */
    @DependsOn
    public static CCBHttpResDto sendCcbWithFile(String url,
                                         String channelId,
                                         String transCode,
                                         String traceId,
                                         String AES_KEY,
                                         String RSA_PRIVATE_KEY,
                                         String RSA_PUBLIC_KEY,
                                         String filePath,
                                         InputStream fis,
                                         String batchNum) {
        log.info("请求ccbFile=================开始");
        OutputStream os = null;
        HttpURLConnection conn = null;
        //FileInputStream fis = null;
        ByteArrayOutputStream bos = null;
        InputStream inputStream = null;
        Map<String, String> headers = setHeaders(channelId, transCode, traceId);
        headers.put(SIGN_TYPE, "HEADER");
        headers.put(FORMAT, "FILE");

        //日志记录相关字段
        int logFlag = 1;
        String logMsg = "";
        String reqJson = "";
        try {
            //签名(Sign-Type为HEADER时，仅请求头参与签名)
            String signStr = signStr(headers, "");
            log.info("sendCcb.encryptAndSign.signStr ：" + signStr);
            Signature signature = Signature.getInstance(SIGN_METHOD_VALUE);
            signature.initSign(getRsaPrivateKey(RSA_PRIVATE_KEY));
            signature.update(signStr.getBytes(DEFAULT_CHARSET));
            byte[] result = signature.sign();
            String sign = Base64.getEncoder().encodeToString(result);
            log.info("sendCcb.S.Signature ：" + sign);
            headers.put(SIGNATURE, Base64.getEncoder().encodeToString(result));
            // 打开和URL之间的连接
            URL realUrl = new URL(url);
            // 设置通用的请求属性
            conn = (HttpURLConnection) realUrl.openConnection();
            /**
             * 写入headers
             */
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                log.info("sendCcb.headers key:" + entry.getKey() + ",value:" + entry.getValue());
                conn.setRequestProperty(entry.getKey(), entry.getValue());
            }
            //通讯设置
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(10000);
            conn.setRequestMethod("POST");
            //加密传输
            fis = fis == null ? new FileInputStream(filePath) : fis;  //明文文件输入流
            os = conn.getOutputStream();          //密文报文输出流
            encrypt(AES_KEY, fis, os);

            // 定义BufferedReader输入流来读取URL的响应
            int resultCode = conn.getResponseCode();
            log.info("sendCcb.响应状态：" + resultCode);

            //检查header
            Map<String, String> resHeader = verifyResHeader(conn);

            //200
            if (HttpURLConnection.HTTP_OK == resultCode) {

                inputStream = conn.getInputStream();
                bos = new ByteArrayOutputStream();

                //验签并解密报文 明文报文输出流
                boolean verfyBool = verifyAndDecrypt(AES_KEY, RSA_PUBLIC_KEY, resHeader, inputStream, bos);
                log.info("sendCcb.验签结果：" + verfyBool);

                if (!verfyBool) {
                    return new CCBHttpResDto("-1","响应信息验签失败",traceId);
                }
                String text = new String(bos.toByteArray(),StandardCharsets.UTF_8);
                CCBHttpResDto ccbHttpResDto = JSON.parseObject(bos.toString(), CCBHttpResDto.class);
                log.info("sendCcb.响应明文：" + text);

                //日志记录参数赋值
                logFlag = CCBCodeEnum.SUCCESS.getCcbCode().equals(ccbHttpResDto.getCode()) ? 0 : 1;
                logMsg = text;
                reqJson = "";
                ccbHttpResDto.setTraceId(traceId);
                return ccbHttpResDto;
            }
        } catch (Exception e) {
            log.error("sendCcb.traceId："+ traceId + " 未知异常：" + e.getMessage());
            logFlag = 1;
            logMsg = e.getMessage();
            e.printStackTrace();
            return new CCBHttpResDto("-1","sendCcb.traceId："+ traceId + " 未知异常：" + e.getMessage(),traceId);
        } finally {
            log.info("请求ccb=================结束");
            //推送日志记录
            CcbReconciliationRecord ccbReconciliationRecord = new CcbReconciliationRecord();
            ccbReconciliationRecord.setType(0);
            ccbReconciliationRecord.setBatchNum(batchNum);
            ccbReconciliationRecord.setReqJson(reqJson);
            ccbReconciliationRecord.setRespJson(logMsg);
            ccbReconciliationRecord.setFlag(logFlag);
            ccbReconciliationRecord.setCreateOperName(CcbConfig.CCB_CHANNEL_ID);
            ccbReconciliationRecord.setUpdateOperName(CcbConfig.CCB_CHANNEL_ID);
            ccbHttpUtils.ccbReconciliationRecordService.saveOrUpdate(ccbReconciliationRecord);

            try {
                if (bos != null) {
                    bos.flush();
                    bos.close();
                }
                if (os != null) {
                    os.close();
                }
                if (fis != null) {
                    fis.close();
                }
                if (null != conn) {
                    conn.disconnect();
                }

                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception ex) {
                log.info("sendCcb.流关闭异常：" + ex.getMessage());
                ex.printStackTrace();
                return new CCBHttpResDto("-1","sendCcb.流关闭异常：" + ex.getMessage(),traceId);
            }
        }
        return new CCBHttpResDto("-1","建行系统对接异常",traceId);
    }

    /**
     * 组装  headers
     *
     * @param channelId 渠道标识，用以区分不同的接入平台。
     * @param transCode 交易码
     * @param traceId   交易跟踪号，由发送方生成，需保证每次唯一， 跟踪日志，无业务含义
     * @return
     */
    private static Map<String, String> setHeaders(String channelId, String transCode, String traceId) {
        Map<String, String> headersMap = new HashMap<>();
        headersMap.put(VERSION, "2.0");
        headersMap.put(CONTENT_TYPE, "application/octet-stream;charset=UTF-8");
        headersMap.put(CHANNEL_ID, channelId);
        headersMap.put(TRANS_CODE, transCode);
        headersMap.put(TRACE_ID, traceId);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String requestTime = sdf.format(new Date());
        headersMap.put(REQUEST_TIME, requestTime);
        headersMap.put(ENCRYPT_METHOD, "AES/ECB/PKCS5Padding");
        headersMap.put(SIGN_METHOD, "SHA256withRSA");
        headersMap.put(SIGN_TYPE, "STANDARD");
        headersMap.put(SIGNATURE, "signature");
        headersMap.put(FORMAT, "JSON");
        return headersMap;
    }


    /**
     * 检查请求 HTTP报文响应头信息
     *
     * @param conn
     * @return
     * @throws ServerException
     */
    private static Map<String, String> verifyResHeader(HttpURLConnection conn) throws ServerException {
        Map<String, List<String>> map = conn.getHeaderFields();
        for (String key : map.keySet()) {
            String value = conn.getHeaderField(key);
            log.info("verifyResHeader. " + key + "--->" + value);
        }

        String version = conn.getHeaderField(VERSION);
        if (null == version || "".equals(version)) {
            throw new ServerException("获取Header版本号为空");
        }
        String contentType = conn.getHeaderField(CONTENT_TYPE);
        if (null == contentType || "".equals(contentType)) {
            throw new ServerException("获取Header数据类型为空");
        }
        String channelId = conn.getHeaderField(CHANNEL_ID);
        if (null == channelId || "".equals(channelId)) {
            throw new ServerException("获取Header渠道标识为空");
        }
        String transCode = conn.getHeaderField(TRANS_CODE);
        if (null == transCode || "".equals(transCode)) {
            throw new ServerException("获取Header交易码为空");
        }
        String traceId = conn.getHeaderField(TRACE_ID);
        if (null == traceId || "".equals(traceId)) {
            throw new ServerException("获取Header交易跟踪号为空");
        }
        String respondTime = conn.getHeaderField(RESPOND_TIME);
        if (null == respondTime || "".equals(respondTime)) {
            throw new ServerException("获取Header响应时间为空");
        }
        String encryptMethod = conn.getHeaderField(ENCRYPT_METHOD);
        if (null == encryptMethod || "".equals(encryptMethod)) {
            throw new ServerException("获取HeaderAES加解密方式为空");
        }
        String signMethod = conn.getHeaderField(SIGN_METHOD);
        if (null == signMethod || "".equals(signMethod)) {
            throw new ServerException("获取Header签名方法为空");
        }
        String signType = conn.getHeaderField(SIGN_TYPE);
        if (null == signType || "".equals(signType)) {
            throw new ServerException("获取Header签名类型为空");
        }
        String format = conn.getHeaderField(FORMAT);
        if (null == format || "".equals(format)) {
            throw new ServerException("获取Header报文的数据格式为空");
        }

        String signature = conn.getHeaderField(SIGNATURE);
        if (null == signature || "".equals(signature)) {
            throw new ServerException("获取签名字符为空");
        }

        Map<String, String> headers = new HashMap<>();
        headers.put(VERSION, version);
        headers.put(CHANNEL_ID, channelId);
        headers.put(TRANS_CODE, transCode);
        headers.put(TRACE_ID, traceId);
        headers.put(RESPOND_TIME, respondTime);
        headers.put(SIGN_TYPE, signType);
        headers.put(SIGNATURE, signature);
        return headers;

    }


    /**
     * 检查请求 HTTP报文头字段
     *
     * @param request
     * @return
     * @throws ServerException
     */
    private static Map<String, String> verifyReqHeader(HttpServletRequest request) throws ServerException {

        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            String value = request.getHeader(name);
            log.info("verifyReqHeader. " + name + "--->" + value);
        }

        String version = request.getHeader(VERSION);
        if (null == version || "".equals(version)) {
            throw new ServerException("获取Header版本号为空");
        }
        String contentType = request.getHeader(CONTENT_TYPE);
        if (null == contentType || "".equals(contentType)) {
            throw new ServerException("获取Header数据类型为空");
        }
        String channelId = request.getHeader(CHANNEL_ID);
        if (null == channelId || "".equals(channelId)) {
            throw new ServerException("获取Header渠道标识为空");
        }
        String transCode = request.getHeader(TRANS_CODE);
        if (null == transCode || "".equals(transCode)) {
            throw new ServerException("获取Header交易码为空");
        }
        String traceId = request.getHeader(TRACE_ID);
        if (null == traceId || "".equals(traceId)) {
            throw new ServerException("获取Header交易跟踪号为空");
        }
        String qequestTime = request.getHeader(REQUEST_TIME);
        if (null == qequestTime || "".equals(qequestTime)) {
            throw new ServerException("获取Header请求时间为空");
        }
        String encryptMethod = request.getHeader(ENCRYPT_METHOD);
        if (null == encryptMethod || "".equals(encryptMethod)) {
            throw new ServerException("获取HeaderAES加解密方式为空");
        }
        String signMethod = request.getHeader(SIGN_METHOD);
        if (null == signMethod || "".equals(signMethod)) {
            throw new ServerException("获取Header签名方法为空");
        }
        String signType = request.getHeader(SIGN_TYPE);
        if (null == signType || "".equals(signType)) {
            throw new ServerException("获取Header签名类型为空");
        }
        String format = request.getHeader(FORMAT);
        if (null == format || "".equals(format)) {
            throw new ServerException("获取Header报文的数据格式为空");
        }

        String signature = request.getHeader(SIGNATURE);
        if (null == signature || "".equals(signature)) {
            throw new ServerException("获取Header签名字符为空");
        }

        Map<String, String> headers = new HashMap<>();
        headers.put(VERSION, version);
        headers.put(CHANNEL_ID, channelId);
        headers.put(TRANS_CODE, transCode);
        headers.put(TRACE_ID, traceId);
        headers.put(REQUEST_TIME, qequestTime);
        headers.put(SIGN_TYPE, signType);
        headers.put(SIGNATURE, signature);
        return headers;
    }


    /**
     * 验签并解密报文
     *
     * @param headers HTTP报文头，请提前检验各必须字段是否存在
     * @param is      密文报文输入流
     * @param os      明文报文输出流
     * @return 是否验签通过
     */
    private static boolean verifyAndDecrypt(String AES_KEY, String RSA_PUBLIC_KEY, Map<String, String> headers, InputStream is, OutputStream os)
            throws NoSuchPaddingException, NoSuchAlgorithmException, IOException, BadPaddingException,
            IllegalBlockSizeException, InvalidKeyException, SignatureException, InvalidKeySpecException {
        String hash = decrypt(AES_KEY, is, os);
        log.info("verifyAndDecrypt.hash:" + hash);
        String signStr = signStr(headers, hash);
        log.info("verifyAndDecrypt.signStr:" + signStr);
        Signature signature = Signature.getInstance(SIGN_METHOD_VALUE);
        signature.initVerify(getRsaPublicKey(RSA_PUBLIC_KEY));
        signature.update(signStr.getBytes(DEFAULT_CHARSET));
        return signature.verify(Base64.getDecoder().decode(headers.get(SIGNATURE)));
    }

    /**
     * 加密报文并验签
     *
     * @param headers HTTP报文头，请提前检验各必须字段是否存在，得到的签名会直接放到这里面
     * @param is      明文报文输入流
     * @param os      密文报文输出流
     */
    private static void encryptAndSign(String AES_KEY, String RSA_PRIVATE_KEY, Map<String, String> headers, InputStream is, OutputStream os)
            throws NoSuchAlgorithmException, IllegalBlockSizeException, InvalidKeyException,
            NoSuchPaddingException, BadPaddingException, IOException, SignatureException, InvalidKeySpecException {

        String hash = encrypt(AES_KEY, is, os);
        log.info("encryptAndSign.hash ：" + hash);
        String signStr = signStr(headers, hash);
        log.info("encryptAndSign.signStr ：" + signStr);
        Signature signature = Signature.getInstance(SIGN_METHOD_VALUE);
        signature.initSign(getRsaPrivateKey(RSA_PRIVATE_KEY));
        signature.update(signStr.getBytes(DEFAULT_CHARSET));
        byte[] result = signature.sign();
        String sign = Base64.getEncoder().encodeToString(result);
        log.info("S.Signature :" + sign);
        headers.put(SIGNATURE, Base64.getEncoder().encodeToString(result));
    }

    /**
     * 获取AES加解密实例
     *
     * @param mode 加解密处理模式，Cipher.ENCRYPT_MODE(1)-加密，Cipher.DECRYPT_MODE(2)-解密
     */
    private static Cipher getAesCipher(String AES_KEY, int mode)
            throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException {
        Cipher cipher = Cipher.getInstance(ENCRYPT_METHOD_VALUE);
        SecretKeySpec keySpec = new SecretKeySpec(Base64.getDecoder().decode(AES_KEY), "AES");
        cipher.init(mode, keySpec);
        return cipher;
    }


    /**
     * 获取RSA私钥实例
     */
    private static PrivateKey getRsaPrivateKey(String RSA_PRIVATE_KEY) throws NoSuchAlgorithmException, InvalidKeySpecException {
        return KeyFactory.getInstance("RSA").generatePrivate(
                new PKCS8EncodedKeySpec(Base64.getDecoder().decode(RSA_PRIVATE_KEY)));
    }

    /**
     * 获取RSA公钥实例
     */
    private static PublicKey getRsaPublicKey(String RSA_PUBLIC_KEY) throws NoSuchAlgorithmException, InvalidKeySpecException {
        return KeyFactory.getInstance("RSA").generatePublic(
                new X509EncodedKeySpec(Base64.getDecoder().decode(RSA_PUBLIC_KEY)));
    }


    /**
     * 解密报文，并输出密文的hash值
     *
     * @param is 密文报文输入流
     * @param os 明文报文输出流
     * @return 返回密文的hash值，用做验签
     */
    private static String decrypt(String AES_KEY, InputStream is, OutputStream os)
            throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException,
            IOException, BadPaddingException, IllegalBlockSizeException {
        //解密及hash准备
        Cipher cipher = getAesCipher(AES_KEY, Cipher.DECRYPT_MODE);
        MessageDigest digest = MessageDigest.getInstance(HASH_METHOD);
        //读报文并处理
        byte[] buffer = new byte[1024];
        int len;
        while ((len = is.read(buffer)) > 0) {
            //计算hash
            digest.update(buffer, 0, len);
            //解密
            byte[] bytes = cipher.update(buffer, 0, len);
            //输出到流
            os.write(bytes);
        }
        //完成最终报文的解密
        byte[] bytes = cipher.doFinal();
        log.info("decrypt bytes: " + Arrays.toString(bytes));
        os.write(bytes);
        //输出hash
        return byte2HexStr(digest.digest());
    }


    /**
     * 加密报文，并输出密文的hash值
     *
     * @param is 明文报文输入流
     * @param os 密文报文输出流
     * @return 返回密文的hash值，用做签名
     */
    private static String encrypt(String AES_KEY, InputStream is, OutputStream os)
            throws BadPaddingException, IllegalBlockSizeException, IOException,
            NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException {
        //解密及hash准备
        Cipher cipher = getAesCipher(AES_KEY, Cipher.ENCRYPT_MODE);
        MessageDigest digest = MessageDigest.getInstance(HASH_METHOD);
        //读报文并处理
        byte[] buffer = new byte[1024];
        int len;
        while ((len = is.read(buffer)) > 0) {
            //加密
            byte[] bytes = cipher.update(buffer, 0, len);
            //计算hash
            digest.update(bytes);
            //输出到流
            os.write(bytes);
        }
        //完成最终报文的加密
        byte[] bytes = cipher.doFinal();




        os.write(bytes);
        //输出hash
        digest.update(bytes);
        return byte2HexStr(digest.digest());
    }


    /**
     * 请求 组装待签名/验签字符串
     *
     * @param headers HTTP报文头，请提前检验各必须字段是否存在
     * @param hash    报文密文的hash，对于HEADER方式的签名，该参数可为空
     * @return 返回待签名/验签字符串
     */
    private static String signStr(Map<String, String> headers, String hash) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(VERSION).append("=").append(headers.get(VERSION)).append("&")
                .append(CHANNEL_ID).append("=").append(headers.get(CHANNEL_ID)).append("&")
                .append(TRANS_CODE).append("=").append(headers.get(TRANS_CODE)).append("&")
                .append(TRACE_ID).append("=").append(headers.get(TRACE_ID));
        if (null != headers.get(REQUEST_TIME) && !"".equals(headers.get(REQUEST_TIME))) {
            stringBuilder.append("&").append(REQUEST_TIME).append("=").append(headers.get(REQUEST_TIME));
        } else {
            stringBuilder.append("&").append(RESPOND_TIME).append("=").append(headers.get(RESPOND_TIME));
        }
        if ("STANDARD".equals(headers.get("Sign-Type"))) {
            stringBuilder.append("&Hash=").append(hash);
        }
        return stringBuilder.toString();
    }


    /**
     * byte数组转换为Hex格式字符串
     * {0x31, 0x32, 0x33} -> "313233"
     *
     * @param packet 待处理的byte数组
     * @return 返回转换后的Hex格式字符串
     */
    private static String byte2HexStr(byte[] packet) {
        StringBuilder chars = new StringBuilder(16);

        for (byte b : packet) {
            int highBits = (b & 240) >> 4;
            int lowBits = b & 15;
            chars.append(ALPHABET[highBits]);
            chars.append(ALPHABET[lowBits]);
        }

        return chars.toString();
    }


    /**
     * 测试请求ccb
     *
     * @param args
     */
    public static void main(String[] args) {


        //解密及hash准备


        String SUPPLIER_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC3+GCpWtZH+24ai9PT2NhOPOyp22W21o1fnfB8APT10Ck966hWljlDS/E8GmYHGg2Kk6U+ph6lkb8vd5SCt/x6+WmCTPbNnC5PuvaMus2CyWJmkH2ZSbyFrqNdV4pUfyG5/zwqAnIeVajVNRw4kpIC1ccJbeoKA/8RkVqdrOR4ZwIDAQAB";
        String content = "sywnXNJ6jSISISvaa88eHFLuEwc/FKGDJT/exeSm3Mfe9Qm99hFrJW3H+jsI42bk8i55PQfH3NL8Fk9oPpExtiGXkMTBEDTym4a6BXEjowi3o2kCnoi43sWo7HzluFCC4eLcVr8tIkhXH7cGsTI7muZT29pFllT1w+e52/MVKqI=";
        String signStr = "Version=0.6&Channel-Id=xxxx&Trans-Code=P5STDS002&Trace-Id=1736492983754&Request-Time=20250110150943&Hash=3C82DE9717845968F0FB3B2B6762F504C0FA25FF45890B42C7A1CE2A917AC56C";
        try {
            Signature signature = Signature.getInstance(SIGN_METHOD_VALUE);
            signature.initVerify(getRsaPublicKey(SUPPLIER_PUBLIC_KEY));
            signature.update(signStr.getBytes(DEFAULT_CHARSET));
            boolean verify = signature.verify(Base64.getDecoder().decode(content));
            System.out.println(verify);

        } catch (Exception e) {

        }
        //模拟ccb发送接收
        ccbSend();


        //========================================获取ccb请求数据
       /*try {
            //%%%%%%%%%%%%%%%%%%%%%%%%%%%%收到数据
            //ccb公钥 参数表里面获取
           String CCB_PUBLIC_KEY ="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDFtvD99Ff1bL7MI1YKpEhwwYXQyp/BENE6MRoPHrhnlljE37QWS4DegyhBT9ifFPkAodc+mNL9aoQ+67Q6qfQfZEhD5+ZXWfYEWCTYThgoVQtwDbnGwgGizn5N/TJFzJ5k8RYa6So5zc+pCXGaiZfGkf1PAiZYoNGdp5PMdoZJ0wIDAQAB";

           String AES_KEY = "N0pmsMKL1zo0/0078d1Inw==";//参数表里面获取

            String strBody = SdkUtils.getBodyPlainText(request, AES_KEY, CCB_PUBLIC_KEY);
            System.out.println("收到信息:" + strBody);

            //%%%%%%%%%%%%%%%%%%%%%%%%%%%%响应数据
            //供应商私钥
            String SUPPLIER_PRIVATE_KEY ="MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALf4YKla1kf7bhqL09PY2E487KnbZbbWjV+d8HwA9PXQKT3rqFaWOUNL8TwaZgcaDYqTpT6mHqWRvy93lIK3/Hr5aYJM9s2cLk+69oy6zYLJYmaQfZlJvIWuo11XilR/Ibn/PCoCch5VqNU1HDiSkgLVxwlt6goD/xGRWp2s5HhnAgMBAAECgYEAjlijykZb6OR15BpvM8rkS10+rYryH8/RIbveOMSwxYZkcuHlpeGkuxqGtL+kVDhnY+MsB9aXffrQ2JSJF7/unZRwdCYF7ihD5D0/fwF6LgLFAVbVlam1u0OGh9Sk0xuUKwhhc5afWVANUU6c3F2Z5kDri2NvXeT9cx+g3GBhiIkCQQDeY1VYFB4/riKfAqZ97WPhXWZf5YAeB0jco4E+RAWSShM0sCvbPE8Fi5815/idWmIPzhVjbZnCj3XNv6CaFmzFAkEA08aT6Vl2khwLHhWhr35kHYMvbwXwbwBWgV4CyKnPCWZd+aQb9Whs8AJjC1MSDOIHIX265J03V9GjUjfXvog7OwI/H0XYuooZttrGA5Zb01mOsiJwQwwC+DYOCPj1M8xc/DVuffpIMIxKkH0R5oK4c2hCYfaZC2JePP3vkv9flkuRAkEAvZN3Fx4h+XunLCA7I+ll39FupdlQOV25EofAwDbaglSj7XTOHmqDknrghOOxNd0kewOdSUe/ohqlNFWPCR3UvwJAWwaaIBiTN2mzi8MeF2IOcdqeE2itcJv3XJPopOW2Aq08f8Y3PhXrb7rO2reHULAWEghrYoKO+ToPgcBR8yLPFQ==";
            String content="响应数据";
            SdkUtils.responseWrite( request,  response,  AES_KEY,  SUPPLIER_PRIVATE_KEY,  content);

        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }*/
        //========================================获取ccb请求数据

        //模拟发送ccb
        //sendCcb();




    }
    /**
     * 模拟ccb发送
     */
    private static void ccbSend() {
        // String url = "http://test.rzltech.com/salemall/zjccbcoupon/send";
        String url="http://127.0.0.1:32539/mdpartner/act/ccb/test";
        String channelId = "xxxx"; //参数表里面获取
        String transCode = "P5STDS002"; //技术文档
        String traceId = System.currentTimeMillis()+"";//"交易跟踪号，由发送方生成，需保证每次唯一， 跟踪日志，无业务含义";
        String AES_KEY = "N0pmsMKL1zo0/0078d1Inw==";//参数表里面获取
        //自己生成一套
        //ccb私钥
        String CCB_PRIVATE_KEY ="MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMW28P30V/VsvswjVgqkSHDBhdDKn8EQ0ToxGg8euGeWWMTftBZLgN6DKEFP2J8U+QCh1z6Y0v1qhD7rtDqp9B9kSEPn5ldZ9gRYJNhOGChVC3ANucbCAaLOfk39MkXMnmTxFhrpKjnNz6kJcZqJl8aR/U8CJlig0Z2nk8x2hknTAgMBAAECgYAl6Nvwdadf4Yf78FKRG8rgA9s6m39iL21OsReECqnlAsjmhtYq6DM/dvqC+2JXrlfILT/uOrNJ7+CHh/44uVmbTQCNA5vQV385O2mM/4WuD1tJwz81Ra1ndXooJUSkZ1+BPPZfC04NDomqhxEJ7vFU0l19KIt0scTiTGq+627z4QJBAOQeA3XgSQ2BFoVDQ5z2uLmVK2BR9P4vB/+KHqSGn5+pX2PTW1e+QTgYPuTmuPY9KR98y2ZgdBBB3rXHYvOmlgkCQQDd4Zpyk8R7pe4jAqhEsdgjMkh0qz3X0ItZIKUxNqL02nGOkpE/a7gdTPeifmSSGxk8gjwumuG6dLCd18Czm3f7AkBHi+5T60ueU62HkIhFU9CuTE31gN8RVhjxGhXwTlUEU7AadDEC/SYhfOFFE+RRPu+F87Mr4falHrPGWcn8V3RZAkEA2Exl0+xZTnCjhV3By7XTjfwmxhVpZEnYjjpUz3UDxs5shSA8mCbahTXS8oVkZUolf+c14/j4tFutETKSmISmoQJAQI4cZ/QSRBcafAN4ix3EpKzRN1P4zj+6VXdpUTOBB/xyeI2dCTfak8RAu9BI+zAgkVjM3jLXCg818sGC9io+Lw==";
        //供应商公钥
        String SUPPLIER_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC3+GCpWtZH+24ai9PT2NhOPOyp22W21o1fnfB8APT10Ck966hWljlDS/E8GmYHGg2Kk6U+ph6lkb8vd5SCt/x6+WmCTPbNnC5PuvaMus2CyWJmkH2ZSbyFrqNdV4pUfyG5/zwqAnIeVajVNRw4kpIC1ccJbeoKA/8RkVqdrOR4ZwIDAQAB";
        //String content = "{\"extrBsnData\":\"{\\\"WeCht_openid_Cd\\\": \\\"oYVMK0RpeVXcltOkLhEiCSHH6uBs\\\",\\\"WeCht_appid_Cd\\\":\\\"01\\\",\\\"Crd_TpCd\\\": \\\"01\\\" }\",\"dccpAvyId\":\"YHQ2021013186011\",\"productId\":\"CCB_WX_1\",\"orderId\":\"rv101685515075427558013\",\"num\":1,\"userId\":\"18457413686\"}";
        String content = "{\n" +
                "    \"source\": \"youmi\",\n" +
                "    \"appid\": \"com.baosight.CarSharing\",\n" +
                "    \"idfa\": \"0B13FB92-2AE5-4503-8B49-F8D6061CB9FE\"\n" +
                "}";
        CCBHttpResDto ccbHttpResDto = sendCcb(url, channelId, transCode, traceId, AES_KEY, CCB_PRIVATE_KEY, SUPPLIER_PUBLIC_KEY, content,null);
        System.out.println("resText:" + ccbHttpResDto.getMsg());
    }

    /**
     * 模拟发送ccb
     */
    private static void sendCcb(){
        String url = "请求地址";
        String channelId = "渠道标识";
        String transCode = "交易码";
        String traceId = "交易跟踪号，由发送方生成，需保证每次唯一， 跟踪日志，无业务含义";
        String AES_KEY = "Aes密钥Base64";

        String RSA_PRIVATE_KEY = "供应商私钥";
        String RSA_PUBLIC_KEY = "ccb公钥";
        String content = "请求信息Json字符串";

        CCBHttpResDto ccbHttpResDto = sendCcb(url, channelId, transCode, traceId, AES_KEY, RSA_PRIVATE_KEY, RSA_PUBLIC_KEY, content,null);
        System.out.println("resText:" + ccbHttpResDto.getMsg());
    }
}