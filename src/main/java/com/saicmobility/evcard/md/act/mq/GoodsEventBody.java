package com.saicmobility.evcard.md.act.mq;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/9
 */
@Data
public class GoodsEventBody {
    private long storeId; // 门店id
    private String operOrgCode; // 运营机构id
    private int action; // 1-门店渠道变更、2-车型变更、3-发布车型变更
    private List<JSONObject> channelList; // 渠道列表
    private List<JSONObject> vehicleModelInfoList; // 车型列表
    private String extParam; // 扩展字段
    private boolean changeChannel;//渠道变化
}
