package com.saicmobility.evcard.md.act.mapper.siac;

import com.saicmobility.evcard.md.act.domain.coupon.CouponConditionDto;
import com.saicmobility.evcard.md.act.domain.coupon.UsedCouponDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserCouponListMapper {


    /**
     * 冻结/解冻优惠券
     */
    int frozenCoupon(@Param("userCouponSeq")Long userCouponSeq, @Param("frozen")int frozen
            , @Param("orderNo")String orderNo, @Param("updatedTime")String updateTime);

    /**
     * 查询会员优惠券
     */
    CouponConditionDto findCouponByAuthIdAndUserCouponSeq(@Param("authId") String authId, @Param("userCouponSeq") Long userCouponSeq);


    /**
     * 查询会员的所有优惠券.<br>
     *
     * @param authId
     * @param remark
     * @param status  0:未使用 1:已使用  2：已经作废  3:已过期  4：全部
     * @param expiresDate 过期日期 默认查询一个月内过期的优惠券
     * @return
     */
    List<CouponConditionDto> findUserCouponsByRemark(@Param("authId")String authId, @Param("remark") String remark,
                                                                                        @Param("status") Integer status, @Param("expiresDate") String expiresDate);

    /**
     * 根据兑换码查询优惠券信息.<br>
     * @param couponCode
     * @return
     */
    CouponConditionDto findByCouponCode(@Param("couponCode")String couponCode);

    List<CouponConditionDto> selectListByIds(@Param("userCouponSeqs")List<Long> userCouponSeqs);

    /**
     * 调整优惠券状态
     */
    int updateCouponStatus(@Param("userCouponSeq")Long userCouponSeq, @Param("newStatus")int newStatus
            , @Param("oldStatus")int oldStatus, @Param("updatedTime")String updateTime, @Param("updatedUser")String updatedUser);

    int offerCoupon(Long userCouponSeq);

    /**
     * 优惠券兑换码 过期
     *  修改成 3
     * @param userCouponSeq
     * @param updateTime
     * @param updatedUser
     * @return
     */
    int expireCouponCodeStatus(@Param("userCouponSeq")Long userCouponSeq, @Param("oldCouponCodeStatus")int oldCouponCodeStatus, @Param("updatedTime")String updateTime, @Param("updatedUser")String updatedUser);


    /**
     * 作废 优惠券兑换码
     *  修改成 2
     *  也 把优惠券状态 改成2
     * @param userCouponSeq
     * @param updateTime
     * @param updatedUser
     * @return
     */
    int deprecatedCouponCodeStatus(@Param("userCouponSeq")Long userCouponSeq, @Param("oldCouponCodeStatus")int oldCouponCodeStatus, @Param("updatedTime")String updateTime, @Param("updatedUser")String updatedUser);

    /**
     * 优惠券  过期
     * 修改成 3
     * @param userCouponSeq
     * @param updateTime
     * @param updatedUser
     * @return
     */
    int expireCouponStatus(@Param("userCouponSeq")Long userCouponSeq, @Param("oldStatus")int oldStatus, @Param("updatedTime")String updateTime, @Param("updatedUser")String updatedUser);

    /**
     * 查询未发放兑换码列表
     */
    List<CouponConditionDto> queryAllUnissuedCoupon(@Param("couponSeq") long couponSeq, @Param("actionId") String actionId, @Param("couponOrigin") String couponOrigin);

    /**
     * 更新优惠券发放状态
     */
    int updateCouponOfferFlag(@Param("userCouponSeq")Long userCouponSeq);
}
