package com.saicmobility.evcard.md.act.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * @author: xialei
 */
@Configuration
public class IssDataSourceConfiguration {

    public static final String MAPPER_LOCATION = "classpath:mapper/iss/*.xml";


    @Autowired
    MybatisPlusInterceptor mybatisPlusInterceptor;

    @Bean("issDataSource")
    @ConfigurationProperties("spring.datasource.druid.iss")
    public DataSource issDataSource(){
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("issTransactionManager")
    public DataSourceTransactionManager issTransactionManager() {
        return new DataSourceTransactionManager(issDataSource());
    }

    @Bean("issSqlSessionFactory")
    public SqlSessionFactory issSqlSessionFactory(@Qualifier("issDataSource") DataSource dataSource) throws Exception {
        // 使用到了mybatis plus , 所以这里使用的是mybatis plus组件
        MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION));
        sqlSessionFactoryBean.setPlugins(mybatisPlusInterceptor);
        return sqlSessionFactoryBean.getObject();
    }

    @Bean("issSqlSessionTemplate")
    public SqlSessionTemplate issSqlSessionTemplate(@Qualifier("issDataSource") DataSource dataSource) throws Exception {
        return new SqlSessionTemplate(issSqlSessionFactory(dataSource));
    }

}
