package com.saicmobility.evcard.md.act.service;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.entity.ChannelActivity;

import java.io.FileNotFoundException;
import java.util.List;

public interface ExternalSystemFacade {


    /**
     * 擎路的营销活动同步
     */
    void notifyChannelActivitySync(ChannelActivity activity, boolean isRetry) throws BusinessException;


    /**
     * 擎路的营销活动批量同步
     */
    void batchNotifyChannelActivitySync(List<ChannelActivity> list);

    /**
     * 建行优惠券状态回调
     * @param jsonStr
     */
    void ccbCouponStatusCallBackTask(String jsonStr) throws BusinessException;
    /**
     * 建行优惠券状态回调
     * @param jsonStr
     */
    void ccbPushReconciliationFileTask(String jsonStr) throws BusinessException, FileNotFoundException;
}
