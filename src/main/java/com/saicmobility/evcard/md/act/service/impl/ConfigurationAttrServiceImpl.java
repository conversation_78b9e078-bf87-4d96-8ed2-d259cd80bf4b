package com.saicmobility.evcard.md.act.service.impl;

import com.alibaba.fastjson.JSON;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.exception.BusinessRuntimeException;
import com.saicmobility.evcard.md.act.annotation.ConfigurationAttribute;
import com.saicmobility.evcard.md.act.constant.SystemConst;
import com.saicmobility.evcard.md.act.entity.ConfigurationAttr;
import com.saicmobility.evcard.md.act.mapper.act.ConfigurationAttrMapper;
import com.saicmobility.evcard.md.act.service.ConfigurationAttrService;
import com.saicmobility.evcard.md.act.service.inner.BaseService;
import com.saicmobility.evcard.md.act.util.ConfigurationAttrParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @since 2022-04-06
 */
@Slf4j
@Service
public class ConfigurationAttrServiceImpl extends BaseService<ConfigurationAttrMapper, ConfigurationAttr> implements ConfigurationAttrService {

    @Override
    public void getAttrs(Integer type, Long configurationId, Object configurationObj) {
        if (configurationObj == null) {
            return;
        }
        List<ConfigurationAttr> configurationAttrList = lambdaQuery().eq(ConfigurationAttr::getType, type)
                .eq(ConfigurationAttr::getConfigurationId, configurationId)
                .eq(ConfigurationAttr::getIsDeleted, SystemConst.DATA_STATUS_TRUE)
                .list();
        if (CollectionUtils.isEmpty(configurationAttrList)) {
            return;
        }
        fillAttrs(configurationAttrList, configurationObj);
    }

    @Override
    public <T> T getAttrs(Integer type, Long configurationId, Class<T> configurationClass) {
        if (configurationClass == null) {
            return null;
        }
        try {
            T t = configurationClass.newInstance();
            getAttrs(type, configurationId, t);
            return t;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public <T> Map<Long, T> batchGetAttrs(Integer type, List<Long> configIds, Class<T> configurationClass) {
        if (CollectionUtils.isEmpty(configIds)) {
            return new HashMap<>(0);
        }
        if (configurationClass == null) {
            return new HashMap<>(0);
        }
        List<ConfigurationAttr> configurationAttrList = lambdaQuery()
                .eq(ConfigurationAttr::getType, type)
                .in(ConfigurationAttr::getConfigurationId, configIds)
                .eq(ConfigurationAttr::getIsDeleted, SystemConst.DATA_STATUS_TRUE)
                .list();
        if (CollectionUtils.isEmpty(configurationAttrList)) {
            return new HashMap<>(0);
        }
        Map<Long, List<ConfigurationAttr>> map = new HashMap<>();
        configurationAttrList.forEach(attr -> {
            List<ConfigurationAttr> attrList = map.computeIfAbsent(attr.getConfigurationId(), k -> new ArrayList<>());
            attrList.add(attr);
        });
        Map<Long, T> result = new HashMap<>(map.size());
        try {
            for (List<ConfigurationAttr> attrList : map.values()) {
                T attrObj = configurationClass.newInstance();
                fillAttrs(attrList, attrObj);
                result.put(attrList.get(0).getConfigurationId(), attrObj);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return new HashMap<>(0);
        }
        return result;
    }

    @Transactional
    @Override
    public boolean saveAttrs(Integer type, Long configurationId, Object configurationObj, UserDTO userDTO) {

        Field[] fields = configurationObj.getClass().getDeclaredFields();
        List<ConfigurationAttr> list = new ArrayList<>(fields.length);
        try {

            for (Field field : fields) {
                ConfigurationAttribute configurationAttribute = field.getAnnotation(ConfigurationAttribute.class);
                if (configurationAttribute == null) {
                    continue;
                }
                if (configurationAttribute.isConfigurationId()) {
                    continue;
                }
                PropertyDescriptor prop = new PropertyDescriptor((String) field.getName(), configurationObj.getClass());
                Object fieldValue = prop.getReadMethod().invoke(configurationObj);

                ConfigurationAttr attr = new ConfigurationAttr();
                attr.setType(type);
                attr.setConfigurationId(configurationId);
                if (StringUtils.isNotBlank(configurationAttribute.fieldName())) {
                    attr.setAttrCode(configurationAttribute.fieldName());
                } else {
                    attr.setAttrCode(field.getName());
                }
                if (configurationAttribute.isComplex()) {
                    attr.setComplexValue(JSON.toJSONString(fieldValue));
                } else {
                    attr.setSimpleValue(ConfigurationAttrParser.toString(fieldValue, false));
                }
                list.add(attr);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
        return saveBatch(list, userDTO, new Date());
    }

    @Override
    public boolean deleteAttrs(Integer type, Long configurationId, UserDTO userDTO) {
        return lambdaUpdate().set(ConfigurationAttr::getIsDeleted, SystemConst.DATA_STATUS_FALSE)
                .set(ConfigurationAttr::getUpdateOperId, userDTO.getId())
                .set(ConfigurationAttr::getUpdateOperName, userDTO.getUsername())
                .set(ConfigurationAttr::getUpdateTime, new Date())
                .eq(ConfigurationAttr::getType, type)
                .eq(ConfigurationAttr::getConfigurationId, configurationId)
                .update();
    }

    @Override
    @Transactional
    public boolean updateAttrs(Integer type, Long configurationId, Object configurationObj, UserDTO userDTO) {
        deleteAttrs(type, configurationId, userDTO);
        return saveAttrs(type, configurationId, configurationObj, userDTO);
    }

    private void fillAttrs(List<ConfigurationAttr> configAttrs, Object configObj) {
        Assert.notNull(configObj, "属性配置对象不允许为空");
        if (CollectionUtils.isEmpty(configAttrs)) {
            return;
        }
        Class<?> configurationClass = configObj.getClass();
        Map<String, ConfigurationAttr> attrMap = configAttrs.stream().collect(Collectors.toMap(ConfigurationAttr::getAttrCode, attr -> attr, (exist, replace) -> replace));
        try {
            Field[] declaredFields = configurationClass.getDeclaredFields();
            for (Field configurationField : declaredFields) {
                ConfigurationAttribute annotation = configurationField.getAnnotation(ConfigurationAttribute.class);
                if (annotation == null) {
                    log.debug("{}字段未配置注解，不进行解析", configurationField.getName());
                    continue;
                }
                if (annotation.isConfigurationId() && configurationField.getType().equals(Long.class)) {
                    Long configurationId = configAttrs.get(0).getConfigurationId();
                    PropertyDescriptor prop = new PropertyDescriptor((String) configurationField.getName(), configObj.getClass());
                    prop.getWriteMethod().invoke(configObj, configurationId);
                    continue;
                }
                String fieldAttrCode = annotation.fieldName().equals("") ? configurationField.getName() : annotation.fieldName();
                ConfigurationAttr configurationAttr = attrMap.get(fieldAttrCode);
                if (configurationAttr == null) {
                    continue;
                }
                String configurationValue = annotation.isComplex() ? configurationAttr.getComplexValue() : configurationAttr.getSimpleValue();
                PropertyDescriptor prop = new PropertyDescriptor((String) configurationField.getName(), configObj.getClass());
                prop.getWriteMethod().invoke(configObj, ConfigurationAttrParser.parse(configurationValue, configurationField.getGenericType(), annotation.isComplex()));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessRuntimeException(-1, "属性注入失败, 错误信息：" + e.getMessage());
        }
    }
}
