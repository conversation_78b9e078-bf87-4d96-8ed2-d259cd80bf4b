package com.saicmobility.evcard.md.act.job;

import com.saicmobility.evcard.md.act.service.impl.InitStockActivityDataService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@JobHandler("InitStockActivityDataJob")
public class InitStockActivityDataJob extends IJobHandler {

    @Autowired
    private InitStockActivityDataService service;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            log.info("============> 开始执行 初始化活动门店批处理 定时任务 <============");
            service.doInit();
            return ReturnT.SUCCESS;
        }catch (Exception e){
            log.error("-----------执行初始化活动门店异常:e:{}------------",e);
            return ReturnT.FAIL;
        }finally {
            log.info("============> 结束执行 初始化活动门店批处理 定时任务 <============");
        }
    }
}
