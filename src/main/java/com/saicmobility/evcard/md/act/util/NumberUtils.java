package com.saicmobility.evcard.md.act.util;

import com.ctrip.framework.apollo.core.utils.StringUtils;

import java.math.BigDecimal;
import java.util.Optional;

public class NumberUtils {
    public static String getStr(BigDecimal value) {
        return Optional.ofNullable(value).orElse(BigDecimal.ZERO).toPlainString();
    }

    public static String getStr(Double value) {
        return Optional.ofNullable(value).orElse(0d).toString();
    }

    public static Long getLong(String str) {
        if(StringUtils.isBlank(str)) {
            return null;
        }
        return Long.valueOf(str);
    }

    public static BigDecimal getBigDecimal(String str) {
        if(StringUtils.isBlank(str)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(str).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public static Double getDouble(String str) {
        if(StringUtils.isBlank(str)) {
            return 0d;
        }
        return Double.valueOf(str);
    }
}
