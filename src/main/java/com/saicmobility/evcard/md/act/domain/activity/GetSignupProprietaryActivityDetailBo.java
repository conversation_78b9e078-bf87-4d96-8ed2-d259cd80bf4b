package com.saicmobility.evcard.md.act.domain.activity;

import com.saicmobility.evcard.md.act.dto.market.TimeRange;
import com.saicmobility.evcard.md.act.dto.proprietary.DiscountFlexiblePricing;
import com.saicmobility.evcard.md.act.dto.proprietary.DiscountStandardPricing;
import com.saicmobility.evcard.md.act.dto.proprietary.FullMinusFlexiblePricing;
import com.saicmobility.evcard.md.act.dto.proprietary.FullMinusStandardPricing;
import com.saicmobility.evcard.md.mdactservice.api.ActSignUpDetailInfo;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
public class GetSignupProprietaryActivityDetailBo {
    private String activityName; // 活动名称
    private String activityTag; // 活动标签
    private List<String> orgCodes; // 活动机构列表
    private List<Long> storeIdList;//活动门店列表
    private List<Long> vehicleModelIds; // 活动车型id列表
    private int activityType; // 活动类型：1-满减、2-打折
    private int pricingType; // 定价类型：1-灵活定价、2-规范定价
    private int discountLatitude; // 优惠纬度：1-车辆租金
    private List<FullMinusStandardPricing> fullMinusStandardPricing; // 满减规范定价
    private List<FullMinusFlexiblePricing> fullMinusFlexiblePricing; // 报名前满减灵活定价
    private List<DiscountStandardPricing> discountStandardPricing; // 打折规范定价
    private List<DiscountFlexiblePricing> discountFlexiblePricing; // 报名前打折灵活定价
    private int minRentDays; // 最小租期
    private int maxRentDays; // 最大租期
    private int availableOnHolidays; // 节假日是否可用：1-可用、2-不可用
    private String signUpStartDate; // 报名开始时间 yyyyMMdd
    private String signUpEndDate; // 报名结束时间 yyyyMMdd
    private String pickUpDate; // 取车时间 yyyyMMdd
    private String returnDate; // 还车时间 yyyyMMdd
    private String activityStartDate; // 活动开始时间 yyyyMMdd
    private String activityEndDate; // 活动结束时间 yyyyMMdd
    private List<TimeRange> unavailableDateRanges; // 不可用时间范围
    private Integer sameDayUseFlag;//仅限下单当日取车  0-非当日使用 1-当日使用


    private long activityId; // 活动id
    private long id; // 报名id（未报名时，不存在 值为0。报名后会有值）
    private List<SignupFullMinusFlexiblePricing> signupFullMinusFlexiblePricing; // 报名后满减灵活定价 (报名后会有值)
    private List<SignupDiscountFlexiblePricing> signupDiscountFlexiblePricing; // 报名后打折灵活定价 (报名后会有值)
    private String signupOrgCode; // 报名的机构
    private List<Long> signupVehicleModelIds; // 报名的车型列表
    private int allOrgCodes; // 活动参与机构：全部机构  1-全部  2-不是全部
    private int allModelIds; // 活动参与车型：全部车型  1-全部  2-不是全部
    private int allStore; //全部门店  1-全部  2-不是全部
    private Long signupStoreId;//报名门店
    private int specifyDateFlag;//是否指定下单日期 0-不限制 1-限制
    private String specifyDate;//指定日期，格式以数字，英文分号分割。空代表未选择： 1;3-周一，周三
    private int blockHolidayFlag; //屏蔽节假日 0-不屏蔽 1-屏蔽
    private int intersectionFlag; //取还车时间交集开关，1=取还车都必须在时间范围内 2=取还车任一时间在时间范围内

    public ActSignUpDetailInfo toRes() {
        ActSignUpDetailInfo.Builder builder = ActSignUpDetailInfo.newBuilder();
        builder.setActivityName(Optional.ofNullable(activityName).orElse(""));
        builder.setActivityTag(Optional.ofNullable(activityTag).orElse(""));
        if (!CollectionUtils.isEmpty(orgCodes)) {
            builder.addAllOrgCodes(orgCodes);
        }
        if(!CollectionUtils.isEmpty(storeIdList)){
            builder.addAllStoreIdList(storeIdList);
        }
        if (!CollectionUtils.isEmpty(vehicleModelIds)) {
            builder.addAllVehicleModelIds(vehicleModelIds);
        }
        builder.setActivityType(activityType);
        builder.setPricingType(pricingType);
        builder.setDiscountLatitude(discountLatitude);
        if (!CollectionUtils.isEmpty(fullMinusStandardPricing)) {
            List<com.saicmobility.evcard.md.mdactservice.api.FullMinusStandardPricing> list = fullMinusStandardPricing.stream().map(o -> com.saicmobility.evcard.md.mdactservice.api.FullMinusStandardPricing.newBuilder()
                    .setDays(o.getDays())
                    .setDiscountAmount(o.getDiscountAmount())
                    .build()).collect(Collectors.toList());
            builder.addAllFullMinusStandardPricing(list);
        }
        if (!CollectionUtils.isEmpty(fullMinusFlexiblePricing)) {
            List<com.saicmobility.evcard.md.mdactservice.api.FullMinusFlexiblePricing> list = fullMinusFlexiblePricing.stream().map(o -> com.saicmobility.evcard.md.mdactservice.api.FullMinusFlexiblePricing.newBuilder()
                    .setDays(o.getDays())
                    .setMaxDiscountAmount(o.getMaxDiscountAmount())
                    .setMinDiscountAmount(o.getMinDiscountAmount())
                    .build()).collect(Collectors.toList());
            builder.addAllFullMinusFlexiblePricing(list);
        }
        if (!CollectionUtils.isEmpty(discountStandardPricing)) {
            List<com.saicmobility.evcard.md.mdactservice.api.DiscountStandardPricing> list = discountStandardPricing.stream().map(o -> com.saicmobility.evcard.md.mdactservice.api.DiscountStandardPricing.newBuilder()
                    .setDays(o.getDays())
                    .setDiscount(o.getDiscount())
                    .build()).collect(Collectors.toList());
            builder.addAllDiscountStandardPricing(list);
        }
        if (!CollectionUtils.isEmpty(discountFlexiblePricing)) {
            List<com.saicmobility.evcard.md.mdactservice.api.DiscountFlexiblePricing> list = discountFlexiblePricing.stream().map(o -> com.saicmobility.evcard.md.mdactservice.api.DiscountFlexiblePricing.newBuilder()
                    .setDays(o.getDays())
                    .setMaxDiscount(o.getMaxDiscount())
                    .setMinDiscount(o.getMinDiscount())
                    .build()).collect(Collectors.toList());
            builder.addAllDiscountFlexiblePricing(list);
        }
        builder.setMinRentDays(minRentDays);
        builder.setMaxRentDays(maxRentDays);
        builder.setAvailableOnHolidays(availableOnHolidays);
        builder.setSignUpStartDate(Optional.ofNullable(signUpStartDate).orElse(""));
        builder.setSignUpEndDate(Optional.ofNullable(signUpEndDate).orElse(""));
        builder.setPickUpDate(Optional.ofNullable(pickUpDate).orElse(""));
        builder.setReturnDate(Optional.ofNullable(returnDate).orElse(""));
        builder.setActivityStartDate(Optional.ofNullable(activityStartDate).orElse(""));
        builder.setActivityEndDate(Optional.ofNullable(activityEndDate).orElse(""));
        if (!CollectionUtils.isEmpty(unavailableDateRanges)) {
            List<com.saicmobility.evcard.md.mdactservice.api.TimeRange> list = unavailableDateRanges.stream().map(o -> {
                return com.saicmobility.evcard.md.mdactservice.api.TimeRange.newBuilder()
                        .setStartDate(o.getStartDate())
                        .setEndDate(o.getEndDate())
                        .build();
            }).collect(Collectors.toList());
            builder.addAllUnavailableDateRanges(list);
        }

        builder.setActivityId(activityId);
        builder.setId(id);
        if (!CollectionUtils.isEmpty(signupFullMinusFlexiblePricing)) {
            List<com.saicmobility.evcard.md.mdactservice.api.SignupFullMinusFlexiblePricing> list = signupFullMinusFlexiblePricing.stream().map(o -> com.saicmobility.evcard.md.mdactservice.api.SignupFullMinusFlexiblePricing.newBuilder()
                    .setDays(o.getDays())
                    .setDiscountAmount(o.getDiscountAmount())
                    .setMaxDiscountAmount(o.getMaxDiscountAmount())
                    .setMinDiscountAmount(o.getMinDiscountAmount())
                    .build()).collect(Collectors.toList());
            builder.addAllSignupFullMinusFlexiblePricing(list);
        }
        if (!CollectionUtils.isEmpty(signupDiscountFlexiblePricing)) {
            List<com.saicmobility.evcard.md.mdactservice.api.SignupDiscountFlexiblePricing> list = signupDiscountFlexiblePricing.stream().map(o -> com.saicmobility.evcard.md.mdactservice.api.SignupDiscountFlexiblePricing.newBuilder()
                    .setDays(o.getDays())
                    .setDiscount(o.getDiscount())
                    .setMaxDiscount(o.getMaxDiscount())
                    .setMinDiscount(o.getMinDiscount())
                    .build()).collect(Collectors.toList());
            builder.addAllSignupDiscountFlexiblePricing(list);
        }
        builder.setSignupOrgCode(signupOrgCode);
        if (!CollectionUtils.isEmpty(signupVehicleModelIds)) {
            builder.addAllSignupVehicleModelIds(signupVehicleModelIds);
        }
        builder.setAllOrgCodes(allOrgCodes);
        builder.setAllStore(allStore);
        builder.setAllModelIds(allModelIds);
        builder.setSameDayUseFlag(sameDayUseFlag);
        builder.setSignupStoreId(signupStoreId);
        builder.setSpecifyDateFlag(specifyDateFlag);
        builder.setSpecifyDate(specifyDate);
        builder.setBlockHolidayFlag(blockHolidayFlag);
        builder.setIntersectionFlag(intersectionFlag);
        return builder.build();
    }
}
