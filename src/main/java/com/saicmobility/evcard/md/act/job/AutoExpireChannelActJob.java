package com.saicmobility.evcard.md.act.job;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saicmobility.evcard.md.act.adapter.QingluAdapter;
import com.saicmobility.evcard.md.act.adapter.dto.MarketingCampaignInfoRequest;
import com.saicmobility.evcard.md.act.adapter.dto.OpenMarketingCampaignRequest;
import com.saicmobility.evcard.md.act.adapter.impl.QingluAdapterImpl;
import com.saicmobility.evcard.md.act.constant.SystemConst;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.entity.ChannelActivity;
import com.saicmobility.evcard.md.act.enums.ConfigStateEnum;
import com.saicmobility.evcard.md.act.service.IChannelActivityService;
import com.saicmobility.evcard.md.act.service.MarketingModuleService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@JobHandler("AutoExpireChannelActJob")
public class AutoExpireChannelActJob extends IJobHandler {
    @Resource
    private IChannelActivityService channelActivityService;

    @Resource
    private MarketingModuleService marketingModuleService;

    @Resource
    private QingluAdapter qingluAdapter;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("============> 开始执行 渠道活动自动过期批处理 定时任务 <============");

        UserDTO jobUser = SystemConst.ELASTIC_JOB_USER;
        LocalDate currDate = LocalDate.now();

        channelActivityService.lambdaUpdate()
                .set(ChannelActivity::getActivityStatus, ConfigStateEnum.EXPIRE.getType())
                .set(ChannelActivity::getUpdateOperId, jobUser.getId())
                .set(ChannelActivity::getUpdateOperName, jobUser.getUsername())
                .set(ChannelActivity::getUpdateTime, currDate)
                .eq(ChannelActivity::getActivityStatus, ConfigStateEnum.VALID.getType())
                .lt(ChannelActivity::getActivityEndDate, currDate)
                .update();

        log.info("============> 结束执行 渠道活动自动过期批处理 定时任务 <============");
        return ReturnT.SUCCESS;
    }
}
