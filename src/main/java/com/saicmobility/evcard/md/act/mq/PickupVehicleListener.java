package com.saicmobility.evcard.md.act.mq;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.order.ConsumeOrderContext;
import com.aliyun.openservices.ons.api.order.MessageOrderListener;
import com.aliyun.openservices.ons.api.order.OrderAction;
import com.extracme.evcard.mq.bean.md.MdPickupVehicle;
import com.saicmobility.evcard.md.act.entity.UserOrderReduceActivityRecord;
import com.saicmobility.evcard.md.act.mapper.act.UserOrderReduceActivityRecordMapper;
import com.saicmobility.evcard.md.mdorderservice.api.GetContractActivityReduceInfoReq;
import com.saicmobility.evcard.md.mdorderservice.api.GetContractActivityReduceInfoRes;
import com.saicmobility.evcard.md.mdorderservice.api.MdOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@Service
public class PickupVehicleListener implements MessageOrderListener {

    @Autowired
    private UserOrderReduceActivityRecordMapper userOrderReduceActivityRecordMapper;

    @Autowired
    private MdOrderService mdOrderService;

    @Override
    public OrderAction consume(Message message, ConsumeOrderContext consumeOrderContext) {
        try {
            Boolean success = Boolean.FALSE;
            MdPickupVehicle pickupVehicle = JSON.parseObject(message.getBody(), MdPickupVehicle.class);
            String contractId = pickupVehicle.getContractId();
            if (StringUtils.isNotBlank(contractId)) {
                //根据合同ID查询信息
                GetContractActivityReduceInfoReq req = GetContractActivityReduceInfoReq.newBuilder().setContractId(contractId).build();
                GetContractActivityReduceInfoRes reduceInfoRes = mdOrderService.getContractActivityReduceInfo(req);
                if (reduceInfoRes.getRetCode() == 0 && reduceInfoRes.getReduceActivityId() > 0 ) {
                    String reduceAmount = reduceInfoRes.getReduceAmount();
                    if (StrUtil.isNotBlank(reduceAmount) && new BigDecimal(reduceAmount).compareTo(BigDecimal.ZERO)> 0) {
                        UserOrderReduceActivityRecord record = new UserOrderReduceActivityRecord();
                        record.setActivityId(reduceInfoRes.getReduceActivityId());
                        record.setMid(reduceInfoRes.getMid());
                        record.setOrderNo(contractId);
                        record.setStoreId(reduceInfoRes.getPickStoreId());
                        record.setOrderReduceAmount(StrUtil.isBlank(reduceAmount) ? new BigDecimal("0.00") : new BigDecimal(reduceAmount));
                        int insert = userOrderReduceActivityRecordMapper.insert(record);
                        if (insert > 0) {
                            success = Boolean.TRUE;
                        }
                    }else {
                        success = Boolean.TRUE;
                    }
                }
            }
            if (!success) {
                log.error("消费消息失败：key: {}, tag: {}, body: {}", message.getKey(), message.getTag(), new String(message.getBody()));
            }
        } catch (Exception e) {
            log.error("消费消息异常： " + e.getMessage(), e);
            return OrderAction.Suspend;
        }
        log.info("消费消息成功：{}, {}, {}", message.getTopic(), message.getTag(), message.getMsgID());
        return OrderAction.Success;
    }
}
