package com.saicmobility.evcard.md.act.domain.coupon;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Description 核销优惠券参数
 */
@Data
public class UsedCouponDto  implements Serializable {

    private static final long serialVersionUID = -1945411899250655400L;

    /**
     * 用户优惠券id
     */
    private Long userCouponSeq;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 会员id
     */
    private String authId;

    /**
     * 实际抵扣金额
     */
    private BigDecimal discount;

    /**
     * 核销优惠券的订单所属机构
     */
    private String orderOrgCode;

    /**
     * 操作人
     */
    private String optUser;

    /**
     * 更新时间
     */
    private String updatedTime;

}
