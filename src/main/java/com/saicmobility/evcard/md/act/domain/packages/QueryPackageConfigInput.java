package com.saicmobility.evcard.md.act.domain.packages;

import com.saicmobility.evcard.md.act.domain.common.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: fsh
 * @Date: 2022/4/15 16:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryPackageConfigInput extends Page {

    /**
     * 运营公司ID
     */
    private String orgCode;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     *配置状态
     */
    private Integer configState;

    /**
     * 续租是否可用
     */
    private Integer renewUseFlag;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 车型编号（商品车型）
     */
    private Long goodsModelId;


}
