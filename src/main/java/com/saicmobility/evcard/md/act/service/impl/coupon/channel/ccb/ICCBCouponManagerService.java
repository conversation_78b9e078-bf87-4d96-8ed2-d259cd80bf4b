package com.saicmobility.evcard.md.act.service.impl.coupon.channel.ccb;

import com.saicmobility.evcard.md.mdactservice.api.*;

public interface ICCBCouponManagerService {
    GetCouponDetailForCCBRes getCouponDetailForCCB(GetCouponDetailForCCBReq req);

    GetCouponStockForCCBRes getCouponStockForCCB(GetCouponStockForCCBReq req);

    OfferCouponForCCBRes offCouponForCCB(OfferCouponForCCBReq req);

    CallBackCouponStatusToCCBRes callBackCouponStatusToCCB(CallBackCouponStatusToCCBReq req);

    ReconciliationFileToRes reconciliationFileToCCB(ReconciliationFileToReq req);

    GetChannelCouponStatusForCCBRes getChannelCouponStatusForCCB(GetChannelCouponStatusForCCBReq req);

    InvalidChannelCouponForCCBRes invalidChannelCouponForCCB(InvalidChannelCouponForCCBReq req);

    StatementOfAccountCallBackForCCBRes statementOfAccountCallBackForCCB(StatementOfAccountCallBackForCCBReq req);

    RemoveChannelCouponStockRes removeChannelCouponStock(RemoveChannelCouponStockReq removeChannelCouponStockReq);
}
