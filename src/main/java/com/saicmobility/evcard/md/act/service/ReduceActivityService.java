package com.saicmobility.evcard.md.act.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saicmobility.evcard.md.act.entity.ReduceActivity;
import com.saicmobility.evcard.md.mdactservice.api.*;

public interface ReduceActivityService extends IService<ReduceActivity> {

    SearchReduceActivityRes searchReduceActivity(SearchReduceActivityReq req);

    GetReduceActivityWithStoreRes getReduceActivityWithStore(GetReduceActivityWithStoreReq req);

    AddReduceActivityRes addReduceActivity(AddReduceActivityReq req);

    UpdateReduceActivityRes updateReduceActivity(UpdateReduceActivityReq req);

    OfflineReduceActivityRes offlineReduceActivity(OfflineReduceActivityReq req);

    GetReduceActivityRes getReduceActivity(GetReduceActivityReq req);

    SearchReduceActivityNameRes searchReduceActivityName(SearchReduceActivityNameReq req);

    GetReduceActivityWithGoodModelNameRes getReduceActivityWithGoodModelName(GetReduceActivityWithGoodModelNameReq req);

    SearchAllReduceActivityRes searchAllReduceActivity(SearchAllReduceActivityReq req);

}
