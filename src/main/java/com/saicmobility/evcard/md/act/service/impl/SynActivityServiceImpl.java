package com.saicmobility.evcard.md.act.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.constant.BusinessConst;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.domain.StoreInfoCombobox;
import com.saicmobility.evcard.md.act.entity.*;
import com.saicmobility.evcard.md.act.enums.ConfigStateEnum;
import com.saicmobility.evcard.md.act.enums.IsDeletedEnum;
import com.saicmobility.evcard.md.act.service.*;
import com.saicmobility.evcard.md.act.service.extern.ConfigLoader;
import com.saicmobility.evcard.md.mdstoreservice.api.*;
import krpc.rpc.impl.TracablePool;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class SynActivityServiceImpl implements ISynActivityService {

    @Autowired
    private IProprietaryActivitySignupService activitySignupService;

    @Autowired
    private IChannelActivityService channelActivityService;

    @Resource
    private ExternalSystemFacade externalSystemFacade;

    @Resource
    private IProprietaryActivityService proprietaryActivityService;

    @Autowired
    private MdStoreService mdStoreService;

    @Resource
    private ConfigLoader configLoader;

    @Autowired
    @Qualifier("channelActivitySyncPool")
    private TracablePool channelActivitySyncPool;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSyncActivityVehicle(Long storeId, List<Long> mdModelIds,String orgCode,boolean selfOperatedChannels,List<String> channelList) throws BusinessException {
        if ( storeId == null || mdModelIds == null || mdModelIds.isEmpty() || StringUtils.isBlank(orgCode) ) {
            log.info("tid:{},同步活动车型信息，请求参数为空,storeId:{},mdModelIds:{},orgCode:{},", Trace.currentTraceId(),storeId,mdModelIds,orgCode);
            throw new BusinessException(ErrorEnum.PARAM_LACK.getCode(),ErrorEnum.PARAM_LACK.getMsg());
        }
        String storeIdStr = storeId.toString();
        //1.处理自营活动
        //判断是否有自营渠道活动
        if(selfOperatedChannels){
            List<String> storeIdsList = Arrays.asList("-1", storeIdStr);
            String vehicleModelIdsStr = StringUtils.join(mdModelIds, ",");

            activitySignupService.syncVehicle(storeId, storeIdStr, storeIdsList, vehicleModelIdsStr);
        }
        if(CollectionUtils.isEmpty(channelList)){
            log.info("tid:{},同步活动车型信息,二级渠道列表为空,不进行处理,channelList:{}", Trace.currentTraceId(),JSONObject.toJSONString(channelList));
            return ;
        }
        //2.处理渠道活动处理
        //查询生效的存在当前门店并且是全部车型的渠道活动
        List<ChannelActivity> channelActivityList = channelActivityService.lambdaQuery()
                .eq(ChannelActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .eq(ChannelActivity::getActivityStatus, ConfigStateEnum.VALID.getType())
                .eq(ChannelActivity::getIsAllVehicle, 1)
                .and(o -> o.like(ChannelActivity::getStoreIds, "," + storeIdStr + ",")
                        .or(o1 -> o1.like(ChannelActivity::getStoreIds, "," + storeIdStr ))
                        .or(o1 -> o1.eq(ChannelActivity::getStoreIds, storeIdStr))
                        .or(o1 -> o1.eq(ChannelActivity::getIsAllStore,1)))
                .list();
        //查询渠道活动报名门店
        if(CollectionUtils.isEmpty(channelActivityList)){
            log.info("tid:{},同步渠道活动车型信息,无当前报名门店生效的全部车型的渠道活动,storeId:{}",Trace.currentTraceId(),storeId);
            return;
        }
        //判断渠道活动中的车型（channelActivityList.CarModelIds是按逗号分隔）是否存在
       List<ChannelActivity> updateActivityList = new ArrayList<>(channelActivityList.size());
        for (ChannelActivity o  : channelActivityList) {
            String carModelIds = o.getCarModelIds();
            String orgCodes = o.getOrgCodes();
            if (carModelIds == null || orgCodes == null) {
                continue;
            }
            if(!channelList.contains(o.getSecondAppKey())){
                log.info("tid:{},同步渠道活动车型信息,当前渠道活动的渠道不属于该车型绑定渠道,不进行同步.渠道活动:{},车型所属渠道:{},storeId:{}"
                        ,Trace.currentTraceId(),o.getSecondAppKey(),JSONObject.toJSONString(channelList),storeId);
                continue;
            }
            List<Long> carModelIdsList = Arrays.stream(carModelIds.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            List<String> orgCodeList = Arrays.stream(orgCodes.split(","))
                    .map(String::toString)
                    .collect(Collectors.toList());
            //非该机构报名直接退出
            if(!orgCodeList.contains(orgCode)){
                log.info("tid:{},同步渠道活动车型信息,当前渠道活动的渠道不属于该机构,不进行同步.渠道活动:{},车型所属机构:{},storeId:{}"
                        ,Trace.currentTraceId(),JSONObject.toJSONString(orgCodeList),orgCode,storeId);
                continue;
            }
            //判断
            StringBuilder sb = new StringBuilder();
            for (Long id : mdModelIds){
               if(!carModelIdsList.contains(id)){
                   sb.append(id).append(",");
               }
            }
            if (sb.length()>0) {
                o.setCarModelIds(o.getCarModelIds() + "," +  sb.deleteCharAt(sb.length() - 1));
                updateActivityList.add(o);
            }
        }
        if(CollectionUtils.isEmpty(updateActivityList)){
            log.info("tid:{},同步渠道活动车型信息,该门店信息车型已存在,storeId{},mdModelIds:{}", Trace.currentTraceId(),storeId,JSONObject.toJSONString(mdModelIds));
            return;
        }
        //更新渠道活动报名门店车型
        channelActivityService.updateBatchById(updateActivityList);
        log.info("tid:{},同步渠道活动车型信息,成功！updateActivityList:{},通知擎路开始",Trace.currentTraceId(),JSONObject.toJSONString(updateActivityList));
        for (ChannelActivity act : channelActivityList) {
            if (!BusinessConst.NOT_SYNC_SECOND_APP_KEY_LIST.contains(act.getSecondAppKey())) {
                channelActivitySyncPool.post(()->{
                    //擎路同步
                    try {
                        externalSystemFacade.notifyChannelActivitySync(act, false);
                    }catch (Exception e){
                        log.info("tid:{},同步渠道活动车型信息,调用擎路同步接口异常,act:{}",Trace.currentTraceId(), JSONObject.toJSONString(act),e);
                    }
                });
            }
        }
        log.info("tid:{},同步渠道活动车型信息,成功！通知擎路结束",Trace.currentTraceId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSyncActivityStore(Long storeId,boolean selfOperatedChannels, List<String> channelList, String orgCode) throws BusinessException {
        //1.同步自营活动门店信息
        //判断是否有自营渠道活动
        if(selfOperatedChannels){
            activitySignupService.syncStore(storeId,orgCode);
        }
        if(storeId == null || StringUtils.isBlank(orgCode) ){
            log.info("tid:{},同步渠道活动门店信息,入参为空,storeId{},orgCode:{},channelId:{}"
                    , Trace.currentTraceId(),storeId,orgCode,JSONObject.toJSONString(channelList));
            throw new  BusinessException(ErrorEnum.PARAM_LACK.getCode(),ErrorEnum.PARAM_LACK.getMsg());
        }
        if(CollectionUtils.isEmpty(channelList)){
            log.info("tid:{},同步渠道活动门店信息,二级渠道列表为空,不进行处理,channelList:{}", Trace.currentTraceId(),JSONObject.toJSONString(channelList));
            return;
        }
        //2.同步渠道活动
        List<ChannelActivity> channelActivityList = channelActivityService.lambdaQuery()
                .eq(ChannelActivity::getIsDeleted, IsDeletedEnum.NORMAL.getType())
                .eq(ChannelActivity::getActivityStatus, ConfigStateEnum.VALID.getType())
                .eq(ChannelActivity::getIsAllStore, 1)
                .list();
        if(CollectionUtils.isEmpty(channelActivityList)){
            log.info("tid:{},同步渠道活动门店信息,为查到全部门店的渠道活动数据,storeId{},orgCode:{}", Trace.currentTraceId(),storeId,orgCode);
            return;
        }
        //过滤channelActivityList中的门店是否存在
        List<ChannelActivity> updateActivityList = new ArrayList<>(channelActivityList.size());
        channelActivityList.forEach(o -> {
            String orgCodes = o.getOrgCodes();
            String storeIds = o.getStoreIds();
            // 处理空指针异常
            if (orgCodes == null || storeIds == null) {
                return;
            }
            if(!channelList.contains(o.getSecondAppKey())){
                log.info("tid:{},同步渠道活动门店信息,当前渠道活动的渠道不属于该门店,无需同步该门店.渠道名称:{},所属渠道活动:{},门店所属渠道:{}"
                        ,Trace.currentTraceId(),o.getActivityName(),o.getSecondAppKey(),JSONObject.toJSONString(channelList));
                return;
            }
            Set<String> orgCodeSet = new HashSet<>(Arrays.asList(orgCodes.split(",")));
            //判断该机构是否报名
            if(!orgCodeSet.contains(orgCode)){
                log.info("tid:{},同步渠道活动门店信息,当前渠道活动的机构不属于该机构,无需同步该门店.渠道名称:{},所属渠道活动机构:{},门店所属机构:{}"
                        ,Trace.currentTraceId(),o.getActivityName(),JSONObject.toJSONString(orgCodeSet),orgCode);
                return;
            }
            //判断门店是否已经报名
            Set<Long> storeIdSet = Arrays.stream(storeIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
            if (!storeIdSet.contains(storeId)) {
                o.setStoreIds(o.getStoreIds() + "," + storeId);
                updateActivityList.add(o);
            }
        });
        if(CollectionUtils.isEmpty(updateActivityList)){
            log.info("tid:{},同步渠道活动门店信息,该门店信息所有渠道活动已存在,storeId{},orgCode:{}", Trace.currentTraceId(),storeId,orgCode);
            return;
        }
        //更新渠道活动报名门店车型
        channelActivityService.updateBatchById(updateActivityList);
        log.info("tid:{},同步渠道活动门店信息,成功！通知擎路开始",Trace.currentTraceId());
        for (ChannelActivity act : updateActivityList) {
            if (!BusinessConst.NOT_SYNC_SECOND_APP_KEY_LIST.contains(act.getSecondAppKey())) {
                channelActivitySyncPool.post(()-> {
                    //擎路同步
                    try {
                        externalSystemFacade.notifyChannelActivitySync(act, false);
                    } catch (Exception e) {
                        log.info("tid:{},同步渠道活动门店信息,调用擎路同步接口异常,act:{}", Trace.currentTraceId(), JSONObject.toJSONString(act), e);
                    }
                });
            }
        }
        log.info("tid:{},同步渠道活动门店信息,成功！通知擎路结束",Trace.currentTraceId());
    }


}
