package com.saicmobility.evcard.md.act.service.welfare;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.dto.welfare.CouponWelfareInfo;
import com.saicmobility.evcard.md.act.dto.welfare.GetWelfareInfoInput;
import com.saicmobility.evcard.md.act.dto.welfare.ReceiveWelfareDto;
import com.saicmobility.evcard.md.act.dto.welfare.ReceiveWelfareInput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 一码多券 福利券服务实现
 */
@Slf4j
@Service
public class OneCodeMulCouponScanCodeWelfareServiceImpl extends ScanCodeWelfareServiceImpl{

    @Override
    public boolean checkReceiveWelfareInfoInput(GetWelfareInfoInput input) {
        // 一码多券的 key长度为17位
        String key = input.getKey();
        return super.checkReceiveWelfareInfoInput(input) && key.length() == 17;
    }

    @Override
    protected List<CouponWelfareInfo> getCouponWelfareInfo(GetWelfareInfoInput input) {
        return couponService.getCouponWelfareInfosByCdk(input.getKey());
    }

    @Override
    protected ReceiveWelfareDto receiveCoupon(ReceiveWelfareInput input) throws BusinessException {
        return super.receiveCoupon(input);
    }

}
