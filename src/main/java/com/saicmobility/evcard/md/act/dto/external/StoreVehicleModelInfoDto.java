package com.saicmobility.evcard.md.act.dto.external;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@Data
public class StoreVehicleModelInfoDto {
    private long id; // 门店车型id
    private String storeVehicleModelName; // 门店车型名称
    private int vehicleNoType; // 车牌类型 1 普通，2 沪牌，3 沪C，4 京牌，5 粤A，6 粤B，7 津牌，8 浙A
    private int vehicleModelYear; // 车型年款 范围2010-2030及 1默认为无年款
    private double engineVolume; // 排量
    private int doorsNum; //车门数 1:2门 2:4门
    private int approvedSeats; //核定座位数
    private String vehicleBodyStyle; //车身结构
    private String vehicleLevelName; //车辆等级名称
    private int oilCapacity; //油箱容量 L
    private double electricCharge; //电池能量 kWh
    private int electricMileage; //续驶里程 km
    private String assetVehicleModelName; // 资产车型名称
    private long assetVehicleModelId; // 资产车型id
}
