package com.saicmobility.evcard.md.act.dto.coupon.channel;

import lombok.Data;

/**
 * 优惠券状态
 */
@Data
public class CouponStatusDto {
    private long mmpThirdCouponId;

    //1-优惠券，2-兑换码
    private Integer type;
    //0：未兑换/未使用 1：已兑换/已使用 2：已作废 3: 已过期
    private Integer status;


    //使用时间
    private String useTime;

    //优惠券兑换码
    private String couponCode;
    //**********************************ChannelCoupon表信息**********************************
    //渠道唯一标识
    private String channelId;

    //渠道的用户唯一标识
    private String channelUserId;

    //渠道的订单唯一标识
    private String channelOrderId;

    //渠道的活动编号
    private String channelActivityId;

    //**********************************订单信息**********************************
    //订单原金额（未使用任何优惠）
    private String orderAmt;
    //使用优惠后的用户实付金额
    private String payAmt;
    //优惠券减免金额
    private String prftAmt;
    //取车门店号
    private String pickStoreId;
}
