package com.saicmobility.evcard.md.act.entity.iss;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 活动送券配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="MmpThirdActivity对象", description="活动送券配置表")
public class MmpThirdActivity extends Model<MmpThirdActivity> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动配置ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "单人单日领券次数限制")
    private Integer dayVoucherLimit;

    @ApiModelProperty(value = "单人总领券次数限制")
    private Integer totalVoucherLimit;

    @ApiModelProperty(value = "活动单日发券次数限制")
    private Integer dayOfferLimit;

    @ApiModelProperty(value = "活动总发券次数限制")
    private Integer totalOfferLimit;

    @ApiModelProperty(value = "活动预算总金额")
    private BigDecimal preTotalAmount;

    @ApiModelProperty(value = "活动渠道key")
    private String activityChannelKey;

    @ApiModelProperty(value = "活动渠道")
    private String activityChannel;

    @ApiModelProperty(value = "e币充值金额限制大于等于值")
    private Integer eAmountGreater;

    @ApiModelProperty(value = "e币充值金额限制小于值")
    private Integer eAmountLess;

    @ApiModelProperty(value = "e币总发券次数限制")
    private Integer eOfferNumber;

    @ApiModelProperty(value = "备注")
    private String miscDesc;

    @ApiModelProperty(value = "是否有效（0 无效 1 有效）")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人姓名")
    private String createOperName;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人id")
    private Long updateOperId;

    @ApiModelProperty(value = "修改人姓名")
    private String updateOperName;

    @ApiModelProperty(value = "参与周期")
    private Integer period;

    @ApiModelProperty(value = "参与次数限制")
    private Integer countLimit;

    @ApiModelProperty(value = "活动图片")
    private String imgUrl;

    @ApiModelProperty(value = "活动地址")
    private String activityUrl;

    @ApiModelProperty(value = "充值套餐模板id")
    private Long packagesId;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "官网地址")
    private String officialWebAddress;

    @ApiModelProperty(value = "客服电话")
    private String serviceTelephone;

    @ApiModelProperty(value = "发券节点，0缺省 1新用户审核通过 2首单支付")
    private Integer offerTiming;

    @ApiModelProperty(value = "订单时长，单位分钟")
    private Integer orderDuration;

    @ApiModelProperty(value = "引导下载弹层 0：显示 1：不显示")
    private Integer bootDownloadLayer;

    @ApiModelProperty(value = "活动规则")
    private String activityRules;

    @ApiModelProperty(value = "活动海报地址(小程序端)")
    private String posterMiniProgram;

    @ApiModelProperty(value = "活动主标题")
    private String activityTitle;

    @ApiModelProperty(value = "活动副标题")
    private String activitySubtitle;

    @ApiModelProperty(value = "背景颜色(0-12)")
    private Integer backgroundColor;

    @ApiModelProperty(value = "优惠券交易方式，0赠送 1购买")
    private Long transType;

    @ApiModelProperty(value = "购买方类别，0无  1企业 2个人")
    private Long couponOwner;

    @ApiModelProperty(value = "购买方id(企业id，会员authId)")
    private String ownerId;

    @ApiModelProperty(value = "无门槛优惠券预算金额")
    private Integer preCouponTotalAmount;

    @ApiModelProperty(value = "非收入无门槛优惠券预算金额")
    private Integer preNonrevenueCouponTotalAmount;

    @ApiModelProperty(value = "订单应收金额，-1表示不限制")
    private Double orderAmount;

    @ApiModelProperty(value = "还车日限制,逗号分隔，1,3表示周一和周三可用")
    private String returnDaysOfWeek;

    @ApiModelProperty(value = "取车开始时间点(hhmmss)")
    private String pickupStartTime;

    @ApiModelProperty(value = "取车结束时间点(hhmmss)")
    private String pickupEndTime;

    @ApiModelProperty(value = "还车时间开始时间点(hhmmss)")
    private String returnStartTime;

    @ApiModelProperty(value = "还车时间结束时间点(hhmmss)")
    private String returnEndTime;

    @ApiModelProperty(value = "订单奖励方式 0券 1现金, 默认券")
    private Integer orderRewardType;

    @ApiModelProperty(value = "最低可提现金额，单位元")
    private Double withdrawAmountMin;

    @ApiModelProperty(value = "个人偶然所得税税率，单位%")
    private Integer rewardTaxRate;

    @ApiModelProperty(value = "奖励结算间隔时间，单位天，订单支付后n天结算奖励")
    private Integer rewardDaysLimit;

    @ApiModelProperty(value = "现金奖励比例，单位%, 基于订单现金实付金额")
    private Integer rewardAmountRate;

    @ApiModelProperty(value = "每单最高奖励金额，单位元")
    private Double rewardAmountMax;

    @ApiModelProperty(value = "返现订单数限制，0不限制 1仅首单")
    private Integer orderNumLimit;

    @ApiModelProperty(value = "订单支付距离认证时间，单位天")
    private Integer orderPayInterval;

    @ApiModelProperty(value = "订单实付金额限制，单位元")
    private Double orderAmountLimit;

    @ApiModelProperty(value = "租车模式(已被产品线大类字段替代) 0 即时分时 1 预约分时(废弃) 2即时日租 3预约日租, 空表示不限，多个以逗号分隔")
    private String rentMethods;

    @ApiModelProperty(value = "产品线大类 1 分时 2日租, 空表示不限，多个以逗号分隔")
    private String rentMethodGroup;

    @ApiModelProperty(value = "链接方式，0外部链接，1内部链接")
    private Integer linkType;

    @ApiModelProperty(value = "跳转外部地址(link_type=0时)")
    private String linkUrl;

    @ApiModelProperty(value = "APP跳转编号(link_type=1时)，0邀请好友、1会员任务中心、2车型宝典、3积分商城、4充值E币、5每日签到、6特惠购卡")
    private Integer linkAppId;

    @ApiModelProperty(value = "是否可关闭浮动图标：0可关闭，1不可关闭")
    private Integer closeFloatImg;

    @ApiModelProperty(value = "使用模式 0 仅预约上门送取车辆, 空表示不限，包日租所有业务")
    private String useMethods;

    @ApiModelProperty(value = "领劵方式 0 表示自动领劵, 1 表示手动领劵")
    private Integer couponWay;

    @ApiModelProperty(value = "兑换码二维码图片集合zip文件url")
    private String qrCodeZipUrl;

    @ApiModelProperty(value = "兑换码有效期开始时间")
    private LocalDateTime cdkStartTime;

    @ApiModelProperty(value = "兑换码有效期结束时间")
    private LocalDateTime cdkExpiresTime;

    @ApiModelProperty(value = "一码多券标志 0不是 ，1 是   默认0")
    private Integer oneCodeMulCouponFlag;

    @ApiModelProperty(value = "一码多券一码数目，one_code_mul_coupon_flag为1，才有值")
    private Integer couponCodeNum;


}
