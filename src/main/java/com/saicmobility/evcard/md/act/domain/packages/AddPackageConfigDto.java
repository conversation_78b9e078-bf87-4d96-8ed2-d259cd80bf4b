package com.saicmobility.evcard.md.act.domain.packages;

import com.saicmobility.evcard.md.act.domain.UserInfoInput;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: fsh
 * @Date: 2022/4/15 14:46
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class AddPackageConfigDto extends UserInfoInput implements Serializable {

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 运营公司
     */
    private String orgCode;

    /**
     * 门店
     */
    private Long storeId;

    /**
     * 车型(商品车型)
     */
    private Long goodsModelId;

    /**
     * 用车天数
     */
    private Integer daysNumber;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 用车开始日期
     */
    private LocalDateTime useStartDate;

    /**
     * 用车结束日期
     */
    private LocalDateTime useEndDate;

    /**
     * 生效日期
     */
    private LocalDateTime startTime;

    /**
     * 续租是否可用
     */
    private Integer renewUseFlag;

}
