package com.saicmobility.evcard.md.act.dto.market;

import com.alibaba.fastjson.annotation.JSONField;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.constant.BusinessConst;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.enums.market.CostAllocationMethodEnum;
import com.saicmobility.evcard.md.act.enums.market.CostBearingPartyEnum;
import com.saicmobility.evcard.md.act.enums.market.RestrictDiscount;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.mdactservice.api.CurrentUser;
import com.saicmobility.evcard.md.mdactservice.api.UpdateChannelActReq;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
@Slf4j
public class UpdateChannelActDto {
    private Long id;
    private String appKey;
    private String actName;
    private List<String> orgCodes;
    private List<Long> storeIds;
    private List<Long> catIds;
    private int actType; //活动类型：1-满减、2-打折、3-减至
    private int discountLatitude; //优惠纬度：1-车辆租金、2-订单整单
    private int discountMethod; //优惠方式：1-针对金额、2-针对租期、3-满天减天、4-满天减金额
    private String discountConditional1; // 优惠条件1
    private String discountConditional2; // 优惠条件2
    private int restrictDiscounts; //是否限制优惠：1-有限制、2-无限制
    private BigDecimal maxDiscountAmout; //最高优惠金额，当restrictDiscounts=1才需要设置此字段
    private int maxRentDays; //最大租期，当restrictDiscounts=1才需要设置此字段
    private int minRentDays; //最小租期，当restrictDiscounts=1才需要设置此字段
    private LocalDate actStartDate; //活动开始时间
    private LocalDate actEndDate; //活动结束时间
    private LocalDate pickupStartDate; //取车开始时间
    private LocalDate pickupEndDate; //取车结束时间
    private LocalDate returnStartDate; //还车开始时间
    private LocalDate returnEndDate; //还车结束时间
    private UnavailableDateRanges unavailableDateRanges; //不可用时间范围
    private int costBearingParty; //成本承担方：1-平台全部承担、2-商家全部承担、3-共同承担
    private int costAllocationMethod; //成本分摊方式：1-百分比设置、2-按固定金额设置
    private BigDecimal merchantBear; //商户承担(百分比或金额)
    private BigDecimal platformBear; //平台承担(百分比或金额)
    private String discountCode; //优惠码 EV+数字，50字符内

    @JSONField(serialize = false)
    private CurrentUser currentUser; // 当前用户

    private int  isAllStore;    //是否全部门店   0否、1是
    private int  isAllVehicle;  //是否全部车型  0否、1是
    private Integer intersectionFlag; //取还车时间交集开关，1=取还车都必须在时间范围内 2=取还车任一时间在时间范围内

    public UpdateChannelActDto getDTO(UpdateChannelActReq req) throws BusinessException {
        UpdateChannelActDto dto = new UpdateChannelActDto();
        //id
        dto.setId(req.getId());
        if (StringUtils.isEmpty(req.getChannel())) {
            log.error("二级渠道Key为空，请输入，appKey = {}", dto.getAppKey());
            throw new BusinessException(ErrorEnum.INVALID_APPKEY.getCode(), ErrorEnum.INVALID_APPKEY.getMsg());
        }
        dto.setAppKey(req.getChannel());
        //actName
        if (StringUtils.isEmpty(req.getActName())) {
            log.error("活动名称为空，请输入");
            throw new BusinessException(ErrorEnum.INVALID_ACTNAME.getCode(), ErrorEnum.INVALID_ACTNAME.getMsg());
        }
        dto.setActName(req.getActName());
        //orgCode
        if (CollectionUtils.isEmpty(req.getOrgCodesList())) {
            log.error("参与机构为空，请输入");
            throw new BusinessException(ErrorEnum.INVALID_ORGCODE.getCode(), ErrorEnum.INVALID_ORGCODE.getMsg());
        }
        // DiscountCode
        if (StringUtils.isEmpty(req.getDiscountCode())) {
            log.error("优惠码为空，请输入");
            throw new BusinessException(ErrorEnum.DISCOUNT_CODE_ILLEAGL.getCode(), ErrorEnum.DISCOUNT_CODE_ILLEAGL.getMsg());
        }

        dto.setDiscountCode(req.getDiscountCode());

        dto.setOrgCodes(req.getOrgCodesList());
        //门店车型
        dto.setStoreIds(req.getStoreIdListList());
        dto.setCatIds(req.getCarIdListList());
        //活动类型
        dto.setActType(req.getActType());
        //优惠纬度
        dto.setDiscountLatitude(req.getDiscountLatitude());
        //优惠方式
        dto.setDiscountMethod(req.getDiscountMethod());
        if (StringUtils.isEmpty(req.getDiscountConditional1())) {
            log.error("优惠条件1为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_CONDITION1.getCode(), ErrorEnum.EMPTY_CONDITION1.getMsg());
        }
        dto.setDiscountConditional1(req.getDiscountConditional1());
        if (StringUtils.isEmpty(req.getDiscountConditional2())) {
            log.error("优惠条件2为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_CONDITION2.getCode(), ErrorEnum.EMPTY_CONDITION2.getMsg());
        }
        dto.setDiscountConditional2(req.getDiscountConditional2());
        //是否限制优惠
        dto.setRestrictDiscounts(req.getRestrictDiscounts());
        if (dto.getRestrictDiscounts() == RestrictDiscount.RESTRICT.getType()) { //有限制时才设置这三个字段
            //最高优惠金额非必填
            BigDecimal bigDecimal = StringUtils.isEmpty(req.getMaxDiscountAmount()) ? new BigDecimal("-1") : new BigDecimal(req.getMaxDiscountAmount());
            dto.setMaxDiscountAmout(bigDecimal);
            //最小租期必填
            if (req.getMinRentDays() == 0) {
                throw new BusinessException(ErrorEnum.EMPTY_MIN_RENTDAYS.getCode(), ErrorEnum.EMPTY_MIN_RENTDAYS.getMsg());
            }
            //hello, tx 特殊渠道 不需要判断最大租期
            if (!BusinessConst.NOT_SYNC_SECOND_APP_KEY_LIST.contains(req.getChannel())) {
                //最大租期必填
                if (req.getMaxRentDays() == 0) {
                    throw new BusinessException(ErrorEnum.EMPTY_MAX_RENTDAYS.getCode(), ErrorEnum.EMPTY_MAX_RENTDAYS.getMsg());
                }
            }
            dto.setMaxRentDays(req.getMaxRentDays());
            dto.setMinRentDays(req.getMinRentDays());
        }
        //开始时间、结束时间
        LocalDate startDate = DateUtil.getLocalDateFromStr(req.getActStartDate(), DateUtil.DATE_TYPE5);
        LocalDate endDate = DateUtil.getLocalDateFromStr(req.getActEndDate(), DateUtil.DATE_TYPE5);
        //活动时间
        if (StringUtils.isEmpty(req.getActStartDate())) {
            log.error("活动开始时间为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_ACT_START_DATE.getCode(), ErrorEnum.EMPTY_ACT_START_DATE.getMsg());
        }
        dto.setActStartDate(startDate);
        if (StringUtils.isEmpty(req.getActEndDate())) {
            log.error("活动结束时间为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_ACT_END_DATE.getCode(), ErrorEnum.EMPTY_ACT_END_DATE.getMsg());
        }
        dto.setActEndDate(endDate);
        if (startDate.compareTo(endDate) > 0) {
            log.error("活动结束时间小于活动开始时间, 请检查, actStartDate= {}, actEndDate = {}", req.getActStartDate(), req.getActEndDate());
            throw new BusinessException(ErrorEnum.DATE_ERROR.getCode(), ErrorEnum.DATE_ERROR.getMsg());
        }

        //取车时间
        startDate = DateUtil.getLocalDateFromStr(req.getPickupStartDate(), DateUtil.DATE_TYPE5);
        endDate = DateUtil.getLocalDateFromStr(req.getPickupEndDate(), DateUtil.DATE_TYPE5);
        if (StringUtils.isEmpty(req.getPickupStartDate())) {
            log.error("取车开始时间为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_PICK_START_DATE.getCode(), ErrorEnum.EMPTY_PICK_START_DATE.getMsg());
        }
        dto.setPickupStartDate(startDate);
        if (StringUtils.isEmpty(req.getPickupEndDate())) {
            log.error("取车结束时间为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_PICK_END_DATE.getCode(), ErrorEnum.EMPTY_PICK_END_DATE.getMsg());
        }
        dto.setPickupEndDate(endDate);
        if (startDate.compareTo(endDate) > 0) {
            log.error("取车结束时间小于取车开始时间, 请检查, actStartDate= {}, actEndDate = {}", req.getPickupStartDate(), req.getPickupEndDate());
            throw new BusinessException(ErrorEnum.DATE_ERROR.getCode(), ErrorEnum.DATE_ERROR.getMsg());
        }

        //还车时间
        startDate = DateUtil.getLocalDateFromStr(req.getReturnStartDate(), DateUtil.DATE_TYPE5);
        endDate = DateUtil.getLocalDateFromStr(req.getReturnEndDate(), DateUtil.DATE_TYPE5);
        if (StringUtils.isEmpty(req.getReturnStartDate())) {
            log.error("还车开始时间为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_RETURN_START_DATE.getCode(), ErrorEnum.EMPTY_RETURN_START_DATE.getMsg());
        }
        dto.setReturnStartDate(startDate);
        if (StringUtils.isEmpty(req.getReturnEndDate())) {
            log.error("还车结束时间为空，请输入");
            throw new BusinessException(ErrorEnum.EMPTY_RETURN_END_DATE.getCode(), ErrorEnum.EMPTY_RETURN_END_DATE.getMsg());
        }
        dto.setReturnEndDate(endDate);
        if (startDate.compareTo(endDate) > 0) {
            log.error("还车结束时间小于还车开始时间, 请检查, actStartDate= {}, actEndDate = {}", req.getReturnStartDate(), req.getReturnEndDate());
            throw new BusinessException(ErrorEnum.DATE_ERROR.getCode(), ErrorEnum.DATE_ERROR.getMsg());
        }
        //不可用时间范围
        dto.setUnavailableDateRanges(toTimeRangeList(req.getUnavailableDateRangesList(), req.getPickupStartDate(), req.getPickupEndDate()));

        dto.setCostBearingParty(req.getCostBearingParty());
        //只有共同承担时需要以下字段
        if (req.getCostBearingParty() == CostBearingPartyEnum.TOGETHER.getType()) {
            dto.setCostAllocationMethod(req.getCostAllocationMethod());
            if (req.getCostAllocationMethod() == CostAllocationMethodEnum.PERCENT.getType()) {//百分比设置
                if (req.getMerchantBear().equals("NaN") || req.getPlatformBear().equals("NaN") || new BigDecimal(req.getMerchantBear()).add(new BigDecimal(req.getPlatformBear())).compareTo(new BigDecimal(100)) != 0) {
                    log.error("商家、平台承担百分比不合法，请检查。merchantBear = {}, platformBear = {}", req.getMerchantBear(), req.getPlatformBear());
                    throw new BusinessException(ErrorEnum.DATA_ERROR.getCode(), ErrorEnum.DATA_ERROR.getMsg());
                }
                dto.setMerchantBear(new BigDecimal(req.getMerchantBear()));
                dto.setPlatformBear(new BigDecimal(req.getPlatformBear()));
            } else {//金额设置
                if (StringUtils.isEmpty(req.getMerchantBear())) {
                    dto.setPlatformBear(new BigDecimal(req.getPlatformBear()));
                    dto.setMerchantBear(new BigDecimal("0"));
                } else if (StringUtils.isEmpty(req.getPlatformBear())) {
                    dto.setMerchantBear(new BigDecimal(req.getMerchantBear()));
                    dto.setPlatformBear(new BigDecimal("0"));
                } else {
                    dto.setMerchantBear(new BigDecimal(req.getMerchantBear()));
                    dto.setPlatformBear(new BigDecimal(req.getPlatformBear()));
                }
            }
        } else {//不是共同承担
            dto.setCostAllocationMethod(0);
            dto.setPlatformBear(BigDecimal.ZERO);
            dto.setMerchantBear(BigDecimal.ZERO);
        }
        dto.setCurrentUser(req.getCurrentUser());
        dto.setIsAllStore(req.getIsAllStore());
        dto.setIsAllVehicle(req.getIsAllVehicle());
        dto.setIntersectionFlag(req.getIntersectionFlag());
        return dto;
    }

    /**
     * 将自动生成的TimeRange列表转成自定义的TimeRange列表
     * 列表长度不超过10
     * 不可用时间要在取车时间范围内
     *
     * @param unavailableDateRangesList
     * @return
     */
    private UnavailableDateRanges toTimeRangeList(List<com.saicmobility.evcard.md.mdactservice.api.TimeRange> unavailableDateRangesList, String startDate, String endDate) throws BusinessException {
        if (unavailableDateRangesList.size() > 10) {
            log.error("不可用时间不可超过10条，size = {}", unavailableDateRangesList.size());
            throw new BusinessException(ErrorEnum.OVER_SIZE_UNAVAILABLE_LIST.getCode(), ErrorEnum.OVER_SIZE_UNAVAILABLE_LIST.getMsg());
        }
        UnavailableDateRanges unavailableDateRanges = new UnavailableDateRanges();
        List<TimeRange> list = new ArrayList<>();
        for (com.saicmobility.evcard.md.mdactservice.api.TimeRange timeRange : unavailableDateRangesList) {
            LocalDate startDate1 = DateUtil.getLocalDateFromStr(timeRange.getStartDate(), DateUtil.DATE_TYPE5);
            LocalDate endDate1 = DateUtil.getLocalDateFromStr(timeRange.getEndDate(), DateUtil.DATE_TYPE5);
            // 该条不可用时间数据的合法性
            if (startDate1.compareTo(endDate1) > 0) {
                log.error("该条不可用结束时间小于开始时间, 请检查, actStartDate= {}, actEndDate = {}", timeRange.getStartDate(), timeRange.getEndDate());
                throw new BusinessException(ErrorEnum.DATE_ERROR.getCode(), ErrorEnum.DATE_ERROR.getMsg());
            }
            // 该条不可用时间是否在取车时间范围内
            LocalDate startDate2 = DateUtil.getLocalDateFromStr(startDate, DateUtil.DATE_TYPE5);
            LocalDate endDate2 = DateUtil.getLocalDateFromStr(endDate, DateUtil.DATE_TYPE5);
            if (startDate1.compareTo(startDate2) < 0 || startDate1.compareTo(endDate2) > 0 || endDate1.compareTo(startDate2) < 0 || endDate1.compareTo(endDate2) > 0) {
                log.error("该不可用时间超出取车时间范围");
                throw new BusinessException(ErrorEnum.UNAVAILABLETIME_OVER.getCode(), ErrorEnum.UNAVAILABLETIME_OVER.getMsg());
            }
            TimeRange tm = new TimeRange();
            tm.setStartDate(timeRange.getStartDate());
            tm.setEndDate(timeRange.getEndDate());
            list.add(tm);
        }
        unavailableDateRanges.setUnavailableDateRanges(list);
        return unavailableDateRanges;
    }

    public String toStringFromList(List<Long> list) {
        String str = "";
        for (Long num : list) {
            str += String.valueOf(num) + ",";
        }
        return str.substring(0, str.length() - 1);
    }

    public String toStringFromStrList(List<String> list) {
        String str = "";
        for (String str1 : list) {
            str += str1 + ",";
        }
        return str.substring(0, str.length() - 1);
    }
}
