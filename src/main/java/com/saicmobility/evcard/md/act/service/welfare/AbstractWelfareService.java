package com.saicmobility.evcard.md.act.service.welfare;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.constant.ErrorEnum;
import com.saicmobility.evcard.md.act.dto.welfare.*;
import com.saicmobility.evcard.md.act.enums.welfare.WelfareSourceEnum;
import com.saicmobility.evcard.md.act.enums.welfare.WelfareTypeEnum;
import com.saicmobility.evcard.md.act.service.coupun.CouponService;
import com.saicmobility.evcard.md.act.service.suixiangcard.ISuixiangCardCdkService;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public abstract class AbstractWelfareService implements IWelfareService {

    @Autowired
    protected CouponService couponService;
    @Autowired
    protected ISuixiangCardCdkService suixiangCardCdkService;

    @Override
    public List<CouponWelfareInfo> getCouponWelfareDetailInfo(GetWelfareInfoInput input) {
        if (!checkReceiveWelfareInfoInput(input)) {
            log.error("tid={} ,getCouponWelfareDetailInfo , checkGetWelfareInfoInput 失败，input={}", Trace.currentTraceId(), JSON.toJSONString(input));
            return null;
        }

        return getCouponWelfareInfo(input);
    }

    protected abstract List<CouponWelfareInfo> getCouponWelfareInfo(GetWelfareInfoInput input);

    @Override
    public ReceiveWelfareDto receiveCouponWelfare(ReceiveWelfareInput input) throws BusinessException {
        if (!checkReceiveWelfareInfoInput(input)) {
            log.error("tid={} , receiveCouponWelfare , receiveCouponWelfare 失败，input={}", Trace.currentTraceId(),JSON.toJSONString(input));
            throw new BusinessException(ErrorEnum.PARAM_LACK.getCode(), ErrorEnum.PARAM_LACK.getMsg());
        }

        ReceiveWelfareDto receiveCoupon = receiveCoupon(input);

        return receiveCoupon;
    }

    protected abstract ReceiveWelfareDto receiveCoupon(ReceiveWelfareInput input) throws BusinessException;

    @Override
    public List<SuiXiangCardWelfareInfo> getSuiXiangCardWelfareDetailInfo(GetWelfareInfoInput input) {
        if (!checkReceiveWelfareInfoInput(input)) {
            log.error("tid={} getSuiXiangCardWelfareDetailInfo , checkGetWelfareInfoInput 失败，input={}", Trace.currentTraceId(),JSON.toJSONString(input));
            return null;
        }

        return getSuiXiangCardWelfareInfo(input);
    }

    protected abstract List<SuiXiangCardWelfareInfo> getSuiXiangCardWelfareInfo(GetWelfareInfoInput input);

    @Override
    public ReceiveWelfareDto receiveSuiXiangCardWelfare(ReceiveWelfareInput input) throws BusinessException {
        if (!checkReceiveWelfareInfoInput(input)) {
            log.error("tid={} receiveSuiXiangCardWelfare , receiveCouponWelfare 失败，input={}", Trace.currentTraceId(),JSON.toJSONString(input));
            throw new BusinessException(ErrorEnum.PARAM_LACK.getCode(), ErrorEnum.PARAM_LACK.getMsg());
        }

        ReceiveWelfareDto receiveWelfareDto = receiveSuiXiangCard(input);

        return receiveWelfareDto;
    }

    protected abstract ReceiveWelfareDto receiveSuiXiangCard(ReceiveWelfareInput input) throws BusinessException;

    /**
     * 入参校验
     *
     * @param input
     * @return
     */
    public boolean checkReceiveWelfareInfoInput(GetWelfareInfoInput input) {
        String key = input.getKey();
        int welfareType = input.getWelfareType();
        int source = input.getSource();

        if (StringUtils.isBlank(key)) {
            return false;
        }

        WelfareTypeEnum welfareTypeEnum = WelfareTypeEnum.getWelfareTypeEnum(welfareType);
        if (welfareTypeEnum == null) {
            return false;
        }

        WelfareSourceEnum welfareSourceEnum = WelfareSourceEnum.getWelfareSourceEnum(source);
        if (welfareSourceEnum == null) {
            return false;
        }

        return true;
    }


    /**
     * 入参校验
     *
     * @param input
     * @return
     */
    public boolean checkReceiveWelfareInfoInput(ReceiveWelfareInput input) {
        GetWelfareInfoInput getWelfareInfoInput = input.getGetWelfareInfoInput();
        if (!checkReceiveWelfareInfoInput(getWelfareInfoInput)) {
            return false;
        }

        int type = input.getType();
        String mobile = input.getMobile();
        String mid = input.getMid();

        if (type == 1) {
            if (StringUtils.isBlank(mid)) {
                return false;
            }
        } else if (type == 2) {
            if (StringUtils.isBlank(mobile)) {
                return false;
            }
        }

        return true;
    }
}
