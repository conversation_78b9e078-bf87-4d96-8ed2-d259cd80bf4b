package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 用户参与的立减活动记录表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_user_order_reduce_activity_record")
public class UserOrderReduceActivityRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "立减活动id")
    private Long activityId;

    @ApiModelProperty(value = "参与门店id")
    private Long storeId;

    @ApiModelProperty(value = "新增通用会员id")
    private String mid;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单最终立减金额")
    private BigDecimal orderReduceAmount;

}
