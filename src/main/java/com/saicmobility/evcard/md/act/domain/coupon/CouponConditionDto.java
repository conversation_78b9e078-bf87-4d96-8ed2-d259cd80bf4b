package com.saicmobility.evcard.md.act.domain.coupon;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 获取优惠券详情接口使用
 */
@Data
public class CouponConditionDto{

	private static final long serialVersionUID = -6066169775527907289L;

	/**
	 * 优惠券编号.<br>
	 */
	private Long userCouponSeq;
	
	/**
	 * 会员id.<br>
	 */
	private String authId;

	/**
	 * 优惠券模板编号.<br>
	 */
	private Long couponSeq;
	
	/**
	 * 有效期开始时间.<br>
	 */
	private String startDate;

	/**
	 * 有效期结束时间.<br>
	 */
	private String expiresDate;

	/**
	 * 0：未使用 1：已使用   2 已作废. 3 已过期<br>
	 */
	private Integer status;

	/**
	 * 优惠券名称.<br>
	 */
	private String couponOrigin;

	/**
	 * 优惠券金额.<br>
	 */
	private BigDecimal couponValue;

	/**
	 * 优惠券描述（例如：10元优惠券）<br>
	 */
	private String des;
	
	/**
	 * 优惠券类型 1:直扣 2：折扣.<br>
	 */
	private Integer couponType;
	
	/**
	 * 业务类型（0不限/ 1分时/ 2短租/3长租).<br>
	 */
	private Integer serviceType;

	/**
	 * 最小消费金额
	 * @return
	 */
	private BigDecimal minAmount;
	/**
	 * 	0 不限定 1 取车时间  2 还车时间  3 取/还车时间.<br>
	 */
	private Integer timeType;

	/**
	 * 优惠券可使用开始时间点(hhmmss)
	 */
	private String startTime;

	/**
	 * 优惠券可使用结束时间点(hhmmss)
	 */
	private String endTime;

	/**
	 * 取车网点
	 */
	private String pickshopSeq;
	/**
	 * 取车网点名称 ,分割.<br>
	 */
	private String pickshopName;

	/**
	 * 还车网点
	 */
	private String returnshopSeq;
	/**
	 * 还车网点名称 ,分割.<br>
	 */
	private String returnshopName;

	/**
	 * 车牌号.<br>
	 */
	private String vehicleNo;

	/**
	 * 资产车型（多个车型以,分割）
	 */
	private String vehicleModel;
	/**
	 * 车型名称 ,分割
	 */
	private String vehicleModelName;

	/**
	 * 取车门店/网点所在城市
	 */
	private String pickshopCity;
	/**
	 * 取车门店/网点所在城市名称 ,分割
	 */
	private String pickshopCityName;

	/**
	 * 还车门店/网点所在城市
	 */
	private String returnshopCity;
	/**
	 * 还车门店/网点所在城市名称 ,分割
	 */
	private String returnshopCityName;
	
	/**
	 * 折扣券的折扣率.<br>
	 */
	private Integer discountRate;
	
	/**
	 * 发券机构.<br>
	 */
	private Long orgSeq;
	
	/**
	 * 核销该优惠券的订单号.<br>
	 */
	private String orderSeq;
	
	/**
	 * 抵扣金额，优惠券核销之后才会有该值.<br>
	 */
	private BigDecimal discount;
	
	/**
	 * 是否可与活动叠加；0：可以；1：不可以；
	 */
	private Integer activityOverlap = 0;

	/**
	 * 指定套餐id可使用，默认无限制
	 */
	private String packageIds = StringUtils.EMPTY;

	/**
	 * 法定节假日是否可用, 1:可用，0:不可用
	 */
	private Integer holidaysAvailable = 1;

	/**
	 * 可用天限制,逗号分隔，1,3表示周一和周三可用
	 */
	private String availableDaysOfWeek;

    /**
     * add.
     * 租车模式 0 即时分时 1 预约分时(废弃) 2即时日租 3预约日租, 空表示不限，多个以逗号分隔
     */
    private String rentMethod;

	/**
	 * 空不限制， 预约日租时，0表示仅限上门送取车服务使用
	 */
	private String useMethod;

    /**
     * add.
     * 结合了serviceType和rentMethod的描述信息
     */
    private String serviceTypeDesc;

	/**
	 * rentMethod的描述信息
	 */
	private String rentMethodDesc;

	/**
	 * 订单时长限制
	 */
	private BigDecimal durationLimit;

	/**
	 * 使用限制的标签描述.<br>
	 */
	private List<String> tags = new ArrayList<String>();
	
	/**
	 * 剩余时间条件描述.<br>
	 */
	private String remainTimeTag = StringUtils.EMPTY;
	/**
	 * 车辆使用条件限制（车牌车型）.<br>
	 */
	private String vehicleLimitDesc = StringUtils.EMPTY;

	/** 发放时间  yyyyMMddHHmmssSSS */
	private String  createdTime;

	/** 更新时间 yyyyMMddHHmmssSSS */
	private String updatedTime;

	/** 券交易类型 1 （原）直扣券类 2（原）折扣券类 3 非收入类 4 购买类 */
	private Integer transactionType;

	/**机构Id */
	private String agencyId;

	/**活动Id */
	private String actionId;

	/**产品线大类 1 分时 2日租, 空表示不限，多个以逗号分隔 */
	private String rentMethodGroup;

	/**商品车型（多个车型以,分割） */
	private String goodsVehicleModel;

	/**
	 * 取还车点限制类别add 1网点 2门店
	 */
	private Integer shopLimitType;

	/**
	 * 兑换码有效期开始时间
	 */
	private Date couponCodeStartTime;
	/**
	 * 兑换码有效期结束时间
	 */
	private Date couponCodeExpiresTime;

	/**
	 * 兑换码
	 */
	private String couponCode;

	/**
	 * 兑换码状态 0=未兑换 1=已兑换 2=已过期 3=已作废
	 */
	private Integer couponCodeStatus;

	/**
	 * 兑换码发放标记 0=未发放 1=已发放
	 */
	private Integer couponCodeOfferFlag;
}
