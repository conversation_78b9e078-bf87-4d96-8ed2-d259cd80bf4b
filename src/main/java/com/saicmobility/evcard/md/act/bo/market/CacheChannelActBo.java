package com.saicmobility.evcard.md.act.bo.market;

import com.saicmobility.evcard.md.mdactservice.api.ChannelActDetail;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@Slf4j
public class CacheChannelActBo {
    private List<GetChannelActDetailBo> list;
    private String md5;

    public List<ChannelActDetail> toChannelActDetailList() {
        List<ChannelActDetail> acts = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (GetChannelActDetailBo info : list) {
                try {
                    ChannelActDetail detail = info.boToPb(info);
                    acts.add(detail);
                } catch (Exception e) {
                    log.error("GetChannelActDetailBo 转 ChannelActDetail异常", e);
                }
            }
        }
        return acts;
    }
}
