package com.saicmobility.evcard.md.act.service.coupun;

import com.saicmobility.evcard.md.act.dto.welfare.CouponWelfareInfo;
import com.saicmobility.evcard.md.mdactservice.api.*;

import java.util.List;

public interface CouponService {

    GetCouponRes getCoupon(GetCouponReq req);

    UseCouponRes useCouponRest(UseCouponReq req);

    OrderCouponRes orderCoupons(OrderCouponReq req);

    CheckOrderCouponRes checkOrderCoupon(CheckOrderCouponReq req);

    FrozenCouponRes frozenCoupon(FrozenCouponReq req);

    ModifyOrderOperateCouponRes unFrozenThenFrozenCoupon(ModifyOrderOperateCouponReq req);

    GetCouponViewRes getCouponView(GetCouponViewReq req);

    GetCouponModelViewRes getCouponModelView(GetCouponModelReq req);

    GetCouponModelListViewRes getCouponModelListView(GetCouponModelListReq req);

    OfferThirdCouponsRes offerThirdCoupons(OfferThirdCouponsReq req);

    GetActivityBasicInfoRes getActivityBasicInfo(GetActivityBasicInfoReq req);

    GetMmpCouponListRes getMmpCouponList(GetMmpCouponListReq req);

    UserOrderOfferCouponsRes userOrderOfferCoupons(UserOrderOfferCouponsReq req);

    GetFirstOrderFlagRes getFirstOrderFlag(GetFirstOrderFlagReq req);

    BatchOrderCouponRes batchOrderCoupons(BatchOrderCouponReq batchOrderCouponReq);

    CouponWelfareInfo getCouponWelfareInfoByCode(String couponCode);

    /**
     *  通过 cdk 获取一码多券 的卡券信息集合
     * @param cdk
     * @return
     */
    List<CouponWelfareInfo> getCouponWelfareInfosByCdk(String cdk);
}
