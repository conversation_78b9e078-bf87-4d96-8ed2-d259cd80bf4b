package com.saicmobility.evcard.md.act.service.inner;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.entity.BaseEntity;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public class BaseService<M extends BaseMapper<T>, T extends BaseEntity>
        extends ServiceImpl<M, T> implements IBaseService<T> {

    public BaseService() {
    }

    public boolean save(T entity, UserDTO user, Date dateTime) {
        this.resolveEntity(entity, user, dateTime);
        return super.save(entity);
    }

    public boolean saveBatch(Collection<T> entityList, int batchSize, UserDTO user, Date dateTime) {
        entityList.forEach(e -> resolveEntity(e, user, dateTime));
        return super.saveBatch(entityList, batchSize);
    }

    public boolean saveBatch(Collection<T> entityList, UserDTO user, Date dateTime) {
        entityList.forEach(e -> resolveEntity(e, user, dateTime));
        return super.saveBatch(entityList);
    }

    public boolean updateById(T entity, UserDTO user, Date dateTime) {
        this.resolveEntity(entity, user, dateTime);
        return super.updateById(entity);
    }

    @Override
    public boolean update(T entity, UserDTO user, Date dateTime, Wrapper<T> updateWrapper) {
        this.resolveEntity(entity, user, dateTime);
        return super.update(entity, updateWrapper);
    }

    public boolean updateBatchById(Collection<T> entityList, int batchSize, UserDTO user, Date dateTime) {
        entityList.forEach(e -> resolveEntity(e, user, dateTime));
        return super.updateBatchById(entityList, batchSize);
    }

    public boolean saveOrUpdate(T entity, UserDTO user, Date dateTime) {
        return entity.getId() == null ? this.save(entity, user, dateTime) : this.updateById(entity, user, dateTime);
    }

    public boolean saveOrUpdateBatch(Collection<T> entityList, int batchSize, UserDTO user, Date dateTime) {
        entityList.forEach(e -> resolveEntity(e, user, dateTime));
        return super.saveOrUpdateBatch(entityList, batchSize);
    }

    @Transactional(
            rollbackFor = {Exception.class}
    )
    public boolean deleteLogic(@NotEmpty List<Long> ids, UserDTO user, Date dateTime) {
        List<T> list = new ArrayList();
        ids.forEach((id) -> {
            T entity = BeanUtils.instantiateClass(this.currentModelClass());
            if (user != null) {
                entity.fillUpdateUser(user, dateTime);
            }
            entity.setId(id);
            list.add(entity);
        });
        return super.updateBatchById(list) && super.removeByIds(ids);
    }

    public boolean changeStatus(@NotEmpty List<Long> ids, Integer status, UserDTO user, Date dateTime) {
        List<T> list = new ArrayList();
        ids.forEach((id) -> {
            T entity = BeanUtils.instantiateClass(this.currentModelClass());
            if (user != null) {
                entity.fillUpdateUser(user, dateTime);
            }
            list.add(entity);
        });
        return super.updateBatchById(list);
    }

    private void resolveEntity(T entity, UserDTO user, Date dateTime) {
        if (entity.getId() == null) {
            if (user != null) {
                entity.fillCreateAndUpdateUser(user, dateTime);
            }
        } else if (user != null) {
            entity.fillUpdateUser(user, dateTime);
        }

    }
}
