package com.saicmobility.evcard.md.act.service.rest.entity.coupon;

import com.extracme.evcard.rpc.coupon.dto.BatchOrderCouponDto;
import com.extracme.evcard.rpc.coupon.dto.BatchOrderCouponInfoDto;
import com.extracme.evcard.rpc.coupon.dto.OrderCouponDto;
import com.saicmobility.evcard.md.act.util.DateUtil;
import com.saicmobility.evcard.md.mdactservice.api.BatchOrderCouponInfo;
import com.saicmobility.evcard.md.mdactservice.api.BatchOrderCouponReq;
import com.saicmobility.evcard.md.mdactservice.api.OrderCouponCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Slf4j
public class BatchOrderCouponsRequest extends BatchOrderCouponDto {

    public static BatchOrderCouponsRequest fromRes(BatchOrderCouponReq req) {
        BatchOrderCouponsRequest request = new BatchOrderCouponsRequest();

        int sort = req.getSort();
        List<BatchOrderCouponInfo> infoList = req.getInfoList();
        OrderCouponCondition orderCouponCondition = req.getOrderCouponCondition();

        List<BatchOrderCouponInfoDto> infoDtos = new ArrayList<>();
        infoList.stream().forEach(info ->{
            BatchOrderCouponInfoDto infoDto = new BatchOrderCouponInfoDto();
            infoDto.setActId(info.getActId());
            infoDto.setAmount(info.getAmount());
            infoDto.setDay(info.getDay());
            infoDto.setStoreVehicleModelId(info.getStoreVehicleModelId());
            infoDto.setGoodsModelId(info.getGoodsModelId());
            infoDto.setUseSuiXiangCardId(info.getUseSuiXiangCardId());
            infoDto.setUseMemberCardId(info.getUseMemberCardId());
            infoDtos.add(infoDto);
        });

        OrderCouponDto orderCouponDto = new OrderCouponDto();
        orderCouponDto.setPickupTime(DateUtil.getDateFromDateStr(orderCouponCondition.getPickupTime(), DateUtil.DATE_TYPE1));
        orderCouponDto.setReturnTime(DateUtil.getDateFromDateStr(orderCouponCondition.getReturnTime(), DateUtil.DATE_TYPE1));

        orderCouponDto.setPickshopCity(orderCouponCondition.getPickUpCity());
        orderCouponDto.setReturnshopCity(orderCouponCondition.getReturnCity());
        orderCouponDto.setServiceType(orderCouponCondition.getServiceType());
        orderCouponDto.setPickupStoreId(orderCouponCondition.getPickUpStoreId());
        orderCouponDto.setReturnStoreId(orderCouponCondition.getReturnStoreId());
        orderCouponDto.setServiceType(orderCouponCondition.getServiceType());
        orderCouponDto.setRentMethod(orderCouponCondition.getRentMethod());
        orderCouponDto.setActivityType(orderCouponCondition.getActivityType());
        orderCouponDto.setCostTime(orderCouponCondition.getCostTime());
        orderCouponDto.setServiceTags(orderCouponCondition.getServiceTags());

        request.setSort(sort);
        request.setInfoDtos(infoDtos);
        request.setOrderCouponDto(orderCouponDto);
        return request;
    }
}
