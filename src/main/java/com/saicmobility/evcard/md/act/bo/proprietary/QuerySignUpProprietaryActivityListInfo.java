package com.saicmobility.evcard.md.act.bo.proprietary;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:44
 */
@Data
public class QuerySignUpProprietaryActivityListInfo {
    private long id; // 活动ID
    private long signUpId; //报名ID
    private String activityName; //活动名称
    private String orgCode; //报名机构
    private int activityType; //活动类型
    private String signUpDate; //报名时间
    private int pricingType; //定价类型：1-灵活定价、2-规范定价
    private String orgName;//报名机构名称
    private long storeId ; //报名门店id
    private String storeName; //门店名称
}
