package com.saicmobility.evcard.md.act.domain.coupon;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠券交易记录
 */
public class CouponTransactionRecordDto implements Serializable {
    private static final long serialVersionUID = 3842480934636318099L;
    /**
     * 用户authId/企业机构Id'
     */
    private String userId;
    /**
     * 账户类型 1 个人账户 2 企业账户
     */
    private Integer userType;
    /**
     * 券交易类型 1 （原）直扣券类 2（原）折扣券类 3 非收入类 4 购买类
     */
    private Integer transactionType;

    /**
     * 操作类型 1 发放 2 使用 3 过期  4 系统作废 5 退款作废 6 兑换 (必填)
     */
    private Integer operateType;

    /**
     * 进
     */
    private BigDecimal inAmount;
    /**
     * 出
     */
    private BigDecimal outAmount;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作时间
     */
    private Date operateDateTime;

    /**
     * 优惠券编号
     */
    private Long userCouponSeq;

    /**
     * 备注描述
     */
    private String desc;

    /**
     * 优惠券实际抵扣金额
     */
    private BigDecimal discount;

    public CouponTransactionRecordDto() {
    }

    public CouponTransactionRecordDto(String userId ,Integer userType, Integer transactionType, Integer operateType, BigDecimal inAmount, BigDecimal outAmount, String operator, Long userCouponSeq) {
        this.userId = userId;
        this.userType = userType;
        this.transactionType = transactionType;
        this.operateType = operateType;
        this.inAmount = inAmount;
        this.outAmount = outAmount;
        this.operator = operator;
        this.userCouponSeq = userCouponSeq;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Integer getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(Integer transactionType) {
        this.transactionType = transactionType;
    }

    public BigDecimal getInAmount() {
        return inAmount;
    }

    public void setInAmount(BigDecimal inAmount) {
        this.inAmount = inAmount;
    }

    public BigDecimal getOutAmount() {
        return outAmount;
    }

    public void setOutAmount(BigDecimal outAmount) {
        this.outAmount = outAmount;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperateDateTime() {
        return operateDateTime;
    }

    public void setOperateDateTime(Date operateDateTime) {
        this.operateDateTime = operateDateTime;
    }

    public Long getUserCouponSeq() {
        return userCouponSeq;
    }

    public void setUserCouponSeq(Long userCouponSeq) {
        this.userCouponSeq = userCouponSeq;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }
}
