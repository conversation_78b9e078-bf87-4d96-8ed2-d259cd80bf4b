package com.saicmobility.evcard.md.act.bo.suixiangcard;

import com.saicmobility.evcard.md.mdactservice.api.GetSuiXiangCardThirdSaleInfoRes;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SuiXiangCardThirdSaleInfoBo implements Serializable {

    private BigDecimal price; // 单价
    private int cdkPurpose; // issueType=3时，才生效。用途 1=活动赠送，2=第三方售卖
    private int totalDay; // 随享卡总天数
    private BigDecimal totalPrice; // 总价格
    private int issueType; // 购卡方式： 1：购买 2：赠送 3：兑换码

    public GetSuiXiangCardThirdSaleInfoRes toGetSuiXiangCardThirdSaleInfoRes() {
        return GetSuiXiangCardThirdSaleInfoRes.newBuilder()
                .setPrice(price != null ? price.toPlainString() : "0")
                .setCdkPurpose(cdkPurpose)
                .setTotalDay(totalDay)
                .setTotalPrice(totalPrice != null ? totalPrice.toPlainString() : "0")
                .setIssueType(issueType)
                .build();
    }
}
