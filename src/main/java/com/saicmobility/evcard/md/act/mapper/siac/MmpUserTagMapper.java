package com.saicmobility.evcard.md.act.mapper.siac;

import com.saicmobility.evcard.md.act.domain.MmpUserTagDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface MmpUserTagMapper {

    /**
     * 根据id查询城市
     */
    @Select("SELECT id,spare1 FROM iss.mmp_user_tag  WHERE auth_id = #{authId}")
    List<MmpUserTagDto> selectByAuthId(@Param("authId") String authId);

    @Select("SELECT count(1) FROM iss.mmp_user_tag  WHERE auth_id = #{authId} and spare1 != ''")
    Integer countFirstOrderByAuthId(@Param("authId") String authId);


}