package com.saicmobility.evcard.md.act.adapter.dto;

import lombok.Data;

import java.util.List;

@Data
public class MarketingCampaignInfoRequest {
    private String marketId; //营销信息id
    private Long channelId; //渠道id
    private Integer status; //营销状态 1:进行中， 2:结束
    private String marketName; //活动名称 最长50字符
    private String marketCode; //活动码 最长50字符
    private Integer marketType; //活动类型 1-满减 2-打折 3-减至
    private Integer dimension; //优惠纬度 1-车辆租金 2-订单整单
    private Integer discountMode; //优惠方式1 1-针对金额 2-针对租期
    private Integer discountSubMode; //优惠方式0 1-满天减天 2-满天减金额
    private Integer satisfy; //需满足的条件（天，元（单位分））
    private Integer discount; //优惠（天，元（单位分）,折扣（单位%））
    private Integer maxMoney; //最高优惠金额（单位分）（-1：不设置）     默认值-1
    private Integer maxLeaseTerm; //享受优惠需要满足的最大租期  maxMoney非-1时必填
    private Integer minLeaseTerm; //享受优惠需要满足的最小租期  maxMoney非-1时必填
    private Long placeOrderStart; //下单时间范围开始
    private Long placeOrderEnd; //下单时间范围结束
    private Long pickStart; //取车时间范围开始
    private Long pickEnd; //取车范围结束
    private Long returnStart; //还车时间范围开始
    private Long returnEnd; //还车时间范围结束
    private Integer costParty; //成本承担方 1：平台全部承担，2：商家全部承担，3：共同承担
    private Integer costPartyType; //成本分摊方式 1：按百分比设置，2：按固定金额设置costParty为3时必填
    private Integer merchantCostPartyValue; //商户成本分摊方式值（单位：costPartyType 1：百分比，2：分）costParty为3时必填
    private Integer platformCostPartyValue; //平台成本分摊方式值（单位：costPartyType 1：百分比，2：分）costParty为3时必填
    private List<MarketStoreRequest> marketStoreList; //门店列表  第一次同步的时候可以不传，后续增量以及更新必传
    private List<VehicleModelRequest> vehicleModelList; //车型id列表  第一次同步的时候可以不传，后续增量以及更新必传
    private List<MarketUnserviceableTimeRequest> marketUnserviceableTimeList; //不可用日期列表
    private Boolean delFlg;
}
