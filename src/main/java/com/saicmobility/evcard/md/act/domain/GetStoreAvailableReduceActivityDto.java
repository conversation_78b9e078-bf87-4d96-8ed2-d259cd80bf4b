package com.saicmobility.evcard.md.act.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 获取门店可用的立减活动(app接口)dto
 * @Author: lidong
 * @Date: 2022/06/11
 */
@Data
public class GetStoreAvailableReduceActivityDto implements Serializable {

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 商品车型编号
     */
    private Long goodsModelId;

    /**
     * 当前时间
     */
    private LocalDateTime queryTime;

}
