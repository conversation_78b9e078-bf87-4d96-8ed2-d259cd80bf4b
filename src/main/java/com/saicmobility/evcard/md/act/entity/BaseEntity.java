package com.saicmobility.evcard.md.act.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.extracme.framework.core.dto.UserDTO;
import com.saicmobility.evcard.md.act.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseEntity implements Serializable {

    @ApiModelProperty("主键id")
    @TableId( value = "id",type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "数据是否有效;0 无效 1 有效")
    @TableField("is_deleted")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    @TableField("create_oper_id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人姓名")
    @TableField("create_oper_name")
    private String createOperName;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    @TableField("update_oper_id")
    private Long updateOperId;

    @ApiModelProperty(value = "更新人姓名")
    @TableField("update_oper_name")
    private String updateOperName;


    //仅填充修改用户相关信息
    public void fillUpdateUser(UserDTO user) {
        Date updateTime = DateUtil.getSystemLocalDate2Date();
        this.fillUpdateUser(user, updateTime);
    }

    public void fillUpdateUser(UserDTO user, Date updateTime) {

        this.updateOperId = user.getId();
        this.updateOperName = StringUtils.isNotBlank(user.getUsername()) ? user.getUsername() : user.getName();
        this.updateTime = null == updateTime ? DateUtil.getSystemLocalDate2Date() : updateTime;
    }

    public void fillCreateUser(UserDTO user) {
        Date createTime = DateUtil.getSystemLocalDate2Date();
        this.fillCreateUser(user, createTime);
    }

    public void fillCreateUser(Long createOperId, String createOperName) {
        Date createTime = DateUtil.getSystemLocalDate2Date();
        this.createOperId = createOperId;
        this.createOperName = createOperName;
        this.createTime = createTime;
    }

    public void fillUpdateUser(Long updateOperId, String updateOperName) {
        Date updateTime = DateUtil.getSystemLocalDate2Date();
        this.updateOperId = updateOperId;
        this.updateOperName = updateOperName;
        this.updateTime = updateTime;
    }

    public void fillCreateUser(UserDTO user, Date createTime) {
        this.createOperId = user.getId();
        this.createOperName = StringUtils.isNotBlank(user.getUsername()) ? user.getUsername() : user.getName();
        this.createTime = null == createTime ? DateUtil.getSystemLocalDate2Date() : updateTime;
    }

    //仅填充创建及修改相关用户信息
    public void fillCreateAndUpdateUser(UserDTO user) {
        Date now = DateUtil.getSystemLocalDate2Date();
        this.fillCreateAndUpdateUser(user, now);
    }

    public void fillCreateAndUpdateUser(UserDTO user, Date updateTime) {
        this.createOperId = user.getId();
        this.createOperName = StringUtils.isNotBlank(user.getUsername()) ? user.getUsername() : user.getName();
        this.createTime = null == updateTime ? DateUtil.getSystemLocalDate2Date() : updateTime;
        this.updateOperId = user.getId();
        this.updateOperName = StringUtils.isNotBlank(user.getUsername()) ? user.getUsername() : user.getName();
        this.updateTime = null == updateTime ? DateUtil.getSystemLocalDate2Date() : updateTime;
    }

    public void fillUpdateUser(com.saicmobility.evcard.md.act.domain.common.UserDTO user, Date dateTime) {
        this.updateOperId = user.getId();
        this.updateOperName = StringUtils.isNotBlank(user.getUsername()) ? user.getUsername() : user.getName();
        this.updateTime = null == updateTime ? DateUtil.getSystemLocalDate2Date() : updateTime;
    }

    public void fillCreateAndUpdateUser(com.saicmobility.evcard.md.act.domain.common.UserDTO user, Date updateTime) {
        this.createOperId = user.getId();
        this.createOperName = StringUtils.isNotBlank(user.getUsername()) ? user.getUsername() : user.getName();
        this.createTime = null == updateTime ? DateUtil.getSystemLocalDate2Date() : updateTime;
        this.updateOperId = user.getId();
        this.updateOperName = StringUtils.isNotBlank(user.getUsername()) ? user.getUsername() : user.getName();
        this.updateTime = null == updateTime ? DateUtil.getSystemLocalDate2Date() : updateTime;
    }
}
