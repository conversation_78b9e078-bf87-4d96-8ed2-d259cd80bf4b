package com.saicmobility.evcard.md.act.service.impl.coupon.channel.ccb;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.google.gson.Gson;
import com.saicmobility.evcard.md.act.config.CcbConfig;
import com.saicmobility.evcard.md.act.constant.Constants;
import com.saicmobility.evcard.md.act.domain.common.UserDTO;
import com.saicmobility.evcard.md.act.dto.coupon.channel.*;
import com.saicmobility.evcard.md.act.dto.coupon.channel.ccb.*;
import com.saicmobility.evcard.md.act.entity.Task;
import com.saicmobility.evcard.md.act.entity.siac.CcbReconciliationRecord;
import com.saicmobility.evcard.md.act.enums.channelcoupon.CCBCodeEnum;
import com.saicmobility.evcard.md.act.enums.channelcoupon.CCBCouponOfferHandlerStatusEnum;
import com.saicmobility.evcard.md.act.enums.channelcoupon.CCBCouponStatusChangeEnum;
import com.saicmobility.evcard.md.act.iservice.ITaskService;
import com.saicmobility.evcard.md.act.service.ExternalSystemFacade;
import com.saicmobility.evcard.md.act.service.coupon.ICcbReconciliationRecordService;
import com.saicmobility.evcard.md.act.service.coupun.IChannelCouponManagerService;
import com.saicmobility.evcard.md.act.service.impl.coupon.AbstractChannelCouponManagerServiceImpl;
import com.saicmobility.evcard.md.act.service.rest.MessagePushRestClient;
import com.saicmobility.evcard.md.act.service.rest.entity.messagepush.SendEmailRequest;
import com.saicmobility.evcard.md.act.util.*;
import com.saicmobility.evcard.md.mdactservice.api.*;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static com.saicmobility.evcard.md.act.enums.channelcoupon.CCBCouponStatusChangeEnum.NOT_TO_USED;

@Slf4j
@Service
public class CCBCouponManagerServiceImpl extends AbstractChannelCouponManagerServiceImpl implements ICCBCouponManagerService {

    @Autowired
    private IChannelCouponManagerService ccbCouponManagerServiceImpl;

    @Autowired
    private ITaskService taskService;
    @Autowired
    private ExternalSystemFacade externalSystemFacade;

    @Autowired
    private ICcbReconciliationRecordService ccbReconciliationRecordService;

    @Autowired
    private MessagePushRestClient messagePushRestClient;

    @Value("${krpc.application.dataDir}")
    private String dataDirPath;
    private final String ossPath = "/CCB/ReconciliationFile/";

    @Value("${ccb.mmpThirdCoupon.id}")
    private List<Long> mmpThirdCouponIds;

    @Value("${ccb.callback.email}")
    private List<String> emails;

    @Override
    public List<Long> getMmpThirdCouponIds() {
        return mmpThirdCouponIds;
    }

    @Override
    public String getChannelId() {
        return "ccb_bank";
    }

    @Override
    public int getTotalLimit() {
        return 20;
    }

    @Override
    public GetCouponDetailForCCBRes getCouponDetailForCCB(GetCouponDetailForCCBReq req) {
        GetCouponModelDetailInput getCouponModelDetailInput = new GetCouponModelDetailInput();
        getCouponModelDetailInput.setPageNo(req.getPageNo());
        getCouponModelDetailInput.setPageSize(req.getPageSize());
        getCouponModelDetailInput.setProductId(req.getProductId());
        getCouponModelDetailInput.setProductType(req.getPdCl());
        getCouponModelDetailInput.setInstitutionId(req.getInsId());
        getCouponModelDetailInput.setChannelId(req.getMrchId());

        try {
            GetCouponModelDetailOutput output = this.getCouponModelDetail(getCouponModelDetailInput);
            List<CouponModelDto> couponModelDtos = output.getCouponModelDtos();
            List<CouponDetailInfoForCCB> couponDetailInfoForCCBs = new ArrayList<>();
            for (CouponModelDto couponModelDto : couponModelDtos) {
                ExpireForCCB expireForCCB;
                if (couponModelDto.getValidTimeType() == 1) {
                    String startTime = DateUtil.dateToString(DateUtil.getLocalDateFromStr(couponModelDto.getStartDate(), DateUtil.DATE_TYPE5).atStartOfDay(), DateUtil.DATE_TYPE4);
                    String endTime = DateUtil.dateToString(DateUtil.getLocalDateFromStr(couponModelDto.getExpiresDate(), DateUtil.DATE_TYPE5).atStartOfDay(), DateUtil.DATE_TYPE28);
                    expireForCCB = ExpireForCCB.newBuilder()
                            .setExpireType("1")
                            .setExpireTm(startTime + "|" + endTime)
                            .build();
                } else {
                    expireForCCB = ExpireForCCB.newBuilder()
                            .setExpireType("2")
                            .setExpireTm(String.valueOf(couponModelDto.getValidDays()))
                            .build();
                }


                couponDetailInfoForCCBs.add(CouponDetailInfoForCCB.newBuilder()
                        .setProductId(String.valueOf(couponModelDto.getId()))
                        .setProductName(couponModelDto.getCouponName())
                        .setDesc(couponModelDto.getRemark())
                        .setMrchId(CcbConfig.CCB_CHANNEL_ID)
                        .setFVal(String.valueOf(couponModelDto.getCouponValue()))
                        .setCstPrc(String.valueOf(couponModelDto.getCouponValue()))
                        .setAttr("41120022")
                        .setUdcrgTm(couponModelDto.getCdkExpiresTime() == null ? "" : DateUtil.dateToString(couponModelDto.getCdkExpiresTime(), DateUtil.DATE_TYPE4))
                        .setExpire(expireForCCB)
                        .setLimit("｜" + getTotalLimit() + "｜")
                        .build());
            }
            return GetCouponDetailForCCBRes.newBuilder()
                    .setTotalPage(output.getTotalPage())
                    .setTotal(output.getTotal())
                    .setPageSize(output.getPageSize())
                    .setPageNo(output.getPageNo())
                    .addAllProducts(couponDetailInfoForCCBs)
                    .build();
        } catch (BusinessException e) {
            return GetCouponDetailForCCBRes.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            return GetCouponDetailForCCBRes.failed(CCBCodeEnum.SYSTEM_ERROR.getInnerCode(), "");
        }
    }

    @Override
    public GetCouponStockForCCBRes getCouponStockForCCB(GetCouponStockForCCBReq req) {
        try {
            CouponStockDto couponStock = this.getCouponStock(req.getProductId(), req.getDccpAvyId());
            return GetCouponStockForCCBRes.newBuilder()
                    .setProductId(String.valueOf(couponStock.getMmpThirdCouponId()))
                    .setIssuNum(String.valueOf(couponStock.getTotalNum()))
                    .setPrpIvntNum(String.valueOf(couponStock.getTotalNum()))
                    .setAvlIvntNum(String.valueOf(couponStock.getAvailableNum()))
                    .setUdtTm(couponStock.getRefreshTime())
                    .build();
        } catch (BusinessException e) {
            return GetCouponStockForCCBRes.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            return GetCouponStockForCCBRes.failed(CCBCodeEnum.SYSTEM_ERROR.getInnerCode(), "");
        }
    }

    @Override
    //@ErrorNotify(emails = {"<EMAIL>"})
    public OfferCouponForCCBRes offCouponForCCB(OfferCouponForCCBReq req) {
        try {
            OfferCouponInput input = new OfferCouponInput();
            input.setUserId(req.getUserId());
            input.setProductId(req.getProductId());
            input.setOrderId(req.getOrderId());
            input.setRetryCnt(req.getRetryCnt());
            input.setNum(req.getNum());
            input.setDccpAvyId(req.getDccpAvyId());
            input.setLatitude(req.getLatitude());
            input.setLongitude(req.getLongitude());
            input.setUserRealIp(req.getUserRealIp());

            OfferCouponOutput offerCouponOutput = ccbCouponManagerServiceImpl.offerCoupon(input);
            List<ChannelCouponDto> coupons = offerCouponOutput.getCoupons();
            List<CouponForCCB> couponForCCBs = new ArrayList<>();
            for (ChannelCouponDto channelCouponDto : coupons) {
                couponForCCBs.add(CouponForCCB.newBuilder()
                        .setCouponCode(channelCouponDto.getCouponCode())
                        .build()
                );
            }

            return OfferCouponForCCBRes.newBuilder()
                    .setStatus(String.valueOf(offerCouponOutput.getStatus()))
                    .setOrderId(offerCouponOutput.getOrderId())
                    .setAsynFlag(String.valueOf(offerCouponOutput.getAsynFlag()))
                    .setUseType(String.valueOf(offerCouponOutput.getUseType()))
                    .setRespFlag(String.valueOf(offerCouponOutput.getRespFlag()))
                    .setCompleteTm(offerCouponOutput.getCompleteTm())
                    .addAllCoupons(couponForCCBs)
                    .build();
        } catch (BusinessException e) {
            // 订单号锁获取失败，返回订单处理中
            if (e.getCode() == -25259999) {
                return OfferCouponForCCBRes.newBuilder()
                        .setStatus(String.valueOf(CCBCouponOfferHandlerStatusEnum.ORDER_PROCESSING.getStatus()))
                        .setRetCode(e.getCode())
                        .setRetMsg(e.getMessage())
                        .build();
            }
            return OfferCouponForCCBRes.newBuilder()
                    .setStatus(String.valueOf(CCBCouponOfferHandlerStatusEnum.ORDER_PROCESSING_FAILED.getStatus()))
                    .setRetCode(e.getCode())
                    .setRetMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            return OfferCouponForCCBRes.newBuilder()
                    .setStatus(String.valueOf(CCBCouponOfferHandlerStatusEnum.ORDER_PROCESSING_FAILED.getStatus()))
                    .setRetCode(CCBCodeEnum.SYSTEM_ERROR.getInnerCode())
                    .setRetMsg("")
                    .build();
        }
    }



    @Override
    public CallBackCouponStatusToCCBRes callBackCouponStatusToCCB(CallBackCouponStatusToCCBReq req) {
        long userCouponSeq = req.getUserCouponSeq();
        int type = req.getType();
        int status = req.getStatus();
        String operation = null;
        if (status == 1){
            //不管优惠券还是兑换码，仅状态位为已使用，进行回调，否则无需推送
            if (type == 1){
                operation = CCBCouponStatusChangeEnum.NOT_TO_USED.getCode();
                //优惠券，查询订单系统，获取已使用优惠券对应金额等信息
            } else if (type == 2) {
                operation = CCBCouponStatusChangeEnum.NOT_TO_REDEEM.getCode();
                //优惠券兑换，没有门店
                //暂时建行无意义，兑换不进行数据回推
                return CallBackCouponStatusToCCBRes.ok();
            }
        }else {
            log.info("CouponStatusToCCB No Need CallBack,userCouponSeq->{},type->{},status->{}",userCouponSeq,type,status);
            return CallBackCouponStatusToCCBRes.failed(CCBCodeEnum.COUPON_NOT_NEED_CALLBACK.getInnerCode(),"优惠券状态无需回调");
        }

        CouponStatusDto couponStatusDto = this.callBackCouponStatus(userCouponSeq, type, status);
        if (couponStatusDto == null){
            log.info("CouponStatusToCCB 没有合适数据,不进行回调,userCouponSeq->{},type->{},status->{}",userCouponSeq,type,status);
            return CallBackCouponStatusToCCBRes.failed(CCBCodeEnum.COUPON_NOT_NEED_CALLBACK.getInnerCode(),"没有合适数据，不进行回调");
        }

        CCBCouponStatusDto ccbCouponStatusDto = new CCBCouponStatusDto();
        //选填
        ccbCouponStatusDto.setProductId(String.valueOf(couponStatusDto.getMmpThirdCouponId()));
        //必填
        ccbCouponStatusDto.setOrderId(couponStatusDto.getChannelOrderId());
        ccbCouponStatusDto.setCouponCode(couponStatusDto.getCouponCode());
        ccbCouponStatusDto.setOperation(operation);
        ccbCouponStatusDto.setUseOrderId(couponStatusDto.getChannelUserId());

        //条件必填

        ccbCouponStatusDto.setOrderAmt(couponStatusDto.getOrderAmt());
        ccbCouponStatusDto.setPayAmt(couponStatusDto.getPayAmt());
        ccbCouponStatusDto.setPrftAmt(couponStatusDto.getPrftAmt());
        //ccbCouponStatusDto.setStoreId_1();
        ccbCouponStatusDto.setStoreId_2(couponStatusDto.getPickStoreId());
        ccbCouponStatusDto.setUseTm(couponStatusDto.getUseTime());

        //调用回调接口，回调建行
        Gson gson = new Gson();
        creatCCBCouponStatusCallBackTask(Constants.TASK_TYPE_CCB_COUPON_STATUS_CALLBACK,gson.toJson(ccbCouponStatusDto));
        return CallBackCouponStatusToCCBRes.ok();
    }

    @Override
    public ReconciliationFileToRes reconciliationFileToCCB(ReconciliationFileToReq req) {
        String pushDate = req.getPushDate();
        String beginTime = null;
        String endTime = null;
        if (StringUtils.isBlank(pushDate)){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.DATE,-1);
            pushDate = DateUtils.dateToString(calendar.getTime(), DateUtils.DATE_TYPE5);
            beginTime = DateUtils.dateToString(calendar.getTime(),DateUtils.DATE_TYPE27);
            endTime = DateUtils.dateToString(calendar.getTime(),DateUtils.DATE_TYPE28);
        }else{
            Date date = DateUtil.getDateFromDateStr(pushDate, DateUtils.DATE_TYPE5);
            beginTime = DateUtils.dateToString(date,DateUtils.DATE_TYPE27);
            endTime = DateUtils.dateToString(date,DateUtils.DATE_TYPE28);
        }


        List<ReconciliationFileDto> reconciliationFileDtos = this.pushReconciliationFile(pushDate);
        if (CollectionUtils.isEmpty(reconciliationFileDtos)){
            log.info("CallBackCouponStatusToCCBRes No Need push");
            return ReconciliationFileToRes.failed(CCBCodeEnum.RECONCILIATION_FILE_NOT_NEED_PUSH.getInnerCode(),"无变动优惠券信息，对账文件无需推送");
        }

        List<CCBReconciliationFileDto> ccbReconciliationFileDtos = new ArrayList<>();
        reconciliationFileDtos.forEach(reconciliationFileDto -> {
            CCBReconciliationFileDto ccbReconciliationFileDto = new CCBReconciliationFileDto();
            ccbReconciliationFileDto.setProductId(reconciliationFileDto.getMmpThirdActivityId());
            ccbReconciliationFileDto.setCouponCode(reconciliationFileDto.getCouponCode());
            ccbReconciliationFileDto.setOperation(NOT_TO_USED.getCode());
            ccbReconciliationFileDto.setUseOrderId(reconciliationFileDto.getOrderSeq());
            ccbReconciliationFileDto.setStoreId_2(reconciliationFileDto.getPickUpStoreId());
            ccbReconciliationFileDto.setUseTm(reconciliationFileDto.getUseTime());
            ccbReconciliationFileDtos.add(ccbReconciliationFileDto);
        });
        //生成文件内容
        String content = CCBUtils.buildReconciliationFileContent(null, null, beginTime, endTime, ccbReconciliationFileDtos);

        //生成文件
        String dateStr = DateUtils.dateToString(new Date(), DateUtils.DATE_TYPE4);
        String fileName = dateStr.concat(".txt");
        Boolean txtFileFlag = FileUtils.createTxtFile(dataDirPath, fileName, content);
        if (txtFileFlag){
            //上传Oss留存
            OssUtil.uploadSynByFilePath(dataDirPath.concat("/").concat(fileName), ossPath + fileName);
            //推送文件给建行
            CCBFilePushDto ccbFilePushDto = new CCBFilePushDto();
            ccbFilePushDto.setFileName(dateStr);
            ccbFilePushDto.setFullFileName(fileName);
            ccbFilePushDto.setFilePath(ossPath);
            ccbFilePushDto.setFullFilePath(ossPath+fileName);
            ccbFilePushDto.setCcbUrl(CcbConfig.reconciliationFileUrl.concat(dateStr));
            ccbFilePushDto.setTransCode(CcbConfig.reconciliationFileTransCode);
            Gson gson = new Gson();
            creatCCBCouponStatusCallBackTask(Constants.TASK_TYPE_CCB_RECONCILIATION_FILE,gson.toJson(ccbFilePushDto));
            //删除文件
            FileUtils.deleteTxtFile(dataDirPath.concat("/").concat(fileName));
        }


        return ReconciliationFileToRes.ok();
    }

    /**
     * 回调入任务表
     * @return
     * @throws BusinessException
     */
    public Boolean creatCCBCouponStatusCallBackTask(Integer taskType,String taskParam){
        try {
            UserDTO userDTO = new UserDTO();
            userDTO.setId(-1L);
            userDTO.setName("system");

            Task task = new Task();
            task.setTaskType(taskType);
            task.setTaskStatus(Constants.TASK_STATUS_PENDING);
            task.setNextRunTime(new Date(new Date().getTime() + 60 * 1000L)); // 60秒后再尝试
            //报文
            task.setTaskParam(taskParam);
            taskService.save(task, userDTO, new Date());
            return Boolean.TRUE;
        }catch (Exception e){
            log.error("creatCCBCouponStatusCallBackTask error:", e);
            return Boolean.FALSE;
        }
    }

    @Override
    public GetChannelCouponStatusForCCBRes getChannelCouponStatusForCCB(GetChannelCouponStatusForCCBReq req) {
        GetChannelCouponStatusInput input = new GetChannelCouponStatusInput();
        input.setCouponCode(req.getCouponCode());
        input.setProductId(req.getProductId());
        input.setUserId(req.getUserId());
        try {
            GetChannelCouponStatusDto dto = this.getChannelCouponStatus(input);
            return dto.toGetChannelCouponStatusForCCBRes();
        } catch (BusinessException e) {
            return GetChannelCouponStatusForCCBRes.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            return GetChannelCouponStatusForCCBRes.failed(CCBCodeEnum.SYSTEM_ERROR.getInnerCode(), "");
        }
    }

    @Override
    public InvalidChannelCouponForCCBRes invalidChannelCouponForCCB(InvalidChannelCouponForCCBReq req) {
        InvalidChannelCouponInput input = new InvalidChannelCouponInput();
        input.setCouponCode(req.getCouponCode());
        input.setProductId(req.getProductId());
        input.setUserId(req.getUserId());
        input.setOperation(req.getOperation());
        input.setOrderId(req.getOrderId());
        try {
            BaseResponse invalidChannelCouponDto = this.invalidChannelCoupon(input);
            if (invalidChannelCouponDto.getCode() == 0) {
                return InvalidChannelCouponForCCBRes.ok();
            }else{
                return InvalidChannelCouponForCCBRes.failed(invalidChannelCouponDto.getCode(), invalidChannelCouponDto.getMessage());
            }
        } catch (Exception e) {
            return InvalidChannelCouponForCCBRes.failed(CCBCodeEnum.SYSTEM_ERROR.getInnerCode(), "");
        }
    }

    @Override
    public StatementOfAccountCallBackForCCBRes statementOfAccountCallBackForCCB(StatementOfAccountCallBackForCCBReq req) {
        StatementOfAccountCallBackInput input = new StatementOfAccountCallBackInput();
        input.setBatchNum(req.getBatchNum());
        input.setResult(req.getResult());
        input.setMsg(req.getMsg());
        try {
            BaseResponse response = this.statementOfAccountCallBack(input);
            if (response.getCode() == 0) {
                return StatementOfAccountCallBackForCCBRes.ok();
            }else{
                return StatementOfAccountCallBackForCCBRes.failed(response.getCode(), response.getMessage());
            }
        } catch (Exception e) {
            return StatementOfAccountCallBackForCCBRes.failed(CCBCodeEnum.SYSTEM_ERROR.getInnerCode(), "");
        }
    }

    @Override
    public BaseResponse handleStatementOfAccountCallBack(StatementOfAccountCallBackInput input) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setCode(0);
        baseResponse.setMessage("成功");
        CcbReconciliationRecord ccbReconciliationRecord = new CcbReconciliationRecord();
        ccbReconciliationRecord.setType(1);
        ccbReconciliationRecord.setBatchNum(input.getBatchNum());
        ccbReconciliationRecord.setReqJson(JSON.toJSONString(input));
        ccbReconciliationRecord.setRespJson(JSON.toJSONString(baseResponse));
        ccbReconciliationRecord.setCreateOperName(getChannelId());
        ccbReconciliationRecord.setUpdateOperName(getChannelId());
        if ("00".equals(input.getResult())) {
            ccbReconciliationRecord.setFlag(0);
        } else {
            ccbReconciliationRecord.setFlag(1);
            try {
                SendEmailRequest sendEmailRequest = new SendEmailRequest();
                sendEmailRequest.setSubject("【建行权益中心】对账结果回调");
                sendEmailRequest.setEmailMsg("建行对账回调结果：有对账失败的记录啦，快来看一下吧，入参:" + JSON.toJSONString(input) + ",tid:" + Trace.currentTraceId());
                for (String email : emails) {
                    sendEmailRequest.setEmail(email);
                    messagePushRestClient.syncSendEmail(sendEmailRequest);
                }
            } catch (Exception e) {
                log.error("tid:{}, 发送邮件失败.", Trace.currentTraceId());
            }
        }
        ccbReconciliationRecord.setFlag(1);
        ccbReconciliationRecordService.save(ccbReconciliationRecord);



        return baseResponse;
    }


    @Override
    public RemoveChannelCouponStockRes removeChannelCouponStock(RemoveChannelCouponStockReq removeChannelCouponStockReq) {
        try {
            List<Long> userCouponSeqs = ccbCouponManagerServiceImpl.removeCouponStock(removeChannelCouponStockReq.getUserCouponSeqsList());
            return RemoveChannelCouponStockRes.newBuilder().addAllUserCouponSeqs(userCouponSeqs).build();
        } catch (BusinessException e) {
            return RemoveChannelCouponStockRes.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("removeChannelCouponStock error", e);
            return RemoveChannelCouponStockRes.failed(CCBCodeEnum.SYSTEM_ERROR.getInnerCode(), "移除库存失败");
        }
    }

    @Override
    public String getApolloInitRedisKey(long mmpThirdCouponId) {
        return "2534_APOLLO_INIT_STOCK:" + getChannelId() + ":" + mmpThirdCouponId;
    }
}
