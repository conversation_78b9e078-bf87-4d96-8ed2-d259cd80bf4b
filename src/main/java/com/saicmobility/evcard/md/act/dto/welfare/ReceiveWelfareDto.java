package com.saicmobility.evcard.md.act.dto.welfare;


import com.saicmobility.evcard.md.mdactservice.api.ReceiveWelfareRes;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReceiveWelfareDto implements Serializable {

    private List<String> keys;

    private String mid;

    private Boolean isNewUser; // 是否为新用户标识，source=3时使用

    public ReceiveWelfareRes toRes() {
        return ReceiveWelfareRes.newBuilder().addAllId(keys).build();
    }
}
