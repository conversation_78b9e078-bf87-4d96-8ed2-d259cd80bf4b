package com.saicmobility.evcard.md.act.util;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class DateUtils {
    public static final DateTimeFormatter DATE_TYPE1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_TYPE2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    public static final DateTimeFormatter DATE_TYPE3 = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    public static final DateTimeFormatter DATE_TYPE4 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter DATE_TYPE5 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter DATE_TYPE6 = DateTimeFormatter.ofPattern("yy-MM-dd-HH-mm-ss");
    public static final DateTimeFormatter DATE_TYPE7 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    public static final DateTimeFormatter DATE_TYPE8 = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter DATE_TYPE9 = DateTimeFormatter.ofPattern("yyyy-M-d H:m:s:S");
    public static final DateTimeFormatter DATE_TYPE10 = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
    public static final DateTimeFormatter DATE_TYPE11 = DateTimeFormatter.ofPattern("yyyy-M-d H:m:s");
    public static final DateTimeFormatter DATE_TYPE12 = DateTimeFormatter.ofPattern("yy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_TYPE13 = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
    public static final DateTimeFormatter DATE_TYPE14 = DateTimeFormatter.ofPattern("MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_TYPE15 = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm");
    public static final DateTimeFormatter DATE_TYPE16 = DateTimeFormatter.ofPattern("MM月dd日 HH:mm");
    public static final DateTimeFormatter DATE_TYPE17 = DateTimeFormatter.ofPattern("yyyy/MM/dd");
    public static final DateTimeFormatter DATE_TYPE18 = DateTimeFormatter.ofPattern("yyMMddHHmmss");
    public static final DateTimeFormatter DATE_TYPE19 = DateTimeFormatter.ofPattern("yyyy-MM");
    public static final DateTimeFormatter DATE_TYPE20 = DateTimeFormatter.ofPattern("yyyyMM");
    public static final DateTimeFormatter DATE_TYPE21 = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
    public static final DateTimeFormatter DATE_TYPE22 = DateTimeFormatter.ofPattern("yyyyMMdd H:mm");
    public static final DateTimeFormatter DATE_TYPE23 = DateTimeFormatter.ofPattern("HH:mm");
    public static final DateTimeFormatter DATE_TYPE24 = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'+08:00'");
    public static final DateTimeFormatter DATE_TYPE25 = DateTimeFormatter.ofPattern("yyMMddHH");
    public static final DateTimeFormatter DATE_TYPE26 = DateTimeFormatter.ofPattern("yyMMdd");
    public static final DateTimeFormatter DATE_TYPE27 = DateTimeFormatter.ofPattern("yyyyMMdd000000");
    public static final DateTimeFormatter DATE_TYPE28 = DateTimeFormatter.ofPattern("yyyyMMdd235959");
    public static ZoneId timeZoneChina = ZoneId.of("Asia/Shanghai");

    public DateUtils() {
    }

    public static LocalDate dateToLocalDate(String date, DateTimeFormatter pattern) {
        return LocalDate.parse(date, pattern);
    }

    public static LocalDate dateToLocalDate(Date date,DateTimeFormatter pattern){
        String dateString =  dateToString(date,pattern);
        return LocalDate.parse(dateString, pattern);
    }

    public static String dateToString(LocalDate date, DateTimeFormatter pattern) {
        return date.format(pattern);
    }

    public static String dateToString(LocalDateTime date, DateTimeFormatter pattern) {
        return date.format(pattern);
    }

    public static String dateToString(Date date, DateTimeFormatter pattern) {
        if (date == null) {
            return "";
        } else {
            LocalDateTime ldt = date.toInstant().atZone(timeZoneChina).toLocalDateTime();
            return ldt.format(pattern);
        }
    }

    public static long getClockMillis() {
        Clock clock = Clock.systemDefaultZone();
        return clock.millis();
    }

    public static String timestampToDateStr(String timestamp, DateTimeFormatter format) {
        return Instant.ofEpochMilli(Long.parseLong(timestamp)).atZone(timeZoneChina).toLocalDateTime().format(format);
    }

    public static String timestampToDateStr(long timestamp, DateTimeFormatter format) {
        return Instant.ofEpochMilli(timestamp).atZone(timeZoneChina).toLocalDateTime().format(format);
    }

    public static LocalDateTime timestampToDate(long timestamp) {
        return Instant.ofEpochMilli(timestamp).atZone(timeZoneChina).toLocalDateTime();
    }

    public static String dateToString(LocalDateTime date) {
        return dateToString(date, DATE_TYPE1);
    }

    public static String getSystemDate(DateTimeFormatter format) {
        return LocalDateTime.now().atZone(timeZoneChina).format(format);
    }

    public static LocalDateTime getSystemDate() {
        return LocalDateTime.now().atZone(timeZoneChina).toLocalDateTime();
    }

    public static LocalDateTime AddMin(LocalDateTime date, int min) {
        return date.plusMinutes((long)min);
    }

    public static LocalDateTime addMin(Date date, int min) {
        LocalDateTime datetime = dateToLocalTimeDate(date);
        return AddMin(datetime, min);
    }

    public static LocalDateTime addSeconds(LocalDateTime date, int second) {
        return date.plusSeconds((long)second);
    }

    public static LocalDateTime addSeconds(Date time, int second) {
        LocalDateTime date = dateToLocalTimeDate(time);
        return addSeconds(date, second);
    }

    public static Date localTimeToDate(LocalDateTime date) {
        return Date.from(date.atZone(timeZoneChina).toInstant());
    }

    public static Date getSystemLocalDate2Date() {
        return localTimeToDate(getSystemDate());
    }

    public static LocalDateTime getDateFromStr(String dateStr, DateTimeFormatter fromType) {
        return LocalDateTime.parse(dateStr, fromType);
    }

    public static LocalDate getLocalDateFromStr(String dateStr, DateTimeFormatter fromType) {
        return LocalDate.parse(dateStr, fromType);
    }

    public static Date getDateFromTimeStr(String dateStr, DateTimeFormatter fromType) {
        return localTimeToDate(LocalDateTime.parse(dateStr, fromType));
    }

    public static Date getDateFromTimeStr(String dateStr, DateTimeFormatter fromType, Date defaultValue) {
        try {
            return localTimeToDate(LocalDateTime.parse(dateStr, fromType));
        } catch (Exception var4) {
            return defaultValue;
        }
    }

    public static Date getDateFromDateStr(String dateStr, DateTimeFormatter fromType) {
        LocalDate localDate = LocalDate.parse(dateStr, fromType);
        return Date.from(localDate.atStartOfDay(timeZoneChina).toInstant());
    }

    public static LocalDateTime addDay(LocalDateTime date, int day) {
        return date.plusDays((long)day);
    }

    public static LocalDateTime addDay(Date time, int day) {
        LocalDateTime date = dateToLocalTimeDate(time);
        return date.plusDays((long)day);
    }

    public static LocalDateTime dateToLocalTimeDate(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        return localDateTime;
    }

    public static String getFormatDate(String dateStr, DateTimeFormatter fromType, DateTimeFormatter toType) {
        return dateToString(getDateFromStr(dateStr, fromType), toType);
    }

    public static long getDateUnix(LocalDateTime dateTime) {
        return dateTime.atZone(timeZoneChina).toEpochSecond();
    }

    public static long getDateUnixMilli(LocalDateTime dateTime) {
        return dateTime.atZone(timeZoneChina).toInstant().toEpochMilli();
    }

    public static boolean isValidDate(String str, DateTimeFormatter formatter) {
        try {
            getDateFromStr(str, formatter);
            return true;
        } catch (DateTimeParseException var3) {
            return false;
        }
    }

    public static boolean isValidOnlyDate(String str, DateTimeFormatter formatter) {
        try {
            getLocalDateFromStr(str, formatter);
            return true;
        } catch (DateTimeParseException var3) {
            return false;
        }
    }

    public static String format(Date date, String pattern) {
        return DateFormatUtils.format(date, pattern);
    }

    public static String format(long millis, String pattern) {
        return DateFormatUtils.format(millis, pattern);
    }

    public static Date parse(String str, String parsePattern) {
        try {
            return org.apache.commons.lang3.time.DateUtils.parseDate(str, new String[]{parsePattern});
        } catch (ParseException var3) {
            var3.printStackTrace();
            return null;
        }
    }

    public static boolean isSameDay(Date day1, Date day2) {
        String ds1 = dateToString(day1, DATE_TYPE8);
        String ds2 = dateToString(day2, DATE_TYPE8);
        return ds1.equals(ds2);
    }

    public static boolean isSameDay(long day1, long day2) {
        String ds1 = dateToString(new Date(day1), DATE_TYPE8);
        String ds2 = dateToString(new Date(day2), DATE_TYPE8);
        return ds1.equals(ds2);
    }

    public static int dayOfWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(7) - 1;
        if (w < 0) {
            w = 0;
        }

        return w;
    }

    public static Date monthFirstDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(5, 1);
        return calendar.getTime();
    }

    public static Date nextMonthFirstDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(5, 1);
        calendar.add(2, 1);
        return calendar.getTime();
    }

    public static Date getStartOfDay(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date getEndOfDay(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        return Date.from(endOfDay.atZone(timeZoneChina).toInstant());
    }

    public static Date getEndOfDay(String dateStr, DateTimeFormatter fromType) {
        LocalDate localDate = LocalDate.parse(dateStr, fromType);
        LocalDateTime endOfDay = localDate.atStartOfDay().with(LocalTime.MAX);
        return Date.from(endOfDay.atZone(timeZoneChina).toInstant());
    }

    public static Date getStartOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(2, 0);
        cal.set(5, 1);
        cal.set(11, 0);
        cal.set(12, 0);
        cal.set(13, 0);
        return cal.getTime();
    }

    public static Date getEndOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(2, 1);
        cal.set(5, 0);
        cal.set(11, 23);
        cal.set(12, 59);
        cal.set(13, 59);
        return cal.getTime();
    }

    public static Date getStartOfWeek(Date date) {
        date = getDateFromDateStr(dateToString(date, DATE_TYPE8), DATE_TYPE8);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayWeek = cal.get(7);
        if (1 == dayWeek) {
            cal.add(5, -1);
        }

        cal.setFirstDayOfWeek(2);
        int day = cal.get(7);
        cal.add(5, cal.getFirstDayOfWeek() - day);
        return cal.getTime();
    }

    public static Date getEndOfWeek() {
        Calendar currentDate = Calendar.getInstance();
        currentDate.setFirstDayOfWeek(2);
        currentDate.set(11, 23);
        currentDate.set(12, 59);
        currentDate.set(13, 59);
        currentDate.set(7, 1);
        return currentDate.getTime();
    }

    public static Long dayBetween(Date date1, Date date2) {
        LocalDate ld1 = LocalDateTime.ofInstant(Instant.ofEpochMilli(date1.getTime()), ZoneId.systemDefault()).toLocalDate();
        LocalDate ld2 = LocalDateTime.ofInstant(Instant.ofEpochMilli(date2.getTime()), ZoneId.systemDefault()).toLocalDate();
        return ld1.until(ld2, ChronoUnit.DAYS);
    }

    public static Long secondBetween(Date date1, Date date2) {
        LocalDateTime ld1 = LocalDateTime.ofInstant(Instant.ofEpochMilli(date1.getTime()), ZoneId.systemDefault());
        LocalDateTime ld2 = LocalDateTime.ofInstant(Instant.ofEpochMilli(date2.getTime()), ZoneId.systemDefault());
        return ld1.until(ld2, ChronoUnit.SECONDS);
    }

    public static Date getDateFromStr(String dateStr, String fromType) {
        try {
            DateFormat dateFromFmt = new SimpleDateFormat(fromType);
            return dateFromFmt.parse(dateStr);
        } catch (ParseException var3) {
            var3.printStackTrace();
            return null;
        }
    }

    public static List<LocalDate> getDatesBetween(LocalDate startDate,LocalDate endDate,DateTimeFormatter pattern){
        List<LocalDate> dates = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            dates.add(date);
        }
        return dates;
    }
    public static List<String> getDatesBetweenStr(LocalDate startDate,LocalDate endDate,DateTimeFormatter pattern) {
        List<String> dates = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            dates.add(date.toString());
        }
        return dates;
    }

}
