package com.saicmobility.evcard.md.act.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CcbConfig {

    //***************以下为建行对接******************
    //供应商平台ID，一般与请求头Channel-Id一致
    public static final String CCB_PLATFORM_ID = "evcard";
    public static final String CCB_CHANNEL_ID = "evcard";

    public static String ccbBasePath;
    public static String ccbAesKey;
    // 我方私钥

    public static String ccbRsaPrivateKey;
    //建行侧公钥

    public static String ccbRsaPublicKey;



    public static String callBackCouponStatusUrl;
    //回推优惠券状态交易码

    public static String callBackCouponStatusCode;
    //推送对账文件路径

    public static String reconciliationFileUrl;
    //推送对账文件交易码

    public static String reconciliationFileTransCode;

    @Value("${ccb.basePath}")
    public void setCcbBasePath(String ccbBasePath) {
        CcbConfig.ccbBasePath = ccbBasePath;
    }

    @Value("${ccb.aesKey}")
    public void setCcbAesKey(String ccbAesKey) {
        CcbConfig.ccbAesKey = ccbAesKey;
    }
    @Value("${ccb.rsaPrivateKey}")
    public void setCcbRsaPrivateKey(String ccbRsaPrivateKey) {
        CcbConfig.ccbRsaPrivateKey = ccbRsaPrivateKey;
    }
    @Value("${ccb.rsaPublicKey}")
    public void setCcbRsaPublicKey(String ccbRsaPublicKey) {
        CcbConfig.ccbRsaPublicKey = ccbRsaPublicKey;
    }
    @Value("${ccb.callBackCouponStatusUrl:/api/P5STDR002}")
    public void setCallBackCouponStatusUrl(String callBackCouponStatusUrl) {
        CcbConfig.callBackCouponStatusUrl = callBackCouponStatusUrl;
    }
    @Value("${ccb.callBackCouponStatusCode:P5STDR002}")
    public void setCallBackCouponStatusCode(String callBackCouponStatusCode) {
        CcbConfig.callBackCouponStatusCode = callBackCouponStatusCode;
    }
    @Value("${ccb.reconciliationFileUrl:/api/P5STDE001?batchNum=}")
    public void setReconciliationFileUrl(String reconciliationFileUrl) {
        CcbConfig.reconciliationFileUrl = reconciliationFileUrl;
    }
    @Value("${ccb.reconciliationFileTransCode:P5STDE001}")
    public void setReconciliationFileTransCode(String reconciliationFileTransCode) {
        CcbConfig.reconciliationFileTransCode = reconciliationFileTransCode;
    }

    public static String getCcbAesKey() {
        return ccbAesKey;
    }

    public static String getCcbRsaPrivateKey() {
        return ccbRsaPrivateKey;
    }

    public static String getCcbRsaPublicKey() {
        return ccbRsaPublicKey;
    }

    public static String getCallBackCouponStatusUrl() {
        return callBackCouponStatusUrl;
    }

    public static String getCallBackCouponStatusCode() {
        return callBackCouponStatusCode;
    }

    public static String getReconciliationFileUrl() {
        return reconciliationFileUrl;
    }

    public static String getReconciliationFileTransCode() {
        return reconciliationFileTransCode;
    }

    public static String getCcbBasePath() {
        return ccbBasePath;
    }
}
