package com.saicmobility.evcard.md.act.enums.market;

public enum ActType {
    FULLREDUCTION(1, "满减"),
    DISCOUNT(2, "打折"),
    DECREASE(3, "减至");

    private Integer type;
    private String msg;

    ActType(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }
}
