package com.saicmobility.evcard.md.act.enums.coupon;

/**
 * 优惠券 兑换码 状态
 *兑换码状态 0=未兑换 1=已兑换 2=已作废 3=已过期
 *
 */
public enum CouponCodeStatusEnum {
    NOT_EXCHANGE(0, "未兑换"),
    HAS_EXCHANGED(1, "已兑换"),
    DEPRECATED(2, "已作废"),
    EXPIRED(3, "已过期"),
    ;
    private Integer type;
    private String msg;

    CouponCodeStatusEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }


    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

}
