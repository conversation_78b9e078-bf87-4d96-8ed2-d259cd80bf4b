package com.saicmobility.evcard.md.act.enums.channelcoupon;
public enum CCBExceptionEnum {
    SUCCESS("0000","成功"),
    REQUEST_BODY_MISS_KEY("2102","报文体缺失关键字段"),

    COUPON_CODE_HAS_EXCHANGED("3905","兑换码到账不再支持作废"),
    COUPON_CODE_HAS_EXPIRED("3901","优惠券当前状态是已失效,不再支持作废"),

    COUPON_CODE_DEPRECATED_FAIL("4000","优惠券作废失败"),
    ;

    private String code;
    private String desc;

    CCBExceptionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
