package com.saicmobility.evcard.md.act.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * <AUTHOR> chennian.
 * @Date ：Created in 17:52 2021/11/30
 */
@Configuration
public class SiacDataSourceConfiguration {

    public static final String MAPPER_LOCATION = "classpath:mapper/siac/*.xml";

    @Bean("siacDataSource")
    @ConfigurationProperties("spring.datasource.druid.siac")
    public DataSource siacDataSource(){
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("siacTransactionManager")
    public DataSourceTransactionManager siacTransactionManager() {
        return new DataSourceTransactionManager(siacDataSource());
    }

    @Bean("siacSqlSessionFactory")
    public SqlSessionFactory siacSqlSessionFactory(@Qualifier("siacDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION));
        return sqlSessionFactoryBean.getObject();
    }

    @Bean("siacSqlSessionTemplate")
    public SqlSessionTemplate siacSqlSessionTemplate(@Qualifier("siacDataSource") DataSource dataSource) throws Exception {
        return new SqlSessionTemplate(siacSqlSessionFactory(dataSource));
    }

}
