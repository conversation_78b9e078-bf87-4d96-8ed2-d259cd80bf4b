package com.saicmobility.evcard.md.act.dto.coupon.channel;

import lombok.Data;

@Data
public class CouponStockDto {

    private long mmpThirdCouponId;

    /**
     * 券配置的张数
     */
    private long totalNum;

    /**
     * 当前可用的库存余量，专指在系统层面上已就绪，可供客户直接领取的权益数量
     */
    private long availableNum;

    /**
     * 已同步给渠道的兑换码张数, 渠道发放的数量(累计已核销数量)
     */
    private long issuedNum;


    /**
     * 库存信息刷新的时间(yyyyMMddHHmmss)。标志着本次响应返回数据的统计时间
     */
    private String refreshTime;
}
