package com.saicmobility.evcard.md.act.entity.siac;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 建行对账文件处理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CcbReconciliationRecord对象", description="建行对账文件处理表")
public class CcbReconciliationRecord extends Model<CcbReconciliationRecord> {

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "0=推送 1=回调")
    private Integer type;

    @ApiModelProperty(value = "文件批次号")
    private String batchNum;

    @ApiModelProperty(value = "请求报文")
    private String reqJson;

    @ApiModelProperty(value = "返回报文")
    private String respJson;

    @ApiModelProperty(value = "0：成功 1：失败")
    private Integer flag;

    @ApiModelProperty(value = "状态（0=正常 1=已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人名称")
    private String createOperName;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateOperId;

    @ApiModelProperty(value = "更新人名称")
    private String updateOperName;


}
