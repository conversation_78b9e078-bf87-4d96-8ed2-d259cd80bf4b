package com.saicmobility.evcard.md.act.service.welfare;

import com.extracme.evcard.rpc.exception.BusinessException;
import com.saicmobility.evcard.md.act.dto.welfare.CouponWelfareInfo;
import com.saicmobility.evcard.md.act.dto.welfare.GetWelfareInfoInput;
import com.saicmobility.evcard.md.act.dto.welfare.ReceiveWelfareDto;
import com.saicmobility.evcard.md.act.dto.welfare.ReceiveWelfareInput;

import java.util.List;

public interface ICouponWelfareService {

    List<CouponWelfareInfo> getCouponWelfareDetailInfo(GetWelfareInfoInput input);

    ReceiveWelfareDto receiveCouponWelfare(ReceiveWelfareInput input) throws BusinessException;
}
