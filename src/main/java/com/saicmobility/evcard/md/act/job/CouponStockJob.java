package com.saicmobility.evcard.md.act.job;

import com.saicmobility.evcard.md.act.domain.coupon.CouponConditionDto;
import com.saicmobility.evcard.md.act.entity.MmpThirdCoupon;
import com.saicmobility.evcard.md.act.entity.siac.MmpPackNightActivity;
import com.saicmobility.evcard.md.act.mapper.siac.MmpPackNightActivityMapper;
import com.saicmobility.evcard.md.act.mapper.siac.UserCouponListMapper;
import com.saicmobility.evcard.md.act.service.coupun.IChannelCouponManagerService;
import com.saicmobility.evcard.md.act.service.coupun.IMmpThirdCouponService;
import com.saicmobility.evcard.md.act.service.impl.coupon.channel.ccb.CCBCouponManagerServiceImpl;
import com.saicmobility.evcard.md.act.util.RedisLock;
import com.saicmobility.evcard.md.act.util.RedisLockUtil;
import com.saicmobility.evcard.md.act.util.RedisUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 刷新库存
 */
@Slf4j
@Component
@JobHandler("CouponStockJob")
public class CouponStockJob extends IJobHandler {

    @Autowired
    private CCBCouponManagerServiceImpl ccbCouponManagerServiceImpl;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("tid:{}, CouponStockJob start", Trace.currentTraceId());
        List<Long> mmpThirdCouponIds = new ArrayList<>();
        if (StringUtils.isBlank(s)) {
            mmpThirdCouponIds = ccbCouponManagerServiceImpl.getMmpThirdCouponIds();
        }
        else {
            mmpThirdCouponIds = Arrays.stream(s.split(",")).map(Long::parseLong).collect(Collectors.toList());
        }

        for (Long mmpThirdCouponId : mmpThirdCouponIds) {
            log.info("tid:{}, CouponStockJob running.mmpThirdCouponId:{}", Trace.currentTraceId(), mmpThirdCouponId);
            ccbCouponManagerServiceImpl.initCouponStock(mmpThirdCouponId);
        }
        log.info("tid:{}, CouponStockJob end", Trace.currentTraceId());

        return ReturnT.SUCCESS;
    }
}
