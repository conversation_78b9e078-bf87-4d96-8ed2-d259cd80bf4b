package com.saicmobility.evcard.md.act.enums;

public enum ChannelType {
    XIANXIA(1, "线下"),
    XIECHENG(2, "携程"),
    FEIZHU(3, "飞猪"),
    HALUO(4, "哈罗"),
    ZUZUCHE(5, "租租车"),
    WUKONG(6, "悟空"),
    XIECHENGFENXIAO(10, "携程分销"),
    NULL(-1, "");

    private Integer type;
    private String msg;

    ChannelType(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }
}
