spring.profiles.active=sit
spring.application.name=mdactservice
spring.main.banner-mode=off
spring.main.web-application-type=none
git.name=md-act-service
service.version=md-act-service build @ 20220606

#krpc
krpc.enabled=true
krpc.registry.type=consul
#krpc.registry.addrs=************:8500
#krpc.registry.aclToken=
krpc.application.dataDir=/opt/data/${git.name}
krpc.monitor.selfCheckPort=12534
#krpc.webserver.port=32500
krpc.server.port = 22534
krpc.server.threads=100
krpc.server.maxThreads=200
krpc.service.interfaceName=com.saicmobility.evcard.md.mdactservice.api.MdActService

krpc.referers[0].interfaceName=com.saicmobility.evcard.md.mdstoreservice.api.MdStoreService
krpc.referers[0].timeout=10000

krpc.referers[1].interfaceName=com.saicmobility.evcard.md.mdgoodsservice.api.MdGoodsService
krpc.referers[1].timeout=10000

krpc.referers[2].interfaceName=com.saicmobility.evcard.md.mduserservice.api.MdUserService
krpc.referers[2].timeout=10000

krpc.referers[3].interfaceName=com.saicmobility.evcard.md.mdorderservice.api.MdOrderService
krpc.referers[3].timeout=10000

krpc.referers[4].interfaceName=com.saicmobility.evcard.md.mdstockservice.api.MdStockService
krpc.referers[4].timeout=10000

krpc.referers[5].interfaceName=com.saicmobility.evcard.md.mdempservice.api.MdEmpService
krpc.referers[5].timeout=10000

#datasource
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.max-active=100
spring.datasource.druid.initial-size=1
#spring.datasource.druid.filters=dbauditlog
#spring.datasource.druid.max-wait=10000
spring.datasource.druid.min-idle=1
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=select 'x'
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false

#redis
#spring.redis.port=6379
#spring.redis.timeout=10000
#spring.redis.database=0
#spring.redis.host=************
#spring.redis.jedis.pool.max-active=20
#spring.redis.jedis.pool.max-idle=5
#spring.redis.jedis.pool.min-idle=2
#spring.redis.jedis.pool.max-wait=50


#redis
#spring.redis.timeout=10000
#spring.redis.database=0
#spring.redis.jedis.pool.max-active=20
#spring.redis.jedis.pool.max-idle=5
#spring.redis.jedis.pool.min-idle=2



#mybatis com.saicmobility.evcard.md.user
#mybatis-plus.mapper-locations=classpath*:com/saicmobility/evcard/md/act/mapper/xml/*.xml
#mybatis-plus.mapper-locations=classpath:mapper/act/*Mapper.xml
mybatis-plus.configuration.cache-enabled=true
mybatis-plus.configuration.lazy-loading-enabled=false
mybatis-plus.configuration.multiple-result-sets-enabled=true
mybatis-plus.configuration.use-column-label=true
mybatis-plus.configuration.use-generated-keys=true
mybatis-plus.configuration.auto-mapping-behavior=partial
mybatis-plus.configuration.default-executor-type=simple
mybatis-plus.configuration.default-statement-timeout=25
mybatis-plus.configuration.safe-row-bounds-enabled=false
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.local-cache-scope=session
mybatis-plus.configuration.jdbc-type-for-null=other
mybatis-plus.configuration.lazy-load-trigger-methods=equals,clone,hashCode,toString
mybatis-plus.configuration.logPrefix=dao.


#apollo
app.id= ${git.name}
apollo.cluster=evcard
apollo.bootstrap.enabled=true
apollo.bootstrap.namespaces=application
apollo.bootstrap.eagerLoad.enabled=true
krpc.application.dynamicRoutePlugin=consul

##elasticjob
#elasticjob.regCenter.serverLists=**************:2181
#elasticjob.regCenter.namespace=elastic-job

##logger
logging.config=classpath:logback.xml
logging.file.max-history=30
logging.file.max-size=100MB
logging.queue.size=512
logging.level.root=debug


#restTemplate
spring.resttemplate.connectTimeout=3000
spring.resttemplate.readTimeout=5000
spring.resttemplate.usePool=false
spring.resttemplate.maxTotalConnect=128
spring.resttemplate.maxConnectPerRoute=32
spring.resttemplate.errorCountToAlarm=5
spring.resttemplate.logAllHeaders=false
spring.resttemplate.logHeaderNames=X-EXP-COOKIE
envconfig.httpLogMaxChars=500
envconfig.httpSlowMillis=1000


#dubbo-rest-proxy routs
evcard.inner.api.baseUrl=http://inner-dev.evcard.vip/
evcard.inner.api.dubboProxyPath=md-rest/api/
#coupon
evcard.inner.api.orderCoupons=coupon/orderCoupons
evcard.inner.api.checkOrderCoupon=coupon/checkOrderCoupon
evcard.inner.api.useCoupon=coupon/useCoupon
evcard.inner.api.getCouponModelByCouponSeq=coupon/getCouponModelByCouponSeq
evcard.inner.api.getCouponModelListByCouponSeq=coupon/getCouponModelListByCouponSeq
evcard.inner.api.getCouponDes=coupon/getCouponDes
evcard.inner.api.getCouponModelView=coupon/getCouponModelByCouponSeq
evcard.inner.api.getCouponModelListView=coupon/getCouponModelListByCouponSeq
evcard.inner.api.batchOrderCoupons=coupon/batchOrderCoupons
evcard.inner.api.exchange=coupon/exchange
#activity
evcard.inner.api.offerThirdCoupons=activity/offerThirdCoupons
evcard.inner.api.getMmpCouponList=activity/getMmpCouponList
evcard.inner.api.syncSendEmail=invoice/syncSendEmail

xxl.job.executor.appname=md-act-service
xxl.job.executor.ip=
xxl.job.executor.port=9999
xxl.job.accessToken=
xxl.job.executor.logpath=/app/logs/${git.name}/log/xxl.log
xxl.job.executor.logretentiondays=-1

#\u7ACB\u51CF\u6D3B\u52A8\u56FE\u7247\u8DEF\u5F84\u524D\u7F00
oss.path.prefix=http://evcard.oss-cn-shanghai.aliyuncs.com/



