<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.iss.SuixiangCardCdkConfigDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.iss.SuixiangCardCdkConfigDetail">
        <id column="id" property="id" />
        <result column="card_cdk_config_id" property="cardCdkConfigId" />
        <result column="card_base_id" property="cardBaseId" />
        <result column="card_rent_id" property="cardRentId" />
        <result column="card_price_id" property="cardPriceId" />
        <result column="quantity" property="quantity" />
        <result column="act_desc" property="actDesc" />
        <result column="is_deleted" property="isDeleted" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, card_cdk_config_id, card_base_id, card_rent_id, card_price_id, quantity, act_desc, is_deleted, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
    </sql>

</mapper>
