<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.iss.SuixiangCardBaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.iss.SuixiangCardBase">
        <id column="id" property="id" />
        <result column="card_name" property="cardName" />
        <result column="org_id" property="orgId" />
        <result column="city_id" property="cityId" />
        <result column="advance_notice_time" property="advanceNoticeTime" />
        <result column="sale_start_time" property="saleStartTime" />
        <result column="sale_end_time" property="saleEndTime" />
        <result column="effective_days" property="effectiveDays" />
        <result column="valid_days_type" property="validDaysType" />
        <result column="init_stock" property="initStock" />
        <result column="stock" property="stock" />
        <result column="sales" property="sales" />
        <result column="display_flag" property="displayFlag" />
        <result column="single_order_duration" property="singleOrderDuration" />
        <result column="style_type" property="styleType" />
        <result column="back_url" property="backUrl" />
        <result column="rules" property="rules" />
        <result column="holiday_available" property="holidayAvailable" />
        <result column="unavailable_date" property="unavailableDate" />
        <result column="card_status" property="cardStatus" />
        <result column="purchase_limit_num" property="purchaseLimitNum" />
        <result column="merge_flag" property="mergeFlag" />
        <result column="vehicle_brand_ids" property="vehicleBrandIds" />
        <result column="landing_page_flag" property="landingPageFlag" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, card_name, org_id, city_id, advance_notice_time, sale_start_time, sale_end_time, effective_days, valid_days_type, init_stock, stock, sales, display_flag, single_order_duration, style_type, back_url, rules, holiday_available, unavailable_date, card_status, purchase_limit_num, merge_flag, vehicle_brand_ids, landing_page_flag, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name, is_deleted
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from suixiang_card_base
        where id = #{id,jdbcType=BIGINT}
    </select>
</mapper>
