<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.iss.SuixiangCardCdkConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.iss.SuixiangCardCdkConfig">
        <id column="id" property="id" />
        <result column="card_base_id" property="cardBaseId" />
        <result column="act_name" property="actName" />
        <result column="card_name" property="cardName" />
        <result column="total_quantity" property="totalQuantity" />
        <result column="create_qr_code_flag" property="createQrCodeFlag" />
        <result column="is_deleted" property="isDeleted" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, card_base_id, act_name, card_name, total_quantity, create_qr_code_flag, is_deleted, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
    </sql>

</mapper>
