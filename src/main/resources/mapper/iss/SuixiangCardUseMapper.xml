<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.iss.SuixiangCardUseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.iss.SuixiangCardUse">
        <id column="id" property="id" />
        <result column="card_base_id" property="cardBaseId" />
        <result column="card_price_id" property="cardPriceId" />
        <result column="user_id" property="userId" />
        <result column="purchase_id" property="purchaseId" />
        <result column="card_type" property="cardType" />
        <result column="card_name" property="cardName" />
        <result column="card_status" property="cardStatus" />
        <result column="start_time" property="startTime" />
        <result column="expires_time" property="expiresTime" />
        <result column="total_order" property="totalOrder" />
        <result column="total_discount_amount" property="totalDiscountAmount" />
        <result column="init_days" property="initDays" />
        <result column="available_days" property="availableDays" />
        <result column="used_days" property="usedDays" />
        <result column="frozen_days" property="frozenDays" />
        <result column="active_flag" property="activeFlag" />
        <result column="cdkey" property="cdkey" />
        <result column="merge_flag" property="mergeFlag" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, card_base_id, card_price_id, user_id, purchase_id, card_type, card_name, card_status, start_time, expires_time, total_order, total_discount_amount, init_days, available_days, used_days, frozen_days, active_flag, cdkey, merge_flag, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name, is_deleted
    </sql>

</mapper>
