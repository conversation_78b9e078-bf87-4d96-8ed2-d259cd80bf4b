<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.iss.SuixiangCardPurchaseRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.iss.SuixiangCardPurchaseRecord">
        <id column="id" property="id" />
        <result column="org_id" property="orgId" />
        <result column="user_id" property="userId" />
        <result column="user_mobile" property="userMobile" />
        <result column="card_base_id" property="cardBaseId" />
        <result column="card_name" property="cardName" />
        <result column="card_rent_id" property="cardRentId" />
        <result column="card_price_id" property="cardPriceId" />
        <result column="cdk_id" property="cdkId" />
        <result column="issue_type" property="issueType" />
        <result column="obtain_type" property="obtainType" />
        <result column="payment_status" property="paymentStatus" />
        <result column="quantity" property="quantity" />
        <result column="pay_time" property="payTime" />
        <result column="real_amount" property="realAmount" />
        <result column="out_trade_seq" property="outTradeSeq" />
        <result column="card_use_id" property="cardUseId" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="order_seq" property="orderSeq" />
        <result column="pay_order_no" property="payOrderNo" />
        <result column="remind_status" property="remindStatus" />
        <result column="cancel_time" property="cancelTime" />
        <result column="merge_pay_origin" property="mergePayOrigin" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
        <result column="is_deleted" property="isDeleted" />
        <result column="card_use_ids" property="cardUseIds" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, org_id, user_id, user_mobile, card_base_id, card_name, card_rent_id, card_price_id, cdk_id, issue_type, obtain_type, payment_status, quantity, pay_time, real_amount, out_trade_seq, card_use_id, start_time, end_time, order_seq, pay_order_no, remind_status, cancel_time, merge_pay_origin, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name, is_deleted, card_use_ids
    </sql>

</mapper>
