<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.iss.SuixiangCardCdkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.iss.SuixiangCardCdk">
        <id column="id" property="id" />
        <result column="card_cdk_config_detail_id" property="cardCdkConfigDetailId" />
        <result column="card_base_id" property="cardBaseId" />
        <result column="card_rent_id" property="cardRentId" />
        <result column="card_price_id" property="cardPriceId" />
        <result column="is_activated" property="isActivated" />
        <result column="activated_mid" property="activatedMid" />
        <result column="activated_user_name" property="activatedUserName" />
        <result column="activated_user_mobile" property="activatedUserMobile" />
        <result column="activated_time" property="activatedTime" />
        <result column="card_use_id" property="cardUseId" />
        <result column="wechat_cdk_qr_url" property="wechatCdkQrUrl" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, card_cdk_config_detail_id, card_base_id, card_rent_id, card_price_id, is_activated, activated_mid, activated_user_name, activated_user_mobile, activated_time, card_use_id, wechat_cdk_qr_url, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name, is_deleted
    </sql>

    <select id="getCdkThirdSaleDto"
            resultType="com.saicmobility.evcard.md.act.dto.suixiangcard.SuixiangCardCdkThirdSaleDto">
        SELECT
        c.id AS cdkId,
        ccd.id AS cdkConfigDetailId,
        cc.id AS cdkConfigId,
        cc.purpose AS cdkPurpose,
        ccd.third_sales_price AS thirdSalesPrice
        FROM
        suixiang_card_cdk c
        LEFT JOIN suixiang_card_cdk_config_detail ccd ON c.card_cdk_config_detail_id = ccd.id
        LEFT JOIN suixiang_card_cdk_config cc ON cc.id = ccd.card_cdk_config_id
        WHERE
        c.id = #{cdkId}
    </select>

</mapper>
