<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.iss.MmpThirdCouponMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.MmpThirdCoupon">
        <id column="id" property="id" />
        <result column="third_activity_id" property="thirdActivityId" />
        <result column="coupon_seq" property="couponSeq" />
        <result column="offer_quantity" property="offerQuantity" />
        <result column="coupon_name" property="couponName" />
        <result column="EFFECTIVE_DAYS" property="effectiveDays" />
        <result column="VALID_DAYS" property="validDays" />
        <result column="START_DATE" property="startDate" />
        <result column="EXPIRES_DATE" property="expiresDate" />
        <result column="VALID_TIME_TYPE" property="validTimeType" />
        <result column="org_id" property="orgId" />
        <result column="coupon_type" property="couponType" />
        <result column="coupon_limit" property="couponLimit" />
        <result column="coupon_target" property="couponTarget" />
        <result column="offer_timing" property="offerTiming" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, third_activity_id, coupon_seq, offer_quantity, coupon_name, EFFECTIVE_DAYS, VALID_DAYS, START_DATE, EXPIRES_DATE, VALID_TIME_TYPE, org_id, coupon_type, coupon_limit, coupon_target, offer_timing
    </sql>

</mapper>
