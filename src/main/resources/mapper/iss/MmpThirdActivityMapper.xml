<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.iss.MmpThirdActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.iss.MmpThirdActivity">
        <id column="id" property="id" />
        <result column="day_voucher_limit" property="dayVoucherLimit" />
        <result column="total_voucher_limit" property="totalVoucherLimit" />
        <result column="day_offer_limit" property="dayOfferLimit" />
        <result column="total_offer_limit" property="totalOfferLimit" />
        <result column="pre_total_amount" property="preTotalAmount" />
        <result column="activity_channel_key" property="activityChannelKey" />
        <result column="activity_channel" property="activityChannel" />
        <result column="e_amount_greater" property="eAmountGreater" />
        <result column="e_amount_less" property="eAmountLess" />
        <result column="e_offer_number" property="eOfferNumber" />
        <result column="misc_desc" property="miscDesc" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
        <result column="period" property="period" />
        <result column="count_limit" property="countLimit" />
        <result column="img_url" property="imgUrl" />
        <result column="activity_url" property="activityUrl" />
        <result column="packages_id" property="packagesId" />
        <result column="brand_name" property="brandName" />
        <result column="official_web_address" property="officialWebAddress" />
        <result column="service_telephone" property="serviceTelephone" />
        <result column="offer_timing" property="offerTiming" />
        <result column="order_duration" property="orderDuration" />
        <result column="boot_download_layer" property="bootDownloadLayer" />
        <result column="activity_rules" property="activityRules" />
        <result column="poster_mini_program" property="posterMiniProgram" />
        <result column="activity_title" property="activityTitle" />
        <result column="activity_subtitle" property="activitySubtitle" />
        <result column="background_color" property="backgroundColor" />
        <result column="trans_type" property="transType" />
        <result column="coupon_owner" property="couponOwner" />
        <result column="owner_id" property="ownerId" />
        <result column="pre_coupon_total_amount" property="preCouponTotalAmount" />
        <result column="pre_nonrevenue_coupon_total_amount" property="preNonrevenueCouponTotalAmount" />
        <result column="order_amount" property="orderAmount" />
        <result column="return_days_of_week" property="returnDaysOfWeek" />
        <result column="pickup_start_time" property="pickupStartTime" />
        <result column="pickup_end_time" property="pickupEndTime" />
        <result column="return_start_time" property="returnStartTime" />
        <result column="return_end_time" property="returnEndTime" />
        <result column="order_reward_type" property="orderRewardType" />
        <result column="withdraw_amount_min" property="withdrawAmountMin" />
        <result column="reward_tax_rate" property="rewardTaxRate" />
        <result column="reward_days_limit" property="rewardDaysLimit" />
        <result column="reward_amount_rate" property="rewardAmountRate" />
        <result column="reward_amount_max" property="rewardAmountMax" />
        <result column="order_num_limit" property="orderNumLimit" />
        <result column="order_pay_interval" property="orderPayInterval" />
        <result column="order_amount_limit" property="orderAmountLimit" />
        <result column="rent_methods" property="rentMethods" />
        <result column="rent_method_group" property="rentMethodGroup" />
        <result column="link_type" property="linkType" />
        <result column="link_url" property="linkUrl" />
        <result column="link_app_id" property="linkAppId" />
        <result column="close_float_img" property="closeFloatImg" />
        <result column="use_methods" property="useMethods" />
        <result column="coupon_way" property="couponWay" />
        <result column="qr_code_zip_url" property="qrCodeZipUrl" />
        <result column="cdk_start_time" property="cdkStartTime" />
        <result column="cdk_expires_time" property="cdkExpiresTime" />
        <result column="one_code_mul_coupon_flag" property="oneCodeMulCouponFlag" />
        <result column="coupon_code_num" property="couponCodeNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, day_voucher_limit, total_voucher_limit, day_offer_limit, total_offer_limit, pre_total_amount, activity_channel_key, activity_channel, e_amount_greater, e_amount_less, e_offer_number, misc_desc, status, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name, period, count_limit, img_url, activity_url, packages_id, brand_name, official_web_address, service_telephone, offer_timing, order_duration, boot_download_layer, activity_rules, poster_mini_program, activity_title, activity_subtitle, background_color, trans_type, coupon_owner, owner_id, pre_coupon_total_amount, pre_nonrevenue_coupon_total_amount, order_amount, return_days_of_week, pickup_start_time, pickup_end_time, return_start_time, return_end_time, order_reward_type, withdraw_amount_min, reward_tax_rate, reward_days_limit, reward_amount_rate, reward_amount_max, order_num_limit, order_pay_interval, order_amount_limit, rent_methods, rent_method_group, link_type, link_url, link_app_id, close_float_img, use_methods, coupon_way, qr_code_zip_url, cdk_start_time, cdk_expires_time, one_code_mul_coupon_flag, coupon_code_num
    </sql>

</mapper>
