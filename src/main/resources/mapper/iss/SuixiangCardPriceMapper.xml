<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.iss.SuixiangCardPriceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.iss.SuixiangCardPrice">
        <id column="id" property="id" />
        <result column="card_base_id" property="cardBaseId" />
        <result column="card_rent_id" property="cardRentId" />
        <result column="sales_price" property="salesPrice" />
        <result column="underline_price" property="underlinePrice" />
        <result column="car_model_group" property="carModelGroup" />
        <result column="car_model_ids" property="carModelIds" />
        <result column="sales" property="sales" />
        <result column="landing_page_pic_url" property="landingPagePicUrl" />
        <result column="landing_page_head_pic_url" property="landingPageHeadPicUrl" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, card_base_id, card_rent_id, sales_price, underline_price, car_model_group, car_model_ids, sales, landing_page_pic_url, landing_page_head_pic_url, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name, is_deleted
    </sql>

    <!-- 根据基础表id 查询随享卡基础信息 -->
    <select id="selectInfoByBaseId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from suixiang_card_price
        where is_deleted=0
        and card_base_id = #{cardBaseId}
        order by id asc
    </select>
</mapper>
