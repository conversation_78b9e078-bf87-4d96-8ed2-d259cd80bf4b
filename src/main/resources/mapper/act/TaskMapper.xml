<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.act.TaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.Task">
        <id column="id" property="id" />
        <result column="task_type" property="taskType" />
        <result column="task_param" property="taskParam" />
        <result column="task_status" property="taskStatus" />
        <result column="next_run_time" property="nextRunTime" />
        <result column="last_run_time" property="lastRunTime" />
        <result column="last_run_msg" property="lastRunMsg" />
        <result column="failed_times" property="failedTimes" />
        <result column="is_deleted" property="isDeleted" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_type, task_param, task_status, next_run_time, last_run_time, last_run_msg, failed_times, is_deleted, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
    </sql>

</mapper>
