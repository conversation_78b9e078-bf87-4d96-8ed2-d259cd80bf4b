<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.act.ReduceActivityParticipateLogMapper">


    <select id="searchActivityParticipateLog"
            resultType="com.saicmobility.evcard.md.act.entity.ReduceActivityParticipateLog">
        select * from t_reduce_activity_participate_log
        where store_id = #{storeId}
        and activity_id = #{activityId}
        <if test="goodsModelId > 0">
            AND goods_model_id = #{goodsModelId}
        </if>
        and is_deleted = 0 order by id desc
    </select>

    <select id="searchAllParticipateLog" resultType="com.saicmobility.evcard.md.act.entity.OperateLog">
        SELECT * FROM(	SELECT
				id, create_time,create_oper_name,create_oper_org_name,operate_content
				FROM t_operate_log as log
				WHERE log.foreign_id = #{foreignId}
				and log.operate_type = 2
                and log.is_deleted = 0

        <if test="storeId > 0">
            UNION
            SELECT
            id, create_time,create_oper_name,create_oper_org_name,operate_content
            FROM t_reduce_activity_participate_log act

            WHERE act.activity_id = #{activityId}
            and act.store_id = #{storeId}
            <if test="goodsModelId > 0">
                AND act.goods_model_id = #{goodsModelId}
            </if>
            and is_deleted = 0
        </if>
				) as alog

				ORDER BY alog.create_time desc
    </select>
</mapper>
