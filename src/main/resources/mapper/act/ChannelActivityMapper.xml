<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.act.ChannelActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.ChannelActivity">
        <id column="id" property="id"/>
        <result column="channel" property="channel"/>
        <result column="discount_code" property="discountCode"/>
        <result column="activity_name" property="activityName"/>
        <result column="orgcode" property="orgcode"/>
        <result column="activity_status" property="activityStatus"/>
        <result column="refer_store_model" property="referStoreModel"/>
        <result column="activity_type" property="activityType"/>
        <result column="discount_latitude" property="discountLatitude"/>
        <result column="discount_method" property="discountMethod"/>
        <result column="discount_condition1" property="discountCondition1"/>
        <result column="discount_condition2" property="discountCondition2"/>
        <result column="restrict_discounts" property="restrictDiscounts"/>
        <result column="max_discount_amount" property="maxDiscountAmount"/>
        <result column="max_rent_days" property="maxRentDays"/>
        <result column="min_rent_days" property="minRentDays"/>
        <result column="activity_start_date" property="activityStartDate"/>
        <result column="activity_end_date" property="activityEndDate"/>
        <result column="pick_up_start_date" property="pickUpStartDate"/>
        <result column="pick_up_end_date" property="pickUpEndDate"/>
        <result column="return_start_date" property="returnStartDate"/>
        <result column="return_end_date" property="returnEndDate"/>
        <result column="unavailable_date_ranges" property="unavailableDateRanges"/>
        <result column="cost_bearing_party" property="costBearingParty"/>
        <result column="cost_allocation_method" property="costAllocationMethod"/>
        <result column="merchant_bear" property="merchantBear"/>
        <result column="platform_bear" property="platformBear"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_time" property="createTime"/>
        <result column="create_oper_id" property="createOperId"/>
        <result column="create_oper_name" property="createOperName"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_oper_id" property="updateOperId"/>
        <result column="update_oper_name" property="updateOperName"/>
        <result column="intersection_flag" property="intersectionFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, channel, discount_code, activity_name, orgcode, activity_status, refer_store_model, activity_type, discount_latitude, discount_method, discount_condition1, discount_condition2, restrict_discounts, max_discount_amount, max_rent_days, min_rent_days, activity_start_date, activity_end_date, pick_up_start_date, pick_up_end_date, return_start_date, return_end_date, unavailable_date_ranges, cost_bearing_party, cost_allocation_method, merchant_bear, platform_bear, is_deleted, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name, intersection_flag
    </sql>

</mapper>
