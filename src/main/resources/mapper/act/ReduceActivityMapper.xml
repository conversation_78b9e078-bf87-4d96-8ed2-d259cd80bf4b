<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.act.ReduceActivityMapper">


    <select id="queryReduceActivity" resultType="com.saicmobility.evcard.md.act.domain.ReduceActivityBo">
        SELECT
        act.*
        FROM
        (SELECT *,
        (
        case when activity_start_time &lt;= now()  and activity_end_time &gt;= now()
          and offline_flag !=2 then 1

        when activity_start_time &gt;= now()  and activity_end_time &gt;= now()
         and offline_flag !=2 then 2

        else 3
        end
        ) as activityStatus

        FROM t_reduce_activity ) as act

        WHERE 1=1
        <if test="orgCode != null and orgCode != ''">
            AND act.org_code= #{orgCode}
        </if>
        <if test="activityStatus != 0">
            AND act.activityStatus = #{activityStatus}
        </if>
        <if test="activityName != null and activityName != ''">
            AND act.activity_name like concat('%',#{activityName},'%')
        </if>
        <if test="activityId != 0">
            AND act.id = #{activityId}
        </if>
        ORDER BY act.activityStatus,activity_start_time
    </select>

    <select id="getByActivityId" resultType="com.saicmobility.evcard.md.act.domain.ReduceActivityBo">
        SELECT
        act.*
        FROM
        (SELECT *,
        (
        case when activity_start_time &lt;= now()  and activity_end_time &gt;= now()
          and offline_flag !=2 then 1

        when activity_start_time &gt;= now()  and activity_end_time &gt;= now()
         and offline_flag !=2 then 2

        else 3
        end
        ) as activityStatus

        FROM t_reduce_activity ) as act

        WHERE act.id = #{activityId}
    </select>

    <select id="searchReduceActivityName"
            resultType="com.saicmobility.evcard.md.act.domain.ReduceActivityNameBo">
        select id,activity_name
        from t_reduce_activity
        where is_deleted = 0

    </select>

    <select id="getAllReduceActivity" resultType="com.saicmobility.evcard.md.act.domain.ReduceActivityBo">
        SELECT
        act.*
        FROM
        (SELECT *,
        (
        case when activity_start_time &lt;= now()  and activity_end_time &gt;= now()
          and offline_flag !=2 then 1

        when activity_start_time &gt;= now()  and activity_end_time &gt;= now()
         and offline_flag !=2 then 2

        else 3
        end
        ) as activityStatus

        FROM t_reduce_activity ) as act
        ORDER BY act.activityStatus,activity_start_time
    </select>

    <select id="queryOfflineReduceActivity"
            resultType="com.saicmobility.evcard.md.act.domain.ReduceActivityBo">
        SELECT
        act.*
        FROM
        (SELECT *,
        (
        case when activity_start_time &lt;= now()  and activity_end_time &gt;= now()
          and offline_flag !=2 then 1

        when activity_start_time &gt;= now()  and activity_end_time &gt;= now()
         and offline_flag !=2 then 2

        else 3
        end
        ) as activityStatus

        FROM t_reduce_activity ) as act
        where  act.activityStatus = 3 and offline_flag =1
        ORDER BY act.activityStatus,activity_start_time
    </select>

</mapper>
