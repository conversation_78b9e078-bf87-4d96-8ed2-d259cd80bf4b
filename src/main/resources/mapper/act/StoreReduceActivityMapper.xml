<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.act.StoreReduceActivityMapper">

    <select id="getStoreAvailableReduceActivity"
            resultType="com.saicmobility.evcard.md.act.domain.GetStoreAvailableReduceActivityBo">
        SELECT
        rat.id as id ,
        rat.activity_name as activityName,
        rat.user_participate_number as userParticipateNumber,
        rat.activity_start_time as activityStartTime,
        rat.activity_end_time as activityEndTime,
        rat.register_start_time as registerStartTime,
        rat.register_end_time as registerEndTime,
        rat.first_order_available as firstOrderAvailable,
        rat.activity_discount as activityDiscount,
        rat.activity_rule_description as activityRuleDescription,
        rat.activity_pic_url as activityPicUrl
        FROM t_store_reduce_activity sra
        INNER JOIN t_store_reduce_activity_goods_model sgm
        ON sra.id = sgm.store_reduce_activity_id
        INNER JOIN
        (SELECT *,
        (
        case when activity_start_time &lt;= now() and activity_end_time &gt;= now()
         and offline_flag !=2 then 1

        when activity_start_time &gt;= now() and activity_end_time &gt;= now()
         and offline_flag !=2 then 2

        else 3
        end
        ) as activityStatus

        FROM t_reduce_activity ) as rat

        ON sra.activity_id = rat.id
        WHERE 1 = 1
        AND sra.store_id = #{storeId}
        AND rat.activityStatus = 1
        <if test="goodsModelId > 0">
            AND sgm.goods_model_id = #{goodsModelId}
        </if>
        AND activity_start_time &lt;= now()
        AND activity_end_time &gt;= now()
        AND sra.is_deleted = 0
        AND sgm.is_deleted = 0
        AND rat.is_deleted = 0
        ORDER BY rat.id ASC,rat.create_time ASC
    </select>

    <select id="SearchStoreUnParticipateActivity"
            resultType="com.saicmobility.evcard.md.act.domain.StoreReduceActivityBo">
        SELECT
        rat.*

        FROM
        (SELECT *,
        (
        case when activity_start_time &lt;= now()  and activity_end_time &gt;= now()
         and offline_flag !=2 then 1

        when activity_start_time &gt;= now()  and activity_end_time &gt;= now()
         and offline_flag !=2 then 2

        else 3
        end
        ) as activityStatus

        FROM t_reduce_activity ) as rat

        WHERE rat.id not in
        (SELECT DISTINCT activity_id FROM t_store_reduce_activity sra
        INNER JOIN t_store_reduce_activity_goods_model sgm
        ON sra.id = sgm.store_reduce_activity_id
        WHERE 1 = 1
        <if test="orgCode != null and orgCode != '' and orgCode != '00'">
            AND sra.org_code like concat(#{orgCode},'%')
        </if>
        <if test="storeId != 0">
            AND sra.store_id = #{storeId}
        </if>
        AND sra.is_deleted = 0
        AND sgm.is_deleted = 0
        )

        <if test="activityStatus != 0">
            AND rat.activityStatus = #{activityStatus}
        </if>
        <if test="activityName != null and activityName != ''">
            AND rat.activity_name like concat('%',#{activityName},'%')
        </if>
        <if test="activityId != 0">
            AND rat.id = #{activityId}
        </if>
        ORDER BY rat.activityStatus ASC,rat.create_time desc

    </select>




    <select id="SearchStoreParticipateActivity"
            resultType="com.saicmobility.evcard.md.act.domain.StoreReduceActivityBo">
        SELECT rat.*,
        sgm.goods_model_id as goodsModelId,
        sra.store_id as storeId,
        sra.org_code as operOrgCode
        FROM t_store_reduce_activity sra
        INNER JOIN t_store_reduce_activity_goods_model sgm
        ON sra.id = sgm.store_reduce_activity_id
        INNER JOIN
        (SELECT *,
        (
        case when activity_start_time &lt;= now()  and activity_end_time &gt;= now()
          and offline_flag !=2 then 1

        when activity_start_time &gt;= now()  and activity_end_time &gt;= now()
         and offline_flag !=2 then 2

        else 3
        end
        ) as activityStatus

        FROM t_reduce_activity ) as rat
        ON sra.activity_id = rat.id
        WHERE 1 = 1
        <if test="storeId > 0">
            AND sra.store_id = #{storeId}
        </if>
        <if test="orgCode != null and orgCode != '' and orgCode != '00'">
            AND sra.org_code like concat(#{orgCode},'%')
        </if>
        <if test="activityStatus != 0">
            AND rat.activityStatus = #{activityStatus}
        </if>
        <if test="activityName != null and activityName != ''">
            AND rat.activity_name like concat('%',#{activityName},'%')
        </if>
        <if test="activityId > 0">
            AND rat.id = #{activityId}
        </if>
        <if test="goodsModelId > 0">
            AND sgm.goods_model_id = #{goodsModelId}
        </if>
        AND sra.is_deleted = 0
        AND sgm.is_deleted = 0
        AND rat.is_deleted = 0
        ORDER BY rat.activityStatus ASC,rat.create_time desc,sra.store_id ASC , sgm.goods_model_id ASC
    </select>

    <select id="SearchStoreParticipateGoodsModelId" resultType="java.lang.Long">
        SELECT DISTINCT goods_model_id FROM t_store_reduce_activity sra
        INNER JOIN t_store_reduce_activity_goods_model sgm
        ON sra.id = sgm.store_reduce_activity_id
        WHERE 1 = 1
        <if test="activityId > 0">
            AND sra.activity_id = #{activityId}
        </if>
        <if test="storeId > 0">
            AND sra.store_id = #{storeId}
        </if>
        AND sra.is_deleted = 0
        AND sgm.is_deleted = 0
    </select>

    <select id="checkActivityEffectTime" resultType="java.lang.Integer">
        select count(1) from
        (SELECT *,
        (
        case when activity_start_time &lt;= now()  and activity_end_time &gt;= now()
          and offline_flag !=2 then 1

        when activity_start_time &gt;= now()  and activity_end_time &gt;= now()
         and offline_flag !=2 then 2

        else 3
        end
        ) as activityStatus

        FROM t_reduce_activity ) as act
        inner join t_store_reduce_activity  sra
        on act.id = sra.activity_id
        where
        act.activityStatus in (1,2)
        and sra.store_id = ${storeId}
        and act.is_deleted = 0
        and sra.is_deleted = 0
        and (
        (act.activity_start_time &gt;= #{activityStartTime} AND act.activity_start_time &lt;= #{activityEndTime})
        OR (act.activity_start_time &lt;= #{activityStartTime} AND act.activity_end_time &gt;= #{activityEndTime})
        OR (act.activity_end_time &gt;= #{activityStartTime} AND act.activity_end_time &lt;= #{activityEndTime}) )
    </select>
</mapper>
