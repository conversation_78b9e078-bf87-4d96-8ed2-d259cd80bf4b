<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.act.PackageConfigurationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.PackageConfiguration">
        <id column="id" property="id" />
        <result column="org_code" property="orgCode" />
        <result column="goods_model_id" property="goodsModelId" />
        <result column="store_id" property="storeId" />
        <result column="package_name" property="packageName" />
        <result column="days_number" property="daysNumber" />
        <result column="total_price" property="totalPrice" />
        <result column="use_start_date" property="useStartDate" />
        <result column="use_end_date" property="useEndDate" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="renew_use_flag" property="renewUseFlag" />
        <result column="config_state" property="configState" />
        <result column="is_deleted" property="isDeleted" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, org_code, goods_model_id, store_id, package_name, days_number, total_price, use_start_date, use_end_date, start_time, end_time, renew_use_flag, config_state, is_deleted, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
    </sql>
    <select id="searchPackageName" resultType="com.saicmobility.evcard.md.act.domain.packages.PackageNameDto">
        select id,package_name from t_package_configuration where 1=1
        <if test="name != null and name != '' ">
            package_name like concat('%',#{name},'%')
        </if>
         and is_deleted = 0 order by id
    </select>

    <select id="getAvailablePackage" resultType="com.saicmobility.evcard.md.act.entity.PackageConfiguration">
        select  * from  t_package_configuration
        where  org_code = #{dto.orgCode}
        and store_id = #{dto.storeId}
        and goods_model_id = #{dto.goodsModelId}
        <if test="dto.renewUseFlag == 1">
            and renew_use_flag = 1
        </if>
        and days_number  &lt;= #{dto.daysNumber}
        and config_state = 1
        and (
        (use_start_date &gt;= #{dto.useStartDate} AND use_start_date &lt;= #{dto.useEndDate})
        OR (use_start_date &lt;= #{dto.useStartDate} AND use_end_date &gt;= #{dto.useEndDate})
        OR (use_end_date &gt;= #{dto.useStartDate} AND use_end_date &lt;= #{dto.useEndDate}) )
    </select>
    <select id="getUpgradePackage" resultType="com.saicmobility.evcard.md.act.entity.PackageConfiguration">
        select  * from  t_package_configuration
        where  org_code = #{dto.orgCode}
        and store_id = #{dto.storeId}
        and goods_model_id = #{dto.goodsModelId}
        <if test="dto.renewUseFlag == 1">
            and renew_use_flag = 1
        </if>
        and days_number  &gt; #{dto.daysNumber}
        and config_state = 1
        and (
        (use_start_date &gt;= #{dto.useStartDate} AND use_start_date &lt;= #{dto.useEndDate})
        OR (use_start_date &lt;= #{dto.useStartDate} AND use_end_date &gt;= #{dto.useEndDate})
        OR (use_end_date &gt;= #{dto.useStartDate} AND use_end_date &lt;= #{dto.useEndDate}) )
    </select>
</mapper>
