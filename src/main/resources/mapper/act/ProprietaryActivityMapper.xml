<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.act.ProprietaryActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.ProprietaryActivity">
        <id column="id" property="id" />
        <result column="activity_name" property="activityName" />
        <result column="activity_tag" property="activityTag" />
        <result column="org_codes" property="orgCodes" />
        <result column="store_ids" property="storeIds" />
        <result column="vehicle_model_ids" property="vehicleModelIds" />
        <result column="activity_type" property="activityType" />
        <result column="activity_status" property="activityStatus" />
        <result column="pricing_type" property="pricingType" />
        <result column="discount_latitude" property="discountLatitude" />
        <result column="full_minus_standard_pricing" property="fullMinusStandardPricing" />
        <result column="full_minus_flexible_pricing" property="fullMinusFlexiblePricing" />
        <result column="discount_standard_pricing" property="discountStandardPricing" />
        <result column="discount_flexible_pricing" property="discountFlexiblePricing" />
        <result column="min_rent_days" property="minRentDays" />
        <result column="max_rent_days" property="maxRentDays" />
        <result column="available_on_holidays" property="availableOnHolidays" />
        <result column="sign_up_start_date" property="signUpStartDate" />
        <result column="sign_up_end_date" property="signUpEndDate" />
        <result column="pick_up_date" property="pickUpDate" />
        <result column="return_date" property="returnDate" />
        <result column="activity_start_date" property="activityStartDate" />
        <result column="activity_end_date" property="activityEndDate" />
        <result column="unavailable_date_ranges" property="unavailableDateRanges" />
        <result column="is_deleted" property="isDeleted" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
        <result column="same_day_use_flag" property="sameDayUseFlag" />
        <result column="intersection_flag" property="intersectionFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, activity_name, activity_tag, org_codes, store_ids ,vehicle_model_ids, activity_type, activity_status, pricing_type, discount_latitude, full_minus_standard_pricing, full_minus_flexible_pricing, discount_standard_pricing, discount_flexible_pricing, min_rent_days,max_rent_days, available_on_holidays, sign_up_start_date, sign_up_end_date, pick_up_date, return_date, activity_start_date, activity_end_date, unavailable_date_ranges, is_deleted, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name,same_day_use_flag, intersection_flag
    </sql>


    <update id="updateBatchStoreId">
        UPDATE t_proprietary_activity
        SET store_ids = CASE
            <foreach collection="list" item="item" >
                WHEN id = #{item.id} THEN #{item.storeIds}
            </foreach>
            ELSE store_ids
            END
        WHERE id IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
    </update>

        <select id="queryProprietaryActByStoreIds" resultType="com.saicmobility.evcard.md.act.entity.ProprietaryActivity">
        select
            <include refid="Base_Column_List"/>
        from t_proprietary_activity a
        where a.pick_up_date &lt;= NOW()
            and a.return_date &gt;= NOW()
            and a.activity_type = '2'
            and a.id in (
                select DISTINCT s.activity_id from t_proprietary_activity_signup s
                where s.is_deleted = 0
                and s.store_id in
                <foreach item="item" collection="list" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            )
    </select>
</mapper>
