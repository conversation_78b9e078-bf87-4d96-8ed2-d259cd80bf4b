<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.act.ProprietaryActivitySignupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.ProprietaryActivitySignup">
        <id column="id" property="id" />
        <result column="activity_id" property="activityId" />
        <result column="signup_status" property="signupStatus" />
        <result column="org_code" property="orgCode" />
        <result column="store_id" property="storeId" />
        <result column="vehicle_model_ids" property="vehicleModelIds" />
        <result column="full_minus_flexible_pricing" property="fullMinusFlexiblePricing" />
        <result column="discount_flexible_pricing" property="discountFlexiblePricing" />
        <result column="is_deleted" property="isDeleted" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, activity_id, signup_status, org_code, store_id,vehicle_model_ids, full_minus_flexible_pricing, discount_flexible_pricing, is_deleted, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
    </sql>

    <select id="querySignupProprietaryActivityList" resultType="com.saicmobility.evcard.md.act.domain.activity.SignupProprietaryActivityBo">
            SELECT
                *
            FROM (
                SELECT
                    t1.id as activityId,
                    t1.activity_name as activityName,
                    t1.activity_tag as activityTag,
                    t1.activity_type as activityType,
                    t1.sign_up_start_date as signUpStartDate,
                    t1.sign_up_end_date as signUpEndDate,
                    t1.pricing_type as pricingType,
                    t1.activity_status as activityStatus,
                    (CASE WHEN t1.org_codes = '-1' THEN concat('(',#{orgCode},')') ELSE t1.org_codes END ) AS orgCodes,
                    (case when t2.signup_status = 2 THEN 2 ELSE 1  END  )as  signupStatus,
                    (CASE WHEN t2.id > 0 THEN t2.id  ELSE 0  END ) AS id,
                    t2.create_time as signupTime,
                    t2.store_id as storeId
                FROM t_proprietary_activity t1
                LEFT JOIN t_proprietary_activity_signup t2 ON t2.is_deleted = 0 and t1.id = t2.activity_id
                    and t2.org_code = #{orgCode}
                    WHERE t1.is_deleted = 0 and t1.activity_status in (2,3,4)
            ) AS t3
        WHERE 1=1
        <if test="orgCode != null and orgCode != ''">
            and   t3.orgCodes like concat('%',concat('(',#{orgCode},')'),'%')
        </if>
        <if test="activityName != null and activityName != ''">
            and t3.activityName like concat('%',#{activityName},'%')
        </if>
        <if test="activityTag != null and activityTag !='' ">
            and t3.activityTag like concat('%',#{activityTag},'%')
        </if>
        <if test="activityStatus !=0 ">
            and t3.activityStatus = #{activityStatus}
        </if>
        <if test="activityType !=0 ">
            and t3.activityType = #{activityType}
        </if>
        <if test="pricingType !=0 ">
            and t3.pricingType = #{pricingType}
        </if>
        <if test="signUpEndDate != null">
            <![CDATA[ and t3.signUpStartDate <= #{signUpEndDate}]]>
        </if>
        <if test="signUpStartDate != null">
            <![CDATA[ and t3.signUpEndDate >= #{signUpStartDate}]]>
        </if>
        <if test="signupStatus != 0">
            and t3.signupStatus = #{signupStatus}
        </if>
        ORDER BY FIELD(t3.activityStatus,2,3,4), t3.signupTime DESC, t3.activityId DESC,t3.id asc
    </select>


    <insert id="insertBatch">
        INSERT INTO  t_proprietary_activity_signup ( `activity_id`, `signup_status`, `org_code`
        , `store_id`, `vehicle_model_ids`, `full_minus_flexible_pricing`, `discount_flexible_pricing`
        , `is_deleted`, `create_time`, `create_oper_id`, `create_oper_name`, `update_time`, `update_oper_id`
        , `update_oper_name`)
        values
        <foreach collection ="list" item="item" separator =",">
            (
                #{item.activityId}, #{item.signupStatus}, #{item.orgCode},#{item.storeId},
                #{item.vehicleModelIds}, #{item.fullMinusFlexiblePricing}, #{item.discountFlexiblePricing},
                #{item.isDeleted} , #{item.createTime} , #{item.createOperId} , #{item.createOperName} ,
                #{item.updateTime}  , #{item.updateOperId}  , #{item.updateOperName}
            )
        </foreach>
    </insert>

    <update id="updateBatchStoreId">
        UPDATE t_proprietary_activity_signup
        SET store_id = CASE
        <foreach collection="list" item="item" >
            WHEN id = #{item.id} THEN #{item.storeId}
        </foreach>
        ELSE store_id
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
    </update>

    <!--
        CASE
        WHEN t3.activityStatus = 2 THEN 1
        WHEN t3.activityStatus = 3 THEN 2
        WHEN t3.activityStatus = 4 THEN 3
        END ASC,

        或

        t3.activityStatus ASC

        或

        FIELD(t3.activityStatus,2,3,4)
    -->
    <select id="querySignupProprietaryActivityListGroupOrgCode" resultType="com.saicmobility.evcard.md.act.domain.activity.SignupProprietaryActivityBo">
        select
            activityId,activityName,activityTag,activityType,signUpStartDate,signUpEndDate,activityStatus,orgCodes,pricingType,storeIds
        from (
            SELECT
                    *
                FROM (
                    SELECT
                        t1.id as activityId,
                        t1.activity_name as activityName,
                        t1.activity_tag as activityTag,
                        t1.activity_type as activityType,
                        t1.sign_up_start_date as signUpStartDate,
                        t1.sign_up_end_date as signUpEndDate,
                        t1.pricing_type as pricingType,
                        t1.activity_status as activityStatus,
                        case when t1.store_ids  = '-1' then concat('(',#{storeId},')') else t1.store_ids end as storeIds,
                        (CASE WHEN t1.org_codes = '-1' THEN concat('(',#{orgCode},')') ELSE t1.org_codes END ) AS orgCodes,
                        (case when t2.signup_status = 2 THEN 2 ELSE 1  END  )as  signupStatus
                    FROM t_proprietary_activity t1
                        LEFT JOIN t_proprietary_activity_signup t2 ON t2.is_deleted = 0 and t1.id = t2.activity_id
                        and t2.org_code = #{orgCode}
                        WHERE t1.is_deleted = 0 and t1.activity_status in (2,3,4)
                ) AS t3
                WHERE 1=1
                <if test="orgCode != null and orgCode != ''">
                    and   t3.orgCodes like concat('%',concat('(',#{orgCode},')'),'%')
                </if>
                <if test="storeId != null and storeId != ''">
                    AND  t3.storeIds like CONCAT('%', #{storeId}, '%')
                </if>
                <if test="activityName != null and activityName != ''">
                    and t3.activityName like concat('%',#{activityName},'%')
                </if>
                <if test="activityTag != null and activityTag !='' ">
                    and t3.activityTag like concat('%',#{activityTag},'%')
                </if>
                <if test="activityStatus !=0 ">
                    and t3.activityStatus = #{activityStatus}
                </if>
                <if test="activityType !=0 ">
                    and t3.activityType = #{activityType}
                </if>
                <if test="pricingType !=0 ">
                    and t3.pricingType = #{pricingType}
                </if>
                <if test="signUpEndDate != null">
                    <![CDATA[ and t3.signUpStartDate <= #{signUpEndDate}]]>
                </if>
                <if test="signUpStartDate != null">
                    <![CDATA[ and t3.signUpEndDate >= #{signUpStartDate}]]>
                </if>
                <if test="signupStatus != 0">
                    and t3.signupStatus = #{signupStatus}
                </if>
        )t
        group by activityId,activityName,activityTag,activityType,signUpStartDate,signUpEndDate
        ,activityStatus,orgCodes,pricingType,storeIds
    </select>

</mapper>
