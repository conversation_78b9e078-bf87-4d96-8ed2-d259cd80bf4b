<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.act.AdvertisementActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.AdvertisementActivity">
        <id column="id" property="id" />
        <result column="source" property="source" />
        <result column="appid" property="appid" />
        <result column="device_platform" property="devicePlatform" />
        <result column="device_type" property="deviceType" />
        <result column="device_code" property="deviceCode" />
        <result column="callback_url" property="callbackUrl" />
        <result column="callback_result" property="callbackResult" />
        <result column="callback_time" property="callbackTime" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, source, appid, device_platform, device_type, device_code, callback_url, callback_result, callback_time, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name, is_deleted
    </sql>

</mapper>
