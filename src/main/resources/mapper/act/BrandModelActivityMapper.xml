<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.BrandModelActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.BrandModelActivity">
        <id column="id" property="id" />
        <result column="is_deleted" property="isDeleted" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
        <result column="brand_model_name" property="brandModelName" />
        <result column="subtitle_content" property="subtitleContent" />
        <result column="activity_status" property="activityStatus" />
        <result column="listpage_slogan" property="listpageSlogan" />
        <result column="activity_citys" property="activityCitys" />
        <result column="activity_start_date" property="activityStartDate" />
        <result column="activity_end_date" property="activityEndDate" />
        <result column="home_page_pic_url" property="homePagePicUrl" />
        <result column="list_page_pic_url" property="listPagePicUrl" />
        <result column="detail_page_pic_url" property="detailPagePicUrl" />
        <result column="related_orders_flag" property="relatedOrdersFlag" />
        <result column="related_model_ids" property="relatedModelIds" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, is_deleted, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name, brand_model_name, subtitle_content, activity_status, listpage_slogan, activity_citys, activity_start_date, activity_end_date, home_page_pic_url, list_page_pic_url, detail_page_pic_url, related_orders_flag, related_model_ids
    </sql>

</mapper>
