<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.siac.MemberCdkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.siac.MemberCdk">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="cdkey" property="cdkey" />
        <result column="status" property="status" />
        <result column="action_id" property="actionId" />
        <result column="activated_way" property="activatedWay" />
        <result column="activated_mid" property="activatedMid" />
        <result column="activated_user_name" property="activatedUserName" />
        <result column="activated_user_mobile" property="activatedUserMobile" />
        <result column="activated_time" property="activatedTime" />
        <result column="validity_start_time" property="validityStartTime" />
        <result column="validity_end_time" property="validityEndTime" />
        <result column="wechat_cdk_qr_url" property="wechatCdkQrUrl" />
        <result column="coupon_num" property="couponNum" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, cdkey, status, action_id, activated_way, activated_mid, activated_user_name, activated_user_mobile, activated_time, validity_start_time, validity_end_time, wechat_cdk_qr_url, coupon_num, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name, is_deleted
    </sql>

    <select id="selectByMemberCdk" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from siac.member_cdk
        where cdkey = #{cdkey,jdbcType=VARCHAR}
    </select>

</mapper>
