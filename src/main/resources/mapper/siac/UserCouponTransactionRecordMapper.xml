<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.siac.UserCouponTransactionRecordMapper">
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.siac.UserCouponTransactionRecord">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="operate_type" jdbcType="INTEGER" property="operateType" />
        <result column="transaction_type" jdbcType="INTEGER" property="transactionType" />
        <result column="in_amount" jdbcType="DECIMAL" property="inAmount" />
        <result column="out_amount" jdbcType="DECIMAL" property="outAmount" />
        <result column="user_coupon_seq" jdbcType="BIGINT" property="userCouponSeq" />
        <result column="transaction_amount" jdbcType="DECIMAL" property="transactionAmount" />
        <result column="account_balance" jdbcType="DECIMAL" property="accountBalance" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="misc_desc" jdbcType="VARCHAR" property="miscDesc" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
        <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
        <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
        <result column="account_type" jdbcType="INTEGER" property="accountType" />
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, operate_type, transaction_type, in_amount, out_amount, user_coupon_seq,
        transaction_amount, account_balance, status, misc_desc, create_time, create_oper_id,
        create_oper_name, update_time, update_oper_id, update_oper_name, account_type
    </sql>

    <select id="selectByTypeAndTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        FROM user_coupon_transaction_record
        WHERE status = 1
        and date_format(create_time, '%Y-%m-%d') = #{date}
        and operate_type =#{type}
    </select>

    <select id="selectByTypeAndSeq" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        FROM user_coupon_transaction_record
        WHERE status = 1
        and operate_type =#{type}
        and user_coupon_seq = #{userCouponSeq}
    </select>
</mapper>
