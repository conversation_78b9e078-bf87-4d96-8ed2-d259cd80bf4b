<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.siac.MemberCdkRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.siac.MemberCdkRelation">
        <id column="id" property="id" />
        <result column="member_cdk_id" property="memberCdkId" />
        <result column="relation_id" property="relationId" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, member_cdk_id, relation_id, type, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name, is_deleted
    </sql>


    <select id="selectListByMembermemberCdkId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from siac.member_cdk_relation
        where member_cdk_id = #{memberCdkId,jdbcType=INTEGER}
        and is_deleted = 0
        and type = 1
    </select>

</mapper>
