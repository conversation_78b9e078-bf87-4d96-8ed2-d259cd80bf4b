<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.siac.MmpPackNightActivityMapper">

	<sql id="Base_Column_List">
		id, activity_name as activityName, activity_status AS activityStatus, org_id AS orgId, org_ids AS orgIds,
		 activity_start_date AS activityStartDate, activity_end_date AS activityEndDate,
		activity_start_time activityStartTime, activity_end_time AS activityEndTime,
		activity_license_plate AS activityLicensePlate, activity_weeds activityWeeds, remark,
		create_time AS createTime, create_oper_id AS createOperId, create_oper_name AS createOperName,
		 update_time AS updateTime, update_oper_id AS updateOperId, update_oper_name AS updateOperName,
		`type`, third_activity_id AS thirdActivityId, sign_id AS signId, quota_org_id  AS quotaOrgId,password,group_id AS groupId
	</sql>

	<select id="selectById" resultType="com.saicmobility.evcard.md.act.entity.siac.MmpPackNightActivity">
		select
		<include refid="Base_Column_List"/>
		from iss.mmp_pack_night_activity
		where id = #{id,jdbcType=BIGINT}
	</select>

	<select id="selectActivitiesByIds"
			resultType="com.saicmobility.evcard.md.act.entity.siac.MmpPackNightActivity">
		SELECT
		<include refid="Base_Column_List"/>
		FROM iss.mmp_pack_night_activity
		WHERE id IN
		<foreach collection="activityIds" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="selectCouponByMmpIds" resultType="com.saicmobility.evcard.md.act.domain.ActivityCouponDTO">
		SELECT
		b.id as id,
		a.org_id as orgId,
		a.type as activityType,
		a.id as activityId,
		a.activity_status AS activityStatus,
		b.coupon_name as couponName,
		b.coupon_seq as couponSeq,
		b.coupon_target as couponTarget,
		b.coupon_type as couponType,
		b.offer_quantity as offerQuantity,
		b.EFFECTIVE_DAYS as effectiveDays,
		b.VALID_DAYS as validDays,
		b.START_DATE as startDate,
		b.EXPIRES_DATE as expiresDate,
		b.VALID_TIME_TYPE as validTimeType,
		cd.COUPON_VALUE as couponValue,
		cd.DISCOUNT_RATE as discountRate,
		cd.MIN_AMOUNT as minAmount,
		cd.duration_limit as durationLimit
		FROM
		iss.`mmp_pack_night_activity` a
		LEFT JOIN iss.mmp_third_coupon b ON a.third_activity_id = b.third_activity_id
		JOIN siac.coupon_def  cd on b.coupon_seq=cd.COUPON_SEQ
		where b.id in
		<foreach item="item" collection="mmpIds" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>
	<select id="selectActivityByThirdActivityId"
			resultType="com.saicmobility.evcard.md.act.entity.siac.MmpPackNightActivity">
		SELECT
		<include refid="Base_Column_List"/>
		FROM iss.mmp_pack_night_activity
		WHERE third_activity_id = #{thirdActivityId}
	</select>
	<select id="selectActivityByThirdActivityIds"
			resultType="com.saicmobility.evcard.md.act.entity.siac.MmpPackNightActivity">
		SELECT
		<include refid="Base_Column_List"/>
		from iss.mmp_pack_night_activity
		WHERE third_activity_id in
		<foreach collection="thirdActivityIds" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>


</mapper>
