<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.siac.UserCouponListMapper">

    <sql id="Base_Column_List">
        uc.USER_COUPON_SEQ as userCouponSeq, uc.AUTH_ID as authId, uc.COUPON_SEQ as couponSeq, uc.START_DATE as startDate, uc.EXPIRES_DATE as expiresDate, uc.STATUS as status, uc.COUPON_ORIGIN as couponOrigin, cd.COUPON_VALUE as couponValue, cd.DISCOUNT_RATE as discountRate, cd.DES as des, cd.COUPON_TYPE as couponType, cd.SERVICE_TYPE as serviceType, IFNULL(cd.MIN_AMOUNT,0) as minAmount, cd.TIME_TYPE as timeType,
        cd.START_TIME as startTime, cd.END_TIME as endTime, cd.PICKSHOP_SEQ as pickshopSeq , cd.RETURNSHOP_SEQ as returnshopSeq, cd.VEHICLE_NO as vehicleNo, cd.VEHICLE_MODLE as vehicleModel, cd.PICKSHOP_CITY as pickshopCity, cd.RETURNSHOP_CITY as returnshopCity, cd.DISCOUNT_RATE as discountRate, cd.ACTIVITY_OVERLAP as activityOverlap, cd.package_ids as packageIds, cd.AVAILABLE_DAYS_OF_WEEK as availableDaysOfWeek,
        cd.RENT_METHOD as rentMethod, cd.USE_METHOD as useMethod, cd.HOLIDAYS_AVAILABLE as holidaysAvailable, cd.duration_limit as durationLimit, uc.org_seq as orgSeq, uc.order_seq as orderSeq, uc.discount as discount, uc.transaction_type as transactionType, uc.agency_id as agencyId,
        uc.action_id as actionId, cd.rent_method_group as rentMethodGroup, cd.goods_vehicle_model as goodsVehicleModel,
        cd.shop_limit_type as shopLimitType, uc.coupon_code as couponCode, uc.coupon_code_offer_flag as couponCodeOfferFlag
    </sql>


    <update id="frozenCoupon">
    UPDATE siac.user_coupon_list SET frozen = #{frozen}, order_seq=#{orderNo}, UPDATED_TIME = #{updatedTime}
		where USER_COUPON_SEQ=#{userCouponSeq}
    </update>

    <select id="findCouponByAuthIdAndUserCouponSeq"
            resultType="com.saicmobility.evcard.md.act.domain.coupon.CouponConditionDto">
        SELECT
        uc.USER_COUPON_SEQ as userCouponSeq,
        uc.AUTH_ID as authId,
        uc.COUPON_SEQ as couponSeq,
        uc.START_DATE as startDate,
        uc.EXPIRES_DATE as expiresDate,
        uc.STATUS as status,
        uc.COUPON_ORIGIN as couponOrigin,
        cd.COUPON_VALUE as couponValue,
        cd.DISCOUNT_RATE as discountRate,
        cd.DES as des,
        cd.COUPON_TYPE as couponType,
        cd.SERVICE_TYPE as serviceType,
        IFNULL(cd.MIN_AMOUNT,0) as minAmount,
        cd.TIME_TYPE as timeType,
        cd.START_TIME as startTime,
        cd.END_TIME as endTime,
        cd.PICKSHOP_SEQ as pickshopSeq ,
        cd.RETURNSHOP_SEQ as returnshopSeq,
        cd.VEHICLE_NO as vehicleNo,
        cd.VEHICLE_MODLE as vehicleModel,
        cd.PICKSHOP_CITY as pickshopCity,
        cd.RETURNSHOP_CITY as returnshopCity,
        cd.DISCOUNT_RATE as discountRate,
        cd.ACTIVITY_OVERLAP as activityOverlap,
        cd.package_ids as packageIds,
        cd.AVAILABLE_DAYS_OF_WEEK as availableDaysOfWeek,
        cd.RENT_METHOD as rentMethod,
        cd.USE_METHOD as useMethod,
        cd.HOLIDAYS_AVAILABLE as holidaysAvailable,
        cd.duration_limit as durationLimit,
        uc.org_seq as orgSeq,
        uc.order_seq as orderSeq,
        uc.discount as discount,
        uc.transaction_type as transactionType,
        uc.agency_id as agencyId,
        uc.action_id as actionId,
        cd.rent_method_group as rentMethodGroup,
        cd.goods_vehicle_model as goodsVehicleModel,
        cd.shop_limit_type as shopLimitType
        from siac.user_coupon_list uc
        JOIN  siac.coupon_def  cd on uc.COUPON_SEQ=cd.COUPON_SEQ
        WHERE uc.USER_COUPON_SEQ=#{userCouponSeq}
        <if test=" authId!=null and authId!='' ">
            AND uc.AUTH_ID=#{authId}
        </if>
    </select>
    <select id="findUserCouponsByRemark"
            resultType="com.saicmobility.evcard.md.act.domain.coupon.CouponConditionDto">
        select
        uc.USER_COUPON_SEQ as userCouponSeq,
        uc.AUTH_ID as authId,
        uc.COUPON_SEQ as couponSeq,
        uc.START_DATE as startDate,
        uc.EXPIRES_DATE as expiresDate,
        uc.STATUS as status,
        uc.COUPON_ORIGIN as couponOrigin,
        cd.COUPON_VALUE as couponValue,
        cd.DISCOUNT_RATE as discountRate,
        cd.DES as des,
        cd.COUPON_TYPE as couponType,
        cd.SERVICE_TYPE as serviceType,
        IFNULL(cd.MIN_AMOUNT,0) as minAmount,
        cd.TIME_TYPE as timeType,
        cd.START_TIME as startTime,
        cd.END_TIME as endTime,
        cd.PICKSHOP_SEQ as pickshopSeq ,
        cd.RETURNSHOP_SEQ as returnshopSeq,
        cd.PICKSHOP_CITY as pickshopCity,
        cd.RETURNSHOP_CITY as returnshopCity,
        cd.VEHICLE_NO as vehicleNo,
        cd.VEHICLE_MODLE as vehicleModel,
        cd.PICKSHOP_CITY as pickshopCity,
        cd.RETURNSHOP_CITY as returnshopCity,
        cd.DISCOUNT_RATE as discountRate,
        uc.org_seq as orgSeq,
        uc.order_seq as orderSeq,
        uc.discount as discount,
        cd.ACTIVITY_OVERLAP as activityOverlap,
        cd.package_ids as packageIds,
        cd.AVAILABLE_DAYS_OF_WEEK as availableDaysOfWeek,
        cd.RENT_METHOD as rentMethod,
        cd.USE_METHOD as useMethod,
        cd.HOLIDAYS_AVAILABLE as holidaysAvailable,
        cd.duration_limit as durationLimit,
        uc.agency_id as agencyId,
        uc.action_id as actionId,
        cd.rent_method_group as rentMethodGroup,
        cd.goods_vehicle_model as goodsVehicleModel
        from siac.user_coupon_list uc force index(IDX21_USER_COUPON_LIST)
        JOIN  siac.coupon_def  cd on uc.COUPON_SEQ=cd.COUPON_SEQ
        <where>
            uc.AUTH_ID =#{authId}
            <if test=" status!=null">
                <if test=" status==0 "> and uc.status=#{status}  AND uc.EXPIRES_DATE &gt;= SUBSTR(NOW(),1,10)  </if>
                <if test=" status==1 "> and uc.status=#{status} </if>
                <if test=" status==2 "> and uc.status=#{status} </if>
                <!-- status=3标识查询已过期的优惠券 -->
                <if test=" status==3 "> and uc.status in (0,3) AND uc.EXPIRES_DATE &lt; SUBSTR(NOW(),1,10) and uc.EXPIRES_DATE &gt;=#{expiresDate}</if>
            </if>
            <if test="remark!=null and remark!=''">
                and (uc.ORIGIN_REF_SEQ = #{remark}
                or uc.remark like concat('%',#{remark},'%') )
            </if>
        </where>
        ORDER BY uc.CREATED_TIME
    </select>


    <!-- 根据couponCode查询优惠券 -->
    <select id="findByCouponCode" resultType="com.saicmobility.evcard.md.act.domain.coupon.CouponConditionDto">
        SELECT
        uc.USER_COUPON_SEQ as userCouponSeq,
        uc.AUTH_ID as authId,
        uc.COUPON_SEQ as couponSeq,
        uc.START_DATE as startDate,
        uc.EXPIRES_DATE as expiresDate,
        uc.STATUS as status,
        uc.COUPON_ORIGIN as couponOrigin,
        cd.COUPON_VALUE as couponValue,
        cd.DISCOUNT_RATE as discountRate,
        cd.DES as des,
        cd.COUPON_TYPE as couponType,
        cd.SERVICE_TYPE as serviceType,
        IFNULL(cd.MIN_AMOUNT,0) as minAmount,
        cd.TIME_TYPE as timeType,
        cd.START_TIME as startTime,
        cd.END_TIME as endTime,
        cd.PICKSHOP_SEQ as pickshopSeq ,
        cd.RETURNSHOP_SEQ as returnshopSeq,
        cd.VEHICLE_NO as vehicleNo,
        cd.VEHICLE_MODLE as vehicleModel,
        cd.PICKSHOP_CITY as pickshopCity,
        cd.RETURNSHOP_CITY as returnshopCity,
        cd.DISCOUNT_RATE as discountRate,
        cd.ACTIVITY_OVERLAP as activityOverlap,
        cd.package_ids as packageIds,
        cd.AVAILABLE_DAYS_OF_WEEK as availableDaysOfWeek,
        cd.RENT_METHOD as rentMethod,
        cd.USE_METHOD as useMethod,
        cd.HOLIDAYS_AVAILABLE as holidaysAvailable,
        cd.duration_limit as durationLimit,
        uc.org_seq as orgSeq,
        uc.order_seq as orderSeq,
        uc.discount as discount,
        uc.transaction_type as transactionType,
        uc.agency_id as agencyId,
        uc.action_id as actionId,
        uc.coupon_code_start_time as couponCodeStartTime,
        uc.coupon_code_expires_time as couponCodeExpiresTime,
        uc.coupon_code_status as couponCodeStatus,
        uc.coupon_code_offer_flag as couponCodeOfferFlag,
        uc.updated_time as updatedTime,
        cd.rent_method_group as rentMethodGroup,
        cd.goods_vehicle_model as goodsVehicleModel,
        cd.shop_limit_type as shopLimitType
        from siac.user_coupon_list uc
        JOIN  siac.coupon_def  cd on uc.COUPON_SEQ=cd.COUPON_SEQ
        WHERE uc.COUPON_CODE=#{couponCode}
    </select>

    <select id="selectListByIds" resultType="com.saicmobility.evcard.md.act.domain.coupon.CouponConditionDto">
        SELECT
        <include refid="Base_Column_List"/>
        from siac.user_coupon_list uc
        JOIN  siac.coupon_def  cd on uc.COUPON_SEQ=cd.COUPON_SEQ
        WHERE uc.USER_COUPON_SEQ in
        <foreach collection="userCouponSeqs" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateCouponStatus">
        UPDATE siac.user_coupon_list SET STATUS = #{newStatus}, UPDATED_TIME = #{updatedTime},UPDATED_USER = #{updatedUser}
        where USER_COUPON_SEQ=#{userCouponSeq} and STATUS = #{oldStatus}
    </update>

    <update id="offerCoupon">
        UPDATE siac.user_coupon_list SET coupon_code_offer_flag = 1
        WHERE USER_COUPON_SEQ=#{userCouponSeq} AND coupon_code_offer_status = 0
    </update>

    <update id="expireCouponCodeStatus">
        UPDATE siac.user_coupon_list SET coupon_code_status = 3, UPDATED_TIME = #{updatedTime},UPDATED_USER = #{updatedUser}
        where USER_COUPON_SEQ=#{userCouponSeq} and coupon_code_status =  #{oldCouponCodeStatus}
    </update>

    <update id="deprecatedCouponCodeStatus">
        UPDATE siac.user_coupon_list SET coupon_code_status = 2, STATUS = 2,UPDATED_TIME = #{updatedTime},UPDATED_USER = #{updatedUser}
        where USER_COUPON_SEQ=#{userCouponSeq} and coupon_code_status =  #{oldCouponCodeStatus}
    </update>

    <update id="expireCouponStatus">
        UPDATE siac.user_coupon_list SET STATUS = 3, UPDATED_TIME = #{updatedTime},UPDATED_USER = #{updatedUser}
        where USER_COUPON_SEQ=#{userCouponSeq} and STATUS =  #{oldStatus}
    </update>

    <select id="queryAllUnissuedCoupon"
            resultType="com.saicmobility.evcard.md.act.domain.coupon.CouponConditionDto">
        SELECT
        uc.USER_COUPON_SEQ as userCouponSeq
        FROM siac.user_coupon_list uc
        WHERE ACTION_ID = #{actionId} AND COUPON_SEQ = #{couponSeq} AND coupon_code_offer_flag = 0 AND COUPON_ORIGIN = #{couponOrigin} AND STATUS = 0;
    </select>

    <update id="updateCouponOfferFlag">
        UPDATE siac.user_coupon_list SET coupon_code_offer_flag = 1
        WHERE USER_COUPON_SEQ=#{userCouponSeq} AND coupon_code_offer_flag = 0
    </update>
</mapper>
