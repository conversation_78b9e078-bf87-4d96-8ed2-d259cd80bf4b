<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.siacPlus.UserCouponListSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.siac.UserCouponListSnapshot">
        <id column="USER_COUPON_SEQ" property="userCouponSeq" />
        <result column="AUTH_ID" property="authId" />
        <result column="COUPON_SEQ" property="couponSeq" />
        <result column="START_DATE" property="startDate" />
        <result column="EXPIRES_DATE" property="expiresDate" />
        <result column="BEFORE_STATUS" property="beforeStatus" />
        <result column="STATUS" property="status" />
        <result column="STATUS_CHANGE_DATE" property="statusChangeDate" />
        <result column="CREATED_TIME" property="createdTime" />
        <result column="CREATED_USER" property="createdUser" />
        <result column="UPDATED_TIME" property="updatedTime" />
        <result column="UPDATED_USER" property="updatedUser" />
        <result column="COUPON_ORIGIN" property="couponOrigin" />
        <result column="coupon_code" property="couponCode" />
        <result column="coupon_code_start_time" property="couponCodeStartTime" />
        <result column="coupon_code_expires_time" property="couponCodeExpiresTime" />
        <result column="CRM_USER_COUPON_SEQ" property="crmUserCouponSeq" />
        <result column="exchangeTime" property="exchangetime" />
        <result column="remark" property="remark" />
        <result column="OFFER_TYPE" property="offerType" />
        <result column="ACTION_ID" property="actionId" />
        <result column="ORG_SEQ" property="orgSeq" />
        <result column="ORDER_ORG_SEQ" property="orderOrgSeq" />
        <result column="ORDER_SEQ" property="orderSeq" />
        <result column="DISCOUNT" property="discount" />
        <result column="ORIGIN_REF_SEQ" property="originRefSeq" />
        <result column="transaction_type" property="transactionType" />
        <result column="agency_id" property="agencyId" />
        <result column="frozen" property="frozen" />
        <result column="wechat_coupon_code_qr_url" property="wechatCouponCodeQrUrl" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        USER_COUPON_SEQ, AUTH_ID, COUPON_SEQ, START_DATE, EXPIRES_DATE, BEFORE_STATUS, STATUS, STATUS_CHANGE_DATE, CREATED_TIME, CREATED_USER, UPDATED_TIME, UPDATED_USER, COUPON_ORIGIN, coupon_code, coupon_code_start_time, coupon_code_expires_time, CRM_USER_COUPON_SEQ, exchangeTime, remark, OFFER_TYPE, ACTION_ID, ORG_SEQ, ORDER_ORG_SEQ, ORDER_SEQ, DISCOUNT, ORIGIN_REF_SEQ, transaction_type, agency_id, frozen, wechat_coupon_code_qr_url
    </sql>

</mapper>
