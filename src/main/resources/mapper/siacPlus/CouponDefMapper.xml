<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.siacPlus.CouponDefMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.siac.CouponDef">
        <id column="COUPON_SEQ" property="couponSeq" />
        <result column="RULE_SEQ" property="ruleSeq" />
        <result column="COUPON_TYPE" property="couponType" />
        <result column="ORG_ID" property="orgId" />
        <result column="TIME_TYPE" property="timeType" />
        <result column="START_TIME" property="startTime" />
        <result column="END_TIME" property="endTime" />
        <result column="MIN_AMOUNT" property="minAmount" />
        <result column="COUPON_VALUE" property="couponValue" />
        <result column="DISCOUNT_RATE" property="discountRate" />
        <result column="DES" property="des" />
        <result column="IMG_URL_IOS" property="imgUrlIos" />
        <result column="IMG_URL_ANDROID" property="imgUrlAndroid" />
        <result column="PICKSHOP_SEQ" property="pickshopSeq" />
        <result column="IMG_URL_IOS_EXP" property="imgUrlIosExp" />
        <result column="IMG_URL_ANDROID_EXP" property="imgUrlAndroidExp" />
        <result column="RETURNSHOP_SEQ" property="returnshopSeq" />
        <result column="VEHICLE_MODLE" property="vehicleModle" />
        <result column="VEHICLE_NO" property="vehicleNo" />
        <result column="PICKSHOP_CITY" property="pickshopCity" />
        <result column="RETURNSHOP_CITY" property="returnshopCity" />
        <result column="ACTIVITY_OVERLAP" property="activityOverlap" />
        <result column="package_ids" property="packageIds" />
        <result column="SERVICE_TYPE" property="serviceType" />
        <result column="VALID_TIME_TYPE" property="validTimeType" />
        <result column="EFFECTIVE_DAYS" property="effectiveDays" />
        <result column="VALID_DAYS" property="validDays" />
        <result column="START_DATE" property="startDate" />
        <result column="EXPIRES_DATE" property="expiresDate" />
        <result column="COUPON_NAME" property="couponName" />
        <result column="COUPON_DES" property="couponDes" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_NAME" property="createName" />
        <result column="AVAILABLE_DAYS_OF_WEEK" property="availableDaysOfWeek" />
        <result column="HOLIDAYS_AVAILABLE" property="holidaysAvailable" />
        <result column="duration_limit" property="durationLimit" />
        <result column="RENT_METHOD" property="rentMethod" />
        <result column="USE_METHOD" property="useMethod" />
        <result column="rent_method_group" property="rentMethodGroup" />
        <result column="goods_vehicle_model" property="goodsVehicleModel" />
        <result column="shop_limit_type" property="shopLimitType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        COUPON_SEQ, RULE_SEQ, COUPON_TYPE, ORG_ID, TIME_TYPE, START_TIME, END_TIME, MIN_AMOUNT, COUPON_VALUE, DISCOUNT_RATE, DES, IMG_URL_IOS, IMG_URL_ANDROID, PICKSHOP_SEQ, IMG_URL_IOS_EXP, IMG_URL_ANDROID_EXP, RETURNSHOP_SEQ, VEHICLE_MODLE, VEHICLE_NO, PICKSHOP_CITY, RETURNSHOP_CITY, ACTIVITY_OVERLAP, package_ids, SERVICE_TYPE, VALID_TIME_TYPE, EFFECTIVE_DAYS, VALID_DAYS, START_DATE, EXPIRES_DATE, COUPON_NAME, COUPON_DES, CREATE_TIME, CREATE_NAME, AVAILABLE_DAYS_OF_WEEK, HOLIDAYS_AVAILABLE, duration_limit, RENT_METHOD, USE_METHOD, rent_method_group, goods_vehicle_model, shop_limit_type
    </sql>

</mapper>
