<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.siacPlus.ChannelCouponSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.siac.ChannelCouponSnapshot">
        <id column="id" property="id" />
        <result column="channel_id" property="channelId" />
        <result column="channel_user_id" property="channelUserId" />
        <result column="channel_order_id" property="channelOrderId" />
        <result column="channel_activity_id" property="channelActivityId" />
        <result column="mmp_third_coupon_id" property="mmpThirdCouponId" />
        <result column="user_coupon_seq" property="userCouponSeq" />
        <result column="coupon_code" property="couponCode" />
        <result column="before_coupon_code_status" property="beforeCouponCodeStatus" />
        <result column="STATUS_CHANGE_DATE" property="statusChangeDate" />
        <result column="coupon_code_status" property="couponCodeStatus" />
        <result column="is_deleted" property="isDeleted" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, channel_id, channel_user_id, channel_order_id, channel_activity_id, mmp_third_coupon_id, user_coupon_seq, coupon_code, before_coupon_code_status, STATUS_CHANGE_DATE, coupon_code_status, is_deleted, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
    </sql>

</mapper>
