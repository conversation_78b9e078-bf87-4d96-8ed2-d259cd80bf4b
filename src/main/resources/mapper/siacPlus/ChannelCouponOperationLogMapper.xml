<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saicmobility.evcard.md.act.mapper.siacPlus.ChannelCouponOperationLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saicmobility.evcard.md.act.entity.siac.ChannelCouponOperationLog">
        <id column="id" property="id" />
        <result column="operation_type" property="operationType" />
        <result column="channel_coupon_id" property="channelCouponId" />
        <result column="channel_id" property="channelId" />
        <result column="channel_user_id" property="channelUserId" />
        <result column="channel_order_id" property="channelOrderId" />
        <result column="channel_activity_id" property="channelActivityId" />
        <result column="mmp_third_coupon_id" property="mmpThirdCouponId" />
        <result column="offer_num" property="offerNum" />
        <result column="success_flag" property="successFlag" />
        <result column="origin_request" property="originRequest" />
        <result column="origin_response" property="originResponse" />
        <result column="is_deleted" property="isDeleted" />
        <result column="create_time" property="createTime" />
        <result column="create_oper_id" property="createOperId" />
        <result column="create_oper_name" property="createOperName" />
        <result column="update_time" property="updateTime" />
        <result column="update_oper_id" property="updateOperId" />
        <result column="update_oper_name" property="updateOperName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, operation_type, channel_coupon_id, channel_id, channel_user_id, channel_order_id, channel_activity_id, mmp_third_coupon_id, offer_num, success_flag, origin_request, origin_response, is_deleted, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
    </sql>

</mapper>
