<?xml version="1.0" encoding="UTF-8"?>
<configuration>
 
    <property name="LOG_HOME" value="/opt/logs/${git.name}"/>
 
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
 
    <appender name="ROOT" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/log/root.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/log/root.log.%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>5</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
 
    <appender name="ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/log/all.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/log/all.log.%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
 
    <appender name="ASYNC_ALL" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1234</queueSize>
        <appender-ref ref="ALL"/>
    </appender>
 
    <appender name="SERVERLOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/auditlog/server.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/auditlog/server.log.%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="CLIENTLOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/auditlog/client.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/auditlog/client.log.%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="STATSLOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/auditlog/stats.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/auditlog/stats.log.%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>5</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
 
    <logger name="krpc" level="info" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ASYNC_ALL"/>
    </logger>
    <logger name="krpc.serverlog" level="info" additivity="false">
        <appender-ref ref="SERVERLOG"/>
    </logger>
    <logger name="krpc.webserverlog" level="info" additivity="false">
        <appender-ref ref="SERVERLOG"/>
    </logger>
    <logger name="krpc.clientlog" level="info" additivity="false">
        <appender-ref ref="CLIENTLOG"/>
    </logger>
    <logger name="krpc.statslog" level="info" additivity="false">
        <appender-ref ref="STATSLOG"/>
    </logger>
    <logger name="auditlog.client" level="info" additivity="false">
        <appender-ref ref="CLIENTLOG"/>
    </logger>
    <logger name="auditlog.server" level="info" additivity="false">
        <appender-ref ref="SERVERLOG"/>
    </logger>
    <logger name="com.saicmobility.common" level="info" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ASYNC_ALL"/>
    </logger>
    <logger name="com.saicmobility" level="info" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ASYNC_ALL"/>
    </logger>
 
    <root level="info">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ROOT"/>
    </root>
 
</configuration>