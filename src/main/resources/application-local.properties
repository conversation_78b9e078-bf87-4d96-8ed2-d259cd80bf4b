spring.profiles=local

#krpc
krpc.webserver.port=32534
krpc.webserver.autoRoute=true
#krpc.registry.addrs=*************:8500
#krpc.monitor.serverAddr=
#krpc.application.traceAdapter=cat:server=127.0.0.1:8003;enabled=false
krpc.registry.addrs=consul-st.evcard.vip:8500
#??????????
krpc.monitor.serverAddr = krpc-monitor-sit.gcsrental.com:7002
krpc.application.traceAdapter = cat:server=krpc-ktrace-sit.gcsrental.com:8003

#datasource
spring.datasource.druid.act.url=********************************************************************************************************************************************************************************************
spring.datasource.druid.act.username=actuser
spring.datasource.druid.act.password=bKs4kqiME7Cc

spring.datasource.druid.siac.url=*****************************************************************************************************************************************************************************
spring.datasource.druid.siac.username=devuser
spring.datasource.druid.siac.password=blk2ZsEB

spring.datasource.druid.iss.url=****************************************************************************************************************************************************************************
spring.datasource.druid.iss.username=devuser
spring.datasource.druid.iss.password=blk2ZsEB

#redis
spring.redis.host = evcard-st-lan.redis.rds.aliyuncs.com
spring.redis.port = 6379
spring.redis.password = Wp4uJK*Vc3v2
spring.redis.timeout = 10000
spring.redis.lettuce.pool.min-idle = 8
spring.redis.lettuce.pool.max-idle = 16
spring.redis.lettuce.pool.max-active = 100

#apollo
#apollo.meta=http://apollo-dev.evcard.vip:58080
apollo.bootstrap.enabled=false
apollo.bootstrap.namespaces=
apollo.bootstrap.eagerLoad.enabled=false

#rest apis
#evcard.inner.api.baseUrl=http://localhost:8080/
evcard.inner.api.baseUrl=http://inner-dev.evcard.vip/
evcard.rest.api.baseUrl = http://inner-st.evcard.vip/

# ������·���������
qinglu.merchantId=58
qinglu.appKey=a621bf428794bbed4db7eb82f0bcfea7
qinglu.appSecret=e85355a08841caf5a7e9bdcad50f5d42
qinglu.url=http://open.qinglusaas-dev.com/open

## MD_CHANNEL_CHANGE_SYNC_QL_TOPIC_UAT????
#MD_CHANNEL_CHANGE_SYNC_QL_TOPIC_PROD????
#MD_CHANNEL_CHANGE_SYNC_QL_TOPIC_STRESS????
#MD_CHANNEL_CHANGE_SYNC_QL_TOPIC_SIT??????
ons.md.channel.change.sync.ql.topic = MD_CHANNEL_CHANGE_SYNC_QL_TOPIC_SIT

scheduler.addrs = krpc-job-sit.gcsrental.com:7004

krpc.monitor.maskFields = authId,driverCode,mobilePhone,name,fileNo,passportNo,idCardNumber,drivingLicense,drivingLicenseImgUrl,fileNoImgUrl,faceRecognitionImgUrl,password,holdIdcardPicUrl,idcardPicUrl
task.fail.times.limit = 5

ons.contract.syn_act.groupId = GID_MD_ACT_SYN_SERVICE_SIT
ons.nameSrvAddr = http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
ons.accessKey = LTAI4ClyMUapT6MD
ons.secretKey = ******************************
# ??????
ons.contract.normal.topic = MD_CONTRACT_TOPIC_SIT
# Group
ons.contract.groupId = GID_MD_ACT_SERVICE_SIT

#Oss
ali.ons.endPoint = http://oss-cn-shanghai.aliyuncs.com
ali.ons.basePath = http://evcard.oss-cn-shanghai.aliyuncs.com
ali.oss.httpsBasePath = https://evcard.oss-cn-shanghai.aliyuncs.com
ali.oss.onsAddr = http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
ali.oss.ossBucket = evcard
ali.oss.accessId = LTAI5tJumdVNXHPLszk49yAk
ali.oss.accessKey = ******************************
ali.oss.env = test