spring.profiles=dev
# todo \u672A\u914D\u7F6Eapollo\u5730\u5740
apollo.bootstrap.enabled=false

#krpc
krpc.webserver.port=32534
krpc.webserver.autoRoute=true
krpc.registry.addrs=consul-dev.evcard.vip:8500
# todo \u8FDE\u63A5\u8D85\u65F6\uFF0C\u5F85\u89E3\u51B3
#krpc.monitor.serverAddr=krpc-monitor-dev.gcsrental.com:7002
#krpc.application.traceAdapter=cat:server=krpc-ktrace-dev.gcsrental.com:8003;enabled=false

#datasource
spring.datasource.druid.act.url=****************************************************************************************************************************************************************************************
spring.datasource.druid.act.username=actuser
spring.datasource.druid.act.password=xgtr7TFj

spring.datasource.druid.siac.url=*****************************************************************************************************************************************************************************
spring.datasource.druid.siac.username=storeuser
spring.datasource.druid.siac.password=nfeP8H7WT3fQ

spring.datasource.druid.iss.url=*****************************************************************************************************************************************************************************
spring.datasource.druid.iss.username=storeuser
spring.datasource.druid.iss.password=nfeP8H7WT3fQ

#redis
spring.redis.host=evcard-dev-lan.redis.rds.aliyuncs.com
spring.redis.port=6379
spring.redis.password=EiK2jUv9VTaF
spring.redis.timeout=10000
spring.redis.lettuce.pool.min-idle=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.max-active=32

#apollo
apollo.meta=http://apollo-test.evcard.vip:58080

#rest apis
evcard.inner.api.baseUrl=http://inner-dev.evcard.vip/

xxl.job.admin.addresses=http://xxljob-dev.evcard.vip/xxl-job-admin

ons.nameSrvAddr = http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
ons.accessKey = LTAI5t6i6Tb9XN6gRqkstxYH
ons.secretKey = ******************************
# 订单普通消息
ons.contract.normal.topic = MD_CONTRACT_TOPIC_DEV

ons.contract.groupId = GID_MD_ACT_SERVICE_DEV

# 调用擎路的相关配置
qinglu.merchantId=58
qinglu.appKey=a621bf428794bbed4db7eb82f0bcfea7
qinglu.appSecret=e85355a08841caf5a7e9bdcad50f5d42
qinglu.url=http://open.qinglusaas-dev.com/open

#Oss
ali.ons.endPoint = http://oss-cn-shanghai.aliyuncs.com
ali.ons.basePath = http://evcard.oss-cn-shanghai.aliyuncs.com
ali.oss.httpsBasePath = https://evcard.oss-cn-shanghai.aliyuncs.com
ali.oss.onsAddr = http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
ali.oss.ossBucket = evcard
ali.oss.accessId = LTAI5tJumdVNXHPLszk49yAk
ali.oss.accessKey = ******************************
ali.oss.env = dev