//package com.saicmobility.evcard.md.act;
//
//import com.saicmobility.evcard.md.act.service.OutService;
//import com.saicmobility.evcard.md.act.service.impl.StoreReduceActivityServiceImpl;
//import com.saicmobility.evcard.md.mdactservice.api.SearchStoreUnParticipateActivityReq;
//import com.saicmobility.evcard.md.mdactservice.api.SearchStoreUnParticipateActivityRes;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.util.Arrays;
//import java.util.List;
//import java.util.Map;
//
//@SpringBootTest
//@RunWith(SpringRunner.class)
//public class ActivityTest {
//    @Resource
//    private StoreReduceActivityServiceImpl storeReduceActivityServiceImpl;
//
//    @Resource
//    private OutService outService;
//
//    @Test
//    public void searchStoreUnParticipateActivityTest() {
//        SearchStoreUnParticipateActivityReq.Builder builder = SearchStoreUnParticipateActivityReq.newBuilder();
//        SearchStoreUnParticipateActivityReq build = builder.setOrgCode("110").setPageNum(2).setPageSize(1).setStoreId(2L).build();
//        SearchStoreUnParticipateActivityRes res = storeReduceActivityServiceImpl.searchStoreUnParticipateActivity(build);
//        System.out.println(res);
//    }
//
//    @Test
//    public void getGoodsNameMapTest() {
//        List<Long> idList = Arrays.asList(8L, 9L, 10L, 11L, 12L);
//        Map<Long, String> goodsNameMap = outService.getGoodsNameMap(idList);
//        System.out.println(goodsNameMap);
//    }
//}
