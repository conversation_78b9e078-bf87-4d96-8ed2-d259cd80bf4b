//package com.saicmobility.evcard.md.act.job;
//
//import com.alibaba.fastjson.JSON;
//import com.xxl.job.core.biz.model.ReturnT;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR>
// * @date 2022/12/5
// */
//@SpringBootTest
//@RunWith(SpringRunner.class)
//public class UpdatePackageStatusJobTest {
//
//    @Resource
//    private UpdatePackageStatusJob updatePackageStatusJob;
//
//    @Test
//    public void testExecute() throws Exception {
//        ReturnT<String> returnT = updatePackageStatusJob.execute("");
//        System.out.println(JSON.toJSONString(returnT));
//    }
//}
