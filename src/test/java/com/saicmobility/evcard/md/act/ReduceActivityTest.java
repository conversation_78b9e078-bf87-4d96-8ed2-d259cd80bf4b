//package com.saicmobility.evcard.md.act;
//
//import com.saicmobility.evcard.md.act.service.ReduceActivityService;
//import com.saicmobility.evcard.md.act.service.StoreReduceActivityService;
//import com.saicmobility.evcard.md.mdactservice.api.*;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.Arrays;
//import java.util.List;
//
//@SpringBootTest
//@RunWith(SpringRunner.class)
//public class ReduceActivityTest {
//    @Resource
//    private ReduceActivityService reduceActivityService;
//
//    @Resource
//    private StoreReduceActivityService storeReduceActivityService;
//
//    @Test
//    public void searchReduceActivityTest() {
//        SearchReduceActivityReq build = SearchReduceActivityReq.newBuilder()
//                .setPageNum(1).setPageSize(10)
//                //.setOrgCode("111")
//                .build();
//        SearchReduceActivityRes res = reduceActivityService.searchReduceActivity(build);
//        System.out.println(res);
//    }
//
//    @Test
//    public void getReduceActivityWithStoreTest() {
//        GetReduceActivityWithStoreReq.Builder builder = GetReduceActivityWithStoreReq.newBuilder().setActivityId(92L);
//        GetReduceActivityWithStoreRes res = reduceActivityService.getReduceActivityWithStore(builder.build());
//        System.out.println(res);
//    }
//
//    @Test
//    public void addReduceActivityTest() {
//        AddReduceActivityReq.Builder builder = AddReduceActivityReq.newBuilder();
//        builder.setActivityName("环球活动测试");
//        builder.setOrgCode("00");
//        builder.setSignUpDeadline("2024-06-14 00:00:00");
//        builder.setActivityStartTime("2024-06-14 00:00:00");
//        builder.setActivityEndTime("2024-06-14 00:00:00");
//        //builder.setRegisterStartTime("2023-06-14 00:00:00");
//        //builder.setRegisterEndTime("2023-06-14 00:00:00");
//        builder.setFirstOrderAvailable(1);
//        builder.setUserParticipateNumber(3);
//        builder.setActivityDiscount("300.00");
//        builder.setActivityRuleDescription("jjj");
//        builder.setActivityPicUrl("kkk");
//        AddReduceActivityRes res = reduceActivityService.addReduceActivity(builder.build());
//        System.out.println(res);
//    }
//
//    @Test
//    public void updateReduceActivityTest() {
//        UpdateReduceActivityReq.Builder builder = UpdateReduceActivityReq.newBuilder();
//        builder.setActivityId(89L);
//        builder.setActivityName("测试345");
//        builder.setOrgCode("190");
//        builder.setSignUpDeadline("2024-06-14 00:00:00");
//        builder.setActivityStartTime("2024-06-14 00:00:00");
//        builder.setActivityEndTime("2024-06-14 00:00:00");
//        builder.setRegisterStartTime("2024-06-14 00:00:00");
//        builder.setRegisterEndTime("2024-06-14 00:00:00");
//        builder.setFirstOrderAvailable(1);
//        builder.setUserParticipateNumber(3);
//        builder.setActivityDiscount("300");
//        builder.setActivityRuleDescription("jjj");
//        builder.setActivityPicUrl("kkk");
//        UpdateReduceActivityRes res = reduceActivityService.updateReduceActivity(builder.build());
//        System.out.println(res);
//    }
//
//    @Test
//    public void searchReduceActivityName() {
//        SearchReduceActivityNameRes res = reduceActivityService.searchReduceActivityName(SearchReduceActivityNameReq.newBuilder().build());
//        System.out.println(res);
//    }
//
//    @Test
//    public void getAllReduceActivity() {
//        SearchAllReduceActivityReq build = SearchAllReduceActivityReq.newBuilder().build();
//        SearchAllReduceActivityRes res = reduceActivityService.searchAllReduceActivity(build);
//        System.out.println(res);
//    }
//
//    @Test
//    public void searchStoreUnParticipateActivityTest() {
//        SearchStoreUnParticipateActivityReq build = SearchStoreUnParticipateActivityReq.newBuilder().setPageNum(1).setPageSize(10)
//                //.setStoreId(102L)
//                .build();
//        SearchStoreUnParticipateActivityRes res = storeReduceActivityService.searchStoreUnParticipateActivity(build);
//        System.out.println(res);
//    }
//
//    @Test
//    public void searchStoreParticipateActivityTest() {
//        SearchStoreParticipateActivityReq build = SearchStoreParticipateActivityReq.newBuilder()
//                //.setOrgCode("100")
//                .setPageNum(1).setPageSize(10)
//                .setStoreId(102L)
//                .build();
//        SearchStoreParticipateActivityRes res = storeReduceActivityService.searchStoreParticipateActivity(build);
//        System.out.println(res);
//    }
//
//    @Test
//    public void ParticipateStoreReduceActivityReqTest() {
//        List<Long> list = Arrays.asList(9L, 10L);
//        ParticipateStoreReduceActivityReq build = ParticipateStoreReduceActivityReq.newBuilder()
//                .setActivityId(98L).setOrgCode("12").setStoreId(100L).addAllGoodsModelId(list)
//                .build();
//        ParticipateStoreReduceActivityRes res = storeReduceActivityService.participateStoreReduceActivity(build);
//        System.out.println(res);
//    }
//
//    @Test
//    public void cancelStoreReduceActivityTest() {
//        List<Long> list = Arrays.asList(661L, 662L);
//        CancelStoreReduceActivityReq build = CancelStoreReduceActivityReq.newBuilder()
//                .setActivityId(1L).setOrgCode("888").setStoreId(108L).setGoodsModelId(9L)
//                //.setStoreId(102L)
//                .build();
//        CancelStoreReduceActivityRes res = storeReduceActivityService.cancelStoreReduceActivity(build);
//        System.out.println(res);
//    }
//
//    @Test
//    public void searchStoreActivityTest() {
//        SearchStoreParticipateActivityReq.Builder builder = SearchStoreParticipateActivityReq.newBuilder();
//        SearchStoreParticipateActivityReq build = builder.setPageNum(1).setPageSize(10).build();
//        SearchStoreParticipateActivityRes res = storeReduceActivityService.searchStoreParticipateActivity(build);
//        System.out.println(res);
//    }
//
//    @Test
//    public void goodNameTest() {
//        GetReduceActivityWithGoodModelNameReq.Builder builder = GetReduceActivityWithGoodModelNameReq.newBuilder();
//        builder.setActivityId(1L).setStoreId(108L);
//        GetReduceActivityWithGoodModelNameRes res = reduceActivityService.getReduceActivityWithGoodModelName(builder.build());
//        System.out.println(res);
//    }
//
//}
