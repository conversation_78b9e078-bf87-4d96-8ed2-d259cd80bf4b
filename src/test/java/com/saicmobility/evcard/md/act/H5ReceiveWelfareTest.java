package com.saicmobility.evcard.md.act;

import com.saicmobility.evcard.md.act.dto.welfare.GetWelfareInfoInput;
import com.saicmobility.evcard.md.act.dto.welfare.ReceiveWelfareDto;
import com.saicmobility.evcard.md.act.dto.welfare.ReceiveWelfareInput;
import com.saicmobility.evcard.md.act.service.welfare.H5ReceiveWelfareServiceImpl;
import com.saicmobility.evcard.md.act.service.welfare.IWelfareService;
import com.saicmobility.evcard.md.act.service.welfare.WelfareServiceAdapter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * H5领取福利测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class H5ReceiveWelfareTest {

    @Autowired
    private WelfareServiceAdapter welfareServiceAdapter;

    @Autowired
    private H5ReceiveWelfareServiceImpl h5ReceiveWelfareService;

    @Test
    public void testH5ReceiveWelfare() {
        // 测试 source=3 的场景
        ReceiveWelfareInput input = new ReceiveWelfareInput();
        input.setMobile("13800138000");
        input.setAuthcode("123456");
        input.setMembershipPolicyVersion("1.0");
        input.setPrivacyPolicyVersion("1.0");
        input.setType(2); // 预绑定领取

        GetWelfareInfoInput getWelfareInfoInput = new GetWelfareInfoInput();
        getWelfareInfoInput.setWelfareType(2); // 优惠券
        getWelfareInfoInput.setKey("test_coupon_code");
        getWelfareInfoInput.setSource(3); // H5领取
        input.setGetWelfareInfoInput(getWelfareInfoInput);

        try {
            // 测试参数校验
            boolean isValid = h5ReceiveWelfareService.checkReceiveWelfareInfoInput(input);
            System.out.println("参数校验结果: " + isValid);

            // 测试获取服务
            IWelfareService welfareService = welfareServiceAdapter.getWelfareService(getWelfareInfoInput);
            System.out.println("获取到的服务: " + (welfareService != null ? welfareService.getClass().getSimpleName() : "null"));

            // 注意：由于依赖外部服务，实际的福利领取测试需要在有完整环境的情况下进行
            // 这里主要测试配置和基本逻辑
            
        } catch (Exception e) {
            System.out.println("测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testWelfareServiceSelection() {
        // 测试不同 source 值的服务选择
        GetWelfareInfoInput input1 = new GetWelfareInfoInput();
        input1.setSource(1);
        input1.setKey("test_key");
        IWelfareService service1 = welfareServiceAdapter.getWelfareService(input1);
        System.out.println("Source=1 服务: " + (service1 != null ? service1.getClass().getSimpleName() : "null"));

        GetWelfareInfoInput input3 = new GetWelfareInfoInput();
        input3.setSource(3);
        input3.setKey("test_key");
        IWelfareService service3 = welfareServiceAdapter.getWelfareService(input3);
        System.out.println("Source=3 服务: " + (service3 != null ? service3.getClass().getSimpleName() : "null"));
    }
}
