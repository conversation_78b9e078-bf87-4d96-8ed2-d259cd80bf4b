//package com.saicmobility.evcard.md.act;
//
//import com.saicmobility.evcard.md.act.mapper.siac.UserCouponAccountMapper;
//import com.saicmobility.evcard.md.act.service.coupun.CouponService;
//import com.saicmobility.evcard.md.mdactservice.api.*;
//import org.apache.commons.lang3.StringUtils;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.util.Arrays;
//import java.util.List;
//
//@SpringBootTest
//@RunWith(SpringRunner.class)
//public class CouponServiceTest {
//    @Resource
//    private UserCouponAccountMapper userCouponAccountMapper;
//
//    @Resource
//    private CouponService couponService;
//
//
//    @Test
//    public void frozenCoupon() {
//        FrozenCouponReq build = FrozenCouponReq.newBuilder().setUserCouponSeq(3L)
//                .setFrozen(1).setOrderNo("111").build();
//        FrozenCouponRes res = couponService.frozenCoupon(build);
//        System.out.println(res);
//
//    }
//
//    @Test
//    public void getCoupon() {
//        String mid = "*************";
//        Long userCouponSeq= 16L;
//        GetCouponReq build = GetCouponReq.newBuilder().setUserCouponSeq(userCouponSeq).build();
//        GetCouponRes res = couponService.getCoupon(build);
//        System.out.println(res);
//    }
//
//    @Test
//    public void getActivityBasicInfoTest() {
//
//        GetActivityBasicInfoReq.Builder builder = GetActivityBasicInfoReq.newBuilder();
//        List<Long> list = Arrays.asList(134L, 138L, 20348437L);
//        GetActivityBasicInfoReq build = builder.addAllActivityId(list).build();
//        GetActivityBasicInfoRes res = couponService.getActivityBasicInfo(build);
//        System.out.println(res);
//
//    }
//
//    @Test
//    public void getMmpCouponListTest() {
//        GetMmpCouponListReq.Builder builder = GetMmpCouponListReq.newBuilder();
//        List<Long> longs = Arrays.asList(1L, 2L);
//        GetMmpCouponListReq req = builder.addAllActivityCouponIds(longs).build();
//        GetMmpCouponListRes res = couponService.getMmpCouponList(req);
//        System.out.println(res);
//
//    }
//
//
//    @Test
//    public void useCouponFlow() {
//        //orderCoupons();
//        /**
//         * 1115958527   限制
//         * 1115958531L  可用
//         * 1115630730   已使用
//         */
//        //checkOrderCoupon(1115958527L);
//        //checkOrderCoupon(1115958531L);
//        //checkOrderCoupon(1115630730L);
//
//        //useCouponRest(1115630730L);
//        useCouponRest(1115958531L);
//    }
//
//    @Test
//    public void orderCoupons() {
//        OrderCouponCondition orderCouponCondition = OrderCouponCondition.newBuilder()
//                .setMid("1811161000001").setAmount("20.5").setActivityType(1)
//                .setCostTime(20).setOrderNo("MC040448040404")
//                .setGoodsModelId("1")
//                .setPickUpCity("310101").setPickUpStoreId("116").setPickupTime("2022-06-18 10:11:32")
//                .setReturnCity("310101").setReturnStoreId("116")
//                .setRentMethod(4).setServiceTags("0").setServiceType(1).setVehicleNo(StringUtils.EMPTY)
//                .build();
//        OrderCouponReq req = OrderCouponReq.newBuilder().setOrderCouponCondition(orderCouponCondition).setSort(0).build();
//        OrderCouponRes res = couponService.orderCoupons(req);
//        System.out.println(res);
//    }
//
//    @Test
//    public void orderCouponsTest2() {
//        OrderCouponCondition orderCouponCondition = OrderCouponCondition.newBuilder()
//                .setMid("1611301100002").setAmount("20.5").setActivityType(1)
//                .setCostTime(20).setOrderNo("MC040448040404")
//                .setGoodsModelId("8")
//                .setPickUpCity("310101").setPickUpStoreId("100").setPickupTime("2022-06-18 10:11:32")
//                .setReturnCity("310101").setReturnStoreId("100")
//                .setRentMethod(4).setServiceTags("0").setServiceType(1).setVehicleNo(StringUtils.EMPTY)
//                .build();
//        OrderCouponReq req = OrderCouponReq.newBuilder().setOrderCouponCondition(orderCouponCondition).setSort(0).build();
//        OrderCouponRes res = couponService.orderCoupons(req);
//        System.out.println(res);
//    }
//
//
//    @Test
//    public void thirdCouponOffer() {
//        OfferThirdCouponsReq.Builder builder = OfferThirdCouponsReq.newBuilder()
//                .setMid("1811161000001")
//                .setCouponName("补偿券发放测试")
//                .setActivityCouponId(32956L)
//                .setOptUser("test")
//
//                .setOriginRefSeq("MC040448040404").setOrgCode("000T");
//        OfferThirdCouponsRes res = couponService.offerThirdCoupons(builder.build());
//        System.out.println(res);
//
//        builder.setExpiresDate("2022-06-19").setStartDate("2022-08-22");
//        res = couponService.offerThirdCoupons(builder.build());
//        System.out.println(res);
//    }
//
//    public void checkOrderCoupon(Long userCouponSeq) {
//        OrderCouponCondition orderCouponCondition = OrderCouponCondition.newBuilder()
//                .setMid("1811161000001").setAmount("20.5").setActivityType(1)
//                .setCostTime(20).setOrderNo("MC040448040404")
//                .setGoodsModelId("1")
//                .setPickUpCity("310101").setPickUpStoreId("116").setPickupTime("2022-06-18 10:11:32")
//                .setReturnCity("310101").setReturnStoreId("116")
//                .setRentMethod(4).setServiceTags("0").setServiceType(1).setVehicleNo(StringUtils.EMPTY)
//                .build();
//        CheckOrderCouponReq checkOrderCouponReq = CheckOrderCouponReq.newBuilder()
//                .setUserCouponSeq(userCouponSeq).setOrderCouponCondition(orderCouponCondition).build();
//        CheckOrderCouponRes res = couponService.checkOrderCoupon(checkOrderCouponReq);
//        System.out.println(res);
//    }
//
//
//    public void useCouponRest(Long userCouponSeq) {
//        UseCouponReq req = UseCouponReq.newBuilder().setMid("1811161000001").setOrderNo("MC04044804041111104")
//                .setDiscount("5.10").setUserCouponSeq(userCouponSeq)
//                .setOperatorId(1L).setOperatorName("unitTest").setOrderOrgCode("000T").build();
//        UseCouponRes res = couponService.useCouponRest(req);
//        System.out.println(res);
//    }
//
//    @Test
//    public void userOrderOfferCoupons() {
//        UserOrderOfferCouponsReq.Builder builder = UserOrderOfferCouponsReq.newBuilder();
//        builder.setMid("1701171900002").setOrderNo("123456").setStatus(2);
//        UserOrderOfferCouponsRes res = couponService.userOrderOfferCoupons(builder.build());
//        System.out.println(res);
//    }
//
//    @Test
//    public void getCouponViewTest() {
//        GetCouponViewReq.Builder builder = GetCouponViewReq.newBuilder();
//        builder.setMid("1811161000001").setUserCouponSeq(1115958909l);
//        GetCouponViewRes res = couponService.getCouponView(builder.build());
//        System.out.println(res);
//    }
//
//    @Test
//    public void offerThirdCoupons() {
//        OfferThirdCouponsReq.Builder builder = OfferThirdCouponsReq.newBuilder();
//        builder.setMid("2104301300001").setOrgCode("00")
//                //.setCouponName("ttt")
//                .setActivityCouponId(76343)
//                .setOriginRefSeq("MC220721100000101").setOptUser("3452742409");
//        OfferThirdCouponsRes res = couponService.offerThirdCoupons(builder.build());
//        System.out.println(res);
//    }
//
//    @Test
//    public void getCouponView1Test() {
//        GetCouponModelReq.Builder builder = GetCouponModelReq.newBuilder();
//        builder.setCouponSeq(3);
//        GetCouponModelViewRes res = couponService.getCouponModelView(builder.build());
//        System.out.println(res);
//    }
//
//    @Test
//    public void getFirstOrderFlag() {
//        GetFirstOrderFlagReq req = GetFirstOrderFlagReq.newBuilder().setMid("1710181900001").build();
//        GetFirstOrderFlagRes res = couponService.getFirstOrderFlag(req);
//        System.out.println(res);
//
//    }
//}
