//package com.saicmobility.evcard.md.act;
//
//import com.saicmobility.evcard.md.act.service.OperateLogService;
//import com.saicmobility.evcard.md.act.service.impl.StoreReduceActivityServiceImpl;
//import com.saicmobility.evcard.md.act.service.PackageConfigurationService;
//import com.saicmobility.evcard.md.mdactservice.api.*;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//
//@SpringBootTest
//@RunWith(SpringRunner.class)
//public class packageTest {
//
//    @Resource
//    private PackageConfigurationService packageConfigurationService;
//
//    @Resource
//    private StoreReduceActivityServiceImpl storeReduceActivityServiceImpl;
//
//    @Resource
//    private OperateLogService operateLogService;
//
//    @Test
//    public void packageTest() {
//        SearchAvailablePackageReq.Builder builder = SearchAvailablePackageReq.newBuilder();
//        builder.setOrgCode("000T")
//                .setStoreId(174L)
//                .setGoodsModelId(23L)
//                .setUseStartDate("20231001000000")
//                .setUseEndDate("20231009000000");
//
//        SearchAvailablePackageRes searchAvailablePackageRes = packageConfigurationService.searchAvailablePackage(builder.build());
//        System.out.println(searchAvailablePackageRes);
//    }
//
//
//    @Test
//    public void packageTest111() {
//        SearchAvailablePackageReq.Builder builder = SearchAvailablePackageReq.newBuilder();
//        builder.setOrgCode("000T")
//                .setStoreId(117L)
//                .setGoodsModelId(11L)
//                .setUseStartDate("20221001164500")
//                .setUseEndDate("20221005164500");
//
//        SearchAvailablePackageRes searchAvailablePackageRes = packageConfigurationService.searchAvailablePackage(builder.build());
//        System.out.println(searchAvailablePackageRes);
//    }
//
//
//    @Test
//    public void activityTest() {
//        GetStoreAvailableReduceActivityReq.Builder builder = GetStoreAvailableReduceActivityReq.newBuilder();
//        builder.setStoreId(108L).setGoodsModelId(13L).setMid("1705051300001");
//
//        GetStoreAvailableReduceActivityRes res = storeReduceActivityServiceImpl.getStoreAvailableReduceActivity(builder.build());
//        System.out.println(res);
//    }
//
//    @Test
//    public void getPackageByIdTest() {
//        GetPackageByIdReq.Builder builder = GetPackageByIdReq.newBuilder().setPackageId(1000L);
//        GetPackageByIdRes packageById = packageConfigurationService.getPackageById(builder.build());
//        System.out.println(packageById);
//    }
//
//    @Test
//    public void getPackageByIdTest2() {
//        GetPackageByIdReq.Builder builder = GetPackageByIdReq.newBuilder().setPackageId(1000L);
//        GetPackageByIdRes packageById = packageConfigurationService.getPackageDetail(builder.build());
//        System.out.println(packageById);
//    }
//
//    @Test
//    public void searchPackageTest() {
//        CurrentUser user = CurrentUser.newBuilder().setOrgCode("00").build();
//        SearchPackageReq build = SearchPackageReq.newBuilder().setCurrentUser(user).setPageNum(1).setPageSize(50).build();
//        SearchPackageRes searchPackageRes = packageConfigurationService.searchPackage(build);
//        System.out.println(searchPackageRes);
//
//    }
//
//    @Test
//    public void addPackageTest() {
//        AddPackageReq.Builder builder = AddPackageReq.newBuilder();
//        builder.setPackageName("早鸟套餐01");
//        builder.setOrgCode("123");
//        builder.setStoreId(1100L);
//        builder.setGoodsModelId(2100L);
//        builder.setDaysNumber(3);
//        builder.setTotalPrice("220");
//        builder.setUseStartDate("2022-12-15 00:00:00");
//        builder.setUseEndDate("2022-12-30 23:59:59");
//        builder.setStartTime("2022-12-05 12:20:00");
//        builder.setEarlyStartDate("2022-12-05 00:00:00");
//        builder.setEarlyEndDate("2022-12-14 23:59:59");
//        builder.setEarlyPrice("202");
//        builder.setRenewUseFlag(1);
//        AddPackageRes addPackageRes = packageConfigurationService.addPackage(builder.build());
//        System.out.println(addPackageRes);
//    }
//
//    @Test
//    public void offlinePackageTest() {
//        OfflinePackageReq.Builder builder = OfflinePackageReq.newBuilder().setPackageId(4L);
//        CurrentUser currentUser = CurrentUser.newBuilder().setUserId(222L).setUserName("user").build();
//        builder.setCurrentUser(currentUser);
//        OfflinePackageRes offlinePackageRes = packageConfigurationService.offlinePackage(builder.build());
//        System.out.println(offlinePackageRes);
//    }
//
//    @Test
//    public void searchPackageNameTest() {
//        SearchPackageNameReq build = SearchPackageNameReq.newBuilder().build();
//        SearchPackageNameRes searchPackageNameRes = packageConfigurationService.searchPackageName(build);
//        System.out.println(searchPackageNameRes);
//    }
//
//    @Test
//    public void searchOperateLogTest() {
//        SearchOperateLogReq build = SearchOperateLogReq.newBuilder()
//                .setPageNum(1).setPageSize(10)
//                .setForeignId("5").setOperateType(1).build();
//        SearchOperateLogRes res = operateLogService.searchOperateLog(build);
//        System.out.println(res);
//    }
//
//    @Test
//    public void searchActivityParticipateLogTest() {
//        SearchActivityParticipateLogReq build = SearchActivityParticipateLogReq.newBuilder()
//                .setStoreId(100L)
//                .setActivityId(96L)
//                .setGoodsModelId(8L)
//                .setPageNum(1)
//                .setPageSize(4)
//                .build();
//
//        SearchActivityParticipateLogRes res = operateLogService.searchActivityParticipateLog(build);
//        System.out.println(res);
//
//    }
//
//    @Test
//    public void searchAllPackage() {
//        /* SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
//        Date timeDate1 = simpleFormat.parse("2021-01-10 12:00:00");
//        Date timeDate2 = simpleFormat.parse("2021-01-05 12:00:00");*/
//
//        /*SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyyMMddHHmmss");
//        Date timeDate1 = simpleFormat.parse("20210110120000");
//        Date timeDate2 = simpleFormat.parse("20210105120000");
//        long time1 = timeDate1.getTime();
//        long time2 = timeDate2.getTime();
//
//        int days = (int) ((time1 - time2) / (1000 * 60 * 60 * 24));
//        System.out.println("日期差值:" + days);*/
//        /*LocalDateTime now = LocalDateTime.now();
//        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        System.out.println(df.format(now));*/
//
//        /*DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        LocalDate parse = LocalDate.parse("2022-01-01", df);
//        System.out.println(parse.toString());
//
//        System.out.println("2022-01-01".replace("-",""));*/
//        //System.out.println("2022-06-14 00:00:00".substring(0, 10));
//        SearchAllPackageReq build = SearchAllPackageReq.newBuilder().build();
//        SearchAllPackageRes res = packageConfigurationService.searchAllPackage(build);
//        System.out.println(res);
//
//    }
//
//
//
//
//}
