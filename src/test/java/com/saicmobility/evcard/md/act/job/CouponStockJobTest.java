package com.saicmobility.evcard.md.act.job;

import com.saicmobility.evcard.md.act.MainApplication;
import com.xxl.job.core.biz.model.ReturnT;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = MainApplication.class)
public class CouponStockJobTest {

    @Autowired
    private CouponStockJob couponStockJob;

    @Test
    public void test() throws Exception {
        ReturnT<String> result = couponStockJob.execute("");
        Thread.sleep(100000000L);
        System.out.println(11);

    }


}
