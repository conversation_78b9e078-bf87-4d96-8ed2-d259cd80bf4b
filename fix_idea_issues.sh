#!/bin/bash

echo "=== IntelliJ IDEA 项目修复脚本 ==="
echo "此脚本将帮助您解决 IDEA 项目打开问题"
echo ""

# 1. 清理 Gradle 缓存
echo "1. 清理 Gradle 缓存..."
./gradlew clean
if [ $? -eq 0 ]; then
    echo "✓ Gradle 清理完成"
else
    echo "✗ Gradle 清理失败，尝试手动清理..."
    rm -rf build/
    rm -rf .gradle/
    echo "✓ 手动清理完成"
fi

# 2. 清理 IDEA 项目文件
echo ""
echo "2. 清理 IDEA 项目文件..."
rm -rf .idea/
echo "✓ IDEA 配置文件已清理"

# 3. 重新生成 IDEA 项目文件
echo ""
echo "3. 重新生成项目文件..."
gradle idea
if [ $? -eq 0 ]; then
    echo "✓ IDEA 项目文件重新生成完成"
else
    echo "! 无法自动生成 IDEA 项目文件，请手动导入项目"
fi

# 4. 验证项目结构
echo ""
echo "4. 验证项目结构..."
if [ -f "build.gradle" ]; then
    echo "✓ build.gradle 存在"
else
    echo "✗ build.gradle 不存在"
fi

if [ -d "src/main/java" ]; then
    echo "✓ 源码目录存在"
else
    echo "✗ 源码目录不存在"
fi

# 5. 编译测试
echo ""
echo "5. 编译测试..."
gradle compileJava
if [ $? -eq 0 ]; then
    echo "✓ 项目编译成功"
else
    echo "✗ 项目编译失败，请检查代码错误"
fi

echo ""
echo "=== 修复完成 ==="
echo "请按以下步骤重新打开项目："
echo "1. 关闭 IntelliJ IDEA"
echo "2. 重新启动 IntelliJ IDEA"
echo "3. 选择 'Open' 并选择项目根目录"
echo "4. 等待 IDEA 重新索引项目"
echo "5. 如果仍有问题，请尝试 'File -> Invalidate Caches and Restart'"
